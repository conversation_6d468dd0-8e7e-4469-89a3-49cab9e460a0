services:
  #  spine-db:
  #    image: postgres:16
  #    environment:
  #      POSTGRES_USER: spine
  #      POSTGRES_PASSWORD: spine@123
  #      POSTGRES_DB: spine_db
  #    volumes:
  #      - postgres_data:/var/lib/postgresql/data
  #    ports:
  #      - "5432:5432"
  #    healthcheck:
  #      test: [ "CMD-SHELL", "pg_isready -U spine" ]
  #      interval: 10s
  #      timeout: 5s
  #      retries: 5
  #    logging:
  #      driver: "json-file"
  #      options:
  #        max-size: "10m"
  #        max-file: "3"

  spine-core:
    build:
      context: .
      dockerfile: ./spine-soap/src/main/docker/Dockerfile-local
    #    depends_on:
    #      postgres:
    #        condition: service_healthy
    environment:
      JAVAX_SQL_DATASOURCE_SPINECOREDS_JDBCURL: "********************************************"
      JAVAX_SQL_DATASOURCE_SPINECOREDS_DATASOURCE_USER: "postgres"
      JAVAX_SQL_DATASOURCE_SPINECOREDS_DATASOURCE_PASSWORD: "pO5t$$zum" # Need to use double $$ to escape the $
      JAVAX_SQL_DATASOURCE_SPINECOREDS_DATASOURCE_CURRENTSCHEMA: "prod"
      SECURITY_PROPERTIES_KEYCLOAK_URI: "https://iam.local.egs-dev.site"
      SECURITY_PROPERTIES_KEYCLOAK_REALM: "spine"
      SECURITY_PROPERTIES_KEYCLOAK_CLIENT_ID: "spine-core-iam"
      SECURITY_PROPERTIES_KEYCLOAK_CLIENT_SECRET: "cBTWzxSum5HEDIEGbE7WSOSgMV0YN1uW"
      LIQUIBASE_CONTEXT: "dev"
    ports:
      - "8080:8080"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - spine_network

  spine-core-frontend:
    build: ./spine-ui
    ports:
      - "8082:8082"
    depends_on:
      - spine-core
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - spine_network

#volumes:
#  postgres_data:

networks:
  spine_network:
    driver: bridge
