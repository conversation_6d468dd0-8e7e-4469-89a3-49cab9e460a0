# Composite Key New Format Implementation Guide

## Overview

The SPINE framework now supports an enhanced composite key format: `{key1:val1}_{key2:val2}_{key3:val3}` which provides explicit key-value mapping and supports flexible key ordering. This guide demonstrates the implementation with practical examples.

## New Format Structure

### Pattern
```
{keyName1:value1}_{keyName2:value2}_{keyName3:value3}
```

### Key Features
- **Explicit Mapping**: Each value is explicitly bound to its key name
- **Flexible Ordering**: Keys can appear in any order
- **Type Safety**: Values are correctly converted to their target types
- **Backward Compatibility**: Legacy format `{val1}_{val2}_{val3}` still supported

## Code Implementation Examples

### 1. Customer Product Entity Example

```java
@Entity
@Table(name = "customer_product")
@IdClass(CustomerProductId.class)
@RestRepository(path = "customer-products")
public class CustomerProduct {
    
    @Id
    @JsonProperty("customerId")
    private Long customerId;
    
    @Id
    @JsonProperty("productId")
    private String productId;
    
    @Id
    @JsonProperty("version")
    private Integer version;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("price")
    private BigDecimal price;
    
    // Constructors, getters, setters...
}
```

### 2. Updated Query Format Examples

#### Standard Order
```java
// Parse composite key with explicit mapping
String compositeKey = "{customerId:123}_{productId:PROD-001}_{version:1}";
Map<String, String> keyValueMap = CompositeKeyUtils.parseCompositeKeyWithMapping(compositeKey);

// Results in:
// keyValueMap.get("customerId") = "123"
// keyValueMap.get("productId") = "PROD-001" 
// keyValueMap.get("version") = "1"
```

#### Different Key Orders
```java
// Order 1: customerId, productId, version
String order1 = "{customerId:123}_{productId:PROD-001}_{version:1}";

// Order 2: productId, version, customerId
String order2 = "{productId:PROD-001}_{version:1}_{customerId:123}";

// Order 3: version, customerId, productId
String order3 = "{version:1}_{customerId:123}_{productId:PROD-001}";

// All three orders are equivalent and produce the same result:
CompositeKeyData keyData1 = CompositeKeyUtils.createCompositeKeyDataWithMapping(order1, CustomerProduct.class);
CompositeKeyData keyData2 = CompositeKeyUtils.createCompositeKeyDataWithMapping(order2, CustomerProduct.class);
CompositeKeyData keyData3 = CompositeKeyUtils.createCompositeKeyDataWithMapping(order3, CustomerProduct.class);

// All keyData objects contain the same values mapped to correct fields
```

### 3. Database Query Examples

#### JPA Repository Queries
```java
@Repository
public class CustomerProductRepository extends AbstractRestCRUDRepositoryImpl<CustomerProduct, String> {
    
    /**
     * Find by composite key using new format
     */
    public Optional<CustomerProduct> findByCompositeKey(String compositeKeyStr) {
        try {
            CompositeKeyData keyData = CompositeKeyUtils.createCompositeKeyDataWithMapping(
                compositeKeyStr, CustomerProduct.class);
            
            CriteriaBuilder cb = entityManager.getCriteriaBuilder();
            CriteriaQuery<CustomerProduct> query = cb.createQuery(CustomerProduct.class);
            Root<CustomerProduct> root = query.from(CustomerProduct.class);
            
            List<Predicate> predicates = new ArrayList<>();
            
            for (CompositeKeyUtils.CompositeKeyData.KeyComponent component : keyData.getComponents()) {
                predicates.add(cb.equal(root.get(component.fieldName()), component.value()));
            }
            
            query.where(predicates.toArray(new Predicate[0]));
            
            List<CustomerProduct> results = entityManager.createQuery(query).getResultList();
            return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
            
        } catch (JpaException e) {
            log.error("Error parsing composite key: {}", compositeKeyStr, e);
            return Optional.empty();
        }
    }
    
    /**
     * Find by individual key components (flexible parameter order)
     */
    public List<CustomerProduct> findByKeyComponents(Map<String, Object> keyValueMap) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<CustomerProduct> query = cb.createQuery(CustomerProduct.class);
        Root<CustomerProduct> root = query.from(CustomerProduct.class);
        
        List<Predicate> predicates = new ArrayList<>();
        
        // Support flexible key mapping
        if (keyValueMap.containsKey("customerId")) {
            predicates.add(cb.equal(root.get("customerId"), keyValueMap.get("customerId")));
        }
        if (keyValueMap.containsKey("productId")) {
            predicates.add(cb.equal(root.get("productId"), keyValueMap.get("productId")));
        }
        if (keyValueMap.containsKey("version")) {
            predicates.add(cb.equal(root.get("version"), keyValueMap.get("version")));
        }
        
        query.where(predicates.toArray(new Predicate[0]));
        return entityManager.createQuery(query).getResultList();
    }
}
```

#### JPQL Queries with Dynamic Key Mapping
```java
@Service
public class CustomerProductService {
    
    /**
     * Dynamic query builder supporting flexible key ordering
     */
    public List<CustomerProduct> findByDynamicCompositeKey(String compositeKeyStr) throws SpineException {
        Map<String, String> keyValueMap = CompositeKeyUtils.parseCompositeKeyWithMapping(compositeKeyStr);
        
        StringBuilder jpql = new StringBuilder("SELECT cp FROM CustomerProduct cp WHERE ");
        Map<String, Object> parameters = new HashMap<>();
        List<String> conditions = new ArrayList<>();
        
        // Build query conditions dynamically based on available keys
        if (keyValueMap.containsKey("customerId")) {
            conditions.add("cp.customerId = :customerId");
            parameters.put("customerId", Long.valueOf(keyValueMap.get("customerId")));
        }
        
        if (keyValueMap.containsKey("productId")) {
            conditions.add("cp.productId = :productId");
            parameters.put("productId", keyValueMap.get("productId"));
        }
        
        if (keyValueMap.containsKey("version")) {
            conditions.add("cp.version = :version");
            parameters.put("version", Integer.valueOf(keyValueMap.get("version")));
        }
        
        jpql.append(String.join(" AND ", conditions));
        
        TypedQuery<CustomerProduct> query = entityManager.createQuery(jpql.toString(), CustomerProduct.class);
        
        // Set parameters
        for (Map.Entry<String, Object> param : parameters.entrySet()) {
            query.setParameter(param.getKey(), param.getValue());
        }
        
        return query.getResultList();
    }
}
```

### 4. REST API Usage Examples

#### Different Key Orders in REST URLs
```http
# Order 1: Standard alphabetical order
GET /api/v1/data-rest/customer-products/{customerId:123}_{productId:PROD-001}_{version:1}

# Order 2: Business logic order (product first)
GET /api/v1/data-rest/customer-products/{productId:PROD-001}_{customerId:123}_{version:1}

# Order 3: Version-first order
GET /api/v1/data-rest/customer-products/{version:1}_{productId:PROD-001}_{customerId:123}

# All URLs above resolve to the same entity
```

#### Creating Entities with Composite Keys
```http
POST /api/v1/data-rest/customer-products
Content-Type: application/json

{
  "description": "Premium Widget",
  "price": 299.99
}

# Response Location Header (keys sorted alphabetically for consistency):
Location: /api/v1/data-rest/customer-products/{customerId:123}_{productId:PROD-001}_{version:1}
```

### 5. Service Layer Implementation

```java
@Service
public class CompositeKeyExampleService {
    
    @Inject
    private EntityManager entityManager;
    
    /**
     * Demonstrates flexible key value assignment
     */
    public CustomerProduct findByFlexibleKey(Long customerId, String productId, Integer version) {
        // Method 1: Build key string with explicit mapping
        Map<String, Object> keyMap = Map.of(
            "customerId", customerId,
            "productId", productId,
            "version", version
        );
        String compositeKey = CompositeKeyUtils.formatCompositeKeyWithMapping(keyMap);
        
        // Method 2: Use different key orders
        String compositeKey1 = String.format("{customerId:%d}_{productId:%s}_{version:%d}", 
                customerId, productId, version);
        String compositeKey2 = String.format("{productId:%s}_{version:%d}_{customerId:%d}", 
                productId, version, customerId);
        String compositeKey3 = String.format("{version:%d}_{customerId:%d}_{productId:%s}", 
                version, customerId, productId);
        
        // All three keys resolve to the same entity
        CompositeKeyData keyData = CompositeKeyUtils.createCompositeKeyDataWithMapping(
                compositeKey1, CustomerProduct.class);
        
        return findEntityByCompositeKeyData(keyData);
    }
    
    /**
     * Generic method to find entity by composite key data
     */
    private CustomerProduct findEntityByCompositeKeyData(CompositeKeyData keyData) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<CustomerProduct> query = cb.createQuery(CustomerProduct.class);
        Root<CustomerProduct> root = query.from(CustomerProduct.class);
        
        List<Predicate> predicates = new ArrayList<>();
        
        for (CompositeKeyUtils.CompositeKeyData.KeyComponent component : keyData.getComponents()) {
            predicates.add(cb.equal(root.get(component.fieldName()), component.value()));
        }
        
        query.where(predicates.toArray(new Predicate[0]));
        
        List<CustomerProduct> results = entityManager.createQuery(query).getResultList();
        return results.isEmpty() ? null : results.get(0);
    }
}
```

### 6. Advanced Query Examples

#### Partial Key Matching
```java
/**
 * Find entities by partial composite key (any combination of key components)
 */
public List<CustomerProduct> findByPartialKey(String partialCompositeKey) throws SpineException {
    Map<String, String> availableKeys = CompositeKeyUtils.parseCompositeKeyWithMapping(partialCompositeKey);
    
    CriteriaBuilder cb = entityManager.getCriteriaBuilder();
    CriteriaQuery<CustomerProduct> query = cb.createQuery(CustomerProduct.class);
    Root<CustomerProduct> root = query.from(CustomerProduct.class);
    
    List<Predicate> predicates = new ArrayList<>();
    
    // Add predicates only for available keys
    if (availableKeys.containsKey("customerId")) {
        predicates.add(cb.equal(root.get("customerId"), 
                Long.valueOf(availableKeys.get("customerId"))));
    }
    
    if (availableKeys.containsKey("productId")) {
        predicates.add(cb.equal(root.get("productId"), availableKeys.get("productId")));
    }
    
    if (availableKeys.containsKey("version")) {
        predicates.add(cb.equal(root.get("version"), 
                Integer.valueOf(availableKeys.get("version"))));
    }
    
    if (!predicates.isEmpty()) {
        query.where(predicates.toArray(new Predicate[0]));
    }
    
    return entityManager.createQuery(query).getResultList();
}

// Usage examples:
// findByPartialKey("{customerId:123}") - finds all products for customer 123
// findByPartialKey("{productId:PROD-001}") - finds all versions of PROD-001
// findByPartialKey("{customerId:123}_{productId:PROD-001}") - finds all versions of customer's product
```

#### Range Queries with Composite Keys
```java
/**
 * Find entities within a range using composite key components
 */
public List<CustomerProduct> findByKeyRange(Long minCustomerId, Long maxCustomerId, 
                                           String productPattern, Integer minVersion) {
    CriteriaBuilder cb = entityManager.getCriteriaBuilder();
    CriteriaQuery<CustomerProduct> query = cb.createQuery(CustomerProduct.class);
    Root<CustomerProduct> root = query.from(CustomerProduct.class);
    
    List<Predicate> predicates = new ArrayList<>();
    
    // Range predicates
    if (minCustomerId != null && maxCustomerId != null) {
        predicates.add(cb.between(root.get("customerId"), minCustomerId, maxCustomerId));
    }
    
    if (productPattern != null) {
        predicates.add(cb.like(root.get("productId"), productPattern));
    }
    
    if (minVersion != null) {
        predicates.add(cb.greaterThanOrEqualTo(root.get("version"), minVersion));
    }
    
    query.where(predicates.toArray(new Predicate[0]));
    query.orderBy(cb.asc(root.get("customerId")), 
                  cb.asc(root.get("productId")), 
                  cb.desc(root.get("version")));
    
    return entityManager.createQuery(query).getResultList();
}
```

### 7. Testing Examples

```java
@Test
public class CompositeKeyNewFormatTest {
    
    @Test
    void shouldSupportDifferentKeyOrders() throws SpineException {
        // Different key orders should produce equivalent results
        String order1 = "{customerId:123}_{productId:PROD-001}_{version:1}";
        String order2 = "{productId:PROD-001}_{version:1}_{customerId:123}";
        String order3 = "{version:1}_{customerId:123}_{productId:PROD-001}";
        
        CompositeKeyData keyData1 = CompositeKeyUtils.createCompositeKeyDataWithMapping(
                order1, CustomerProduct.class);
        CompositeKeyData keyData2 = CompositeKeyUtils.createCompositeKeyDataWithMapping(
                order2, CustomerProduct.class);
        CompositeKeyData keyData3 = CompositeKeyUtils.createCompositeKeyDataWithMapping(
                order3, CustomerProduct.class);
        
        // All should have same components with correct values
        assertEquals(3, keyData1.size());
        assertEquals(3, keyData2.size());
        assertEquals(3, keyData3.size());
        
        // Verify values are correctly mapped regardless of order
        for (CompositeKeyData keyData : List.of(keyData1, keyData2, keyData3)) {
            Map<String, Object> componentMap = keyData.getComponents().stream()
                    .collect(Collectors.toMap(
                            CompositeKeyUtils.CompositeKeyData.KeyComponent::fieldName,
                            CompositeKeyUtils.CompositeKeyData.KeyComponent::value
                    ));
            
            assertEquals(123L, componentMap.get("customerId"));
            assertEquals("PROD-001", componentMap.get("productId"));
            assertEquals(1, componentMap.get("version"));
        }
    }
    
    @Test
    void shouldFormatKeyWithCorrectMapping() throws SpineException {
        Map<String, Object> keyMap = Map.of(
            "customerId", 456L,
            "productId", "WIDGET-002",
            "version", 2
        );
        
        String formatted = CompositeKeyUtils.formatCompositeKeyWithMapping(keyMap);
        
        // Should be sorted alphabetically: customerId, productId, version
        assertEquals("{customerId:456}_{productId:WIDGET-002}_{version:2}", formatted);
    }
    
    @Test
    void shouldHandleComplexKeyValues() throws SpineException {
        // Test with special characters in values
        String complexKey = "{customerId:123}_{productId:COMPLEX-PROD.001_test}_{version:1}";
        
        Map<String, String> parsed = CompositeKeyUtils.parseCompositeKeyWithMapping(complexKey);
        
        assertEquals("123", parsed.get("customerId"));
        assertEquals("COMPLEX-PROD.001_test", parsed.get("productId"));
        assertEquals("1", parsed.get("version"));
    }
    
    @Test
    void shouldValidateKeyNamesAndValues() {
        // Invalid key name (starts with number)
        assertThrows(JpaException.class, () -> 
                CompositeKeyUtils.parseCompositeKeyWithMapping("{123key:value}"));
        
        // Invalid key name (contains special characters)
        assertThrows(JpaException.class, () -> 
                CompositeKeyUtils.parseCompositeKeyWithMapping("{key-name:value}"));
        
        // Invalid value (contains invalid characters)
        assertThrows(JpaException.class, () -> 
                CompositeKeyUtils.parseCompositeKeyWithMapping("{keyName:val@ue}"));
    }
    
    @Test
    void shouldSupportBackwardCompatibility() throws SpineException {
        // Legacy format should still work
        String legacyKey = "{123}_{PROD-001}_{1}";
        
        CompositeKeyData keyData = CompositeKeyUtils.createCompositeKeyData(
                legacyKey, CustomerProduct.class);
        
        assertEquals(3, keyData.size());
        // Values should be assigned in field name order (alphabetical)
    }
}
```

### 8. Performance Optimizations

```java
@Component
public class CompositeKeyPerformanceOptimizer {
    
    // Cache for parsed key patterns
    private final Map<String, Map<String, String>> keyParseCache = new ConcurrentHashMap<>();
    
    /**
     * Cached parsing for frequently used key patterns
     */
    public Map<String, String> parseCached(String compositeKey) throws SpineException {
        return keyParseCache.computeIfAbsent(compositeKey, key -> {
            try {
                return CompositeKeyUtils.parseCompositeKeyWithMapping(key);
            } catch (JpaException e) {
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * Batch processing for multiple composite keys
     */
    public List<CompositeKeyData> parseBatch(List<String> compositeKeys, Class<?> entityClass) {
        return compositeKeys.parallelStream()
                .map(key -> {
                    try {
                        return CompositeKeyUtils.createCompositeKeyDataWithMapping(key, entityClass);
                    } catch (JpaException e) {
                        log.warn("Failed to parse key: {}", key, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
```

## Migration from Legacy Format

### Automatic Detection and Conversion
```java
public class CompositeKeyMigrationService {
    
    /**
     * Converts legacy format to new format
     */
    public String convertLegacyToNewFormat(String legacyKey, Class<?> entityClass) throws SpineException {
        // Check if already in new format
        if (legacyKey.contains(CompositeKeyUtils.KEY_VALUE_SEPARATOR)) {
            return legacyKey; // Already in new format
        }
        
        // Parse legacy format
        List<String> values = CompositeKeyUtils.parseCompositeKey(legacyKey);
        List<Field> idFields = CompositeKeyUtils.getIdFields(entityClass);
        
        if (values.size() != idFields.size()) {
            throw new SpineException("Legacy key component count mismatch");
        }
        
        // Build new format with explicit mapping
        Map<String, Object> keyMap = new HashMap<>();
        for (int i = 0; i < idFields.size(); i++) {
            keyMap.put(idFields.get(i).getName(), values.get(i));
        }
        
        return CompositeKeyUtils.formatCompositeKeyWithMapping(keyMap);
    }
}
```

## Conclusion

The new composite key format `{key1:val1}_{key2:val2}_{key3:val3}` provides:

1. **Explicit Key-Value Mapping**: Clear association between keys and values
2. **Flexible Ordering**: Keys can appear in any order
3. **Type Safety**: Automatic type conversion based on field definitions
4. **Enhanced Security**: Strict validation of key names and values
5. **Backward Compatibility**: Legacy format continues to work
6. **Performance Optimization**: Efficient parsing and caching mechanisms

This implementation ensures robust, secure, and maintainable composite key handling while providing the flexibility needed for complex database operations. 