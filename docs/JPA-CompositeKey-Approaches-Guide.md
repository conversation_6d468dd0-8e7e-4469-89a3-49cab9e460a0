# JPA Composite Key Approaches Guide

## Overview

The SPINE framework supports three different JPA approaches for implementing composite primary keys. This guide explains the differences, use cases, advantages, and implementation details for each approach.

## Table of Contents

1. [Multiple @Id Fields Approach](#multiple-id-fields-approach)
2. [@IdClass Approach](#idclass-approach)
3. [@EmbeddedId Approach](#embeddedid-approach)
4. [Comparison Matrix](#comparison-matrix)
5. [Performance Considerations](#performance-considerations)
6. [Migration Strategies](#migration-strategies)
7. [Best Practices](#best-practices)

---

## Multiple @Id Fields Approach

### Description
The simplest approach where multiple fields in the entity class are annotated with `@Id`. JPA automatically treats these fields as a composite primary key.

### Implementation Example

```java
@Entity
@Table(name = "component_status")
@RestRepository(path = "component-status")
public class ComponentStatus {
    
    @Id
    @JsonProperty("componentId")
    private Long componentId;
    
    @Id
    @JsonProperty("statusType")
    private String statusType;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("lastUpdated")
    private LocalDateTime lastUpdated;
    
    // Constructors, getters, setters...
}
```

### Repository Implementation

```java
@ApplicationScoped
@RestRepository(path = "component-status")
public class ComponentStatusRepository extends AbstractRestCRUDRepositoryImpl<ComponentStatus, String> {
    
    @Override
    protected boolean isNew(ComponentStatus entity) {
        return entity.getComponentId() == null && entity.getStatusType() == null;
    }
    
    @Override
    public Class<ComponentStatus> getDomainType() {
        return ComponentStatus.class;
    }
}
```

### REST API Usage

```http
# Create
POST /api/v1/data-rest/component-status
{
  "status": "ACTIVE",
  "lastUpdated": "2024-01-15T10:30:00"
}

# Response: Location header will contain {componentId}_{statusType}
# Example: /api/v1/data-rest/component-status/{123}_{OPERATIONAL}

# Retrieve
GET /api/v1/data-rest/component-status/{123}_{OPERATIONAL}

# Update
PUT /api/v1/data-rest/component-status/{123}_{OPERATIONAL}
{
  "status": "INACTIVE",
  "lastUpdated": "2024-01-15T15:45:00"
}

# Delete
DELETE /api/v1/data-rest/component-status/{123}_{OPERATIONAL}
```

### Advantages
- **Simplicity**: Easiest to implement and understand
- **Direct Field Access**: No additional classes required
- **Minimal Boilerplate**: Least amount of code
- **JPA Query Friendly**: Direct field references in JPQL/Criteria queries

### Disadvantages
- **No Type Safety**: No dedicated type for the composite key
- **Parameter Explosion**: Methods require multiple parameters for the key
- **Limited Reusability**: Key logic scattered across entity methods

### Use Cases
- **Simple Composite Keys**: When the composite key has 2-3 components
- **Legacy Systems**: When migrating existing simple composite keys
- **Prototyping**: Quick development scenarios
- **Join Tables**: Many-to-many relationship mappings

---

## @IdClass Approach

### Description
Uses a separate class to define the composite key structure. The entity class has multiple `@Id` fields, and the `@IdClass` annotation points to the separate key class.

### Implementation Example

```java
// Separate ID class
public class CustomerProductId implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private Long customerId;
    private String productId;
    
    // Must have default constructor
    public CustomerProductId() {}
    
    public CustomerProductId(Long customerId, String productId) {
        this.customerId = customerId;
        this.productId = productId;
    }
    
    // Must implement equals and hashCode
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        CustomerProductId that = (CustomerProductId) obj;
        return Objects.equals(customerId, that.customerId) &&
               Objects.equals(productId, that.productId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(customerId, productId);
    }
    
    // Getters and setters...
}

// Entity class
@Entity
@Table(name = "customer_product")
@IdClass(CustomerProductId.class)
@RestRepository(path = "customer-products-idclass")
public class CustomerProduct {
    
    @Id
    @JsonProperty("customerId")
    private Long customerId;
    
    @Id
    @JsonProperty("productId")
    private String productId;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("price")
    private BigDecimal price;
    
    // Constructors, getters, setters...
}
```

### Repository Implementation

```java
@ApplicationScoped
@RestRepository(path = "customer-products-idclass")
public class CustomerProductRepository extends AbstractRestCRUDRepositoryImpl<CustomerProduct, String> {
    
    @Override
    protected boolean isNew(CustomerProduct entity) {
        return entity.getCustomerId() == null && entity.getProductId() == null;
    }
    
    @Override
    public Class<CustomerProduct> getDomainType() {
        return CustomerProduct.class;
    }
}
```

### Advanced Usage

```java
// Using the ID class directly
public class CustomerProductService {
    
    @Inject
    private EntityManager entityManager;
    
    public CustomerProduct findByCompositeKey(CustomerProductId id) {
        return entityManager.find(CustomerProduct.class, id);
    }
    
    public List<CustomerProduct> findByCustomerId(Long customerId) {
        return entityManager.createQuery(
            "SELECT cp FROM CustomerProduct cp WHERE cp.customerId = :customerId", 
            CustomerProduct.class)
            .setParameter("customerId", customerId)
            .getResultList();
    }
}
```

### REST API Usage

```http
# Create
POST /api/v1/data-rest/customer-products-idclass
{
  "description": "Premium Product",
  "price": 99.99
}

# Response: Location header contains {customerId}_{productId}
# Example: /api/v1/data-rest/customer-products-idclass/{123}_{PROD-001}

# Retrieve
GET /api/v1/data-rest/customer-products-idclass/{123}_{PROD-001}

# Update
PUT /api/v1/data-rest/customer-products-idclass/{123}_{PROD-001}
{
  "description": "Updated Premium Product",
  "price": 149.99
}

# Delete
DELETE /api/v1/data-rest/customer-products-idclass/{123}_{PROD-001}
```

### Advantages
- **Type Safety**: Dedicated type for composite keys
- **Clean API**: Methods can accept single ID parameter
- **Reusability**: ID class can be used across different contexts
- **JPA Native Support**: Full JPA feature support including `find()` methods
- **Familiar Pattern**: Similar to single-key entities

### Disadvantages
- **Additional Class**: Requires separate ID class
- **Field Duplication**: Key fields exist in both entity and ID class
- **Synchronization Overhead**: Must keep entity and ID class fields in sync
- **Boilerplate Code**: More code for equals, hashCode, serialization

### Use Cases
- **Business Objects**: When composite key represents a business concept
- **External APIs**: When key structure is defined by external systems
- **Legacy Integration**: Matching existing database schemas
- **Service Layer**: When service methods need to work with key objects

---

## @EmbeddedId Approach

### Description
Uses an embeddable class that contains all composite key fields. The entity has a single field annotated with `@EmbeddedId` that holds the composite key.

### Implementation Example

```java
// Embeddable ID class
@Embeddable
public class OrderItemKey implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @NotNull
    @JsonProperty("orderId")
    private Long orderId;
    
    @NotNull
    @Size(max = 50)
    @JsonProperty("itemId")
    private String itemId;
    
    // Constructors
    public OrderItemKey() {}
    
    public OrderItemKey(Long orderId, String itemId) {
        this.orderId = orderId;
        this.itemId = itemId;
    }
    
    // Must implement equals and hashCode
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        OrderItemKey that = (OrderItemKey) obj;
        return Objects.equals(orderId, that.orderId) &&
               Objects.equals(itemId, that.itemId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(orderId, itemId);
    }
    
    // Getters and setters...
}

// Entity class
@Entity
@Table(name = "order_item")
@RestRepository(path = "order-items-embedded")
public class OrderItem {
    
    @EmbeddedId
    @JsonProperty("id")
    private OrderItemKey id;
    
    @JsonProperty("itemName")
    private String itemName;
    
    @JsonProperty("unitPrice")
    private BigDecimal unitPrice;
    
    @JsonProperty("quantity")
    private Integer quantity;
    
    // Convenience constructors
    public OrderItem() {}
    
    public OrderItem(Long orderId, String itemId, String itemName, 
                    BigDecimal unitPrice, Integer quantity) {
        this.id = new OrderItemKey(orderId, itemId);
        this.itemName = itemName;
        this.unitPrice = unitPrice;
        this.quantity = quantity;
    }
    
    // Convenience getters/setters for key components
    @JsonProperty("orderId")
    public Long getOrderId() {
        return id != null ? id.getOrderId() : null;
    }
    
    @JsonProperty("itemId")
    public String getItemId() {
        return id != null ? id.getItemId() : null;
    }
    
    public void setOrderId(Long orderId) {
        if (this.id == null) {
            this.id = new OrderItemKey();
        }
        this.id.setOrderId(orderId);
    }
    
    public void setItemId(String itemId) {
        if (this.id == null) {
            this.id = new OrderItemKey();
        }
        this.id.setItemId(itemId);
    }
    
    // Standard getters and setters...
}
```

### Repository Implementation

```java
@ApplicationScoped
@RestRepository(path = "order-items-embedded")
public class OrderItemRepository extends AbstractRestCRUDRepositoryImpl<OrderItem, String> {
    
    @Override
    protected boolean isNew(OrderItem entity) {
        return entity.getId() == null || 
               (entity.getId().getOrderId() == null && entity.getId().getItemId() == null);
    }
    
    @Override
    public Class<OrderItem> getDomainType() {
        return OrderItem.class;
    }
}
```

### Advanced Usage

```java
// Using embedded ID directly
public class OrderItemService {
    
    @Inject
    private EntityManager entityManager;
    
    public OrderItem findByCompositeKey(OrderItemKey id) {
        return entityManager.find(OrderItem.class, id);
    }
    
    public OrderItem findByComponents(Long orderId, String itemId) {
        OrderItemKey key = new OrderItemKey(orderId, itemId);
        return entityManager.find(OrderItem.class, key);
    }
    
    public List<OrderItem> findByOrderId(Long orderId) {
        return entityManager.createQuery(
            "SELECT oi FROM OrderItem oi WHERE oi.id.orderId = :orderId", 
            OrderItem.class)
            .setParameter("orderId", orderId)
            .getResultList();
    }
}
```

### REST API Usage

```http
# Create
POST /api/v1/data-rest/order-items-embedded
{
  "itemName": "Premium Widget",
  "unitPrice": 99.99,
  "quantity": 5
}

# Response: Location header contains {itemId}_{orderId} (sorted alphabetically)
# Example: /api/v1/data-rest/order-items-embedded/{ITEM-001}_{1001}

# Retrieve
GET /api/v1/data-rest/order-items-embedded/{ITEM-001}_{1001}

# Update
PUT /api/v1/data-rest/order-items-embedded/{ITEM-001}_{1001}
{
  "itemName": "Updated Premium Widget",
  "unitPrice": 149.99,
  "quantity": 3
}

# Delete
DELETE /api/v1/data-rest/order-items-embedded/{ITEM-001}_{1001}
```

### Advantages
- **Encapsulation**: Complete key encapsulation in single object
- **Clean Entity Design**: Entity has single ID field
- **Rich Key Behavior**: Key class can have business methods
- **Validation**: Bean validation annotations on key components
- **JSON Friendly**: Better JSON serialization control

### Disadvantages
- **Query Complexity**: Requires dot notation in queries (`id.orderId`)
- **Additional Class**: Requires embeddable ID class
- **Framework Learning Curve**: Less familiar to developers
- **Potential N+1**: Can cause performance issues if not properly handled

### Use Cases
- **Domain-Driven Design**: When composite key is a value object
- **Complex Keys**: Keys with 3+ components or complex validation
- **Rich Key Behavior**: When key needs business methods
- **API Design**: When key should be treated as single unit in APIs

---

## Comparison Matrix

| Aspect | Multiple @Id | @IdClass | @EmbeddedId |
|--------|-------------|----------|-------------|
| **Complexity** | Low | Medium | Medium-High |
| **Code Volume** | Minimal | Medium | Medium |
| **Type Safety** | None | High | High |
| **Query Syntax** | `entity.field` | `entity.field` | `entity.id.field` |
| **JPA Support** | Full | Full | Full |
| **JSON Control** | Basic | Medium | High |
| **Key Reusability** | None | High | High |
| **Learning Curve** | None | Low | Medium |
| **Performance** | Best | Good | Good |
| **Maintenance** | Easy | Medium | Medium |

## Performance Considerations

### Query Performance

```java
// Multiple @Id - Direct field access (fastest)
@Query("SELECT e FROM Entity e WHERE e.field1 = :f1 AND e.field2 = :f2")

// @IdClass - Direct field access (fast)
@Query("SELECT e FROM Entity e WHERE e.field1 = :f1 AND e.field2 = :f2")

// @EmbeddedId - Dot notation (slightly slower)
@Query("SELECT e FROM Entity e WHERE e.id.field1 = :f1 AND e.id.field2 = :f2")
```

### Memory Usage

- **Multiple @Id**: Lowest memory overhead
- **@IdClass**: Additional object allocation for ID instances
- **@EmbeddedId**: Additional object allocation, but better encapsulation

### Database Operations

All approaches generate identical SQL, so database performance is equivalent.

## Migration Strategies

### From Multiple @Id to @IdClass

```java
// Step 1: Create ID class
public class EntityId implements Serializable {
    private Long field1;
    private String field2;
    // equals, hashCode, constructors...
}

// Step 2: Add @IdClass annotation
@Entity
@IdClass(EntityId.class)
public class Entity {
    @Id private Long field1;
    @Id private String field2;
    // existing fields...
}

// Step 3: Update service methods gradually
public class EntityService {
    // Old method (still works)
    public Entity findByFields(Long field1, String field2) {
        // implementation...
    }
    
    // New method
    public Entity findById(EntityId id) {
        return entityManager.find(Entity.class, id);
    }
}
```

### From @IdClass to @EmbeddedId

```java
// Step 1: Convert ID class to embeddable
@Embeddable
public class EntityKey implements Serializable {
    @JsonProperty("field1")
    private Long field1;
    
    @JsonProperty("field2")
    private String field2;
    // equals, hashCode, constructors...
}

// Step 2: Update entity
@Entity
public class Entity {
    @EmbeddedId
    @JsonProperty("id")
    private EntityKey id;
    
    // Add convenience getters/setters
    @JsonProperty("field1")
    public Long getField1() {
        return id != null ? id.getField1() : null;
    }
    
    // other fields...
}

// Step 3: Update queries
@Query("SELECT e FROM Entity e WHERE e.id.field1 = :field1")
```

## Best Practices

### 1. Choose the Right Approach

- **Use Multiple @Id for**: Simple composite keys, legacy systems, prototypes
- **Use @IdClass for**: Business key objects, external API integration, service layer design
- **Use @EmbeddedId for**: Domain-driven design, complex keys, rich key behavior

### 2. Implement Required Methods

All composite key classes must implement:
- `equals()` method
- `hashCode()` method
- `Serializable` interface
- Default constructor

### 3. JSON Handling

```java
// Good: Explicit JSON control
@JsonProperty("customerId")
private Long customerId;

// Better: Convenience accessors for @EmbeddedId
@JsonProperty("customerId")
public Long getCustomerId() {
    return id != null ? id.getCustomerId() : null;
}
```

### 4. Repository Design

```java
// Good: Check all key components
@Override
protected boolean isNew(Entity entity) {
    return entity.getField1() == null && entity.getField2() == null;
}

// Better: For @EmbeddedId, check both ID object and components
@Override
protected boolean isNew(Entity entity) {
    return entity.getId() == null || 
           (entity.getId().getField1() == null && entity.getId().getField2() == null);
}
```

### 5. Error Handling

```java
// Validate all key components
public void validateCompositeKey(EntityKey key) {
    if (key == null) {
        throw new IllegalArgumentException("Key cannot be null");
    }
    if (key.getField1() == null || key.getField2() == null) {
        throw new IllegalArgumentException("All key components must be non-null");
    }
}
```

### 6. Testing

```java
@Test
void shouldSupportFullCrudCycle() {
    // Create entity with composite key
    Entity entity = new Entity();
    entity.setField1(123L);
    entity.setField2("TEST");
    
    // Test extraction
    Object id = EntityReflectionUtils.extractId(entity);
    assertEquals("{123}_{TEST}", id);
    
    // Test parsing
    CompositeKeyData keyData = CompositeKeyUtils.createCompositeKeyData(
        (String) id, Entity.class);
    assertEquals(2, keyData.size());
    
    // Test setting
    Entity newEntity = new Entity();
    EntityReflectionUtils.setIdOnEntity(newEntity, id);
    assertEquals(entity.getField1(), newEntity.getField1());
    assertEquals(entity.getField2(), newEntity.getField2());
}
```

## Conclusion

The SPINE framework's composite key support provides flexibility to choose the most appropriate JPA approach for your use case:

- **Multiple @Id**: Simple, direct, minimal overhead
- **@IdClass**: Type-safe, familiar, service-friendly
- **@EmbeddedId**: Encapsulated, rich behavior, domain-driven

All approaches seamlessly integrate with the `{key1}_{key2}` REST API format, providing consistent external interfaces regardless of internal implementation choice. 