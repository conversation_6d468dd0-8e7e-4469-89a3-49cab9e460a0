# Composite Key REST API Documentation

## Overview

The SPINE framework has been enhanced to support composite primary keys in REST API endpoints using the format `{key1}_{key2}_{key3}`. This feature enables RESTful operations on entities with multiple primary key components while maintaining enterprise-level security, performance, and scalability standards.

## Key Features

- **Flexible Key Support**: Handles 1-10 key components per entity
- **Type Safety**: Automatic conversion to appropriate data types (Long, Integer, String, Boolean, etc.)
- **Security**: Built-in protection against injection attacks and malicious input
- **Performance**: Optimized with caching for high-throughput environments
- **Backward Compatibility**: Single-key entities continue to work unchanged

## Composite Key Format

### URL Pattern
```
/api/v1/data-rest/{entityPath}/{key1}_{key2}_{keyN}
```

### Format Rules
- Each key component must be wrapped in curly braces: `{value}`
- Components are separated by underscores: `_`
- Component values support: letters, numbers, dots, hyphens, underscores
- Maximum 10 components per composite key
- Maximum 100 characters per component
- Maximum 500 characters total key length

### Examples
```
{123}_{456}                    # Two-component key
{customer-001}_{product.abc}   # Alphanumeric with special chars
{1}_{2}_{3}                    # Three-component key
```

## Entity Definition

### Composite Key Entity
```java
@Entity
@Table(name = "customer_product")
@RestRepository(path = "customer-products")
public class CustomerProduct {
    
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "customer_seq")
    @JsonProperty("customerId")
    private Long customerId;
    
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "product_seq")
    @JsonProperty("productId")
    private String productId;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("price")
    private Double price;
    
    // Constructors, getters, setters...
}
```

### Repository Configuration
```java
@RestRepository(path = "customer-products")
@ApplicationScoped
public class CustomerProductRepository extends AbstractRestCRUDRepositoryImpl<CustomerProduct, String> {
    
    @Override
    protected boolean isNew(CustomerProduct entity) {
        return entity.getCustomerId() == null && entity.getProductId() == null;
    }
    
    @Override
    public Class<CustomerProduct> getDomainType() {
        return CustomerProduct.class;
    }
}
```

## REST API Operations

### 1. Create Entity (POST)

**Request:**
```http
POST /api/v1/data-rest/customer-products
Content-Type: application/json

{
  "description": "Premium Product",
  "price": 99.99
}
```

**Response:**
```http
HTTP/1.1 201 Created
Location: /api/v1/data-rest/customer-products/{123}_{abc456}
Content-Type: application/json

{
  "customerId": 123,
  "productId": "abc456",
  "description": "Premium Product",
  "price": 99.99
}
```

### 2. Get Entity by Composite Key (GET)

**Request:**
```http
GET /api/v1/data-rest/customer-products/{123}_{abc456}
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "customerId": 123,
  "productId": "abc456",
  "description": "Premium Product",
  "price": 99.99
}
```

### 3. Update Entity (PUT)

**Request:**
```http
PUT /api/v1/data-rest/customer-products/{123}_{abc456}
Content-Type: application/json

{
  "customerId": 123,
  "productId": "abc456",
  "description": "Updated Premium Product",
  "price": 149.99
}
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "customerId": 123,
  "productId": "abc456",
  "description": "Updated Premium Product",
  "price": 149.99
}
```

### 4. Delete Entity (DELETE)

**Request:**
```http
DELETE /api/v1/data-rest/customer-products/{123}_{abc456}
```

**Response:**
```http
HTTP/1.1 204 No Content
```

### 5. List Entities with Pagination (GET)

**Request:**
```http
GET /api/v1/data-rest/customer-products?page=0&size=20&sort=customerId,asc
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "content": [
    {
      "customerId": 123,
      "productId": "abc456",
      "description": "Premium Product",
      "price": 99.99
    }
  ],
  "page": {
    "size": 20,
    "number": 0,
    "totalElements": 1,
    "totalPages": 1
  }
}
```

## Error Handling

### Security Violations

**Invalid Characters:**
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "error": "Invalid characters detected in composite key",
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/api/v1/data-rest/customer-products/{script}_{test}"
}
```

**SQL Injection Attempt:**
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "error": "Invalid characters detected in ID string",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Format Violations

**Missing Braces:**
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "error": "Composite key must use format {key1}_{key2}_{key3}. Invalid: 123_456",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Component Count Mismatch:**
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "error": "Composite key '{123}' has 1 components but entity 'CustomerProduct' requires 2 components",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Type Conversion Error:**
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "error": "Failed to convert key component 'abc' to type Long for field 'customerId'",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Length Violations

**Excessive Key Length:**
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "error": "ID string exceeds maximum allowed length",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Too Many Components:**
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "error": "Too many key components: 11. Maximum allowed: 10",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Security Considerations

### Input Validation
- All key components are validated for safe characters: `[a-zA-Z0-9._-]`
- Maximum length limits prevent DoS attacks
- SQL injection patterns are detected and rejected
- Script injection attempts are blocked

### Allowed Characters
- Letters: `a-z`, `A-Z`
- Numbers: `0-9`
- Special characters: `.` (dot), `-` (hyphen), `_` (underscore)

### Blocked Patterns
- SQL keywords: `select`, `insert`, `update`, `delete`, `drop`, `union`
- Script tags: `script`, `javascript`, `vbscript`
- SQL comments: `--`, `/*`, `*/`
- System procedures: `xp_`, `sp_`, `exec`

## Performance Optimizations

### Caching Strategy
- Entity reflection analysis is cached for repeated operations
- ID field detection uses concurrent maps for thread safety
- Cache clearing available for memory management

### Database Optimization
- Composite key queries use JPA Criteria API for optimal performance
- Proper indexing recommended on composite key columns
- Connection pooling and transaction management handled by framework

### Scalability Features
- Thread-safe implementation using concurrent collections
- Minimal memory footprint with defensive copying
- Efficient regex patterns for key parsing

## Migration Guide

### From Single Keys
Existing single-key entities require no changes and continue to work as before.

### Adding Composite Keys
1. Add multiple `@Id` annotations to entity fields
2. Update repository to handle null checks for all key components
3. Test with new composite key format in URLs

### Example Migration
```java
// Before (single key)
@Entity
public class Product {
    @Id
    private Long id;
}

// After (composite key)
@Entity  
public class Product {
    @Id
    private Long customerId;
    
    @Id
    private String productCode;
}
```

## Testing Examples

### Unit Testing
```java
@Test
void shouldParseValidCompositeKey() throws SpineException {
    List<String> components = CompositeKeyUtils.parseCompositeKey("{123}_{abc}");
    assertEquals(2, components.size());
    assertEquals("123", components.get(0));
    assertEquals("abc", components.get(1));
}
```

### Integration Testing
```java
@Test
void shouldCreateAndRetrieveEntityWithCompositeKey() {
    // Create entity
    ResponseEntity<CustomerProduct> createResponse = restTemplate.postForEntity(
        "/api/v1/data-rest/customer-products", 
        new CustomerProduct("Test Product", 99.99),
        CustomerProduct.class
    );
    
    // Extract composite key from Location header
    String location = createResponse.getHeaders().getLocation().toString();
    String compositeKey = location.substring(location.lastIndexOf('/') + 1);
    
    // Retrieve entity
    ResponseEntity<CustomerProduct> getResponse = restTemplate.getForEntity(
        "/api/v1/data-rest/customer-products/" + compositeKey,
        CustomerProduct.class
    );
    
    assertEquals(HttpStatus.OK, getResponse.getStatusCode());
}
```

## Monitoring and Logging

### Key Metrics
- Composite key parsing performance
- Cache hit rates for entity reflection
- Security violation frequencies
- Database query performance

### Log Levels
- `DEBUG`: Key parsing operations and cache usage
- `WARN`: Security violations and format errors  
- `ERROR`: System exceptions and conversion failures

### Example Log Output
```
2024-01-15 10:30:00 DEBUG CompositeKeyUtils - Parsed composite key '{123}_{abc}' into 2 components: [123, abc]
2024-01-15 10:30:01 WARN  CompositeKeyUtils - Dangerous pattern 'script' detected in composite key
2024-01-15 10:30:02 ERROR EntityReflectionUtils - Failed to convert ID '123abc' to type Long
```

## Best Practices

### Entity Design
- Use meaningful field names for composite key components
- Keep key components reasonably sized (< 50 characters recommended)
- Consider using generated values for better performance
- Document key component relationships and constraints

### API Usage
- Always validate composite key format on client side
- Use proper error handling for malformed keys
- Cache entity metadata to improve client performance
- Implement proper retry logic for transient failures

### Security
- Never expose internal system identifiers in composite keys
- Validate all key components against business rules
- Log suspicious patterns for security monitoring
- Implement rate limiting for key validation endpoints

## Troubleshooting

### Common Issues

**1. Key Component Not Found**
- Verify all `@Id` fields are properly annotated
- Check field name spelling in composite key
- Ensure entity class is properly registered

**2. Type Conversion Errors**
- Verify key component format matches field type
- Check for leading/trailing whitespace in components
- Ensure numeric values are within valid ranges

**3. Performance Issues**
- Monitor cache hit rates and clear if necessary
- Check database indexing on composite key columns
- Verify connection pool configuration

**4. Security Rejections**
- Review key component character restrictions
- Avoid SQL keywords in key values
- Check total key length limits

### Support Contacts
- Architecture Team: <EMAIL>
- Security Team: <EMAIL>
- Operations Team: <EMAIL> 