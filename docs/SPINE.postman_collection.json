{"info": {"_postman_id": "04fb1aa3-8142-4478-bfb2-0c706aa7a64e", "name": "SPINE", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Login - Get token via Authorization tab", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "clientSecret", "value": "", "type": "string"}, {"key": "scope", "value": "api://61c03713-2799-4bc5-aef4-f98b4dfb91da/access_as_user", "type": "string"}, {"key": "accessTokenUrl", "value": "https://login.microsoftonline.com/SPPAL.onmicrosoft.com/oauth2/v2.0/token", "type": "string"}, {"key": "authUrl", "value": "https://login.microsoftonline.com/SPPAL.onmicrosoft.com/oauth2/v2.0/authorize", "type": "string"}, {"key": "clientId", "value": "12e99a1e-562f-4102-97ef-6bb9d2073865", "type": "string"}, {"key": "useBrowser", "value": true, "type": "boolean"}, {"key": "tokenName", "value": "spine test", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": ""}}, "response": []}, {"name": "GetAllProjects", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml", "type": "text", "disabled": true}, {"key": "SOAPAction", "value": "\"#GET\"", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">\r\n  <s:Body xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\r\n    <getAllProjects xmlns=\"http://toolchain.siemens.pl\">\r\n      <arg0 xmlns=\"\">{{auth}}</arg0>\r\n      <arg1 xmlns=\"\"/>\r\n    </getAllProjects>\r\n  </s:Body>\r\n</s:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{base_address}}/getAllProjects", "host": ["{{base_address}}"], "path": ["getAllProjects"]}, "description": "Gets information about all projects available for authenticated user\n\narg1 - not used\nreturns: list of project info\n````\nRequest body:\n<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">  \n  <s:Body xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">    \n    <getAllProjects xmlns=\"http://toolchain.siemens.pl\">      \n      <arg0 xmlns=\"\">{{auth}}</arg0>      \n      <arg1 xmlns=\"\"/>    \n    </getAllProjects>  \n  </s:Body>\n</s:Envelope>\n\nResponse body:\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <ns2:getAllProjectsResponse xmlns:ns2=\"http://toolchain.siemens.pl\">\n      <return>\n        <main_Project_State>EXECUTION</main_Project_State>\n        <packageType>Baggage</packageType>\n        <project_ID>ACC Ghana 2</project_ID>\n        <sap_Project_Key>DACCG2</sap_Project_Key>\n        <type>TEST</type>\n      </return>\n      <return>\n        <main_Project_State>OFFER</main_Project_State>\n        <packageType>Baggage</packageType>\n        <project_ID>ACC Ghana 3</project_ID>\n        <sap_Project_Key>DACCG3</sap_Project_Key>\n        <type>TEST</type>\n      </return>\n      <return>\n        <main_Project_State>OFFER</main_Project_State>\n        <packageType>Baggage</packageType>\n        <project_ID>ASPINE3757-testclosed5</project_ID>\n        <sap_Project_Key>W10007</sap_Project_Key>\n        <type>TEST</type>\n      </return>\n    </ns2:getAllProjectsResponse>\n  </soap:Body>\n</soap:Envelope>\n````"}, "response": []}, {"name": "[SIDE EFFECT] GetAllValuesFromNamedCounter", "request": {"method": "POST", "header": [{"key": "Content-Type", "type": "text", "value": "text/xml", "disabled": true}, {"key": "SOAPAction", "type": "text", "value": "\"#GET\"", "disabled": true}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">\r\n  <s:Body xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\r\n    <getValuesFromNamedCounter xmlns=\"http://toolchain.siemens.pl\">\r\n      <arg0 xmlns=\"\">{{auth}}</arg0>\r\n      <arg1 xmlns=\"\">1</arg1>\r\n      <arg2 xmlns=\"\">BoMCounter</arg2>\r\n    </getValuesFromNamedCounter>\r\n  </s:Body>\r\n</s:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{base_address}}/getAllValuesFromNamedCounter", "host": ["{{base_address}}"], "path": ["getAllValuesFromNamedCounter"]}, "description": "Gets new ids from named counter, with side effect of increasing that counter value\n\narg1 - how many Ids to return  \narg2 - name of the counter \"BoMCounter\" or \"globalCounterForSPINE\" or \"SAPBogusNumber\" (used in LayCon)\nreturns: list of freshly generated values from specified counter\n````\n<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <s:Body xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n    <getValuesFromNamedCounter xmlns=\"http://toolchain.siemens.pl\">\n      <arg0 xmlns=\"\">{{auth}}</arg0>\n      <arg1 xmlns=\"\">1</arg1>\n      <arg2 xmlns=\"\">BoMCounter</arg2>\n    </getValuesFromNamedCounter>\n  </s:Body>\n</s:Envelope>\n\n\nResponse body:\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <ns2:getValuesFromNamedCounterResponse xmlns:ns2=\"http://toolchain.siemens.pl\">\n      <return>3989</return>\n    </ns2:getValuesFromNamedCounterResponse>\n  </soap:Body>\n</soap:Envelope>\n````"}, "response": []}, {"name": "GetAllTypes", "request": {"method": "POST", "header": [{"key": "Content-Type", "type": "text", "value": "text/xml", "disabled": true}, {"key": "SOAPAction", "type": "text", "value": "\"#GET\"", "disabled": true}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">\r\n  <s:Body xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\r\n    <getAllTypes xmlns=\"http://toolchain.siemens.pl\">\r\n      <arg0 xmlns=\"\">{{auth}}</arg0>\r\n      <arg1 xmlns=\"\">MariusTest</arg1>\r\n      <arg2 xmlns=\"\"/>\r\n    </getAllTypes>\r\n  </s:Body>\r\n</s:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{base_address}}/getAllTypes", "host": ["{{base_address}}"], "path": ["getAllTypes"]}, "description": "Gets information about available component types for a given project\n\narg1 - project name (can be null?)\narg2 - not used\nreturns: list of all available component information in CSV format (see slang script 397531)\n````\n<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <s:Body xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n    <getAllTypes xmlns=\"http://toolchain.siemens.pl\">\n      <arg0 xmlns=\"\">{{auth}}</arg0>\n      <arg1 xmlns=\"\"/>\n      <arg2 xmlns=\"\"/>\n    </getAllTypes>\n  </s:Body>\n</s:Envelope>\n\n\nResponse body:\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <ns2:getAllTypesResponse xmlns:ns2=\"http://toolchain.siemens.pl\">\n      <return>\"SMTSST04RT\";\"Tray\";\"Metering\";;\"Tray\";\"Tray Metering\";\"0.0\";\"1.5\";\"10.5\";\"7.5\";\"9999.0\";;\"https://confluence.ppal.directory/x/QAkNBQ\";\"PR-52\";;;;\"Common\";;\"1\";;\"SIEMENS\";\"1.0\";;\"**************\";\"9999.0\";\"12.0\";\"0.58\";\"4.6\";\"0.0\";\"0.033\";\"0.0\";\"0.0\";\"mechanic\";;\"generic\";\"Conveyor\";\"0\";\n\"SMTSST05\";\"Tray\";\"Filler\";;\"VarioTray\";\"Tray Variable Connection 0-100\";\"0.0\";\"3.0\";\"3.5\";\"3.2\";\"9999.0\";;\"https://confluence.ppal.directory/x/4gENBQ\";\"0\";;;;\"Common\";;\"0\";;\"SIEMENS\";\"0.0\";;\"00.021.002.002\";\"9999.0\";\"9999.0\";\"0.0\";\"0.0\";\"0.0\";\"0.0\";\"0.0\";\"0.0\";\"mechanic\";;\"generic\";\"Conveyor\";\"0\";\n\"SMTSST06\";\"Tray\";\"Straight\";\"Reverse\";\"VarioTray\";\"VarioTray VTR Straight Reverse\";\"0.0\";\"1.5\";\"10.5\";\"7.5\";\"9999.0\";;\"https://confluence.ppal.directory/x/XYBFBg\";\"PR-51\";;;;\"Common\";\"x\";\"1\";;\"SIEMENS\";\"1.0\";;\"00.021.002.010\";\"9999.0\";\"12.0\";\"0.58\";\"4.6\";\"191.0\";\"0.033\";\"35.0\";\"0.0\";\"mechanic\";;\"generic\";\"Conveyor\";\"0\";\n\"SMTSST06RT\";\"Tray\";\"Straight\";\"Reverse\";\"Tray\";\"Tray Straight Reverse\";\"0.0\";\"1.5\";\"10.5\";\"7.5\";\"9999.0\";;\"https://confluence.ppal.directory/x/XYBFBg\";\"PR-51\";;;;\"Common\";\"x\";\"1\";;\"SIEMENS\";\"1.0\";;\"00.021.002.010\";\"9999.0\";\"12.0\";\"0.58\";\"4.6\";\"0.0\";\"0.033\";\"0.0\";\"0.0\";\"mechanic\";;\"generic\";\"Conveyor\";\"0\";\n\"SMTSST07\";\"Tray\";\"Metering\";\"Reverse\";\"VarioTray\";\"VarioTray VTR Metering Reverse\";\"0.0\";\"1.5\";\"10.5\";\"7.5\";\"9999.0\";;\"https://confluence.ppal.directory/x/QAkNBQ\";\"PR-52\";;;;\"Common\";\"x\";\"1\";;\"SIEMENS\";\"1.0\";;\"00.021.002.011\";\"9999.0\";\"12.0\";\"0.58\";\"4.6\";\"191.0\";\"0.033\";\"35.0\";\"0.0\";\"mechanic\";;\"generic\";\"Conveyor\";\"0\";\n\"SMTSST07RT\";\"Tray\";\"Metering\";\"Reverse\";\"Tray\";\"Tray Metering Reverse\";\"0.0\";\"1.5\";\"10.5\";\"7.5\";\"9999.0\";;\"https://confluence.ppal.directory/x/QAkNBQ\";\"PR-52\";;;;\"Common\";\"x\";\"1\";;\"SIEMENS\";\"1.0\";;\"00.021.002.011\";\"9999.0\";\"12.0\";\"0.58\";\"4.6\";\"0.0\";\"0.033\";\"0.0\";\"0.0\";\"mechanic\";;\"generic\";\"Conveyor\";\"0\";\n\"SMTSST08\";\"Tray\";\"Straight\";\"HighSpeed\";\"VarioTray\";\"Highspeed Straight\";\"0.0\";\"3.0\";\"3.5\";\"3.2\";\"9999.0\";;\"https://confluence.ppal.directory/x/XYBFBg\";\"PR-51\";;;;\"Common\";;\"1\";;\"SIEMENS\";\"1.0\";;\"00.021.002.012\";\"9999.0\";\"9999.0\";\"0.58\";\"4.6\";\"0.0\";\"0.033\";\"40.0\";\"0.0\";\"mechanic\";;\"generic\";\"Conveyor\";\"0\";\n\"SMTSST14\";\"Tray\";\"Metering_Infeed\";\"TilterPlus\";\"VarioTray\";\"VarioTray Metering Infeed TilterPlus\";\"0.0\";\"1.5\";\"10.5\";\"7.5\";\"9999.0\";;\"https://confluence.ppal.directory/x/QAkNBQ\";\"PR-52\";;;;\"Common\";;\"1\";;\"SIEMENS\";\"1.0\";;\"**************\";\"9999.0\";\"12.0\";\"0.58\";\"4.6\";\"191.0\";\"0.033\";\"35.0\";\"0.0\";\"mechanic\";;\"generic\";\"Conveyor\";\"0\";\n\"SMTSST24\";\"Tray\";\"Metering_Infeed\";\"SmartTilter\";\"VarioTray\";\"VarioTray Metering Infeed SmartTilter\";\"0.0\";\"1.5\";\"10.5\";\"7.5\";\"9999.0\";;\"https://confluence.ppal.directory/x/QAkNBQ\";\"PR-52\";;;;\"Common\";;\"1\";;\"SIEMENS\";\"1.0\";;\"**************\";\"9999.0\";\"12.0\";\"0.58\";\"4.6\";\"191.0\";\"0.033\";\"35.0\";\"0.0\";\"mechanic\";;\"generic\";\"Conveyor\";\"0\";\n\"SMTSST34\";\"Tray\";\"Metering_Infeed\";\"StaticTilter\";\"VarioTray\";\"VarioTray Metering Infeed StaticTilter\";\"0.0\";\"1.5\";\"10.5\";\"7.5\";\"9999.0\";;\"https://confluence.ppal.directory/x/QAkNBQ\";\"PR-52\";;;;\"Common\";;\"1\";;\"SIEMENS\";\"1.0\";;\"**************\";\"9999.0\";\"12.0\";\"0.58\";\"4.6\";\"191.0\";\"0.033\";\"35.0\";\"0.0\";\"mechanic\";;\"generic\";\"Conveyor\";\"0\";\n\"SMTSST44\";\"Tray\";\"Metering_Infeed\";\"Stacker\";\"VarioTray\";\"VarioTray Metering Infeed Stacker\";\"0.0\";\"1.5\";\"10.5\";\"7.5\";\"9999.0\";;\"https://confluence.ppal.directory/x/QAkNBQ\";\"PR-52\";;;;\"Common\";;\"1\";;\"SIEMENS\";\"1.0\";;\"**************\";\"9999.0\";\"12.0\";\"0.58\";\"4.6\";\"191.0\";\"0.033\";\"35.0\";\"0.0\";\"mechanic\";;\"generic\";\"Conveyor\";\"0\";\n</return>\n    </ns2:getAllTypesResponse>\n  </soap:Body>\n</soap:Envelope>\n````"}, "response": []}, {"name": "[SIDE EFFECT] GenerateComponentUniqueIds", "request": {"method": "POST", "header": [{"key": "Content-Type", "type": "text", "value": "text/xml", "disabled": true}, {"key": "SOAPAction", "type": "text", "value": "\"#GET\"", "disabled": true}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">\r\n  <s:Body xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\r\n    <generateComponentUniqueIds xmlns=\"http://toolchain.siemens.pl\">\r\n      <arg0 xmlns=\"\">{{auth}}</arg0>\r\n      <arg1 xmlns=\"\">4</arg1>\r\n    </generateComponentUniqueIds>\r\n  </s:Body>\r\n</s:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{base_address}}/generateComponentUniqueIds", "host": ["{{base_address}}"], "path": ["generateComponentUniqueIds"]}, "description": "Generates new uniqueIds for the components\n\narg1 - how many Ids to generate\nreturns: list of freshly generated uniqueIds\n````\n<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <s:Body xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n    <generateComponentUniqueIds xmlns=\"http://toolchain.siemens.pl\">\n      <arg0 xmlns=\"\">{{auth}}</arg0>\n      <arg1 xmlns=\"\">4</arg1>\n    </generateComponentUniqueIds>\n  </s:Body>\n</s:Envelope>\n\n\nResponse body:\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <ns2:generateComponentUniqueIdsResponse xmlns:ns2=\"http://toolchain.siemens.pl\">\n      <return>17711432</return>\n      <return>17711433</return>\n      <return>17711434</return>\n      <return>17711435</return>\n    </ns2:generateComponentUniqueIdsResponse>\n  </soap:Body>\n</soap:Envelope>\n````"}, "response": []}, {"name": "[SIDE EFFECT] CreateComponents", "request": {"method": "POST", "header": [{"key": "Content-Type", "type": "text", "value": "text/xml", "disabled": true}, {"key": "SOAPAction", "type": "text", "value": "\"#GET\"", "disabled": true}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">\r\n  <s:Body xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\r\n    <createComponents xmlns=\"http://toolchain.siemens.pl\">\r\n      <arg0 xmlns=\"\">{{auth}}</arg0>\r\n      <arg1 xmlns=\"\">MariusTest</arg1>\r\n      <arg2 xmlns=\"\">RockyPaeTest</arg2>\r\n      <arg3 xmlns=\"\"/>\r\n      <arg4 xmlns=\"\">\r\n        <AKZ/>\r\n        <acceleration>0</acceleration>\r\n        <amount_Buffer_Elec>0</amount_Buffer_Elec>\r\n        <amount_Buffer_Mech>0</amount_Buffer_Mech>\r\n        <angleCorr>0</angleCorr>\r\n        <autoCadLayer>0</autoCadLayer>\r\n        <break_Type>-</break_Type>\r\n        <buffer_Size>0</buffer_Size>\r\n        <build_Section>DEFAULT</build_Section>\r\n        <building_Section>DEFAULT</building_Section>\r\n        <calculation_Area>DEFAULT</calculation_Area>\r\n        <colorGroup>DEFAULT</colorGroup>\r\n        <comment>Belt Curve</comment>\r\n        <connectionPoints>\r\n          <name>CP_1</name>\r\n          <type>IN</type>\r\n          <x>9020</x>\r\n          <y>1383</y>\r\n          <z>0</z>\r\n        </connectionPoints>\r\n        <connectionPoints>\r\n          <name>CP_2</name>\r\n          <type>OUT</type>\r\n          <x>8199</x>\r\n          <y>1603</y>\r\n          <z>0</z>\r\n        </connectionPoints>\r\n        <controlNr/>\r\n        <creation_Date>2022-05-31T17:41:32</creation_Date>\r\n        <curve_Angle>30</curve_Angle>\r\n        <curve_Direction>CCW</curve_Direction>\r\n        <curve_Radius>1550</curve_Radius>\r\n        <customerAKZ/>\r\n        <decompositionLists>\r\n          <mode>overwrite</mode>\r\n          <source>VFDS</source>\r\n        </decompositionLists>\r\n        <designPlant/>\r\n        <drawing>RockyPaeTest</drawing>\r\n        <drawingRevision/>\r\n        <drawingVersion/>\r\n        <drivePulleyDiameter>89.639</drivePulleyDiameter>\r\n        <driveShaftDiameter>0</driveShaftDiameter>\r\n        <driveSide>R</driveSide>\r\n        <drive_Position/>\r\n        <drive_Station/>\r\n        <EStop_Group>DEFAULT</EStop_Group>\r\n        <height>0</height>\r\n        <installation_Section>DEFAULT</installation_Section>\r\n        <last_Modification_Date>2022-05-31T17:57:44</last_Modification_Date>\r\n        <length_Total>862</length_Total>\r\n        <levelEnd/>\r\n        <levelStart/>\r\n        <line_Name>DEFAULT</line_Name>\r\n        <load>0</load>\r\n        <motorController>-</motorController>\r\n        <motor_Direction>V</motor_Direction>\r\n        <motor_Position>Outside, Belt Pulled</motor_Position>\r\n        <orderPlant/>\r\n        <outfit3D/>\r\n        <PLC_Area>DEFAULT</PLC_Area>\r\n        <parcelTypeID>CB_CU_L_30</parcelTypeID>\r\n        <parent/>\r\n        <plant_Domain/>\r\n        <pos_No>00.003</pos_No>\r\n        <position>\r\n          <x>0</x>\r\n          <y>0</y>\r\n          <z>0</z>\r\n        </position>\r\n        <reference/>\r\n        <rotation>0</rotation>\r\n        <rotation_3D>0</rotation_3D>\r\n        <screen>DEFAULT</screen>\r\n        <section1_Angle>0</section1_Angle>\r\n        <section1_Length>0</section1_Length>\r\n        <section2_Angle>0</section2_Angle>\r\n        <section2_Length>0</section2_Length>\r\n        <section3_Angle>0</section3_Angle>\r\n        <section3_Length>0</section3_Length>\r\n        <section4_Angle>1</section4_Angle>\r\n        <section4_Length>0</section4_Length>\r\n        <sequence_Group>DEFAULT</sequence_Group>\r\n        <slave_Drive>NS</slave_Drive>\r\n        <slope>0</slope>\r\n        <speed>1</speed>\r\n        <start_Stop_Cycles>0</start_Stop_Cycles>\r\n        <state>\r\n          <reason>1</reason>\r\n          <state>180</state>\r\n          <unique_ID>17711432</unique_ID>\r\n          <user/>\r\n        </state>\r\n        <storageConveyor>false</storageConveyor>\r\n        <supplier/>\r\n        <throughput>0</throughput>\r\n        <type_ID>M06GK04</type_ID>\r\n        <unique_ID>17711432</unique_ID>\r\n        <unit_Reversible>false</unit_Reversible>\r\n        <usage/>\r\n        <useConnectionPoints>false</useConnectionPoints>\r\n        <useDecompositions>true</useDecompositions>\r\n        <useState>true</useState>\r\n        <user1/>\r\n        <user2/>\r\n        <user3/>\r\n        <user4/>\r\n        <user5/>\r\n        <vaultInstance/>\r\n        <version>2</version>\r\n        <virtual/>\r\n        <width>1000</width>\r\n      </arg4>\r\n      <arg4 xmlns=\"\">\r\n        <AKZ/>\r\n        <acceleration>0</acceleration>\r\n        <amount_Buffer_Elec>0</amount_Buffer_Elec>\r\n        <amount_Buffer_Mech>0</amount_Buffer_Mech>\r\n        <angleCorr>0</angleCorr>\r\n        <autoCadLayer>0</autoCadLayer>\r\n        <break_Type>NB</break_Type>\r\n        <buffer_Size>0</buffer_Size>\r\n        <build_Section>DEFAULT</build_Section>\r\n        <building_Section>DEFAULT</building_Section>\r\n        <calculation_Area>DEFAULT</calculation_Area>\r\n        <colorGroup>DEFAULT</colorGroup>\r\n        <comment>Straight Horizontal</comment>\r\n        <connectionPoints>\r\n          <name>CP_1</name>\r\n          <type>IN</type>\r\n          <x>8199</x>\r\n          <y>1603</y>\r\n          <z>0</z>\r\n        </connectionPoints>\r\n        <connectionPoints>\r\n          <name>CP_2</name>\r\n          <type>OUT</type>\r\n          <x>6199</x>\r\n          <y>1603</y>\r\n          <z>0</z>\r\n        </connectionPoints>\r\n        <controlNr/>\r\n        <creation_Date>2022-05-31T17:41:33</creation_Date>\r\n        <curve_Angle>0</curve_Angle>\r\n        <curve_Direction>-</curve_Direction>\r\n        <curve_Radius>0</curve_Radius>\r\n        <customerAKZ/>\r\n        <decompositionLists>\r\n          <mode>overwrite</mode>\r\n          <source>VFDS</source>\r\n        </decompositionLists>\r\n        <designPlant/>\r\n        <drawing>RockyPaeTest</drawing>\r\n        <drawingRevision/>\r\n        <drawingVersion/>\r\n        <drivePulleyDiameter>150</drivePulleyDiameter>\r\n        <driveShaftDiameter>40</driveShaftDiameter>\r\n        <driveSide>L</driveSide>\r\n        <drive_Position>200</drive_Position>\r\n        <drive_Station/>\r\n        <EStop_Group>DEFAULT</EStop_Group>\r\n        <height>0</height>\r\n        <installation_Section>DEFAULT</installation_Section>\r\n        <last_Modification_Date>2022-05-31T17:57:45</last_Modification_Date>\r\n        <length_Total>2000</length_Total>\r\n        <levelEnd/>\r\n        <levelStart/>\r\n        <line_Name>DEFAULT</line_Name>\r\n        <load>0</load>\r\n        <motorController>FI</motorController>\r\n        <motor_Direction>V</motor_Direction>\r\n        <motor_Position>DS D40 Feather Key, Left</motor_Position>\r\n        <orderPlant/>\r\n        <outfit3D/>\r\n        <PLC_Area>DEFAULT</PLC_Area>\r\n        <parcelTypeID>CB_ST_HO</parcelTypeID>\r\n        <parent/>\r\n        <plant_Domain/>\r\n        <pos_No>00.002</pos_No>\r\n        <position>\r\n          <x>0</x>\r\n          <y>0</y>\r\n          <z>0</z>\r\n        </position>\r\n        <reference/>\r\n        <rotation>0</rotation>\r\n        <rotation_3D>0</rotation_3D>\r\n        <screen>DEFAULT</screen>\r\n        <section1_Angle>0</section1_Angle>\r\n        <section1_Length>2000</section1_Length>\r\n        <section2_Angle>0</section2_Angle>\r\n        <section2_Length>0</section2_Length>\r\n        <section3_Angle>0</section3_Angle>\r\n        <section3_Length>0</section3_Length>\r\n        <section4_Angle>1</section4_Angle>\r\n        <section4_Length>0</section4_Length>\r\n        <sequence_Group>DEFAULT</sequence_Group>\r\n        <slave_Drive>NS</slave_Drive>\r\n        <slope>0</slope>\r\n        <speed>1</speed>\r\n        <start_Stop_Cycles>0</start_Stop_Cycles>\r\n        <state>\r\n          <reason>1</reason>\r\n          <state>180</state>\r\n          <unique_ID>17711433</unique_ID>\r\n          <user/>\r\n        </state>\r\n        <storageConveyor>false</storageConveyor>\r\n        <supplier/>\r\n        <throughput>0</throughput>\r\n        <type_ID>SMVB30GF02</type_ID>\r\n        <unique_ID>17711433</unique_ID>\r\n        <unit_Reversible>false</unit_Reversible>\r\n        <usage/>\r\n        <useConnectionPoints>false</useConnectionPoints>\r\n        <useDecompositions>true</useDecompositions>\r\n        <useState>true</useState>\r\n        <user1/>\r\n        <user2/>\r\n        <user3/>\r\n        <user4/>\r\n        <user5/>\r\n        <vaultInstance/>\r\n        <version>2</version>\r\n        <virtual/>\r\n        <width>1000</width>\r\n      </arg4>\r\n      <arg4 xmlns=\"\">\r\n        <AKZ/>\r\n        <acceleration>0</acceleration>\r\n        <amount_Buffer_Elec>0</amount_Buffer_Elec>\r\n        <amount_Buffer_Mech>0</amount_Buffer_Mech>\r\n        <angleCorr>0</angleCorr>\r\n        <autoCadLayer>0</autoCadLayer>\r\n        <break_Type>NB</break_Type>\r\n        <buffer_Size>0</buffer_Size>\r\n        <build_Section>DEFAULT</build_Section>\r\n        <building_Section>DEFAULT</building_Section>\r\n        <calculation_Area>DEFAULT</calculation_Area>\r\n        <colorGroup>DEFAULT</colorGroup>\r\n        <comment>Straight Horizontal</comment>\r\n        <connectionPoints>\r\n          <name>CP_1</name>\r\n          <type>IN</type>\r\n          <x>6199</x>\r\n          <y>1603</y>\r\n          <z>0</z>\r\n        </connectionPoints>\r\n        <connectionPoints>\r\n          <name>CP_2</name>\r\n          <type>OUT</type>\r\n          <x>4199</x>\r\n          <y>1603</y>\r\n          <z>0</z>\r\n        </connectionPoints>\r\n        <controlNr/>\r\n        <creation_Date>2022-05-31T17:41:34</creation_Date>\r\n        <curve_Angle>0</curve_Angle>\r\n        <curve_Direction>-</curve_Direction>\r\n        <curve_Radius>0</curve_Radius>\r\n        <customerAKZ/>\r\n        <decompositionLists>\r\n          <mode>overwrite</mode>\r\n          <source>VFDS</source>\r\n        </decompositionLists>\r\n        <designPlant/>\r\n        <drawing>RockyPaeTest</drawing>\r\n        <drawingRevision/>\r\n        <drawingVersion/>\r\n        <drivePulleyDiameter>150</drivePulleyDiameter>\r\n        <driveShaftDiameter>40</driveShaftDiameter>\r\n        <driveSide>L</driveSide>\r\n        <drive_Position>200</drive_Position>\r\n        <drive_Station/>\r\n        <EStop_Group>DEFAULT</EStop_Group>\r\n        <height>0</height>\r\n        <installation_Section>DEFAULT</installation_Section>\r\n        <last_Modification_Date>2022-05-31T17:57:45</last_Modification_Date>\r\n        <length_Total>2000</length_Total>\r\n        <levelEnd/>\r\n        <levelStart/>\r\n        <line_Name>DEFAULT</line_Name>\r\n        <load>0</load>\r\n        <motorController>FI</motorController>\r\n        <motor_Direction>V</motor_Direction>\r\n        <motor_Position>DS D40 Feather Key, Left</motor_Position>\r\n        <orderPlant/>\r\n        <outfit3D/>\r\n        <PLC_Area>DEFAULT</PLC_Area>\r\n        <parcelTypeID>CB_ST_HO</parcelTypeID>\r\n        <parent/>\r\n        <plant_Domain/>\r\n        <pos_No>00.001</pos_No>\r\n        <position>\r\n          <x>0</x>\r\n          <y>0</y>\r\n          <z>0</z>\r\n        </position>\r\n        <reference/>\r\n        <rotation>0</rotation>\r\n        <rotation_3D>0</rotation_3D>\r\n        <screen>DEFAULT</screen>\r\n        <section1_Angle>0</section1_Angle>\r\n        <section1_Length>2000</section1_Length>\r\n        <section2_Angle>0</section2_Angle>\r\n        <section2_Length>0</section2_Length>\r\n        <section3_Angle>0</section3_Angle>\r\n        <section3_Length>0</section3_Length>\r\n        <section4_Angle>1</section4_Angle>\r\n        <section4_Length>0</section4_Length>\r\n        <sequence_Group>DEFAULT</sequence_Group>\r\n        <slave_Drive>NS</slave_Drive>\r\n        <slope>0</slope>\r\n        <speed>1</speed>\r\n        <start_Stop_Cycles>0</start_Stop_Cycles>\r\n        <state>\r\n          <reason>1</reason>\r\n          <state>180</state>\r\n          <unique_ID>17711434</unique_ID>\r\n          <user/>\r\n        </state>\r\n        <storageConveyor>false</storageConveyor>\r\n        <supplier/>\r\n        <throughput>0</throughput>\r\n        <type_ID>SMVB30GF02</type_ID>\r\n        <unique_ID>17711434</unique_ID>\r\n        <unit_Reversible>false</unit_Reversible>\r\n        <usage/>\r\n        <useConnectionPoints>false</useConnectionPoints>\r\n        <useDecompositions>true</useDecompositions>\r\n        <useState>true</useState>\r\n        <user1/>\r\n        <user2/>\r\n        <user3/>\r\n        <user4/>\r\n        <user5/>\r\n        <vaultInstance/>\r\n        <version>2</version>\r\n        <virtual/>\r\n        <width>1000</width>\r\n      </arg4>\r\n      <arg4 xmlns=\"\">\r\n        <AKZ/>\r\n        <acceleration>0</acceleration>\r\n        <amount_Buffer_Elec>0</amount_Buffer_Elec>\r\n        <amount_Buffer_Mech>0</amount_Buffer_Mech>\r\n        <angleCorr>0</angleCorr>\r\n        <autoCadLayer>0</autoCadLayer>\r\n        <break_Type>NB</break_Type>\r\n        <buffer_Size>0</buffer_Size>\r\n        <build_Section>DEFAULT</build_Section>\r\n        <building_Section>DEFAULT</building_Section>\r\n        <calculation_Area>DEFAULT</calculation_Area>\r\n        <colorGroup>DEFAULT</colorGroup>\r\n        <comment>Metering Horizontal</comment>\r\n        <connectionPoints>\r\n          <name>CP_1</name>\r\n          <type>IN</type>\r\n          <x>4199</x>\r\n          <y>1603</y>\r\n          <z>0</z>\r\n        </connectionPoints>\r\n        <connectionPoints>\r\n          <name>CP_2</name>\r\n          <type>OUT</type>\r\n          <x>2999</x>\r\n          <y>1603</y>\r\n          <z>0</z>\r\n        </connectionPoints>\r\n        <controlNr/>\r\n        <creation_Date>2022-05-31T17:41:35</creation_Date>\r\n        <curve_Angle>0</curve_Angle>\r\n        <curve_Direction>-</curve_Direction>\r\n        <curve_Radius>0</curve_Radius>\r\n        <customerAKZ/>\r\n        <decompositionLists>\r\n          <mode>overwrite</mode>\r\n          <source>VFDS</source>\r\n        </decompositionLists>\r\n        <designPlant/>\r\n        <drawing>RockyPaeTest</drawing>\r\n        <drawingRevision/>\r\n        <drawingVersion/>\r\n        <drivePulleyDiameter>150</drivePulleyDiameter>\r\n        <driveShaftDiameter>40</driveShaftDiameter>\r\n        <driveSide>L</driveSide>\r\n        <drive_Position>200</drive_Position>\r\n        <drive_Station/>\r\n        <EStop_Group>DEFAULT</EStop_Group>\r\n        <height>0</height>\r\n        <installation_Section>DEFAULT</installation_Section>\r\n        <last_Modification_Date>2022-05-31T17:57:45</last_Modification_Date>\r\n        <length_Total>1200</length_Total>\r\n        <levelEnd/>\r\n        <levelStart/>\r\n        <line_Name>DEFAULT</line_Name>\r\n        <load>0</load>\r\n        <motorController>FI</motorController>\r\n        <motor_Direction>V</motor_Direction>\r\n        <motor_Position>DS D40 Feather Key, Left</motor_Position>\r\n        <orderPlant/>\r\n        <outfit3D/>\r\n        <PLC_Area>DEFAULT</PLC_Area>\r\n        <parcelTypeID>CB_ST_HO</parcelTypeID>\r\n        <parent/>\r\n        <plant_Domain/>\r\n        <pos_No>00.000</pos_No>\r\n        <position>\r\n          <x>0</x>\r\n          <y>0</y>\r\n          <z>0</z>\r\n        </position>\r\n        <reference/>\r\n        <rotation>0</rotation>\r\n        <rotation_3D>0</rotation_3D>\r\n        <screen>DEFAULT</screen>\r\n        <section1_Angle>0</section1_Angle>\r\n        <section1_Length>1200</section1_Length>\r\n        <section2_Angle>0</section2_Angle>\r\n        <section2_Length>0</section2_Length>\r\n        <section3_Angle>0</section3_Angle>\r\n        <section3_Length>0</section3_Length>\r\n        <section4_Angle>1</section4_Angle>\r\n        <section4_Length>0</section4_Length>\r\n        <sequence_Group>DEFAULT</sequence_Group>\r\n        <slave_Drive>NS</slave_Drive>\r\n        <slope>0</slope>\r\n        <speed>1</speed>\r\n        <start_Stop_Cycles>0</start_Stop_Cycles>\r\n        <state>\r\n          <reason>1</reason>\r\n          <state>180</state>\r\n          <unique_ID>17711435</unique_ID>\r\n          <user/>\r\n        </state>\r\n        <storageConveyor>false</storageConveyor>\r\n        <supplier/>\r\n        <throughput>0</throughput>\r\n        <type_ID>SMVB30GF19</type_ID>\r\n        <unique_ID>17711435</unique_ID>\r\n        <unit_Reversible>false</unit_Reversible>\r\n        <usage/>\r\n        <useConnectionPoints>false</useConnectionPoints>\r\n        <useDecompositions>true</useDecompositions>\r\n        <useState>true</useState>\r\n        <user1/>\r\n        <user2/>\r\n        <user3/>\r\n        <user4/>\r\n        <user5/>\r\n        <vaultInstance/>\r\n        <version>2</version>\r\n        <virtual/>\r\n        <width>1000</width>\r\n      </arg4>\r\n    </createComponents>\r\n  </s:Body>\r\n</s:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{base_address}}/createComponents", "host": ["{{base_address}}"], "path": ["createComponents"]}, "description": "Generates new components in specified project and drawing\n\narg1 - project name\narg2 - drawing name\narg3 - not used\narg4 - list of component data\nreturns: list of new component ids\n````\n<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <s:Body xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n    <generateComponentUniqueIds xmlns=\"http://toolchain.siemens.pl\">\n      <arg0 xmlns=\"\">{{auth}}</arg0>\n      <arg1 xmlns=\"\">MariusTest</arg1>\n      <arg2 xmlns=\"\">RockyPaeTest</arg2>\n      <arg3 xmlns=\"\"/>\n      <arg4 xmlns=\"\">\n        <AKZ/>\n        <acceleration>0</acceleration>\n        <amount_Buffer_Elec>0</amount_Buffer_Elec>\n        <amount_Buffer_Mech>0</amount_Buffer_Mech>\n        <angleCorr>0</angleCorr>\n        <autoCadLayer>0</autoCadLayer>\n        <break_Type>-</break_Type>\n        <buffer_Size>0</buffer_Size>\n        <build_Section>DEFAULT</build_Section>\n        <building_Section>DEFAULT</building_Section>\n        <calculation_Area>DEFAULT</calculation_Area>\n        <colorGroup>DEFAULT</colorGroup>\n        <comment>Belt Curve</comment>\n        <connectionPoints>\n          <name>CP_1</name>\n          <type>IN</type>\n          <x>9020</x>\n          <y>1383</y>\n          <z>0</z>\n        </connectionPoints>\n        <connectionPoints>\n          <name>CP_2</name>\n          <type>OUT</type>\n          <x>8199</x>\n          <y>1603</y>\n          <z>0</z>\n        </connectionPoints>\n        <controlNr/>\n        <creation_Date>2022-05-31T17:41:32</creation_Date>\n        <curve_Angle>30</curve_Angle>\n        <curve_Direction>CCW</curve_Direction>\n        <curve_Radius>1550</curve_Radius>\n        <customerAKZ/>\n        <decompositionLists>\n          <mode>overwrite</mode>\n          <source>VFDS</source>\n        </decompositionLists>\n        <designPlant/>\n        <drawing>RockyPaeTest</drawing>\n        <drawingRevision/>\n        <drawingVersion/>\n        <drivePulleyDiameter>89.639</drivePulleyDiameter>\n        <driveShaftDiameter>0</driveShaftDiameter>\n        <driveSide>R</driveSide>\n        <drive_Position/>\n        <drive_Station/>\n        <EStop_Group>DEFAULT</EStop_Group>\n        <height>0</height>\n        <installation_Section>DEFAULT</installation_Section>\n        <last_Modification_Date>2022-05-31T17:57:44</last_Modification_Date>\n        <length_Total>862</length_Total>\n        <levelEnd/>\n        <levelStart/>\n        <line_Name>DEFAULT</line_Name>\n        <load>0</load>\n        <motorController>-</motorController>\n        <motor_Direction>V</motor_Direction>\n        <motor_Position>Outside, Belt Pulled</motor_Position>\n        <orderPlant/>\n        <outfit3D/>\n        <PLC_Area>DEFAULT</PLC_Area>\n        <parcelTypeID>CB_CU_L_30</parcelTypeID>\n        <parent/>\n        <plant_Domain/>\n        <pos_No>00.003</pos_No>\n        <position>\n          <x>0</x>\n          <y>0</y>\n          <z>0</z>\n        </position>\n        <reference/>\n        <rotation>0</rotation>\n        <rotation_3D>0</rotation_3D>\n        <screen>DEFAULT</screen>\n        <section1_Angle>0</section1_Angle>\n        <section1_Length>0</section1_Length>\n        <section2_Angle>0</section2_Angle>\n        <section2_Length>0</section2_Length>\n        <section3_Angle>0</section3_Angle>\n        <section3_Length>0</section3_Length>\n        <section4_Angle>1</section4_Angle>\n        <section4_Length>0</section4_Length>\n        <sequence_Group>DEFAULT</sequence_Group>\n        <slave_Drive>NS</slave_Drive>\n        <slope>0</slope>\n        <speed>1</speed>\n        <start_Stop_Cycles>0</start_Stop_Cycles>\n        <state>\n          <reason>1</reason>\n          <state>180</state>\n          <unique_ID>17711432</unique_ID>\n          <user/>\n        </state>\n        <storageConveyor>false</storageConveyor>\n        <supplier/>\n        <throughput>0</throughput>\n        <type_ID>M06GK04</type_ID>\n        <unique_ID>17711432</unique_ID>\n        <unit_Reversible>false</unit_Reversible>\n        <usage/>\n        <useConnectionPoints>false</useConnectionPoints>\n        <useDecompositions>true</useDecompositions>\n        <useState>true</useState>\n        <user1/>\n        <user2/>\n        <user3/>\n        <user4/>\n        <user5/>\n        <vaultInstance/>\n        <version>2</version>\n        <virtual/>\n        <width>1000</width>\n      </arg4>\n      <arg4 xmlns=\"\">\n        <AKZ/>\n        <acceleration>0</acceleration>\n        <amount_Buffer_Elec>0</amount_Buffer_Elec>\n        <amount_Buffer_Mech>0</amount_Buffer_Mech>\n        <angleCorr>0</angleCorr>\n        <autoCadLayer>0</autoCadLayer>\n        <break_Type>NB</break_Type>\n        <buffer_Size>0</buffer_Size>\n        <build_Section>DEFAULT</build_Section>\n        <building_Section>DEFAULT</building_Section>\n        <calculation_Area>DEFAULT</calculation_Area>\n        <colorGroup>DEFAULT</colorGroup>\n        <comment>Straight Horizontal</comment>\n        <connectionPoints>\n          <name>CP_1</name>\n          <type>IN</type>\n          <x>8199</x>\n          <y>1603</y>\n          <z>0</z>\n        </connectionPoints>\n        <connectionPoints>\n          <name>CP_2</name>\n          <type>OUT</type>\n          <x>6199</x>\n          <y>1603</y>\n          <z>0</z>\n        </connectionPoints>\n        <controlNr/>\n        <creation_Date>2022-05-31T17:41:33</creation_Date>\n        <curve_Angle>0</curve_Angle>\n        <curve_Direction>-</curve_Direction>\n        <curve_Radius>0</curve_Radius>\n        <customerAKZ/>\n        <decompositionLists>\n          <mode>overwrite</mode>\n          <source>VFDS</source>\n        </decompositionLists>\n        <designPlant/>\n        <drawing>RockyPaeTest</drawing>\n        <drawingRevision/>\n        <drawingVersion/>\n        <drivePulleyDiameter>150</drivePulleyDiameter>\n        <driveShaftDiameter>40</driveShaftDiameter>\n        <driveSide>L</driveSide>\n        <drive_Position>200</drive_Position>\n        <drive_Station/>\n        <EStop_Group>DEFAULT</EStop_Group>\n        <height>0</height>\n        <installation_Section>DEFAULT</installation_Section>\n        <last_Modification_Date>2022-05-31T17:57:45</last_Modification_Date>\n        <length_Total>2000</length_Total>\n        <levelEnd/>\n        <levelStart/>\n        <line_Name>DEFAULT</line_Name>\n        <load>0</load>\n        <motorController>FI</motorController>\n        <motor_Direction>V</motor_Direction>\n        <motor_Position>DS D40 Feather Key, Left</motor_Position>\n        <orderPlant/>\n        <outfit3D/>\n        <PLC_Area>DEFAULT</PLC_Area>\n        <parcelTypeID>CB_ST_HO</parcelTypeID>\n        <parent/>\n        <plant_Domain/>\n        <pos_No>00.002</pos_No>\n        <position>\n          <x>0</x>\n          <y>0</y>\n          <z>0</z>\n        </position>\n        <reference/>\n        <rotation>0</rotation>\n        <rotation_3D>0</rotation_3D>\n        <screen>DEFAULT</screen>\n        <section1_Angle>0</section1_Angle>\n        <section1_Length>2000</section1_Length>\n        <section2_Angle>0</section2_Angle>\n        <section2_Length>0</section2_Length>\n        <section3_Angle>0</section3_Angle>\n        <section3_Length>0</section3_Length>\n        <section4_Angle>1</section4_Angle>\n        <section4_Length>0</section4_Length>\n        <sequence_Group>DEFAULT</sequence_Group>\n        <slave_Drive>NS</slave_Drive>\n        <slope>0</slope>\n        <speed>1</speed>\n        <start_Stop_Cycles>0</start_Stop_Cycles>\n        <state>\n          <reason>1</reason>\n          <state>180</state>\n          <unique_ID>17711433</unique_ID>\n          <user/>\n        </state>\n        <storageConveyor>false</storageConveyor>\n        <supplier/>\n        <throughput>0</throughput>\n        <type_ID>SMVB30GF02</type_ID>\n        <unique_ID>17711433</unique_ID>\n        <unit_Reversible>false</unit_Reversible>\n        <usage/>\n        <useConnectionPoints>false</useConnectionPoints>\n        <useDecompositions>true</useDecompositions>\n        <useState>true</useState>\n        <user1/>\n        <user2/>\n        <user3/>\n        <user4/>\n        <user5/>\n        <vaultInstance/>\n        <version>2</version>\n        <virtual/>\n        <width>1000</width>\n      </arg4>\n      <arg4 xmlns=\"\">\n        <AKZ/>\n        <acceleration>0</acceleration>\n        <amount_Buffer_Elec>0</amount_Buffer_Elec>\n        <amount_Buffer_Mech>0</amount_Buffer_Mech>\n        <angleCorr>0</angleCorr>\n        <autoCadLayer>0</autoCadLayer>\n        <break_Type>NB</break_Type>\n        <buffer_Size>0</buffer_Size>\n        <build_Section>DEFAULT</build_Section>\n        <building_Section>DEFAULT</building_Section>\n        <calculation_Area>DEFAULT</calculation_Area>\n        <colorGroup>DEFAULT</colorGroup>\n        <comment>Straight Horizontal</comment>\n        <connectionPoints>\n          <name>CP_1</name>\n          <type>IN</type>\n          <x>6199</x>\n          <y>1603</y>\n          <z>0</z>\n        </connectionPoints>\n        <connectionPoints>\n          <name>CP_2</name>\n          <type>OUT</type>\n          <x>4199</x>\n          <y>1603</y>\n          <z>0</z>\n        </connectionPoints>\n        <controlNr/>\n        <creation_Date>2022-05-31T17:41:34</creation_Date>\n        <curve_Angle>0</curve_Angle>\n        <curve_Direction>-</curve_Direction>\n        <curve_Radius>0</curve_Radius>\n        <customerAKZ/>\n        <decompositionLists>\n          <mode>overwrite</mode>\n          <source>VFDS</source>\n        </decompositionLists>\n        <designPlant/>\n        <drawing>RockyPaeTest</drawing>\n        <drawingRevision/>\n        <drawingVersion/>\n        <drivePulleyDiameter>150</drivePulleyDiameter>\n        <driveShaftDiameter>40</driveShaftDiameter>\n        <driveSide>L</driveSide>\n        <drive_Position>200</drive_Position>\n        <drive_Station/>\n        <EStop_Group>DEFAULT</EStop_Group>\n        <height>0</height>\n        <installation_Section>DEFAULT</installation_Section>\n        <last_Modification_Date>2022-05-31T17:57:45</last_Modification_Date>\n        <length_Total>2000</length_Total>\n        <levelEnd/>\n        <levelStart/>\n        <line_Name>DEFAULT</line_Name>\n        <load>0</load>\n        <motorController>FI</motorController>\n        <motor_Direction>V</motor_Direction>\n        <motor_Position>DS D40 Feather Key, Left</motor_Position>\n        <orderPlant/>\n        <outfit3D/>\n        <PLC_Area>DEFAULT</PLC_Area>\n        <parcelTypeID>CB_ST_HO</parcelTypeID>\n        <parent/>\n        <plant_Domain/>\n        <pos_No>00.001</pos_No>\n        <position>\n          <x>0</x>\n          <y>0</y>\n          <z>0</z>\n        </position>\n        <reference/>\n        <rotation>0</rotation>\n        <rotation_3D>0</rotation_3D>\n        <screen>DEFAULT</screen>\n        <section1_Angle>0</section1_Angle>\n        <section1_Length>2000</section1_Length>\n        <section2_Angle>0</section2_Angle>\n        <section2_Length>0</section2_Length>\n        <section3_Angle>0</section3_Angle>\n        <section3_Length>0</section3_Length>\n        <section4_Angle>1</section4_Angle>\n        <section4_Length>0</section4_Length>\n        <sequence_Group>DEFAULT</sequence_Group>\n        <slave_Drive>NS</slave_Drive>\n        <slope>0</slope>\n        <speed>1</speed>\n        <start_Stop_Cycles>0</start_Stop_Cycles>\n        <state>\n          <reason>1</reason>\n          <state>180</state>\n          <unique_ID>17711434</unique_ID>\n          <user/>\n        </state>\n        <storageConveyor>false</storageConveyor>\n        <supplier/>\n        <throughput>0</throughput>\n        <type_ID>SMVB30GF02</type_ID>\n        <unique_ID>17711434</unique_ID>\n        <unit_Reversible>false</unit_Reversible>\n        <usage/>\n        <useConnectionPoints>false</useConnectionPoints>\n        <useDecompositions>true</useDecompositions>\n        <useState>true</useState>\n        <user1/>\n        <user2/>\n        <user3/>\n        <user4/>\n        <user5/>\n        <vaultInstance/>\n        <version>2</version>\n        <virtual/>\n        <width>1000</width>\n      </arg4>\n      <arg4 xmlns=\"\">\n        <AKZ/>\n        <acceleration>0</acceleration>\n        <amount_Buffer_Elec>0</amount_Buffer_Elec>\n        <amount_Buffer_Mech>0</amount_Buffer_Mech>\n        <angleCorr>0</angleCorr>\n        <autoCadLayer>0</autoCadLayer>\n        <break_Type>NB</break_Type>\n        <buffer_Size>0</buffer_Size>\n        <build_Section>DEFAULT</build_Section>\n        <building_Section>DEFAULT</building_Section>\n        <calculation_Area>DEFAULT</calculation_Area>\n        <colorGroup>DEFAULT</colorGroup>\n        <comment>Metering Horizontal</comment>\n        <connectionPoints>\n          <name>CP_1</name>\n          <type>IN</type>\n          <x>4199</x>\n          <y>1603</y>\n          <z>0</z>\n        </connectionPoints>\n        <connectionPoints>\n          <name>CP_2</name>\n          <type>OUT</type>\n          <x>2999</x>\n          <y>1603</y>\n          <z>0</z>\n        </connectionPoints>\n        <controlNr/>\n        <creation_Date>2022-05-31T17:41:35</creation_Date>\n        <curve_Angle>0</curve_Angle>\n        <curve_Direction>-</curve_Direction>\n        <curve_Radius>0</curve_Radius>\n        <customerAKZ/>\n        <decompositionLists>\n          <mode>overwrite</mode>\n          <source>VFDS</source>\n        </decompositionLists>\n        <designPlant/>\n        <drawing>RockyPaeTest</drawing>\n        <drawingRevision/>\n        <drawingVersion/>\n        <drivePulleyDiameter>150</drivePulleyDiameter>\n        <driveShaftDiameter>40</driveShaftDiameter>\n        <driveSide>L</driveSide>\n        <drive_Position>200</drive_Position>\n        <drive_Station/>\n        <EStop_Group>DEFAULT</EStop_Group>\n        <height>0</height>\n        <installation_Section>DEFAULT</installation_Section>\n        <last_Modification_Date>2022-05-31T17:57:45</last_Modification_Date>\n        <length_Total>1200</length_Total>\n        <levelEnd/>\n        <levelStart/>\n        <line_Name>DEFAULT</line_Name>\n        <load>0</load>\n        <motorController>FI</motorController>\n        <motor_Direction>V</motor_Direction>\n        <motor_Position>DS D40 Feather Key, Left</motor_Position>\n        <orderPlant/>\n        <outfit3D/>\n        <PLC_Area>DEFAULT</PLC_Area>\n        <parcelTypeID>CB_ST_HO</parcelTypeID>\n        <parent/>\n        <plant_Domain/>\n        <pos_No>00.000</pos_No>\n        <position>\n          <x>0</x>\n          <y>0</y>\n          <z>0</z>\n        </position>\n        <reference/>\n        <rotation>0</rotation>\n        <rotation_3D>0</rotation_3D>\n        <screen>DEFAULT</screen>\n        <section1_Angle>0</section1_Angle>\n        <section1_Length>1200</section1_Length>\n        <section2_Angle>0</section2_Angle>\n        <section2_Length>0</section2_Length>\n        <section3_Angle>0</section3_Angle>\n        <section3_Length>0</section3_Length>\n        <section4_Angle>1</section4_Angle>\n        <section4_Length>0</section4_Length>\n        <sequence_Group>DEFAULT</sequence_Group>\n        <slave_Drive>NS</slave_Drive>\n        <slope>0</slope>\n        <speed>1</speed>\n        <start_Stop_Cycles>0</start_Stop_Cycles>\n        <state>\n          <reason>1</reason>\n          <state>180</state>\n          <unique_ID>17711435</unique_ID>\n          <user/>\n        </state>\n        <storageConveyor>false</storageConveyor>\n        <supplier/>\n        <throughput>0</throughput>\n        <type_ID>SMVB30GF19</type_ID>\n        <unique_ID>17711435</unique_ID>\n        <unit_Reversible>false</unit_Reversible>\n        <usage/>\n        <useConnectionPoints>false</useConnectionPoints>\n        <useDecompositions>true</useDecompositions>\n        <useState>true</useState>\n        <user1/>\n        <user2/>\n        <user3/>\n        <user4/>\n        <user5/>\n        <vaultInstance/>\n        <version>2</version>\n        <virtual/>\n        <width>1000</width>\n      </arg4>\n    </createComponents>\n  </s:Body>\n</s:Envelope>\n\n\nResponse body:\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <ns2:createComponentsResponse xmlns:ns2=\"http://toolchain.siemens.pl\">\n      <return>17711432</return>\n      <return>17711433</return>\n      <return>17711434</return>\n      <return>17711435</return>\n    </ns2:createComponentsResponse>\n  </soap:Body>\n</soap:Envelope>\n````"}, "response": []}, {"name": "[SIDE EFFECT] SetBoMState", "request": {"method": "POST", "header": [{"key": "Content-Type", "type": "text", "value": "text/xml", "disabled": true}, {"key": "SOAPAction", "type": "text", "value": "\"#GET\"", "disabled": true}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">\r\n  <s:Body xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\r\n    <setBOMState xmlns=\"http://toolchain.siemens.pl\">\r\n      <arg0 xmlns=\"\">{{auth}}</arg0>\r\n      <arg1 xmlns=\"\">MariusTest</arg1>\r\n      <arg2 xmlns=\"\">10</arg2>\r\n      <arg3 xmlns=\"\">17711435</arg3>\r\n      <arg3 xmlns=\"\">17711434</arg3>\r\n      <arg3 xmlns=\"\">17711433</arg3>\r\n    </setBOMState>\r\n  </s:Body>\r\n</s:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{base_address}}/setBOMState", "host": ["{{base_address}}"], "path": ["setBOMState"]}, "description": "Changes BoM state for selected components\n\narg1 - project name\narg2 - new BoM status\narg3 - list of componentIds to have status changed\nreturns: true if change was successful, false otherwise\n````\n<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <s:Body xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n    <setBOMState xmlns=\"http://toolchain.siemens.pl\">\n      <arg0 xmlns=\"\">{{auth}}</arg0>\n      <arg1 xmlns=\"\">MariusTest</arg1>\n      <arg2 xmlns=\"\">10</arg2>\n      <arg3 xmlns=\"\">17711435</arg3>\n      <arg3 xmlns=\"\">17711434</arg3>\n      <arg3 xmlns=\"\">17711433</arg3>\n    </setBOMState>\n  </s:Body>\n</s:Envelope>\n\n\nResponse body:\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <ns2:setBOMStateResponse xmlns:ns2=\"http://toolchain.siemens.pl\">\n      <return>true</return>\n    </ns2:setBOMStateResponse>\n  </soap:Body>\n</soap:Envelope>\n````"}, "response": []}, {"name": "[OBSOLETE?] GetDecompositionAttributes", "request": {"method": "POST", "header": [{"key": "Content-Type", "type": "text", "value": "text/xml", "disabled": true}, {"key": "SOAPAction", "type": "text", "value": "\"#GET\"", "disabled": true}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{base_address}}/getDecompositionAttributes", "host": ["{{base_address}}"], "path": ["getDecompositionAttributes"]}, "description": "[Possibly obsolete, no calls found on any environments since may 2018]"}, "response": []}], "auth": {"type": "basic", "basic": [{"key": "password", "value": "{{basic_password}}", "type": "string"}, {"key": "username", "value": "{{basic_login}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "auth_token", "value": "JWT token from login"}, {"key": "auth_mail", "value": "<EMAIL>"}, {"key": "auth", "value": "{\"username\":\"{{auth_mail}}\",\"method\":\"jwt-azuread\",\"token\":\"{{auth_token}}\"}"}, {"key": "basic_login", "value": "mechanic_master_devel"}, {"key": "basic_password", "value": ""}, {"key": "base_address", "value": "https://wsdemo.ax4.com/ws/31041/toolchain/ToolChainLogic"}]}