{"info": {"_postman_id": "f133d5fe-aed9-490d-a939-003d9802397c", "name": "spine-local", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "GetAllProjects", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml", "type": "text"}, {"key": "SOAPAction", "value": "#POST", "type": "text"}], "body": {"mode": "raw", "raw": "<?xml version='1.0' encoding='UTF-8'?>\r\n<S:Envelope xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\">\r\n<S:Body><ns2:getAllProjects xmlns:ns2=\"http://soap.spine.siemens.com/\">\r\n<arg0>some_token</arg0>\r\n<arg1/>\r\n</ns2:getAllProjects>\r\n</S:Body>\r\n</S:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "localhost:8080/SoapWSService", "host": ["localhost"], "port": "8080", "path": ["SoapWSService"]}}, "response": []}, {"name": "GetDecompositionAttributes", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml", "type": "text"}, {"key": "SOAPAction", "value": "#POST", "type": "text"}], "body": {"mode": "raw", "raw": "<?xml version='1.0' encoding='UTF-8'?>\r\n<S:Envelope xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\">\r\n<S:Body><ns2:getDecompositionAttributes xmlns:ns2=\"http://soap.spine.siemens.com/\">\r\n<arg0>some_token</arg0>\r\n<arg1>123</arg1>\r\n<arg2/>\r\n</ns2:getDecompositionAttributes>\r\n</S:Body>\r\n</S:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "localhost:8080/SoapWSService", "host": ["localhost"], "port": "8080", "path": ["SoapWSService"]}}, "response": []}, {"name": "GetAllTypes", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml", "type": "text"}, {"key": "SOAPAction", "value": "#POST", "type": "text"}], "body": {"mode": "raw", "raw": "<?xml version='1.0' encoding='UTF-8'?>\r\n<S:Envelope xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\">\r\n<S:Body><ns2:getAllTypes xmlns:ns2=\"http://soap.spine.siemens.com/\">\r\n<arg0>lol</arg0>\r\n<arg1>MariusTest</arg1>\r\n<arg2/>\r\n</ns2:getAllTypes>\r\n</S:Body>\r\n</S:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "localhost:8080/SoapWSService", "host": ["localhost"], "port": "8080", "path": ["SoapWSService"]}}, "response": []}, {"name": "GenerateComponentIds", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml", "type": "text"}, {"key": "SOAPAction", "value": "#POST", "type": "text"}], "body": {"mode": "raw", "raw": "<?xml version='1.0' encoding='UTF-8'?>\r\n<S:Envelope xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\">\r\n<S:Body><ns2:generateComponentUniqueIds xmlns:ns2=\"http://soap.spine.siemens.com/\">\r\n<arg0>lol</arg0>\r\n<arg1>5</arg1>\r\n</ns2:generateComponentUniqueIds>\r\n</S:Body>\r\n</S:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "localhost:8080/SoapWSService", "host": ["localhost"], "port": "8080", "path": ["SoapWSService"]}}, "response": []}, {"name": "setBomState", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml", "type": "text"}, {"key": "SOAPAction", "value": "#POST", "type": "text"}], "body": {"mode": "raw", "raw": "<?xml version='1.0' encoding='UTF-8'?>\r\n<S:Envelope xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\">\r\n<S:Body><ns2:setBomState xmlns:ns2=\"http://soap.spine.siemens.com/\">\r\n<arg0>lol</arg0>\r\n<arg1>Wroclaw</arg1>\r\n<arg2>25</arg2>\r\n<arg3>17615615</arg3>\r\n</ns2:setBomState>\r\n</S:Body>\r\n</S:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "localhost:8080/SoapWSService", "host": ["localhost"], "port": "8080", "path": ["SoapWSService"]}}, "response": []}]}