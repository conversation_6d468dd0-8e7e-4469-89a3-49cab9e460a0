# SpineUi

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 14.2.2.

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.
## Important Routing Infos
- Angular Ui relies on NGINX to redirect all calls to the backend to implicitly redirect to 'index.html', this allows angular to show the Url in the browser
- Keycloak redirects to 'window.location.origin + base_href' through the angular "state", which is redirected from the backend again to 'index.html', angular frontend router will recognize the URL and show the initial page (browser side). Need to config 'Valid redirect URIs' in Keycloak server

## To build and run the Docker image, follow these steps
1. Navigate to the directory containing the Dockerfile.

2. Run the following command: "docker build -t siemens/spine-ui:1.0 --build-arg base_href=/spine-core-frontend/ ."
This command builds the Docker image using the Dockerfile in the current directory and tags it with the name siemens/spine-ui and version 1.0. 
The --build-arg option sets the value of the base_href build argument to /spine-core-frontend/
- We need to configure the production environment from Dockerfile via ENV
    The 'API_URL' needs to point to the public backend-apiUrl
    The 'KEYCLOAK_URL' needs to point to the public keycloak-url
    The 'KEYCLOAK_REALM' needs to point to the public keycloak-realm
    The 'KEYCLOAK_CLIENT_ID' needs to point to the public keycloak-client-id

    This configuration will pass to environment.prod.ts via custom-webpack.config.ts

- We need to configure 'base_href' from the Dockerfile via argument 'base_href'
- We need to configure nginx in 'nginx.conf' file like ports, locations,... If we change 'base_href', we also need to change nginx's configuration location in 'nginx.conf' file

For example, in the default configuration file: "nginx.conf"
- The Nginx configuration file sets up a server for the domain "localhost" on port 80. The server has a root directory of "/usr/share/nginx/html" where the index files "index.html" and "index.htm" are located.
- For requests to the root location "/", Nginx will try to serve the requested file or directory. If it cannot find a match, it will return a 404 error.
- For requests to the location "/spine-core-frontend", Nginx will rewrite the URL to "/spine-core-frontend/".
- For requests to the location "/spine-core-frontend/", Nginx will try to serve the requested file or directory. If it cannot find a match, it will attempt to serve the file "/spine-core-frontend/index.html".

3. After the build is complete:
- Run the following command to start a Docker container based on the image: "docker run -d -p 4200:80 --name spine-ui siemens/spine-ui:1.0"
This command starts a Docker container using the image siemens/spine-ui:1.0 and maps port 4200 on the host to port 80 on the container. The -d option runs the container in the background and the --name option sets the name of the container to spine-ui.
- Once the container is running, you should be able to access the Spine UI application by visiting localhost:4200/spine-core-frontend/ in web browser.



