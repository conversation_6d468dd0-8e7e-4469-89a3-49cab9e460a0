# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
name: spinecore
services:
  postgresql:
    image: postgres:17.4
    volumes:
      - ./postgresql/:/var/lib/postgresql/data/
      - ./schema-db/backupfile_prod.sql:/docker-entrypoint-initdb.d/backupfile_prod.sql  # Auto run import
      - ./schema-db/spine-table.sql:/docker-entrypoint-initdb.d/spine-table.sql  # Auto run import

    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_HOST_AUTH_METHOD=trust
      - POSTGRES_PASSWORD=pO5t$zum
      - POSTGRES_DB=spine
    healthcheck:
      test: [ 'CMD-SHELL', 'pg_isready -U $${POSTGRES_USER}' ]
      interval: 5s
      timeout: 5s
      retries: 10
    # If you want to expose these ports outside your dev PC,
    # remove the "127.0.0.1:" prefix
    ports:
      - 5432:5432
