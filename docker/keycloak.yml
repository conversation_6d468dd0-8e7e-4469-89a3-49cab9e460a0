# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
name: spinecore
services:
  keycloak:
    image: quay.io/keycloak/keycloak:22.0.5
    command: 'start-dev --import-realm'
    volumes:
      - ./realm-config:/opt/keycloak/data/import
      - ./realm-config/keycloak-health-check.sh:/opt/keycloak/health-check.sh
    environment:
      - KC_DB=postgres
      - KC_DB_URL=***************************************
      - KC_DB_SCHEMA=keycloak
      - KC_DB_USERNAME=postgres
      - KC_DB_PASSWORD=pO5t$zum
      - KEYCLOAK_ADMIN=admin
      - KEYCLOAK_ADMIN_PASSWORD=admin
      - KC_FEATURES=scripts
      - KC_HTTP_PORT=9080
      - KC_HTTPS_PORT=9443
      - KC_HEALTH_ENABLED=true
      - KC_HOSTNAME_STRICT=false
      - KC_HTTP_ENABLED=true
      - KC_HOSTNAME=127.0.0.1
    # If you want to expose these ports outside your dev PC,
    # remove the "127.0.0.1:" prefix
    ports:
      - 9080:9080
      - 9443:9443
    healthcheck:
      test: 'bash /opt/keycloak/health-check.sh'
      interval: 5s
      timeout: 5s
      retries: 20
      start_period: 10s
