# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
name: spinecore
services:
  postgresql:
    extends:
      file: ./postgresql.yml
      service: postgresql
    profiles:
      - ''
      - prod
    networks:
      - spine_network
  keycloak:
    extends:
      file: ./keycloak.yml
      service: keycloak
    depends_on:
      postgresql:
        condition: service_healthy
    networks:
      - spine_network
  app:
    extends:
      file: ./apps.yml
      service: spine-core
    depends_on:
      keycloak:
        condition: service_started
      postgresql:
        condition: service_healthy
    networks:
      - spine_network
#  app-ui:
#    extends:
#      file: ./apps.yml
#      service: spine-core-frontend
#    depends_on:
#      - app
#    networks:
#      - spine_network
networks:
  spine_network:
    driver: bridge