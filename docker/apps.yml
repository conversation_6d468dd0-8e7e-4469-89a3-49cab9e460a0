name: spinecore
services:
  spine-core:
    build:
      context: ..
      dockerfile: Dockerfile-local
    depends_on:
      - postgresql
      - keycloak
    environment:
      - JAVAX_SQL_DATASOURCE_SPINECOREDS_USER=postgres
      - JAVAX_SQL_DATASOURCE_SPINECOREDS_PASSWORD=pO5t$zum
      - JAVAX_SQL_DATASOURCE_SPINECOREDS_CURRENTSCHEMA=prod
      - JAVAX_SQL_DATASOURCE_SPINECOREDS_JDBCURL=***************************************
      - SECURITY_JERSEY_ENABLED=true
      - SECURITY_CONFIG_REQUIRE_ENCRYPTION=false
      - SECURITY_PROPERTIES_KEYCLOAK_URI=http://keycloak:9080
      - SECURITY_PROPERTIES_KEYCLOAK_REALM=spine
      - SECURITY_PROPERTIES_KEYCLOAK_CLIENT_ID=spine-core-iam
      - SECURITY_PROPERTIES_KEYCLOAK_CLIENT_SECRET=cBTWzxSum5HEDIEGbE7WSOSgMV0YN1uW
      - SECURITY_PROVIDERS_0_OIDC_ISSUER=http://127.0.0.1:9080/realms/spine
      - SECURITY_PROVIDERS_0_OIDC_USE_PARAM=false
    ports:
      - "8080:8080"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  spine-core-frontend:
    build:
      context: ../spine-ui
      dockerfile: Dockerfile
    ports:
      - "8082:8082"
    depends_on:
      - app
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"


