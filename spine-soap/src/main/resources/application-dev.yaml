server:
  port: 8085
javax:
  sql:
    DataSource:
      spineCoreDS:
        dataSource:
          user: postgres
          password: pO5t$zum
          currentSchema: prod
        jdbcUrl: ********************************************

security:
  jersey:
    enabled: false
  config.require-encryption: false

  properties:
    keycloak-uri: "https://iam.local.egs-dev.site"
    keycloak-realm: "spine"
    keycloak-client-id: "spine-core-iam"
    keycloak-client-secret: "cBTWzxSum5HEDIEGbE7WSOSgMV0YN1uW"

liquibase:
  enabled: true
  contexts: 'dev'
  database-change-log-table: DATABASECHANGELOG
  database-change-log-lock-table: DATABASECHANGELOGLOCK