server:
  port: 8080
  host: 0.0.0.0
  backpressure-strategy: AUTO_FLUSH

  disable-hostname-verification: true
  trust-all: true
  tls:
    trustAll: true

# Change the following to true to enable the optional MicroProfile Metrics REST.request metrics
metrics:
  rest-request:
    enabled: false

cors:
  - path-prefix: "/api/v1/**"
    allow-origins: [ "*" ]
    allow-methods: [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ]
    allow-headers: [ "Authorization", "Content-Type" ]

jakarta:
  sql:
    DataSource:
      spineCoreDS:
        maximumPoolSize: 50
        minimumIdle: 10
        connectionTimeout: 10000
        maxLifetime: 120000

security:
  jersey:
    enabled: true

  properties:
    # This is a nice way to be able to override this with local properties or env-vars
    keycloak-uri: "https://iam.local.egs-dev.site"
    keycloak-realm: "spine"
    keycloak-client-id: "spine-core"
    keycloak-client-secret: ""

  providers:
    - oidc:
        redirect: false
        client-id: "${security.properties.keycloak-client-id}"
        client-secret: "${security.properties.keycloak-client-secret}"
        identity-uri: "${security.properties.keycloak-uri}/realms/${security.properties.keycloak-realm}"
        token-endpoint-uri: "${security.properties.keycloak-uri}/realms/${security.properties.keycloak-realm}/protocol/openid-connect/token"
        cookie-use: false
        header-use: true
        oidc-metadata-well-known: false
        validate-with-jwk: false
        introspect-endpoint-uri: "${security.properties.keycloak-uri}/realms/${security.properties.keycloak-realm}/protocol/openid-connect/token/introspect"
        issuer: "${security.properties.keycloak-uri}/realms/${security.properties.keycloak-realm}"
        audience: "spine-core"

client:
  event-loop:
    name-prefix: client-thread-
    workers: 10
  connect-timeout-millis: 2000
  read-timeout-millis: 2000
  headers:
    - name: "Accept"
      value: [ "application/json","text/plain" ]

user-groups:
  uri: "${security.properties.keycloak-uri}/admin/realms/${security.properties.keycloak-realm}/groups"

# Overridden config for schedule
com.siemens.spine.resource.cache.UserGroupCacheService.synchronizeUserGroup:
  schedule:
    delay: 10
    time-unit: MINUTES
mp:
  openapi:
    servers: http://localhost:7001
    scan:
      disable: false