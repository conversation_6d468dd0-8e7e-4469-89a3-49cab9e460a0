javax:
  sql:
    DataSource:
      spineCoreDS:
        dataSource:
          user: postgres
          password: 8XnDdYLtvl4bVk0zARqHougM6eZrj2xG
          currentSchema: prod
        jdbcUrl: **********************************************

security:
  jersey:
    enabled: true
  config.require-encryption: false

  properties:
    keycloak-uri: "https://spine.kscl.dev/keycloak"
    keycloak-realm: "spine"
    keycloak-client-id: "spine-core-iam"
    keycloak-client-secret: "UrsUM6P9YKocPPXIzHSfr9CsNjQFDj39"


liquibase:
  enabled: true
  contexts: 'prod'
  database-change-log-table: DATABASECHANGELOG
  database-change-log-lock-table: DATABASECHANGELOGLOCK