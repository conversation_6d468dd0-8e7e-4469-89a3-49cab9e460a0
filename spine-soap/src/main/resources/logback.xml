<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration >
<configuration>
    <!-- Development Environment Configuration -->

    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>


    <!-- Console Appender with detailed pattern for development -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>
                [%d{yyyy-MM-dd HH:mm:ss.SSS}] %highlight(%-5level) %cyan([%thread]) [%logger{36}.%M:%L] - %msg%n
            </pattern>
        </encoder>
    </appender>

    <logger name="com.sun.xml.wss.provider" level="OFF" additivity="false"/>

    <!-- Database logging configuration -->
    <logger name="org.hibernate" level="INFO"/>
    <logger name="org.hibernate.orm.jdbc.bind" level="TRACE"/>
    <logger name="org.hibernate.type.descriptor.sql" level="DEBUG"/>
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE"/>
    <logger name="org.hibernate.SQL" level="DEBUG"/>

    <!-- Helidon Security detailed logging -->
    <logger name="io.helidon.security" level="TRACE"/>
    <logger name="io.helidon.security.providers" level="TRACE"/>
    <logger name="io.helidon.security.providers.oidc" level="TRACE"/>
    <logger name="io.helidon.security.jwt" level="TRACE"/>

    <!-- Your existing application loggers -->
    <logger name="com.koerber.spine" level="DEBUG"/>
    <logger name="com.koerber.spine.security" level="DEBUG"/>
    <logger name="com.koerber.spine.interceptor" level="DEBUG"/>
    <logger name="com.koerber.spine.exception" level="DEBUG"/>
    <logger name="com.koerber.spine.resource" level="DEBUG"/>

    <!-- Web server and API access logs -->
    <logger name="io.helidon.webserver.AccessLog" level="TRACE"/>
    <logger name="org.glassfish.jersey.server.ApplicationHandler" level="TRACE"/>

    <!-- Root logger configuration -->
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
