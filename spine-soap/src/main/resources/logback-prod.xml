<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration >
<configuration>
    <!-- Production Environment Configuration -->

    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

    <!-- Properties for logging configuration -->
    <property name="LOG_DIR" value="${LOG_DIR:-/var/log/spine-core}"/>
    <property name="LOG_FILE_NAME" value="spine-core"/>
    <property name="MAX_FILE_SIZE" value="100MB"/>
    <property name="MAX_HISTORY" value="30"/>
    <property name="TOTAL_SIZE_CAP" value="3GB"/>

    <!-- Async Appender for better performance -->
    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <appender-ref ref="FILE"/>
    </appender>

    <!-- Async Error Appender -->
    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>256</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <appender-ref ref="ERROR_FILE"/>
    </appender>

    <!-- Console Appender with concise pattern for production -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>
                [%d{yyyy-MM-dd HH:mm:ss.SSS}] %-5level [%thread] [%logger{36}] - %msg%n
            </pattern>
        </encoder>
    </appender>

    <!-- File Appender for production with size and time based rolling -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/${LOG_FILE_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/${LOG_FILE_NAME}-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] %-5level [%thread] [%logger{36}] - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Error File Appender for critical errors only -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/${LOG_FILE_NAME}-error.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/${LOG_FILE_NAME}-error-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] %-5level [%thread] [%logger{36}] - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Third-party libraries - reduce noise -->
    <logger name="com.arjuna" level="OFF" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="com.sun.xml.wss" level="OFF" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <!-- Database logging configuration - minimal in production -->
    <logger name="org.hibernate" level="WARN"/>
    <logger name="org.hibernate.SQL" level="INFO"/>

    <!-- Application-specific loggers with appropriate production levels -->
    <logger name="com.koerber.spine" level="INFO"/>
    <logger name="com.koerber.spine.security" level="INFO"/>
    <logger name="com.koerber.spine.interceptor" level="INFO"/>
    <logger name="com.koerber.spine.exception" level="INFO"/>

    <!-- Root logger configuration -->
    <root level="WARN">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="ASYNC_FILE"/>
        <appender-ref ref="ASYNC_ERROR_FILE"/>
    </root>
</configuration>
