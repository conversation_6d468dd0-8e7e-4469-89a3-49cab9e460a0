<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:ns="http://xmlns.jcp.org/xml/ns/persistence"
                version="2.0">
    <xsl:output method="xml" encoding="UTF-8" indent="yes"/>
    <!-- The params are passed from pom.xml/plugin -->
    <xsl:param name="hibernate-dialect"/>
    <xsl:param name="hbm2ddl-auto"/>

    <!-- identity transform -->
    <xsl:template match="@*|node()">
        <xsl:copy>
            <xsl:apply-templates select="@*|node()"/>
        </xsl:copy>
    </xsl:template>

    <!--  Replace hibernate.dialect -->
    <xsl:template match="ns:property[@name[.='hibernate.dialect']]/@value">
        <xsl:attribute name="value">
            <xsl:value-of select="$hibernate-dialect"/>
        </xsl:attribute>
    </xsl:template>

    <!--  Replace hbm2ddl.auto -->
    <xsl:template match="ns:property[@name[.='hibernate.hbm2ddl.auto']]/@value">
        <xsl:attribute name="value">
            <xsl:value-of select="$hbm2ddl-auto"/>
        </xsl:attribute>
    </xsl:template>

</xsl:stylesheet>