<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <property name="now" value="now()" dbms="postgresql"/>
    <property name="floatType" value="float" dbms="postgresql"/>
    <property name="clobType" value="clob" dbms="postgresql"/>
    <property name="blobType" value="longblob" dbms="postgresql"/>
    <property name="uuidType" value="varchar(36)" dbms="postgresql"/>
    <property name="datetimeType" value="datetime(6)" dbms="postgresql"/>

    <include file="changelog/sql/00000000000000_initial_schema.sql" relativeToChangelogFile="true"/>
    <include file="changelog/sql/add_table_project_component_version.sql" relativeToChangelogFile="true"/>
    <include file="changelog/sql/20252305_create_partition_spine_audit_0_100000.sql" relativeToChangelogFile="true"/>
    <include file="changelog/sql/add_table_query_template.sql" relativeToChangelogFile="true"/>
    <include file="changelog/sql/add_table_query_template_aud.sql" relativeToChangelogFile="true"/>
</databaseChangeLog>