--liquibase formatted sql

--changeset admin:1747982711144-2
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_class WHERE relname = 'component_aud_rev_1_100000' AND relkind = 'r'
CREATE TABLE component_aud_rev_1_100000 PARTITION OF component_aud FOR VALUES FROM (0) TO (100000);



--changeset admin:1747982711144-3
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_class WHERE relname = 'componentstatus_aud_rev_1_100000' AND relkind = 'r'
CREATE TABLE componentstatus_aud_rev_1_100000 PARTITION OF componentstatus_aud FOR VALUES FROM (0) TO (100000);


--changeset admin:1747982711144-4
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_class WHERE relname = 'connectionpoint_aud_rev_1_100000' AND relkind = 'r'
CREATE TABLE connectionpoint_aud_rev_1_100000 PARTITION OF connectionpoint_aud FOR VALUES FROM (0) TO (100000);


--changeset admin:1747982711144-5
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_class WHERE relname = 'decomposition_aud_rev_1_100000' AND relkind = 'r'
CREATE TABLE decomposition_aud_rev_1_100000 PARTITION OF decomposition_aud FOR VALUES FROM (0) TO (100000);


--changeset admin:1747982711144-6
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_class WHERE relname = 'drive_assignment_data_aud_rev_1_100000' AND relkind = 'r'
CREATE TABLE drive_assignment_data_aud_rev_1_100000 PARTITION OF drive_assignment_data_aud FOR VALUES FROM (0) TO (100000);


--changeset admin:1747982711144-7
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_class WHERE relname = 'group_aud_rev_1_100000' AND relkind = 'r'
CREATE TABLE group_aud_rev_1_100000 PARTITION OF group_aud FOR VALUES FROM (0) TO (100000);


--changeset admin:1747982711144-8
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_class WHERE relname = 'groupcomponent_aud_rev_1_100000' AND relkind = 'r'
CREATE TABLE groupcomponent_aud_rev_1_100000 PARTITION OF groupcomponent_aud FOR VALUES FROM (0) TO (100000);


--changeset admin:1747982711144-9
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_class WHERE relname = 'neighbor_connection_aud_rev_1_100000' AND relkind = 'r'
CREATE TABLE neighbor_connection_aud_rev_1_100000 PARTITION OF neighbor_connection_aud FOR VALUES FROM (0) TO (100000);


--changeset admin:1747982711144-10
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_class WHERE relname = 'position_aud_rev_1_100000' AND relkind = 'r'
CREATE TABLE position_aud_rev_1_100000 PARTITION OF position_aud FOR VALUES FROM (0) TO (100000);


--changeset admin:1747982711144-11
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_class WHERE relname = 'state_aud_rev_1_100000' AND relkind = 'r'
CREATE TABLE state_aud_rev_1_100000 PARTITION OF state_aud FOR VALUES FROM (0) TO (100000);


