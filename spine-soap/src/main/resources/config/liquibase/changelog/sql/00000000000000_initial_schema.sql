--liquibase formatted sql

--changeset admin:1746436054293-1 dbms:postgresql
CREATE TABLE IF NOT EXISTS  "component"
(
    "id"                  BIGINT NOT NULL,
    "amountbufferelec"    INTEGER,
    "amountbuffermech"    INTEGER,
    "breaktype"           VARCHAR(255),
    "buffersize"          INTEGER,
    "buildsection"        VARCHAR(255),
    "calculationarea"     VARCHAR(255),
    "comment"             VARCHAR(255),
    "curveangle"          INTEGER,
    "curvedirection"      VARCHAR(255),
    "curveradius"         INTEGER,
    "drawing"             VARCHAR(255),
    "driveposition"       VARCHAR(255),
    "drivestation"        VARCHAR(255),
    "estopgroup"          VARCHAR(255),
    "height"              INTEGER,
    "lengthtotal"         INTEGER,
    "load"                FLOAT8,
    "motorcontroller"     VARCHAR(255),
    "motordirection"      VARCHAR(255),
    "motorposition"       VARCHAR(255),
    "plcarea"             VARCHAR(255),
    "parent"              VARCHAR(255),
    "plantdomain"         VARCHAR(255),
    "posno"               VARCHAR(255),
    "reference"           VARCHAR(255),
    "rotation"            INTEGER,
    "rotation3d"          FLOAT8,
    "screen"              VARCHAR(255),
    "section1angle"       FLOAT8,
    "section1length"      FLOAT8,
    "section2angle"       FLOAT8,
    "section2length"      FLOAT8,
    "section3angle"       FLOAT8,
    "section3length"      FLOAT8,
    "section4angle"       FLOAT8,
    "section4length"      FLOAT8,
    "sequencegroup"       VARCHAR(255),
    "slavedrive"          VARCHAR(255),
    "slope"               FLOAT8,
    "speed"               VARCHAR(255),
    "startstopcycles"     INTEGER,
    "supplier"            VARCHAR(255),
    "throughput"          INTEGER,
    "typeid"              VARCHAR(255),
    "unitreversible"      BOOLEAN,
    "usage"               VARCHAR(255),
    "version"             INTEGER,
    "virtual"             VARCHAR(255),
    "width"               INTEGER,
    "installationhours"   INTEGER,
    "engineeringhours"    INTEGER,
    "colorgroup"          VARCHAR(255),
    "akz"                 VARCHAR(255),
    "customerakz"         VARCHAR(255),
    "autocadlayer"        VARCHAR(255),
    "user1"               VARCHAR(255),
    "user2"               VARCHAR(255),
    "user3"               VARCHAR(255),
    "user4"               VARCHAR(255),
    "user5"               VARCHAR(255),
    "linename"            VARCHAR(255),
    "buildingsection"     VARCHAR(255),
    "installationsection" VARCHAR(255),
    "driveside"           VARCHAR(255),
    "anglecorr"           VARCHAR(255),
    "levelstart"          VARCHAR(255),
    "levelend"            VARCHAR(255),
    "controlnr"           VARCHAR(255),
    "vaultinstance"       VARCHAR(255),
    "drawingversion"      VARCHAR(255),
    "drawingrevision"     VARCHAR(255),
    "outfit3d"            VARCHAR(255),
    "parceltypeid"        VARCHAR(255),
    "orderplant"          VARCHAR(255),
    "designplant"         VARCHAR(255),
    "storageconveyor"     BOOLEAN,
    "drivepulleydiameter" FLOAT8,
    "driveshaftdiameter"  FLOAT8,
    "acceleration"        FLOAT8,
    "position"            INTEGER,
    "projectid"           INTEGER,
    "currentstate"        INTEGER DEFAULT 20,
    "reasonofchange"      VARCHAR,
    "remark"              VARCHAR,
    "modificationstate"   VARCHAR,
    "spinestatefull"      VARCHAR,
    "mps"                 VARCHAR,
    "sysmoddate"          TIMESTAMP WITHOUT TIME ZONE,
    "syscreatedate"       TIMESTAMP WITHOUT TIME ZONE
);

--changeset admin:1746436054293-2
CREATE TABLE IF NOT EXISTS  "drive_assignment_data"
(
    "id"            BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    "driver_number" INTEGER,
    "data"          VARCHAR,
    "component_id"  BIGINT,
    "project_id"    BIGINT
);

--changeset admin:1746436054293-3
CREATE SEQUENCE IF NOT EXISTS "drive_assignment_id_seq" AS bigint START WITH 1 INCREMENT BY 50 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

--changeset admin:1746436054293-4
CREATE TABLE IF NOT EXISTS "state"
(
    "id"                       INTEGER NOT NULL,
    "state"                    INTEGER,
    "reason"                   INTEGER,
    "username"                 VARCHAR(255),
    "remark"                   VARCHAR(255),
    "status_date"              TIMESTAMP WITHOUT TIME ZONE,
    "component_id"             INTEGER,
    "change_group_id"          BIGINT,
    "change_group_description" TEXT
);

--changeset admin:1746436054293-7
CREATE INDEX IF NOT EXISTS  "idx_state_component" ON "state" ("component_id");

--changeset admin:1746436054293-8
CREATE INDEX IF NOT EXISTS  "idx_state_changegroup" ON "state" ("change_group_id");

--changeset admin:1746436054293-11
CREATE SEQUENCE IF NOT EXISTS "change_group_id_seq" AS bigint START WITH 1 INCREMENT BY 50 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

--changeset admin:1746436054293-12
CREATE SEQUENCE IF NOT EXISTS "component_id_seq" AS bigint START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

--changeset admin:1746436054293-13
CREATE SEQUENCE IF NOT EXISTS "connection_point_id_seq" AS bigint START WITH 1 INCREMENT BY 50 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

--changeset admin:1746436054293-14
CREATE SEQUENCE IF NOT EXISTS "decomposition_id_seq" AS bigint START WITH 1 INCREMENT BY 50 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

--changeset admin:1746436054293-15
CREATE SEQUENCE IF NOT EXISTS "group_id_seq" AS bigint START WITH 1 INCREMENT BY 50 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

--changeset admin:1746436054293-16
CREATE SEQUENCE IF NOT EXISTS "grouptoprojectandrole_id_seq" AS bigint START WITH 1 INCREMENT BY 50 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

--changeset admin:1746436054293-17
CREATE SEQUENCE IF NOT EXISTS "hibernate_sequence" AS bigint START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

--changeset admin:1746436054293-18
CREATE SEQUENCE IF NOT EXISTS "operationtorole_id_seq" AS bigint START WITH 1 INCREMENT BY 50 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

--changeset admin:1746436054293-19
CREATE SEQUENCE IF NOT EXISTS "position_id_seq" AS bigint START WITH 1 INCREMENT BY 50 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

--changeset admin:1746436054293-20
CREATE SEQUENCE IF NOT EXISTS "project_id_seq" AS bigint START WITH 1 INCREMENT BY 50 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

--changeset admin:1746436054293-21
CREATE SEQUENCE IF NOT EXISTS "project_views_group_mapping_id_seq" AS bigint START WITH 1 INCREMENT BY 50 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

--changeset admin:1746436054293-22
CREATE SEQUENCE IF NOT EXISTS "state_id_seq" AS bigint START WITH 1 INCREMENT BY 50 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1;

--changeset admin:1746436054293-23
CREATE TABLE IF NOT EXISTS  "changegroup"
(
    "change_group_id" INTEGER NOT NULL,
    "project_id"      INTEGER NOT NULL,
    "name"            VARCHAR(255),
    "description"     VARCHAR(255),
    "default_reason"  INTEGER
);

--changeset admin:1746436054293-24
CREATE TABLE IF NOT EXISTS  "component_aud"
(
    "id"                       BIGINT  NOT NULL,
    "rev"                      INTEGER NOT NULL,
    "revtype"                  SMALLINT,
    "acceleration"             FLOAT8,
    "acceleration_mod"         BOOLEAN,
    "akz"                      VARCHAR(255),
    "akz_mod"                  BOOLEAN,
    "amountbufferelec"         INTEGER,
    "amountbufferelec_mod"     BOOLEAN,
    "amountbuffermech"         INTEGER,
    "amountbuffermech_mod"     BOOLEAN,
    "anglecorr"                VARCHAR(255),
    "anglecorr_mod"            BOOLEAN,
    "autocadlayer"             VARCHAR(255),
    "autocadlayer_mod"         BOOLEAN,
    "breaktype"                VARCHAR(255),
    "breaktype_mod"            BOOLEAN,
    "buffersize"               INTEGER,
    "buffersize_mod"           BOOLEAN,
    "colorgroup"               VARCHAR(255),
    "colorgroup_mod"           BOOLEAN,
    "comment"                  VARCHAR(255),
    "comment_mod"              BOOLEAN,
    "controlnr"                VARCHAR(255),
    "controlnr_mod"            BOOLEAN,
    "currentstate"             INTEGER,
    "currentstate_mod"         BOOLEAN,
    "curveangle"               INTEGER,
    "curveangle_mod"           BOOLEAN,
    "curvedirection"           VARCHAR(255),
    "curvedirection_mod"       BOOLEAN,
    "curveradius"              INTEGER,
    "curveradius_mod"          BOOLEAN,
    "customerakz"              VARCHAR(255),
    "customerakz_mod"          BOOLEAN,
    "designplant"              VARCHAR(255),
    "designplant_mod"          BOOLEAN,
    "drawingrevision"          VARCHAR(255),
    "drawingrevision_mod"      BOOLEAN,
    "drawingversion"           VARCHAR(255),
    "drawingversion_mod"       BOOLEAN,
    "driveposition"            VARCHAR(255),
    "driveposition_mod"        BOOLEAN,
    "drivepulleydiameter"      FLOAT8,
    "drivepulleydiameter_mod"  BOOLEAN,
    "driveshaftdiameter"       FLOAT8,
    "driveshaftdiameter_mod"   BOOLEAN,
    "driveside"                VARCHAR(255),
    "driveside_mod"            BOOLEAN,
    "drivestation"             VARCHAR(255),
    "drivestation_mod"         BOOLEAN,
    "engineeringhours"         INTEGER,
    "engineeringhours_mod"     BOOLEAN,
    "height"                   INTEGER,
    "height_mod"               BOOLEAN,
    "installationhours"        INTEGER,
    "installationhours_mod"    BOOLEAN,
    "lengthtotal"              INTEGER,
    "lengthtotal_mod"          BOOLEAN,
    "levelend"                 VARCHAR(255),
    "levelend_mod"             BOOLEAN,
    "levelstart"               VARCHAR(255),
    "levelstart_mod"           BOOLEAN,
    "load"                     FLOAT8,
    "load_mod"                 BOOLEAN,
    "motorcontroller"          VARCHAR(255),
    "motorcontroller_mod"      BOOLEAN,
    "motordirection"           VARCHAR(255),
    "motordirection_mod"       BOOLEAN,
    "motorposition"            VARCHAR(255),
    "motorposition_mod"        BOOLEAN,
    "orderplant"               VARCHAR(255),
    "orderplant_mod"           BOOLEAN,
    "outfit3d"                 VARCHAR(255),
    "outfit3d_mod"             BOOLEAN,
    "parceltypeid"             VARCHAR(255),
    "parceltypeid_mod"         BOOLEAN,
    "parent"                   VARCHAR(255),
    "parent_mod"               BOOLEAN,
    "plantdomain"              VARCHAR(255),
    "plantdomain_mod"          BOOLEAN,
    "posno"                    VARCHAR(255),
    "posno_mod"                BOOLEAN,
    "projectid"                BIGINT,
    "projectid_mod"            BOOLEAN,
    "reference"                VARCHAR(255),
    "reference_mod"            BOOLEAN,
    "rotation"                 INTEGER,
    "rotation_mod"             BOOLEAN,
    "rotation3d"               FLOAT8,
    "rotation3d_mod"           BOOLEAN,
    "section1angle"            FLOAT8,
    "section1angle_mod"        BOOLEAN,
    "section1length"           FLOAT8,
    "section1length_mod"       BOOLEAN,
    "section2angle"            FLOAT8,
    "section2angle_mod"        BOOLEAN,
    "section2length"           FLOAT8,
    "section2length_mod"       BOOLEAN,
    "section3angle"            FLOAT8,
    "section3angle_mod"        BOOLEAN,
    "section3length"           FLOAT8,
    "section3length_mod"       BOOLEAN,
    "section4angle"            FLOAT8,
    "section4angle_mod"        BOOLEAN,
    "section4length"           FLOAT8,
    "section4length_mod"       BOOLEAN,
    "slavedrive"               VARCHAR(255),
    "slavedrive_mod"           BOOLEAN,
    "slope"                    FLOAT8,
    "slope_mod"                BOOLEAN,
    "speed"                    VARCHAR(255),
    "speed_mod"                BOOLEAN,
    "startstopcycles"          INTEGER,
    "startstopcycles_mod"      BOOLEAN,
    "storageconveyor"          BOOLEAN,
    "storageconveyor_mod"      BOOLEAN,
    "supplier"                 VARCHAR(255),
    "supplier_mod"             BOOLEAN,
    "syscreatedate"            TIMESTAMP WITHOUT TIME ZONE,
    "syscreatedate_mod"        BOOLEAN,
    "sysmoddate"               TIMESTAMP WITHOUT TIME ZONE,
    "sysmoddate_mod"           BOOLEAN,
    "throughput"               INTEGER,
    "throughput_mod"           BOOLEAN,
    "typeid"                   VARCHAR(255),
    "typeid_mod"               BOOLEAN,
    "unitreversible"           BOOLEAN,
    "unitreversible_mod"       BOOLEAN,
    "usage"                    VARCHAR(255),
    "usage_mod"                BOOLEAN,
    "user1"                    VARCHAR(255),
    "user1_mod"                BOOLEAN,
    "user2"                    VARCHAR(255),
    "user2_mod"                BOOLEAN,
    "user3"                    VARCHAR(255),
    "user3_mod"                BOOLEAN,
    "user4"                    VARCHAR(255),
    "user4_mod"                BOOLEAN,
    "user5"                    VARCHAR(255),
    "user5_mod"                BOOLEAN,
    "vaultinstance"            VARCHAR(255),
    "vaultinstance_mod"        BOOLEAN,
    "version"                  INTEGER,
    "version_mod"              BOOLEAN,
    "virtual"                  VARCHAR(255),
    "virtual_mod"              BOOLEAN,
    "width"                    INTEGER,
    "width_mod"                BOOLEAN,
    "connectionpoints_mod"     BOOLEAN,
    "decompositions_mod"       BOOLEAN,
    "driveassignmentdatas_mod" BOOLEAN,
    "states_mod"               BOOLEAN,
    "position_mod"             BOOLEAN,
    "status_mod"               BOOLEAN,
    "groups_mod"               BOOLEAN,
    CONSTRAINT "component_aud_pkey" PRIMARY KEY ("id", "rev")
) PARTITION BY RANGE (REV);


--changeset admin:1746436054293-25
CREATE TABLE IF NOT EXISTS  "componentstatus"
(
    "uniqueid"                       INTEGER NOT NULL,
    "emulationstate"                 INTEGER,
    "emulationstatedate"             TIMESTAMP WITHOUT TIME ZONE,
    "itstate"                        INTEGER,
    "itstatedate"                    TIMESTAMP WITHOUT TIME ZONE,
    "simulationstate"                INTEGER,
    "simulationstatedate"            TIMESTAMP WITHOUT TIME ZONE,
    "calculationstate"               INTEGER,
    "calculationstatedate"           TIMESTAMP WITHOUT TIME ZONE,
    "bomstate"                       INTEGER,
    "bomstatedate"                   TIMESTAMP WITHOUT TIME ZONE,
    "rownum"                         INTEGER,
    "sapid"                          VARCHAR(255),
    "simulationcomponentlastmoddate" TIMESTAMP WITHOUT TIME ZONE,
    "objectxmlexportstate"           INTEGER,
    "objectxmlexportstatedate"       TIMESTAMP WITHOUT TIME ZONE,
    "electricsynchronizationstate"   INTEGER,
    "electricsynchronizationdate"    TIMESTAMP WITHOUT TIME ZONE
);

--changeset admin:1746436054293-26
CREATE TABLE IF NOT EXISTS  "componentstatus_aud"
(
    "uniqueid"                           BIGINT  NOT NULL,
    "rev"                                INTEGER NOT NULL,
    "revtype"                            SMALLINT,
    "bomstate"                           INTEGER,
    "bomstate_mod"                       BOOLEAN,
    "bomstatedate"                       TIMESTAMP WITHOUT TIME ZONE,
    "bomstatedate_mod"                   BOOLEAN,
    "calculationstate"                   INTEGER,
    "calculationstate_mod"               BOOLEAN,
    "calculationstatedate"               TIMESTAMP WITHOUT TIME ZONE,
    "calculationstatedate_mod"           BOOLEAN,
    "electricsynchronizationdate"        TIMESTAMP WITHOUT TIME ZONE,
    "electricsynchronizationdate_mod"    BOOLEAN,
    "electricsynchronizationstate"       INTEGER,
    "electricsynchronizationstate_mod"   BOOLEAN,
    "emulationstate"                     INTEGER,
    "emulationstate_mod"                 BOOLEAN,
    "emulationstatedate"                 TIMESTAMP WITHOUT TIME ZONE,
    "emulationstatedate_mod"             BOOLEAN,
    "itstate"                            INTEGER,
    "itstate_mod"                        BOOLEAN,
    "itstatedate"                        TIMESTAMP WITHOUT TIME ZONE,
    "itstatedate_mod"                    BOOLEAN,
    "objectxmlexportstate"               INTEGER,
    "objectxmlexportstate_mod"           BOOLEAN,
    "objectxmlexportstatedate"           TIMESTAMP WITHOUT TIME ZONE,
    "objectxmlexportstatedate_mod"       BOOLEAN,
    "rownum"                             INTEGER,
    "rownum_mod"                         BOOLEAN,
    "sapid"                              VARCHAR(255),
    "sapid_mod"                          BOOLEAN,
    "simulationcomponentlastmoddate"     TIMESTAMP WITHOUT TIME ZONE,
    "simulationcomponentlastmoddate_mod" BOOLEAN,
    "simulationstate"                    INTEGER,
    "simulationstate_mod"                BOOLEAN,
    "simulationstatedate"                TIMESTAMP WITHOUT TIME ZONE,
    "simulationstatedate_mod"            BOOLEAN,
    "component_mod"                      BOOLEAN,
    CONSTRAINT "componentstatus_aud_pkey" PRIMARY KEY ("uniqueid", "rev")
) PARTITION BY RANGE (REV);

--changeset admin:1746436054293-27
CREATE TABLE IF NOT EXISTS  "connectionpoint"
(
    "id"           INTEGER NOT NULL,
    "name"         VARCHAR(255),
    "type"         VARCHAR(255),
    "x"            INTEGER,
    "y"            INTEGER,
    "z"            INTEGER,
    "component_id" INTEGER
);

--changeset admin:1746436054293-28
CREATE TABLE IF NOT EXISTS  "connectionpoint_aud"
(
    "id"            BIGINT  NOT NULL,
    "rev"           INTEGER NOT NULL,
    "revtype"       SMALLINT,
    "name"          VARCHAR(255),
    "name_mod"      BOOLEAN,
    "type"          VARCHAR(255),
    "type_mod"      BOOLEAN,
    "x"             INTEGER,
    "x_mod"         BOOLEAN,
    "y"             INTEGER,
    "y_mod"         BOOLEAN,
    "z"             INTEGER,
    "z_mod"         BOOLEAN,
    "component_id"  BIGINT,
    "component_mod" BOOLEAN,
    CONSTRAINT "connectionpoint_aud_pkey" PRIMARY KEY ("id", "rev")
) PARTITION BY RANGE (REV);

--changeset admin:1746436054293-29
CREATE TABLE IF NOT EXISTS  "counter"
(
    "counterid"    BIGINT NOT NULL,
    "name"         VARCHAR(255),
    "countervalue" BIGINT NOT NULL,
    "maxcounter"   BIGINT NOT NULL,
    "mincounter"   BIGINT NOT NULL,
    "warning"      BIGINT NOT NULL
);

--changeset admin:1746436054293-30
CREATE TABLE IF NOT EXISTS  "decomposition"
(
    "component_id"      INTEGER,
    "changedescription" VARCHAR(255),
    "derivedfrom"       VARCHAR(255),
    "description"       VARCHAR(255),
    "mpsprefix"         VARCHAR(255),
    "materialtype"      VARCHAR(255),
    "positionnumber"    VARCHAR(255) NOT NULL,
    "quantity"          INTEGER,
    "sapmaterialnumber" VARCHAR(255) NOT NULL,
    "sourceinternal"    VARCHAR(255),
    "standardmaterial"  BOOLEAN,
    "unitinsap"         VARCHAR(255),
    "zoptions"          VARCHAR(255),
    "source"            VARCHAR(255),
    "id"                BIGINT       NOT NULL
);

--changeset admin:1746436054293-31
CREATE TABLE IF NOT EXISTS  "decomposition_aud"
(
    "id"                    BIGINT  NOT NULL,
    "rev"                   INTEGER NOT NULL,
    "revtype"               SMALLINT,
    "changedescription"     VARCHAR(255),
    "changedescription_mod" BOOLEAN,
    "derivedfrom"           VARCHAR(255),
    "derivedfrom_mod"       BOOLEAN,
    "description"           VARCHAR(255),
    "description_mod"       BOOLEAN,
    "materialtype"          VARCHAR(255),
    "materialtype_mod"      BOOLEAN,
    "mpsprefix"             VARCHAR(255),
    "mpsprefix_mod"         BOOLEAN,
    "positionnumber"        VARCHAR(255),
    "positionnumber_mod"    BOOLEAN,
    "quantity"              INTEGER,
    "quantity_mod"          BOOLEAN,
    "sapmaterialnumber"     VARCHAR(255),
    "sapmaterialnumber_mod" BOOLEAN,
    "source"                VARCHAR(255),
    "source_mod"            BOOLEAN,
    "sourceinternal"        VARCHAR(255),
    "sourceinternal_mod"    BOOLEAN,
    "standardmaterial"      BOOLEAN,
    "standardmaterial_mod"  BOOLEAN,
    "unitinsap"             VARCHAR(255),
    "unitinsap_mod"         BOOLEAN,
    "zoptions"              VARCHAR(255),
    "zoptions_mod"          BOOLEAN,
    "component_id"          BIGINT,
    "component_mod"         BOOLEAN,
    CONSTRAINT "decomposition_aud_pkey" PRIMARY KEY ("id", "rev")
) PARTITION BY RANGE (REV);

--changeset admin:1746436054293-32
CREATE TABLE IF NOT EXISTS  "decompositionattributes"
(
    "sapmaterialnumber" VARCHAR(255),
    "drivebreak"        VARCHAR(255),
    "drivecosphi"       FLOAT8 NOT NULL,
    "drivecurrent"      FLOAT8 NOT NULL,
    "drivefrequency"    FLOAT8 NOT NULL,
    "drivepower"        FLOAT8 NOT NULL,
    "driveprotection"   VARCHAR(255),
    "drivereversible"   VARCHAR(255),
    "drivestarter"      VARCHAR(255),
    "drivetvolt"        VARCHAR(255),
    "drivetype"         VARCHAR(255),
    "drivevfd"          VARCHAR(255),
    "drivevoltage"      VARCHAR(255),
    "drivewiring"       VARCHAR(255),
    "startstopcycles"   FLOAT8 NOT NULL
);

--changeset admin:1746436054293-33
CREATE TABLE IF NOT EXISTS  "drive_assignment_data_aud"
(
    "id"               BIGINT  NOT NULL,
    "rev"              INTEGER NOT NULL,
    "revtype"          SMALLINT,
    "component_id"     BIGINT,
    "componentid_mod"  BOOLEAN,
    "data"             VARCHAR(1000),
    "data_mod"         BOOLEAN,
    "driver_number"    INTEGER,
    "drivernumber_mod" BOOLEAN,
    "component_mod"    BOOLEAN,
    CONSTRAINT "drive_assignment_data_aud_pkey" PRIMARY KEY ("id", "rev")
) PARTITION BY RANGE (REV);

--changeset admin:1746436054293-34
CREATE TABLE IF NOT EXISTS  "group"
(
    "id"           BIGINT  NOT NULL,
    "grouptype"    VARCHAR(255),
    "deliverydate" TIMESTAMP WITHOUT TIME ZONE,
    "comment"      TEXT,
    "subtype"      VARCHAR(255),
    "projectid"    INTEGER NOT NULL,
    "name"         VARCHAR(255)
);

--changeset admin:1746436054293-35
CREATE TABLE IF NOT EXISTS  "group_aud"
(
    "id"               BIGINT  NOT NULL,
    "rev"              INTEGER NOT NULL,
    "revtype"          SMALLINT,
    "components_mod"   BOOLEAN,
    "comment"          VARCHAR(255),
    "comment_mod"      BOOLEAN,
    "deliverydate"     TIMESTAMP WITHOUT TIME ZONE,
    "deliverydate_mod" BOOLEAN,
    "grouptype"        VARCHAR(255),
    "grouptype_mod"    BOOLEAN,
    "name"             VARCHAR(255),
    "name_mod"         BOOLEAN,
    "projectid"        BIGINT,
    "projectid_mod"    BOOLEAN,
    "subtype"          VARCHAR(255),
    "subtype_mod"      BOOLEAN,
    CONSTRAINT "group_aud_pkey" PRIMARY KEY ("id", "rev")
) PARTITION BY RANGE (REV);

--changeset admin:1746436054293-36
CREATE TABLE IF NOT EXISTS  "groupcomponent"
(
    "groupid"     INTEGER NOT NULL,
    "componentid" INTEGER NOT NULL
);

--changeset admin:1746436054293-37
CREATE TABLE IF NOT EXISTS  "groupcomponent_aud"
(
    "rev"         INTEGER NOT NULL,
    "componentid" BIGINT  NOT NULL,
    "groupid"     BIGINT  NOT NULL,
    "revtype"     SMALLINT,
    CONSTRAINT "groupcomponent_aud_pkey" PRIMARY KEY ("rev", "componentid", "groupid")
) PARTITION BY RANGE (REV);

--changeset admin:1746436054293-38
CREATE TABLE IF NOT EXISTS  "grouptoprojectandrole"
(
    "id"        BIGINT NOT NULL,
    "groupname" VARCHAR(255),
    "rolename"  VARCHAR(255),
    "projectid" INTEGER
);

--changeset admin:1746436054293-39
CREATE TABLE IF NOT EXISTS  "neighbor_connection"
(
    "own_cp_id"           BIGINT NOT NULL,
    "neighbor_cp_id"      BIGINT,
    "neighbor_assignment" VARCHAR,
    "is_manual"           BOOLEAN,
    "gap"                 numeric
);

--changeset admin:1746436054293-40
CREATE TABLE IF NOT EXISTS  "neighbor_connection_aud"
(
    "own_cp_id"           BIGINT  NOT NULL,
    "rev"                 INTEGER NOT NULL,
    "revtype"             SMALLINT,
    "neighbor_assignment" VARCHAR(255),
    "assignment_mod"      BOOLEAN,
    "gap"                 FLOAT8,
    "gap_mod"             BOOLEAN,
    "is_manual"           BOOLEAN,
    "manual_mod"          BOOLEAN,
    "neighbor_cp_id"      BIGINT,
    "neighbor_mod"        BOOLEAN,
    CONSTRAINT "neighbor_connection_aud_pkey" PRIMARY KEY ("own_cp_id", "rev")
) PARTITION BY RANGE (REV);

--changeset admin:1746436054293-41
CREATE TABLE IF NOT EXISTS  "operationtorole"
(
    "id"        BIGINT NOT NULL,
    "operation" VARCHAR(255),
    "rolename"  VARCHAR(255)
);

--changeset admin:1746436054293-42
CREATE TABLE IF NOT EXISTS  "position"
(
    "id" INTEGER NOT NULL,
    "x"  INTEGER,
    "y"  INTEGER,
    "z"  INTEGER
);

--changeset admin:1746436054293-43
CREATE TABLE IF NOT EXISTS  "position_aud"
(
    "id"            BIGINT  NOT NULL,
    "rev"           INTEGER NOT NULL,
    "revtype"       SMALLINT,
    "x"             INTEGER,
    "x_mod"         BOOLEAN,
    "y"             INTEGER,
    "y_mod"         BOOLEAN,
    "z"             INTEGER,
    "z_mod"         BOOLEAN,
    "component_mod" BOOLEAN,
    CONSTRAINT "position_aud_pkey" PRIMARY KEY ("id", "rev")
) PARTITION BY RANGE (REV);

--changeset admin:1746436054293-44
CREATE TABLE IF NOT EXISTS  "project"
(
    "projectid"                      INTEGER NOT NULL,
    "averageobjectlengthforbelt"     FLOAT8,
    "averageobjectlengthforoogtray"  FLOAT8,
    "averageobjectlengthfortray"     FLOAT8,
    "averageweightbag"               FLOAT8,
    "averageweightoversizebag"       FLOAT8,
    "bagweight"                      FLOAT8,
    "beltdrivevendor"                VARCHAR(255),
    "brakerelatedzoptionsdol"        VARCHAR(255),
    "brakerelatedzoptionsvfd"        VARCHAR(255),
    "brakeresistorcycletime"         FLOAT8,
    "brakingresistor"                VARCHAR(255),
    "client"                         VARCHAR(255),
    "customername"                   VARCHAR(255),
    "fieldbus"                       VARCHAR(255),
    "gearboxservicefactor"           FLOAT8,
    "generalzoptions"                VARCHAR(255),
    "increasedvfdsize"               VARCHAR(255),
    "mainprojectstate"               VARCHAR(255),
    "maxinputrpmgearbox"             FLOAT8,
    "maxrelativehumidity"            FLOAT8,
    "mingaptimeforbelt"              FLOAT8,
    "mingaptimefortray"              FLOAT8,
    "motorefficiencyclass"           VARCHAR(255),
    "motorservicefactor"             FLOAT8,
    "motorstarterdriveservicefactor" FLOAT8,
    "mountingtype"                   VARCHAR(255),
    "name1"                          VARCHAR(255),
    "operatingtemperaturehigh"       FLOAT8,
    "operatingtemperaturelow"        FLOAT8,
    "oversizebagweight"              FLOAT8,
    "oversizetrayweight"             FLOAT8,
    "packagetype"                    VARCHAR(255),
    "projectleader"                  VARCHAR(255),
    "projectsgroupid"                BIGINT,
    "relativedutyfactor"             FLOAT8,
    "repairswitch"                   VARCHAR(255),
    "sapprojectkey"                  VARCHAR(255),
    "sitename"                       VARCHAR(255),
    "solutionid"                     BIGINT,
    "status"                         VARCHAR(255),
    "supplyfrequency"                FLOAT8,
    "supplyvoltage"                  FLOAT8,
    "traydrivevendor"                VARCHAR(255),
    "traystacksize"                  FLOAT8,
    "trayweight"                     FLOAT8,
    "type"                           VARCHAR(255),
    "vfdaccelerationtorque"          FLOAT8,
    "vfddriveservicefactor"          FLOAT8,
    "vfdoutputvoltage"               FLOAT8,
    "vfdtype"                        VARCHAR(255),
    "weightofbelt"                   FLOAT8,
    "withsimulation"                 BOOLEAN,
    "statechangdate"                 VARCHAR(255)
);
COMMENT
    ON COLUMN "project"."name1" IS 'comment';

--changeset admin:1746436054293-45
CREATE TABLE IF NOT EXISTS  "project_view_group_mapping"
(
    "id"             BIGINT NOT NULL,
    "name"           VARCHAR,
    "sub_group_name" VARCHAR,
    "project_id"     BIGINT
);

--changeset admin:1746436054293-46
CREATE TABLE IF NOT EXISTS  "revinfo"
(
    "rev"         INTEGER NOT NULL,
    "revtstmp"    BIGINT,
    "principal"   VARCHAR(255),
    "version_id"  BIGINT,
    "version_tag" VARCHAR(255),
    CONSTRAINT "revinfo_pkey" PRIMARY KEY ("rev")
);

--changeset admin:1746436054293-48
CREATE TABLE IF NOT EXISTS  "spineuser"
(
    "username" VARCHAR(255),
    "type"     VARCHAR(255)
);

--changeset admin:1746436054293-49
CREATE TABLE IF NOT EXISTS  "state_aud"
(
    "id"              BIGINT  NOT NULL,
    "rev"             INTEGER NOT NULL,
    "revtype"         SMALLINT,
    "reason"          INTEGER,
    "reason_mod"      BOOLEAN,
    "remark"          VARCHAR(255),
    "remark_mod"      BOOLEAN,
    "state"           INTEGER,
    "state_mod"       BOOLEAN,
    "status_date"     TIMESTAMP WITHOUT TIME ZONE,
    "statusdate_mod"  BOOLEAN,
    "username"        VARCHAR(255),
    "username_mod"    BOOLEAN,
    "change_group_id" BIGINT,
    "changegroup_mod" BOOLEAN,
    "component_id"    BIGINT,
    "component_mod"   BOOLEAN,
    CONSTRAINT "state_aud_pkey" PRIMARY KEY ("id", "rev")
) PARTITION BY RANGE (REV);

--changeset admin:1746436054293-50
CREATE TABLE IF NOT EXISTS  "type"
(
    "typeid"                      VARCHAR(255),
    "beltmass"                    FLOAT8,
    "defaultdrivecount"           INTEGER,
    "defaultslope"                VARCHAR(255),
    "description"                 VARCHAR(255),
    "drivepulleydiameter"         FLOAT8,
    "driveshaftdiameter"          FLOAT8,
    "efficiencygearbox"           FLOAT8,
    "engineeringhwhpki"           FLOAT8,
    "engineeringhwoneoffcosts"    FLOAT8,
    "engineeringithkpi"           FLOAT8,
    "engineeringitoneoffcosts"    FLOAT8,
    "engineeringmehkpi"           FLOAT8,
    "engineeringmeoneoffcosts"    FLOAT8,
    "engineeringplchkpi"          FLOAT8,
    "engineeringplconeoffcosts"   FLOAT8,
    "engineeringscadahkpi"        FLOAT8,
    "engineeringscadaoneoffcosts" INTEGER,
    "frictionfactor"              FLOAT8,
    "hierarchytype"               VARCHAR(255),
    "installationhkpi"            FLOAT8,
    "maintype"                    VARCHAR(255),
    "mechmaterialcostkpi"         FLOAT8,
    "mps"                         VARCHAR(255),
    "originaltype"                VARCHAR(255),
    "outdated"                    BOOLEAN NOT NULL,
    "plccommissioninghkpi"        FLOAT8,
    "projecttypeclassification"   VARCHAR(255),
    "requiredacceleration"        FLOAT8,
    "sapmaterialnumber"           VARCHAR(255),
    "solutionid"                  BIGINT,
    "specialtype"                 VARCHAR(255),
    "subtype"                     VARCHAR(255),
    "supplier"                    VARCHAR(255),
    "typeaggregator"              VARCHAR(255),
    "typevariant"                 VARCHAR(255),
    "typeversion"                 VARCHAR(255),
    "url1"                        VARCHAR(255),
    "url2"                        VARCHAR(255),
    "url3"                        VARCHAR(255),
    "url4"                        VARCHAR(255),
    "url5"                        VARCHAR(255),
    "versionnumber"               VARCHAR(255),
    "project"                     INTEGER,
    "projectspecific"             BOOLEAN,
    "creationdate"                TIMESTAMP WITHOUT TIME ZONE,
    "modificationdate"            TIMESTAMP WITHOUT TIME ZONE
);

--changeset admin:1746436054293-51
CREATE TABLE IF NOT EXISTS  "usertogroup"
(
    "id"        BIGINT NOT NULL,
    "groupname" VARCHAR(255),
    "username"  VARCHAR(255),
    "type_test" VARCHAR(255)
);

--changeset admin:1746436054293-52
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_constraint WHERE conname = 'fk1an9cxyiprklfi8hfo7jwal12'
ALTER TABLE "neighbor_connection_aud"
    ADD CONSTRAINT "fk1an9cxyiprklfi8hfo7jwal12" FOREIGN KEY ("rev") REFERENCES "revinfo" ("rev") ON UPDATE NO ACTION ON DELETE NO ACTION;

--changeset admin:1746436054293-53
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_constraint WHERE conname = 'fk3ik2um6sosogg23b0v62ajn2d'
ALTER TABLE "state_aud"
    ADD CONSTRAINT "fk3ik2um6sosogg23b0v62ajn2d" FOREIGN KEY ("rev") REFERENCES "revinfo" ("rev") ON UPDATE NO ACTION ON DELETE NO ACTION;

--changeset admin:1746436054293-54
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_constraint WHERE conname = 'fk4qda8qwie5739h0a29dpi516l'
ALTER TABLE "component_aud"
    ADD CONSTRAINT "fk4qda8qwie5739h0a29dpi516l" FOREIGN KEY ("rev") REFERENCES "revinfo" ("rev") ON UPDATE NO ACTION ON DELETE NO ACTION;

--changeset admin:1746436054293-55
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_constraint WHERE conname = 'fk4twu8jbpxx56clp8plnq8kxgm'
ALTER TABLE "position_aud"
    ADD CONSTRAINT "fk4twu8jbpxx56clp8plnq8kxgm" FOREIGN KEY ("rev") REFERENCES "revinfo" ("rev") ON UPDATE NO ACTION ON DELETE NO ACTION;

--changeset admin:1746436054293-56
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_constraint WHERE conname = 'fk75h5iwy2vbmggxrc6gvpd7avl'
ALTER TABLE "componentstatus_aud"
    ADD CONSTRAINT "fk75h5iwy2vbmggxrc6gvpd7avl" FOREIGN KEY ("rev") REFERENCES "revinfo" ("rev") ON UPDATE NO ACTION ON DELETE NO ACTION;

--changeset admin:1746436054293-58
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_constraint WHERE conname = 'fkag4m0t8gt6kt04jchgga4ami8'
ALTER TABLE "drive_assignment_data_aud"
    ADD CONSTRAINT "fkag4m0t8gt6kt04jchgga4ami8" FOREIGN KEY ("rev") REFERENCES "revinfo" ("rev") ON UPDATE NO ACTION ON DELETE NO ACTION;

--changeset admin:1746436054293-60
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_constraint WHERE conname = 'fkkdpaafan83f671ii9x970v3mo'
ALTER TABLE "group_aud"
    ADD CONSTRAINT "fkkdpaafan83f671ii9x970v3mo" FOREIGN KEY ("rev") REFERENCES "revinfo" ("rev") ON UPDATE NO ACTION ON DELETE NO ACTION;

--changeset admin:1746436054293-61
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_constraint WHERE conname = 'fkksmj7hxkrdweph3xjy4nhqi7e'
ALTER TABLE "decomposition_aud"
    ADD CONSTRAINT "fkksmj7hxkrdweph3xjy4nhqi7e" FOREIGN KEY ("rev") REFERENCES "revinfo" ("rev") ON UPDATE NO ACTION ON DELETE NO ACTION;

--changeset admin:1746436054293-62
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_constraint WHERE conname = 'fknd88pm6191hq968ppuxnrhkpy'
ALTER TABLE "groupcomponent_aud"
    ADD CONSTRAINT "fknd88pm6191hq968ppuxnrhkpy" FOREIGN KEY ("rev") REFERENCES "revinfo" ("rev") ON UPDATE NO ACTION ON DELETE NO ACTION;

--changeset admin:1746436054293-64
--preconditions onFail:MARK_RAN onError:MARK_RAN
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM pg_constraint WHERE conname = 'fkssfnocg98foyo4k96ffn63ueo'
ALTER TABLE "connectionpoint_aud"
    ADD CONSTRAINT "fkssfnocg98foyo4k96ffn63ueo" FOREIGN KEY ("rev") REFERENCES "revinfo" ("rev") ON UPDATE NO ACTION ON DELETE NO ACTION;
