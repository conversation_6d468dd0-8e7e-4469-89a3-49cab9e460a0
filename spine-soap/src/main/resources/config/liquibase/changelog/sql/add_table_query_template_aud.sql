--liquibase formatted sql

--changeset admin:1753087134931
CREATE TABLE IF NOT EXISTS  "query_template_aud"
(
    "id"                       BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    "rev"                      INTEGER NOT NULL,
    "revtype"                  SMALLINT,
    "template_name"            VARCHAR(255) NOT NULL,
    "templatename_mod"        BOOLEAN,
    "sql_template"             TEXT,
    "sqltemplate_mod"         BOOLEAN,
    "description"              TEXT,
    "description_mod"          BOOLEAN,
    "requires_pagination"      BOOLEAN                                NOT NULL,
    "requirespagination_mod"  BOOLEAN                                NOT NULL,
    "sys_create_date"          TIMESTAMP WITHOUT TIME ZONE            DEFAULT CURRENT_TIMESTAMP,
    "syscreatedate_mod"      BOOLEAN,
    "sys_mod_date"             TIMESTAMP WITHOUT TIME ZONE             DEFAULT CURRENT_TIMESTAMP,
    "sysmoddate_mod"         BOOLEAN,
    "created_by"               VARCHAR(255),
    "createdby_mod"           BOOLEAN,
    "modified_by"              VARCHAR(255),
    "modifiedby_mod"          BOOLEAN,
    CONS<PERSON><PERSON>INT "query_template_aud_pkey" PRIMARY KEY ("id", "rev")
) PARTITION BY RANGE (REV);

CREATE TABLE query_template_aud_rev_1_100000 PARTITION OF query_template_aud FOR VALUES FROM (0) TO (100000);
