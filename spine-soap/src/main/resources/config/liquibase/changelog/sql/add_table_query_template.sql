--liquibase formatted sql

--changeset truonglx:1746436054295-1
CREATE TABLE IF NOT EXISTS "query_template"
(
    "id"                 BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    "template_name"      VARCHAR(255)                            NOT NULL UNIQUE,
    "sql_template"       TEXT                                    NOT NULL,
    "description"        TEXT,
    "requires_pagination" BOOLEAN                                NOT NULL DEFAULT FALSE,
    "sys_create_date"    TIMESTAMP WITHOUT TIME ZONE             DEFAULT CURRENT_TIMESTAMP,
    "sys_mod_date"       TIMESTAMP WITHOUT TIME ZONE             DEFAULT CURRENT_TIMESTAMP,
    "created_by"         <PERSON><PERSON><PERSON><PERSON>(255),
    "modified_by"        <PERSON><PERSON><PERSON><PERSON>(255),
    CONSTRAINT "query_template_pkey" PRIMARY KEY ("id")
    );
