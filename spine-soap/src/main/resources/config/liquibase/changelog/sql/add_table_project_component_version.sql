--liquibase formatted sql

--changeset admin:1746436054295-1
CREATE TABLE IF NOT EXISTS "project_component_version"
(
    "id"             BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    "project_id"     BIGINT                                  NOT NULL,
    "rev"            BIGINT                                  NOT NULL,
    "syscreatedate"  TIM<PERSON><PERSON>MP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "sysmoddate"     TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "createdby"      VARCHAR(255),
    "message"        VARCHAR(1000),
    "version_name"   VARCHAR(255)                            NOT NULL,
    "version_id"     VARCHAR(255)                            NOT NULL,
    "component_refs" jsonb,
    CONSTRAINT "project_component_version_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "unique_rev_project" UNIQUE ("rev", "project_id")
);

--changeset admin:1746436054295-2
CREATE INDEX IF NOT EXISTS "idx_project_rev" ON "project_component_version" ("project_id", "rev");