<!--needed to generate ddl from entities-->
<persistence xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns="http://xmlns.jcp.org/xml/ns/persistence"
             xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/persistence http://xmlns.jcp.org/xml/ns/persistence/persistence_2_1.xsd"
             version="2.1">

    <persistence-unit name="spine-core" transaction-type="JTA">
        <provider>org.hibernate.jpa.HibernatePersistenceProvider</provider>
        <jta-data-source>spineCoreDS</jta-data-source>
        <class>com.siemens.spine.db.entity.GroupEntity</class>
        <class>com.siemens.spine.db.entity.ConnectionPointEntity</class>
        <class>com.siemens.spine.db.entity.NeighborConnectionEntity</class>
        <class>com.siemens.spine.db.entity.ComponentEntity</class>
        <class>com.siemens.spine.db.entity.ChangeGroupEntity</class>
        <class>com.siemens.spine.db.entity.ComponentStatusEntity</class>
        <class>com.siemens.spine.db.entity.ProjectEntity</class>
        <class>com.siemens.spine.db.entity.DecompositionEntity</class>
        <class>com.siemens.spine.db.entity.CounterEntity</class>
        <class>com.siemens.spine.db.entity.PositionEntity</class>
        <class>com.siemens.spine.db.entity.TypeEntity</class>
        <class>com.siemens.spine.db.entity.StateEntity</class>
        <class>com.siemens.spine.db.entity.GroupToProjectAndRoleEntity</class>
        <class>com.siemens.spine.db.entity.DecompositionAttributesEntity</class>
        <class>com.siemens.spine.db.entity.StateEntity</class>
        <class>com.siemens.spine.db.entity.OperationToRoleEntity</class>
        <class>com.siemens.spine.db.entity.SpineUserEntity</class>
        <class>com.siemens.spine.db.entity.ChangeGroupEntity</class>
        <class>com.siemens.spine.db.entity.UserToGroupEntity</class>
        <class>com.siemens.spine.db.entity.DriveAssignmentDataEntity</class>
        <class>com.siemens.spine.db.entity.ProjectViewGroupMappingEntity</class>
        <class>com.siemens.spine.db.entity.RevisionInfoEntity</class>
        <class>com.siemens.spine.db.entity.ProjectComponentVersionEntity</class>

        <!-- Elcon Entities -->
        <class>com.siemens.spine.db.entity.elcon.QueryTemplateEntity</class>
        <class>com.siemens.spine.db.entity.elcon.ElconAddressMapping</class>
        <class>com.siemens.spine.db.entity.elcon.ElconArea</class>
        <class>com.siemens.spine.db.entity.elcon.ElconAsiMember</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCarrier</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCfgActiveSlave</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCommunicationUnit</class>
        <class>com.siemens.spine.db.entity.elcon.ElconComponent</class>
        <class>com.siemens.spine.db.entity.elcon.ElconConveyor</class>
        <class>com.siemens.spine.db.entity.elcon.ElconDevice</class>
        <class>com.siemens.spine.db.entity.elcon.ElconDeviceInterface</class>
        <class>com.siemens.spine.db.entity.elcon.ElconDeviceInterfaceConnection</class>
        <class>com.siemens.spine.db.entity.elcon.ElconDeviceInterfacePort</class>
        <class>com.siemens.spine.db.entity.elcon.ElconDevicePlugin</class>
        <class>com.siemens.spine.db.entity.elcon.ElconDischarge</class>
        <class>com.siemens.spine.db.entity.elcon.ElconDrive</class>
        <class>com.siemens.spine.db.entity.elcon.ElconExtCoNeighbour</class>
        <class>com.siemens.spine.db.entity.elcon.ElconExtConveyor</class>
        <class>com.siemens.spine.db.entity.elcon.ElconFieldbusMember</class>
        <class>com.siemens.spine.db.entity.elcon.ElconFieldbusSubnet</class>
        <class>com.siemens.spine.db.entity.elcon.ElconFielddevice</class>
        <class>com.siemens.spine.db.entity.elcon.ElconGlobalArea</class>
        <class>com.siemens.spine.db.entity.elcon.ElconGlobalCfgDefaultObject</class>
        <class>com.siemens.spine.db.entity.elcon.ElconGlobalCfgDefaultObjectId</class>
        <class>com.siemens.spine.db.entity.elcon.ElconGlobalCfgDeviceType</class>
        <class>com.siemens.spine.db.entity.elcon.ElconGlobalCfgGentoolQuery</class>
        <class>com.siemens.spine.db.entity.elcon.ElconGlobalCfgGentoolQueryParameter</class>
        <class>com.siemens.spine.db.entity.elcon.ElconGlobalCfgPassiveConveyor</class>
        <class>com.siemens.spine.db.entity.elcon.ElconGlobalCfgSystypeObjectKindMapping</class>
        <class>com.siemens.spine.db.entity.elcon.ElconGlobalConfigSimuMapping</class>
        <class>com.siemens.spine.db.entity.elcon.ElconGroupHeader</class>
        <class>com.siemens.spine.db.entity.elcon.ElconGroupMember</class>
        <class>com.siemens.spine.db.entity.elcon.ElconHStation</class>
        <class>com.siemens.spine.db.entity.elcon.ElconHlcCommUnit</class>
        <class>com.siemens.spine.db.entity.elcon.ElconImportEvent</class>
        <class>com.siemens.spine.db.entity.elcon.ElconImportMemoryAreaName</class>
        <class>com.siemens.spine.db.entity.elcon.ElconInduction</class>
        <class>com.siemens.spine.db.entity.elcon.ElconIoLinkInterface</class>
        <class>com.siemens.spine.db.entity.elcon.ElconIoLinkMember</class>
        <class>com.siemens.spine.db.entity.elcon.ElconIoMasterPort</class>
        <class>com.siemens.spine.db.entity.elcon.ElconIoSystem</class>
        <class>com.siemens.spine.db.entity.elcon.ElconIoSystemConnection</class>
        <class>com.siemens.spine.db.entity.elcon.ElconLinkEstopBoxSafetyButton</class>
        <class>com.siemens.spine.db.entity.elcon.ElconLinkObjectArea</class>
        <class>com.siemens.spine.db.entity.elcon.ElconLinkSafetyLineDevice</class>
        <class>com.siemens.spine.db.entity.elcon.ElconLinkSafetyZoneButton</class>
        <class>com.siemens.spine.db.entity.elcon.ElconLinkSafetyZoneSafetyLine</class>
        <class>com.siemens.spine.db.entity.elcon.ElconLinkSafetyZoneSupplyVoltageLine</class>
        <class>com.siemens.spine.db.entity.elcon.ElconLoadcheck</class>
        <class>com.siemens.spine.db.entity.elcon.ElconMechanicalDatum</class>
        <class>com.siemens.spine.db.entity.elcon.ElconMrpDomain</class>
        <class>com.siemens.spine.db.entity.elcon.ElconMrpInstance</class>
        <class>com.siemens.spine.db.entity.elcon.ElconMvConveyorNeighbourActive</class>
        <class>com.siemens.spine.db.entity.elcon.ElconNetworkInterface</class>
        <class>com.siemens.spine.db.entity.elcon.ElconObject</class>
        <class>com.siemens.spine.db.entity.elcon.ElconObjectExtension</class>
        <class>com.siemens.spine.db.entity.elcon.ElconOp</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPanel</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPanelNeighbour</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPlcArea</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPlcCommunication</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPlcCommunicationObjectSetup</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPlcCommunicationUnitPairing</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPlcSetup</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPnInterface</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPnMember</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPowerLine</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPowerLineConnection</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPowerLineInterface</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPowerLineMember</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPowerLinePort</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPrjInitCommPartnerExternal</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPrjInitDefaultHlcCommunicationObject</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPrjInitExtensionAutoGeneration</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPrjInitLogisticsProperty</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPrjInitObjectKind</class>
        <class>com.siemens.spine.db.entity.elcon.ElconPrjInitOutfitSensor</class>
        <class>com.siemens.spine.db.entity.elcon.ElconProject</class>
        <class>com.siemens.spine.db.entity.elcon.ElconProjectVersion</class>
        <class>com.siemens.spine.db.entity.elcon.ElconSafety</class>
        <class>com.siemens.spine.db.entity.elcon.ElconSafetyLine</class>
        <class>com.siemens.spine.db.entity.elcon.ElconSafetyPushbutton</class>
        <class>com.siemens.spine.db.entity.elcon.ElconSafetyZone</class>
        <class>com.siemens.spine.db.entity.elcon.ElconScanner</class>
        <class>com.siemens.spine.db.entity.elcon.ElconSimuAvailCheckProperty</class>
        <class>com.siemens.spine.db.entity.elcon.ElconSimuBufferBlockProperty</class>
        <class>com.siemens.spine.db.entity.elcon.ElconSimuBufferLockDiffProperty</class>
        <class>com.siemens.spine.db.entity.elcon.ElconSimuCounterLockProperty</class>
        <class>com.siemens.spine.db.entity.elcon.ElconSimuDivertRatioProperty</class>
        <class>com.siemens.spine.db.entity.elcon.ElconSimuDivertTuRoutingProperty</class>
        <class>com.siemens.spine.db.entity.elcon.ElconSimuMergeProperty</class>
        <class>com.siemens.spine.db.entity.elcon.ElconSimuTuDeleteProperty</class>
        <class>com.siemens.spine.db.entity.elcon.ElconSorter</class>
        <class>com.siemens.spine.db.entity.elcon.ElconTimeSyncSetup</class>
        <class>com.siemens.spine.db.entity.elcon.ElconTrackingarea</class>


        <!-- Additional Elcon Entities -->
        <class>com.siemens.spine.db.entity.elcon.ElconCfgCommPartnerExternal</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCfgCommunicationUnitParameter</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCfgDefaultHlcCommunicationObject</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCfgEstopParameter</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCfgExtensionAutoGeneration</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCfgExtensionGenerationParameter</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCfgExtra</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCfgLogisticsProperty</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCfgObjectKind</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCfgOutfitSensor</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCfgSwBlock</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCfgSwLibrary</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCfgSwSet</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCfgXmlImportSetting</class>
        <class>com.siemens.spine.db.entity.elcon.ElconChute</class>
        <class>com.siemens.spine.db.entity.elcon.ElconClDeviceComponent</class>
        <class>com.siemens.spine.db.entity.elcon.ElconClDeviceComponentPosInfo</class>
        <class>com.siemens.spine.db.entity.elcon.ElconClDeviceIo</class>
        <class>com.siemens.spine.db.entity.elcon.ElconClFieldbusDevice</class>
        <class>com.siemens.spine.db.entity.elcon.ElconClHwOutfit</class>
        <class>com.siemens.spine.db.entity.elcon.ElconClParentComponent</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCoDrive</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCommonCfgSwSet</class>
        <class>com.siemens.spine.db.entity.elcon.ElconCoNeighbour</class>
        <class>com.siemens.spine.db.entity.elcon.ElconNetworkComChannel</class>

        <validation-mode>NONE</validation-mode>
        <!-- Hibernate properties are loaded from hibernate.properties in spine-db module -->
        <properties>
            <!-- Environment-specific properties can be overridden here -->
            <property name="hibernate.dialect" value="org.hibernate.dialect.PostgreSQL10Dialect"/>

            <property name="hibernate.jdbc.batch_size" value="50"/>
            <property name="hibernate.order_inserts" value="true"/>
            <property name="hibernate.order_updates" value="true"/>
            <property name="hibernate.jdbc.batch_versioned_data" value="true"/>
            <property name="hibernate.batch_fetch_style" value="PADDED"/>

            <!-- Statistics and logging configuration -->
            <property name="hibernate.generate_statistics" value="true"/>
            <property name="hibernate.show_sql" value="true"/> <!-- Show SQL in console -->
            <property name="hibernate.format_sql" value="true"/> <!-- Show SQL formatted -->

            <!-- Envers Configuration -->
            <property name="org.hibernate.envers.audit_table_suffix" value="_AUD"/>
            <property name="org.hibernate.envers.revision_field_name" value="REV"/>
            <property name="org.hibernate.envers.revision_type_field_name" value="REVTYPE"/>
            <property name="org.hibernate.envers.store_data_at_delete" value="true"/>
            <property name="org.hibernate.envers.global_with_modified_flag" value="true"/>
        </properties>

    </persistence-unit>

</persistence>
