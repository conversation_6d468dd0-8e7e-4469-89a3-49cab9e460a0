ARG DOCKER_REPOSITORY
FROM ${DOCKER_REPOSITORY}/3rdparty/openjdk:17

# Dedicated service port
EXPOSE 8080
VOLUME /tmp

# Java Run Options
ENV JAVA_OPTIONS="${JAVA_OPTIONS} -XX:+UseContainerSupport -XX:MaxRAMPercentage=75 -XX:InitialRAMPercentage=50 -XX:+UseG1GC -Xmx\${JAVA_HEAP_MAX:=2g} -Xss\${JAVA_STACK_SIZE:=256k}"
ENV RUN_JAR="/spine-soap.jar"

ADD target/spine-soap*.jar ${RUN_JAR}
ADD target/libs/*.jar /libs/
RUN chmod a+r /spine-soap*.jar
RUN chmod a+r /libs/*.jar

RUN mkdir ObjectStore
RUN chmod a+rw ObjectStore
RUN mkdir -p target
RUN chmod a+rw target
