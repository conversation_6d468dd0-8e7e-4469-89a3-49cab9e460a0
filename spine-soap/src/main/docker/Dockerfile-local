# 1st stage, build the app
FROM maven:3.8-eclipse-temurin-17 AS build

WORKDIR /spine

# Copy project files while excluding target directories
COPY pom.xml .
COPY spine-db/pom.xml spine-db/
COPY spine-db/src/ spine-db/src/
COPY spine-logic/pom.xml spine-logic/
COPY spine-logic/src/ spine-logic/src/
COPY spine-soap/pom.xml spine-soap/
COPY spine-soap/src/ spine-soap/src/

RUN mvn package -Dmaven.test.skip -Declipselink.weave.skip

RUN echo "done!"

# 2nd stage, build the runtime image
FROM eclipse-temurin:17-jre-jammy
WORKDIR /spine

# Copy the binary built in the 1st stage
COPY --from=build /spine/spine-soap/target/spine-soap.jar ./
COPY --from=build /spine/spine-soap/target/libs ./libs

EXPOSE 8080

CMD ["java", "-XX:+UseContainerSupport", "-XX:MaxRAMPercentage=75", \
 "-XX:InitialRAMPercentage=50", "-XX:+UseG1GC", \
  "-XX:+HeapDumpOnOutOfMemoryError", "-jar", "spine-soap.jar"]
