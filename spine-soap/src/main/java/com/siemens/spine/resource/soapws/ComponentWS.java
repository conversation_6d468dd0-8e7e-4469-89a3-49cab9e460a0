package com.siemens.spine.resource.soapws;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.siemens.spine.db.constant.Constants;
import com.siemens.spine.db.constant.GroupTypeEnum;
import com.siemens.spine.db.repository.filter.ComponentStateFilter;
import com.siemens.spine.generated.toolchain.Component;
import com.siemens.spine.generated.toolchain.ComponentDriveAssignmentData;
import com.siemens.spine.generated.toolchain.ComponentSpecificationMember;
import com.siemens.spine.generated.toolchain.ComponentStateHistory;
import com.siemens.spine.generated.toolchain.DecompositionAttributes;
import com.siemens.spine.generated.toolchain.Group;
import com.siemens.spine.generated.toolchain.ListStatus;
import com.siemens.spine.generated.toolchain.MarkAsDoneData;
import com.siemens.spine.generated.toolchain.State;
import com.siemens.spine.logic.dto.ComponentConditionDTO;
import com.siemens.spine.logic.dto.ComponentDTO;
import com.siemens.spine.logic.dto.ComponentStatusDto;
import com.siemens.spine.logic.dto.StateDto;
import com.siemens.spine.logic.exception.ForbiddenException;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.mapper.ComponentMapper;
import com.siemens.spine.logic.mapper.ComponentStatusMapper;
import com.siemens.spine.logic.security.SecurityCheck;
import com.siemens.spine.logic.security.SecurityConfig;
import com.siemens.spine.logic.service.ComponentService;
import com.siemens.spine.logic.service.ComponentStateService;
import com.siemens.spine.logic.service.ComponentStatusService;
import com.siemens.spine.logic.service.CounterService;
import com.siemens.spine.logic.service.DriveAssignmentService;
import com.siemens.spine.logic.service.impl.CounterServiceImpl;
import com.siemens.spine.logic.util.SecurityUtils;
import com.siemens.spine.resource.interceptor.annotation.SoapSecure;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.BadRequestException;
import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@SoapSecure
@ApplicationScoped
@Slf4j
public class ComponentWS extends AuthzWS {

    private final ComponentService componentService;

    private final ComponentStatusService componentStatusService;

    private final ComponentStateService componentStateService;

    private final DriveAssignmentService driveAssignmentService;

    private final CounterService counterService;

    private final SecurityConfig securityConfig;

    private final ComponentMapper componentMapper;

    @Inject
    public ComponentWS(SecurityCheck securityCheck,
                       ComponentService componentService,
                       ComponentStatusService componentStatusService,
                       ComponentStateService componentStateService,
                       DriveAssignmentService driveAssignmentService,
                       CounterService counterService, SecurityConfig securityConfig, ComponentMapper componentMapper) {
        super(securityCheck);
        this.componentService = componentService;
        this.componentStatusService = componentStatusService;
        this.componentStateService = componentStateService;
        this.driveAssignmentService = driveAssignmentService;
        this.counterService = counterService;
        this.securityConfig = securityConfig;
        this.componentMapper = componentMapper;
    }

    @SoapSecure
    public List<Long> generateUniqueIds(String token, int amount) {
        // TODO permission COMPONENT_MODIFY
        try {
            return counterService.retrieveNextValuesFromCounter(amount, CounterServiceImpl.COUNTER_COMPONENT_NAME);
        } catch (SpineException e) {
            log.error("", e);
            return Collections.emptyList();
        }
    }

    @SoapSecure
    public List<String> getComponentsBySpecification(String token,
                                                     String projectName,
                                                     List<ComponentSpecificationMember> specificationMembers,
                                                     int start,
                                                     int end) throws SpineException, ForbiddenException {
        securityCheck.checkPermission(projectName, Constants.Permission.COMPONENT_VIEW);
        return componentService.getComponentsBySpecification(projectName, specificationMembers, start, end);
    }

    @SoapSecure
    public List<String> getComponentsByUniqueIds(String token, String projectName, List<Long> ids)
            throws ForbiddenException {
        if (securityConfig.isEnable()) {
            securityCheck.checkPermission(projectName, Constants.Permission.COMPONENT_VIEW);
        }
        return componentService.getCSVComponentsByUniqueIds(projectName, ids);
    }

    @SoapSecure
    public List<Long> getComponentsUniqueIds(String token, String projectName, Group axgroup)
            throws ForbiddenException {
        try {
            securityCheck.checkPermission(projectName, Constants.Permission.COMPONENT_VIEW);
            return componentService.getUniqueIdsByProjectAndGroup(projectName, axgroup);
        } catch (SpineException e) {
            throw new BadRequestException(e);
        }
    }

    @SoapSecure
    public List<XMLGregorianCalendar> getComponentLastModDate(String token, String projectName, List<Long> axids)
            throws ForbiddenException {
        securityCheck.checkPermission(projectName, Constants.Permission.COMPONENT_VIEW);
        ComponentConditionDTO conditionDTO = ComponentConditionDTO.builder()
                .specificProjectName(projectName)
                .componentIds(axids)
                .build();
        List<ComponentDTO> componentDtos = componentService.getComponentsByCriteria(conditionDTO);
        List<XMLGregorianCalendar> ret = new ArrayList<>();
        for (ComponentDTO componentDto : componentDtos) {
            if (componentDto.getSysModDate() == null) {
                continue;
            }

            GregorianCalendar gregorianCalendar = new GregorianCalendar();
            gregorianCalendar.setTimeInMillis(componentDto.getSysModDate().getTime());

            try {
                XMLGregorianCalendar date = DatatypeFactory.newInstance().newXMLGregorianCalendar(gregorianCalendar);
                ret.add(date);
            } catch (DatatypeConfigurationException e) {
                log.error("Could not create data type factory", e);
            }
        }

        return ret;
    }

    @SoapSecure
    public List<Long> getUniqueIdsFromList(String token, String projectName, String listTypes,
                                           List<Group> group, boolean getFullData) throws ForbiddenException {
        securityCheck.checkPermission(projectName, SecurityUtils.getRequiredOperation(listTypes));
        return componentService.getUniqueIdsFromList(projectName, listTypes, group, getFullData);
    }

    @SoapSecure
    public boolean markAsDone(String token, String projectName, List<MarkAsDoneData> data) throws ForbiddenException {
        try {
            securityCheck.checkPermission(projectName, Constants.Permission.TODOLIST_SAPBOM);
            componentStatusService.markAsDone(projectName, data);
            return true;
        } catch (SpineException e) {
            log.error("", e);
            return false;
        }
    }

    @SoapSecure
    public List<String> createComponents(String token, String projectName, String drawing, String arg3,
                                         List<Component> components) throws ForbiddenException {
        if (securityConfig.isEnable()) {
            securityCheck.checkPermission(projectName, Constants.Permission.COMPONENT_MODIFY);
        }
        if (components == null || components.isEmpty()) {
            log.error("input component could not be null");
            return Collections.emptyList();
        }
        log.info("Trying to create/update {} components of project {} from SOAP", components.size(), projectName);

        // convert data to DTO
        List<ComponentDTO> dtos = new ArrayList<>();
        for (Component input : components) {
            // override drawing for HierarchyElement
            if (StringUtils.isNotEmpty(drawing) && !drawing.equalsIgnoreCase(input.getDrawing())) {
                input.setDrawing(drawing);
            }

            ComponentDTO dto = componentMapper.toComponentDto(input);
            dtos.add(dto);
        }

        List<String> ret;

        try {
            List<Long> savedComponentIds = componentService.importComponents(projectName, dtos);
            ret = savedComponentIds.stream().map(String::valueOf).toList();
        } catch (SpineException e) {
            throw new BadRequestException(e);
        }

        log.info("Total {} component(s) of project {} have been saved to the system", ret.size(), projectName);
        return ret;
    }

    @SoapSecure
    public List<DecompositionAttributes> getDecompositionAttributes(String token,
                                                                    List<String> sapNumbers,
                                                                    DecompositionAttributes attributes)
            throws ForbiddenException {
        // TODO permission DECOMPOSITION_ATTRIBUTES_VIEW
        return componentService.getDecompositionAttributes(sapNumbers);
    }

    @SoapSecure
    public boolean createDecompositionAttributes(String token, List<DecompositionAttributes> attributes)
            throws ForbiddenException {
        // TODO permission DECOMPOSITION_ATTRIBUTES_MODIFY
        componentService.createDecompositionAttributes(attributes);
        return true;
    }

    @SoapSecure
    public boolean markAsDeleted(String token, String projectName, List<String> uniqueIds) {
        try {
            securityCheck.checkPermission(projectName, Constants.Permission.COMPONENT_DELETE);
            List<Long> componentIds = uniqueIds.stream().map(Long::parseLong).toList();
            componentStateService.markAsDeleted(componentIds);
        } catch (SpineException | ForbiddenException e) {
            throw new BadRequestException(e);
        }

        return true;
    }

    @SoapSecure
    public boolean setComponentsStatusInList(String token, String project, String operationName, String state,
                                             List<String> componentUniqueIds) {
        try {
            securityCheck.checkPermission(project, SecurityUtils.getRequiredOperation(operationName));
            List<Long> componentIds = componentUniqueIds.stream().map(Long::parseLong).toList();
            componentStatusService.setComponentsStatusInList(project, operationName, state, componentIds);
            return true;
        } catch (SpineException | ForbiddenException e) {
            log.error("", e);
            return false;
        }
    }

    @SoapSecure
    public boolean setState(String token, String projectName, String uniqueId, State state) throws ForbiddenException {
        securityCheck.checkPermissions(projectName, Constants.Permission.COMPONENT_MOVE_BACKWARD,
                Constants.Permission.COMPONENT_MOVE_FORWARD);
        state.setUniqueID(uniqueId);
        return setStates(token, projectName, List.of(state));
    }

    @SoapSecure
    public boolean setStates(String token, String projectName, List<State> states) {
        try {
            //Todo: TruongLX comment for test
            //            securityCheck.checkPermissions(projectName, Constants.Permission.COMPONENT_MOVE_BACKWARD,
            //                    Constants.Permission.COMPONENT_MOVE_FORWARD);
            return componentStateService.setStates(projectName, states);
        } catch (SpineException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    /**
     * Get the current state of the components that belong to project and group (using drawingId)
     *
     * @param token
     * @param projectName
     * @param group
     * @return
     */
    @SoapSecure
    public List<State> getAllStates(String token, String projectName, Group group) throws ForbiddenException {
        securityCheck.checkPermission(projectName, Constants.Permission.COMPONENT_VIEW);
        Pair<GroupTypeEnum, List<String>> groupMapping = null;
        if (!StringUtils.isEmpty(group.getDrawing())) {
            groupMapping = Pair.of(GroupTypeEnum.Drawing, List.of(group.getDrawing()));
        }

        ComponentConditionDTO filter = ComponentConditionDTO.builder()
                .specificProjectName(projectName)
                .groupMapping(groupMapping)
                .build();

        return componentStateService.getLatestStates(filter);
    }

    /**
     * @param token
     * @param projectName
     * @param axids
     * @param state
     * @return
     */
    @SoapSecure
    public List<State> getCurrentStates(String token, String projectName, List<String> axids, State state)
            throws ForbiddenException {
        securityCheck.checkPermission(projectName, Constants.Permission.COMPONENT_VIEW);
        if (StringUtils.isEmpty(projectName)) {
            log.error("Project name could not be null or empty");
            return Collections.emptyList();
        }

        if (axids == null || axids.isEmpty()) {
            log.error("Empty component!");
            return Collections.emptyList();
        }

        List<Long> componentIds = axids.stream().map(Long::parseLong).toList();
        ComponentConditionDTO componentConditionDto = ComponentConditionDTO.builder()
                .specificProjectName(projectName)
                .componentIds(componentIds)
                .build();

        List<State> latestStates = componentStateService.getLatestStates(componentConditionDto);
        return latestStates.stream().map(item -> {
            // OBJECT XML doesn't allow to have statusDate here
            item.setStatusDate(null);
            return item;
        }).toList();
    }

    @SoapSecure
    public List<ListStatus> getChangeListStates(String token,
                                                String projectName,
                                                List<String> uniqueIds,
                                                ListStatus status) throws ForbiddenException {
        securityCheck.checkPermission(projectName, Constants.Permission.COMPONENT_VIEW);
        if (StringUtils.isEmpty(projectName)) {
            log.error("Project name could not be null or empty");
            return Collections.emptyList();
        }

        if (uniqueIds == null || uniqueIds.isEmpty()) {
            log.error("Empty component!");
            return Collections.emptyList();
        }

        List<Long> componentIds = uniqueIds.stream().map(Long::parseLong).toList();
        List<ComponentStatusDto> changeStatusDtos = componentStatusService.getChangeComponentStatuses(projectName,
                componentIds);
        Map<Long, ComponentStatusDto> changeStatusMap = changeStatusDtos.stream()
                .collect(Collectors.toMap(ComponentStatusDto::getUniqueID, Function.identity(), (cs1, cs2) -> cs1));
        List<ComponentStatusDto> results = new ArrayList<>();
        for (Long componentId : componentIds) {
            if (changeStatusMap.containsKey(componentId)) {
                results.add(changeStatusMap.get(componentId));
            } else {
                // TODO this case is for case retransmitted component to sap
                ComponentStatusDto deviveredComponentStatus = new ComponentStatusDto();
                deviveredComponentStatus.setBomState(10); //set default will be ignored in bom export for send to sap.
                results.add(deviveredComponentStatus);
            }
        }

        return ComponentStatusMapper.INSTANCE.toStatusWsList(results);
    }

    /**
     * @param token
     * @param projectName
     * @param simulationState
     * @param drawing
     * @return
     */
    @SoapSecure
    public boolean setSimulationStateForDrawing(String token,
                                                String projectName,
                                                String simulationState,
                                                String drawing) throws ForbiddenException {
        try {
            securityCheck.checkPermission(projectName, Constants.Permission.TODOLIST_SIMULATION);
            componentStatusService.setSimulationStateForDrawing(projectName, simulationState, drawing);
            return true;
        } catch (SpineException e) {
            throw new BadRequestException(e);
        }
    }

    /**
     * @param token
     * @param projectName
     * @param calculationState
     * @param drawing
     * @return
     */
    @SoapSecure
    public boolean setCalculationStateForDrawing(String token,
                                                 String projectName,
                                                 String calculationState,
                                                 String drawing) throws ForbiddenException {
        try {
            securityCheck.checkPermission(projectName, Constants.Permission.TODOLIST_CALCULATION);
            componentStatusService.setCalculationStateForDrawing(projectName, calculationState, drawing);
            return true;
        } catch (SpineException e) {
            throw new BadRequestException(e);
        }
    }

    @SoapSecure
    public List<ComponentDriveAssignmentData> getDriveAssignmentData(String token,
                                                                     String projectName,
                                                                     List<String> componentIds)
            throws ForbiddenException {
        securityCheck.checkPermission(projectName, Constants.Permission.DRIVE_EXPORT);
        if (StringUtils.isEmpty(projectName)) {
            throw new BadRequestException("Project name could not be null or empty");
        }

        if (componentIds == null || componentIds.isEmpty()) {
            throw new BadRequestException("Component id could not be null or empty");
        }

        List<Long> uniqueIds = componentIds.stream().map(Long::parseLong).toList();
        return driveAssignmentService.getDriveAssignment(uniqueIds, projectName);
    }

    /**
     * @param token
     * @param projectName
     * @param componentIds
     * @return
     */
    @SoapSecure
    public boolean setBOMState(String token, String projectName, String bomState, List<Long> componentIds)
            throws ForbiddenException {
        securityCheck.checkPermission(projectName, Constants.Permission.TODOLIST_SAPBOM);
        try {
            if (StringUtils.isEmpty(projectName)) {
                throw new BadRequestException("Project name could not be null or empty");
            }

            if (componentIds == null || componentIds.isEmpty()) {
                throw new BadRequestException("Component id could not be null or empty");
            }

            componentStatusService.setBOMState(projectName, bomState, componentIds);
            return true;
        } catch (SpineException e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    public List<ComponentStateHistory> getComponentsStateHistory(String token,
                                                                 String projectName,
                                                                 List<Long> componentIds) throws ForbiddenException {
        try {
            securityCheck.checkPermission(projectName, Constants.Permission.COMPONENT_VIEW);
            if (StringUtils.isEmpty(projectName)) {
                throw new BadRequestException("Project name could not be null or empty");
            }

            if (componentIds == null || componentIds.isEmpty()) {
                throw new BadRequestException("Component id could not be null or empty");
            }
            List<Long> projectComponentIds = componentService.getUniqueIdsByProject(projectName);
            if (!new HashSet<>(projectComponentIds).containsAll(componentIds)) {
                throw new BadRequestException("Contain component does not belong to project");
            }

            ComponentStateFilter filter = ComponentStateFilter.builder()
                    .componentIds(componentIds)
                    .build();
            List<StateDto> states = componentStateService.getStates(filter);
            Multimap<Long, StateDto> statesPerComponent = ArrayListMultimap.create();
            for (StateDto state : states) {
                statesPerComponent.put(state.getComponentId(), state);
            }
            List<ComponentStateHistory> componentStateHistories = new ArrayList<>();
            for (Long componentId : componentIds) {
                ComponentStateHistory componentStateHistory = new ComponentStateHistory();
                componentStateHistory.setUniqueID(componentId.toString());
                for (StateDto stateDto : statesPerComponent.get(componentId)) {
                    State state = new State();
                    state.setUniqueID(componentId.toString());
                    state.setState(Integer.parseInt(stateDto.getState().getValue()));
                    state.setReason(stateDto.getReason());
                    state.setRemark(stateDto.getRemark());
                    state.setUser(stateDto.getUsername());
                    state.setStatusDate(toXMLGregorianCalendar(stateDto.getStatusDate()));
                    if (stateDto.getChangeGroup() != null) {
                        state.setChangeGroupId(stateDto.getChangeGroup().getId().intValue());
                        state.setChangeGroupDescription(stateDto.getChangeGroup().getDescription());
                    }
                    componentStateHistory.getStateHistory().add(state);
                }
                if (CollectionUtils.isNotEmpty(componentStateHistory.getStateHistory())) {
                    componentStateHistories.add(componentStateHistory);
                }
            }
            return componentStateHistories;
        } catch (DatatypeConfigurationException e) {
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    @SoapSecure
    public boolean setDriveAssignmentData(String token,
                                          String projectName,
                                          List<ComponentDriveAssignmentData> componentDriveAssignmentDataList)
            throws ForbiddenException {
        securityCheck.checkPermission(projectName, Constants.Permission.DRIVE_EXPORT);
        if (StringUtils.isEmpty(projectName)) {
            throw new BadRequestException("Project name could not be null or empty");
        }

        return driveAssignmentService.setDriveAssignment(componentDriveAssignmentDataList);
    }

    private XMLGregorianCalendar toXMLGregorianCalendar(Timestamp timestamp) throws DatatypeConfigurationException {
        LocalDateTime localDateTime = timestamp.toLocalDateTime();

        XMLGregorianCalendar xmlGregorianCalendar = DatatypeFactory.newInstance().newXMLGregorianCalendar();
        xmlGregorianCalendar.setDay(localDateTime.getDayOfMonth());
        xmlGregorianCalendar.setMonth(localDateTime.getMonthValue());
        xmlGregorianCalendar.setYear(localDateTime.getYear());
        xmlGregorianCalendar.setHour(localDateTime.getHour());
        xmlGregorianCalendar.setMinute(localDateTime.getMinute());
        xmlGregorianCalendar.setSecond(localDateTime.getSecond());
        xmlGregorianCalendar.setFractionalSecond(
                new BigDecimal("0." + StringUtils.leftPad(String.valueOf(localDateTime.getNano()), 9, '0')));
        return xmlGregorianCalendar;
    }

}
