package com.siemens.spine.resource.rest;

import com.siemens.spine.logic.dto.request.ComponentProjectRequestDTO;
import com.siemens.spine.logic.dto.request.GetObjectsVersionRequestDTO;
import com.siemens.spine.logic.model.ProjectComponentVersion;
import com.siemens.spine.logic.service.ProjectComponentVersionService;
import com.siemens.spine.resource.interceptor.annotation.SecurityHolder;
import io.helidon.security.annotations.Authenticated;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@Path("/version-toolkit")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Authenticated
@SecurityHolder
@Slf4j
public class VersionControlResource {

    @Inject
    private ProjectComponentVersionService projectComponentVersionService;

    @POST
    //    @SpineRequiresPermission(value = Constants.Permission.COMPONENT_VIEW)
    @Path("/{projectId}/version")
    public Response createNewProjectVersion(@PathParam(value = "projectId") Long projectId,
                                            ComponentProjectRequestDTO dto) {

        if (!projectId.equals(dto.getProjectId())) {
            throw new IllegalArgumentException("Project ID in path and body do not match");
        }

        ProjectComponentVersion projectVersion = projectComponentVersionService.createVersion(dto);
        return Response.ok(projectVersion).build();
    }

    @GET
    @Path("/{projectId}/versions")
    //    @SpineRequiresPermission(value = Constants.Permission.COMPONENT_VIEW)
    public Response getAllProjectComponentVersion(@PathParam("projectId") Long projectId) {
        return Response.ok(projectComponentVersionService.getAllVersions(projectId)).build();
    }

    @POST
    @Path("/{projectId}/version/detail")
    //    @SpineRequiresPermission(value = Constants.Permission.COMPONENT_VIEW)
    public Response getDetailProjectComponentVersion(@PathParam("projectId") Long projectId,
                                                     GetObjectsVersionRequestDTO dto) {
        if (!projectId.equals(dto.getObjectId())) {
            throw new IllegalArgumentException("Project ID in path and body do not match");
        }
        return Response.ok(projectComponentVersionService.getObjectsInVersion(dto))
                .build();
    }

}
