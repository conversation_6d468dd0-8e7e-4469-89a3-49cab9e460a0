package com.siemens.spine.resource.soapws;

import com.siemens.spine.db.constant.Constants.Permission;
import com.siemens.spine.generated.toolchain.Group;
import com.siemens.spine.generated.toolchain.GroupItemData;
import com.siemens.spine.logic.exception.ForbiddenException;
import com.siemens.spine.logic.security.SecurityCheck;
import com.siemens.spine.logic.service.GroupService;
import com.siemens.spine.resource.interceptor.annotation.SoapSecure;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.Collections;
import java.util.List;

@ApplicationScoped
@Slf4j
public class GroupWS extends AuthzWS {

    private final GroupService groupService;

    @Inject
    public GroupWS(SecurityCheck securityCheck, GroupService groupService) {
        super(securityCheck);
        this.groupService = groupService;
    }

    @SoapSecure
    public List<GroupItemData> getGroupItems(String token,
                                             String projectName,
                                             Group group,
                                             GroupItemData groupItemData) throws ForbiddenException {
        securityCheck.checkPermission(projectName, Permission.GROUPS_VIEW);
        if (StringUtils.isEmpty(projectName)) {
            log.error("Please enter the project name");
            return Collections.emptyList();
        }

        return groupService.getGroupItems(projectName, group, groupItemData);
    }

    @SoapSecure
    public boolean deleteGroup(String token, String projectName, GroupItemData group, boolean force) {
        try {
            securityCheck.checkPermission(projectName, Permission.GROUPS_DELETE);
            return groupService.deleteGroup(projectName, group, force);
        } catch (Exception ex) {
            log.error(ex.getMessage());
            return false;
        }
    }

    public boolean renameGroup(String token, String projectName, GroupItemData group, String newName) {
        try {
            securityCheck.checkPermission(projectName, Permission.GROUPS_RENAME);
            return groupService.renameGroup(projectName, group, newName);
        } catch (Exception ex) {
            log.error(ex.getMessage());
            return false;
        }
    }

}
