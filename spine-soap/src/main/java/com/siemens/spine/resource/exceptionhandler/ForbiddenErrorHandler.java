package com.siemens.spine.resource.exceptionhandler;

import io.netty.handler.codec.http.HttpResponseStatus;

import javax.ws.rs.ForbiddenException;
import javax.ws.rs.ext.Provider;

/**
 * <AUTHOR>
 */
@Provider
public class ForbiddenErrorHandler extends AbstractExceptionHandler<ForbiddenException> {

    @Override
    protected int getHttpStatus() {
        return HttpResponseStatus.FORBIDDEN.code();
    }

}
