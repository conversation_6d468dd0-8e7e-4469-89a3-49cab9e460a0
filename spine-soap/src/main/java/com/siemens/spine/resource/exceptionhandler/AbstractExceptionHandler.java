package com.siemens.spine.resource.exceptionhandler;

import com.siemens.spine.logic.dto.BaseErrorDto;
import lombok.extern.slf4j.Slf4j;

import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;

/**
 * Abstract base class for all exception handlers
 *
 * @param <E> The type of exception this handler handles
 */
@Slf4j
public abstract class AbstractExceptionHandler<E extends Throwable> implements ExceptionMapper<E> {

    @Override
    public Response toResponse(E e) {
        log.error("Exception occurred: {}", e.getMessage(), e);

        BaseErrorDto error = BaseErrorDto.builder()
                .timestamp(System.currentTimeMillis())
                .errorMessage(e.getMessage())
                .build();

        return Response.status(getHttpStatus())
                .entity(error)
                .build();
    }

    /**
     * Determines the HTTP status code to use for the response
     *
     * @param e The exception that was thrown
     * @return The HTTP status code
     */
    protected abstract int getHttpStatus();

}
