package com.siemens.spine.resource.rest;

import com.siemens.spine.db.constant.Constants;
import com.siemens.spine.logic.dto.request.GroupToProjectAndRoleRequestDto;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.security.Authentication;
import com.siemens.spine.logic.security.SecurityContextHolder;
import com.siemens.spine.logic.service.GroupRoleService;
import com.siemens.spine.logic.service.IamService;
import com.siemens.spine.logic.service.impl.UserGroupCacheService;
import com.siemens.spine.resource.interceptor.annotation.SecurityHolder;
import com.siemens.spine.resource.interceptor.annotation.SpineRequiresPermission;
import io.helidon.security.annotations.Authenticated;

import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;

/**
 * <AUTHOR> Nguyen
 */
@Path("/iam")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Authenticated
@SecurityHolder
public class IamResource {

    @Inject
    private IamService iamService;

    @Inject
    private GroupRoleService groupRoleService;

    @Inject
    private UserGroupCacheService userGroupCacheService;

    @GET
    @Path("/user-info")
    public Response getUserInfo() {
        Authentication authentication = SecurityContextHolder.getAuthentication();
        return Response.ok(authentication).build();
    }

    @GET
    @Path("/role/project/{projectId}")
    public Response getProjectRoleOfUser(@PathParam(value = "projectId") Long projectId) {
        return Response.ok(iamService.getProjectRoleOfUser(projectId)).build();
    }

    @GET
    @SpineRequiresPermission(value = Constants.Permission.ADMIN_MANAGE_PERMISSIONS)
    public Response getListGroupAndRole(@QueryParam("project_id") Long projectId) throws SpineException {
        return Response.ok(groupRoleService.getGroupRoles(projectId)).build();
    }

    @GET
    @Path("/role")
    @SpineRequiresPermission(value = Constants.Permission.ADMIN_MANAGE_PERMISSIONS)
    public Response getListRole() {
        return Response.ok(groupRoleService.getRoles()).build();
    }

    @GET
    @Path("/user-group")
    @SpineRequiresPermission(value = Constants.Permission.ADMIN_MANAGE_PERMISSIONS)
    public Response getListGroup() {
        return Response.ok(userGroupCacheService.getUserGroups()).build();
    }

    @POST
    @Path("/create")
    @SpineRequiresPermission(value = Constants.Permission.ADMIN_MANAGE_PERMISSIONS)
    public Response createMapperGroupAndRole(GroupToProjectAndRoleRequestDto request) throws SpineException {
        return Response.ok(groupRoleService.createGroupRole(request)).build();
    }

    @PUT
    @Path("/update")
    @SpineRequiresPermission(value = Constants.Permission.ADMIN_MANAGE_PERMISSIONS)
    public Response updateMapperGroupAndRole(
            @QueryParam("group_role_id") Long groupRoleId,
            GroupToProjectAndRoleRequestDto requestDto) throws SpineException {
        return Response.ok(groupRoleService.updateGroupRole(groupRoleId, requestDto)).build();
    }

    @PUT
    @Path("/delete")
    @SpineRequiresPermission(value = Constants.Permission.ADMIN_MANAGE_PERMISSIONS)
    public Response deleteMapperGroupAndRole(List<Long> groupRoleIds) throws SpineException {
        groupRoleService.deleteGroupRole(groupRoleIds);
        return Response.ok().build();
    }

}
