package com.siemens.spine.resource.interceptor.annotation;

import javax.interceptor.InterceptorBinding;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * When the annotation attached in a method, the authentication checking will be run before method execution
 * The {@link #tokenParam} is used to get the access token
 *
 * <AUTHOR>
 */
@InterceptorBinding
@Target({ ElementType.TYPE, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
public @interface SoapSecure {

    String DEFAULT_TOKEN_PARAM = "token";

    /**
     * Name of the input parameter for the access token
     * Example: method(String token)
     *
     * @return
     */
    String tokenParam() default DEFAULT_TOKEN_PARAM;

}
