package com.siemens.spine.resource.interceptor;

import com.siemens.spine.logic.security.AuthService;
import com.siemens.spine.resource.interceptor.annotation.SpineRolesAllowed;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Priority;
import javax.inject.Inject;
import javax.interceptor.AroundInvoke;
import javax.interceptor.Interceptor;
import javax.interceptor.InvocationContext;
import javax.ws.rs.ForbiddenException;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 */
@Priority(InterceptorPriority.ROLE_ALLOWED_PRIORITY)
@Interceptor
@SpineRolesAllowed
@Slf4j
public class SpineRolesAllowedInterceptor {

    private static final String DELIMITER = ",";

    @Inject
    private AuthService authService;

    @AroundInvoke
    public Object checkPermission(InvocationContext invocationContext) throws Exception {
        Method method = invocationContext.getMethod();
        SpineRolesAllowed annotation = method.getAnnotation(SpineRolesAllowed.class);
        String rolesAllowed = annotation.value();
        String[] roles = rolesAllowed.split(DELIMITER);
        if (authService.checkRolesAllowed(roles)) {
            return invocationContext.proceed();
        } else {
            throw new ForbiddenException();
        }
    }

}
