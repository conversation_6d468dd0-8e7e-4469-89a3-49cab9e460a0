package com.siemens.spine.resource.exceptionhandler;

import com.siemens.spine.logic.dto.ValidationErrorInfo;
import io.netty.handler.codec.http.HttpResponseStatus;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.ws.rs.core.Response;
import javax.ws.rs.ext.Provider;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Provider
@Slf4j
public class ValidationErrorHandler extends AbstractExceptionHandler<ConstraintViolationException> {

    @Override
    public Response toResponse(ConstraintViolationException exception) {
        log.warn("exception occurred. message={}", exception.getMessage());

        Set<ConstraintViolation<?>> constraintViolations = exception.getConstraintViolations();
        List<ValidationErrorInfo.ValidationErrorItem> errorItems = constraintViolations.stream()
                .map(v -> new ValidationErrorInfo.ValidationErrorItem(v.getPropertyPath().toString(), v.getMessage()))
                .toList();

        ValidationErrorInfo validationErrorInfo = new ValidationErrorInfo(
                System.currentTimeMillis(),
                "validation error occurred.",
                errorItems);

        return Response
                .status(Response.Status.BAD_REQUEST)
                .entity(validationErrorInfo)
                .build();
    }

    @Override
    protected int getHttpStatus() {
        return HttpResponseStatus.BAD_REQUEST.code();
    }

}
