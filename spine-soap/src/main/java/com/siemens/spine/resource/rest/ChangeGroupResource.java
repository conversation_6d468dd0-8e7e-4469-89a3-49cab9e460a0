package com.siemens.spine.resource.rest;

import com.siemens.spine.db.constant.Constants;
import com.siemens.spine.db.repository.filter.ChangeGroupFilter;
import com.siemens.spine.logic.service.ChangeGroupService;
import com.siemens.spine.resource.interceptor.annotation.SecurityHolder;
import com.siemens.spine.resource.interceptor.annotation.SpineRequiresPermission;
import io.helidon.security.annotations.Authenticated;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@Path("/change-group")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Authenticated
@SecurityHolder
@Slf4j
public class ChangeGroupResource {

    @Inject
    private ChangeGroupService changeGroupService;

    @GET
    @SpineRequiresPermission(value = Constants.Permission.COMPONENT_VIEW)
    public Response getChangeGroups(@QueryParam("project_id") Long projectId,
                                    @QueryParam("text") String text) {
        ChangeGroupFilter filter = ChangeGroupFilter.builder()
                .projectId(projectId)
                .text(text)
                .build();

        return Response.ok(changeGroupService.getChangeGroups(filter)).build();
    }

}
