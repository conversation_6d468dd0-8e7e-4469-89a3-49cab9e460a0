package com.siemens.spine.resource.rest;

import com.siemens.spine.db.constant.Constants;
import com.siemens.spine.db.repository.filter.ComponentStateFilter;
import com.siemens.spine.logic.dto.ComponentStateChangedResponseDto;
import com.siemens.spine.logic.dto.request.StateChangedRequestDto;
import com.siemens.spine.logic.dto.request.UndeleteComponentRequestDto;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.service.ComponentService;
import com.siemens.spine.logic.service.ComponentStateService;
import com.siemens.spine.resource.interceptor.annotation.SecurityHolder;
import com.siemens.spine.resource.interceptor.annotation.SpineRequiresPermission;
import io.helidon.security.annotations.Authenticated;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;

/**
 * <AUTHOR> Pham
 * @version 1.0
 * @since 22/12/2022
 */
@ApplicationScoped
@Path("/components")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Authenticated
@SecurityHolder
@Slf4j
public class ComponentResource {

    private final ComponentService componentService;

    private final ComponentStateService componentStateService;

    @Inject
    public ComponentResource(ComponentService componentService, ComponentStateService componentStateService) {
        this.componentService = componentService;
        this.componentStateService = componentStateService;
    }

    @GET
    @SpineRequiresPermission(value = Constants.Permission.COMPONENT_VIEW)
    public Response getComponents(@QueryParam("project_id") Long projectId) {
        return Response.ok(componentService.getComponentsByProjectId(projectId)).build();
    }

    @GET
    @Path("/{componentId}/revisions/{revision}")
    @SpineRequiresPermission(value = Constants.Permission.COMPONENT_VIEW)
    public Response getComponentAtRevision(@QueryParam("project_id") Long projectId,
                                           @PathParam("revision") Integer revision,
                                           @PathParam(value = "componentId") Long componentId) {
        return Response.ok(componentService.getComponentByIdAtRevision(projectId, componentId, revision)).build();
    }

    @GET
    @Path("/{componentId}/decompositions")
    @SpineRequiresPermission(value = Constants.Permission.COMPONENT_VIEW_DECOMPOSITION)
    public Response getDecompositions(@PathParam(value = "componentId") Long componentId) {
        return Response.ok(componentService.getDecompositions(componentId)).build();
    }

    @GET
    @Path("/{componentId}/connection-points")
    @SpineRequiresPermission(value = Constants.Permission.COMPONENT_VIEW)
    public Response getConnectionPoint(@PathParam(value = "componentId") Long componentId) {
        return Response.ok(componentService.getConnectionPoints(componentId)).build();
    }

    @GET
    @Path("/{componentId}/state-history")
    @SpineRequiresPermission(value = Constants.Permission.COMPONENT_VIEW)
    public Response getComponentStateHistory(@PathParam(value = "componentId") Long componentId) {
        ComponentStateFilter filter = ComponentStateFilter.builder()
                .componentIds(List.of(componentId))
                .build();

        return Response.ok(componentStateService.getStates(filter)).build();
    }

    @PUT
    @Path("/move-forward")
    @SpineRequiresPermission(value = Constants.Permission.COMPONENT_MOVE_FORWARD)
    public Response moveForward(StateChangedRequestDto request) throws SpineException {
        ComponentStateChangedResponseDto ret = componentStateService.moveForward(request);
        return Response.ok(ret).build();
    }

    @PUT
    @Path("/move-backward")
    @SpineRequiresPermission(value = Constants.Permission.COMPONENT_MOVE_BACKWARD)
    public Response moveBackward(StateChangedRequestDto request) throws SpineException {
        return Response.ok(componentStateService.moveBackward(request)).build();
    }

    /**
     * @param request
     * @return
     */
    @POST
    @Path("/move-backward/allowed-state")
    @SpineRequiresPermission(value = Constants.Permission.COMPONENT_MOVE_BACKWARD)
    public Response getAllowedBackwardStatus(StateChangedRequestDto request) throws SpineException {
        return Response.ok(componentStateService.getAllowedBackwardState(request)).build();
    }

    @PUT
    @Path("/mark-deleted")
    @SpineRequiresPermission(value = Constants.Permission.COMPONENT_DELETE)
    public Response markAsDeleted(List<Long> componentIds) throws SpineException {
        return Response.ok(componentStateService.markAsDeleted(componentIds)).build();
    }

    @PUT
    @Path("/undelete")
    @SpineRequiresPermission(value = Constants.Permission.COMPONENT_DELETE)
    public Response undelete(UndeleteComponentRequestDto request) throws SpineException {
        return Response.ok(componentStateService.undelete(request)).build();
    }

    /**
     * @param componentIds
     * @return
     * @throws SpineException
     */
    @PUT
    @Path("/delete")
    @SpineRequiresPermission(value = Constants.Permission.COMPONENT_REMOVE)
    public Response delete(List<Long> componentIds) throws SpineException {
        componentService.delete(componentIds);
        return Response.ok().build();
    }

}
