package com.siemens.spine.resource.config;

import liquibase.Scope;
import liquibase.command.CommandScope;
import liquibase.command.core.UpdateCommandStep;
import liquibase.command.core.helpers.DbUrlConnectionArgumentsCommandStep;
import liquibase.database.Database;
import liquibase.database.DatabaseFactory;
import liquibase.database.jvm.JdbcConnection;
import liquibase.resource.ClassLoaderResourceAccessor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.context.Initialized;
import javax.enterprise.event.Observes;
import javax.inject.Inject;
import javax.inject.Named;
import javax.sql.DataSource;
import java.sql.Connection;
import java.util.Map;

@Slf4j
@ApplicationScoped
public class SpineLiquibaseConfiguration {

    private final DataSource dataSource;
    private final LiquibaseProperties liquibaseProperties;
    private final String schema;
    private Connection connection;
    private Database database;

    @Inject
    public SpineLiquibaseConfiguration(
            @Named("spineCoreDS") DataSource dataSource,
            LiquibaseProperties liquibaseProperties,
            @ConfigProperty(name = "javax.sql.DataSource.spineCoreDS.dataSource.currentSchema",
                    defaultValue = "public") String schema
    ) {
        this.dataSource = dataSource;
        this.liquibaseProperties = liquibaseProperties;
        this.schema = schema;
    }

    public void onStartup(@Observes @Initialized(ApplicationScoped.class) Object init) {
        if (!liquibaseProperties.isEnabled()) {
            log.info("Liquibase is disabled by configuration. Skipping migration.");
            return;
        }

        try {
            log.info("Starting Liquibase migration");
            this.connection = dataSource.getConnection();
            connection.setAutoCommit(false);

            this.database = DatabaseFactory.getInstance()
                    .findCorrectDatabaseImplementation(new JdbcConnection(connection));

            this.database.setDatabaseChangeLogTableName(liquibaseProperties.getDatabaseChangeLogTable());
            this.database.setDatabaseChangeLogLockTableName(liquibaseProperties.getDatabaseChangeLogLockTable());
            this.database.setDefaultSchemaName(schema);

            Scope.child(
                    Map.of(
                            Scope.Attr.database.name(), database,
                            Scope.Attr.resourceAccessor.name(), new ClassLoaderResourceAccessor()
                    ),
                    () -> {
                        log.info("Starting Liquibase migration...");

                        CommandScope updateCommand = new CommandScope(UpdateCommandStep.COMMAND_NAME);

                        //Set migration parameters
                        updateCommand.addArgumentValue(UpdateCommandStep.CONTEXTS_ARG,
                                liquibaseProperties.getContexts());
                        updateCommand.addArgumentValue(DbUrlConnectionArgumentsCommandStep.DATABASE_ARG, database);

                        //Hard configuration for the changelog file
                        updateCommand.addArgumentValue(UpdateCommandStep.CHANGELOG_FILE_ARG,
                                "config/liquibase/master.xml");

                        updateCommand.execute();

                        log.info("Liquibase migration completed successfully.");
                    }
            );

            connection.commit();
            log.info("Database transaction committed after Liquibase migration.");

        } catch (Exception e) {
            log.error("Liquibase migration failed: {}", e.getMessage(), e);
            rollbackSafely(connection);

            //Exit the application with an error code
            System.exit(1);
        } finally {
            closeDatabaseSafely(database);
            closeConnectionSafely(connection);
        }
    }

    private void rollbackSafely(Connection connection) {
        if (connection != null) {
            try {
                if (!connection.isClosed() && !connection.getAutoCommit()) {
                    connection.rollback();
                    log.warn("Rolled back pending database transaction.");
                }
            } catch (Exception e) {
                log.error("Failed to rollback database transaction: {}", e.getMessage(), e);
            }
        }
    }

    private void closeDatabaseSafely(Database database) {
        if (database != null) {
            try {
                database.close();
                log.info("Liquibase Database closed.");
            } catch (Exception e) {
                log.warn("Failed to close Liquibase Database: {}", e.getMessage(), e);
            }
        }
    }

    private void closeConnectionSafely(Connection connection) {
        if (connection != null) {
            try {
                if (!connection.isClosed()) {
                    connection.close();
                    log.info("JDBC Connection closed.");
                }
            } catch (Exception e) {
                log.warn("Failed to close JDBC Connection: {}", e.getMessage(), e);
            }
        }
    }

    @Setter
    @Getter
    @ApplicationScoped
    public static class LiquibaseProperties {

        @Inject
        @ConfigProperty(name = "liquibase.enabled", defaultValue = "true")
        private boolean enabled;

        @Inject
        @ConfigProperty(name = "liquibase.contexts", defaultValue = "")
        private String contexts;

        @Inject
        @ConfigProperty(name = "liquibase.database-change-log-table", defaultValue = "DATABASECHANGELOG")
        private String databaseChangeLogTable;

        @Inject
        @ConfigProperty(name = "liquibase.database-change-log-lock-table", defaultValue = "DATABASECHANGELOGLOCK")
        private String databaseChangeLogLockTable;

    }

}
