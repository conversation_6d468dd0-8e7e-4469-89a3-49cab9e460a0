package com.siemens.spine.resource.config;

import org.eclipse.microprofile.health.HealthCheck;
import org.eclipse.microprofile.health.HealthCheckResponse;
import org.eclipse.microprofile.health.Liveness;

import javax.enterprise.context.ApplicationScoped;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17/3/2023
 */
@Liveness
@ApplicationScoped
public class SpineLivenessCheck implements HealthCheck {

    @Override
    public HealthCheckResponse call() {
        return HealthCheckResponse.named("LivenessCheckSpine")
                .up()
                .withData("time", System.currentTimeMillis())
                .build();
    }

}
