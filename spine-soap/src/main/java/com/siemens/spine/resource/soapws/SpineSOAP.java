package com.siemens.spine.resource.soapws;

import com.siemens.spine.db.constant.Constants;
import com.siemens.spine.generated.toolchain.Attribute;
import com.siemens.spine.generated.toolchain.BoMGetDataResult;
import com.siemens.spine.generated.toolchain.ChangeGroup;
import com.siemens.spine.generated.toolchain.Component;
import com.siemens.spine.generated.toolchain.ComponentDriveAssignmentData;
import com.siemens.spine.generated.toolchain.ComponentNeighborConnections;
import com.siemens.spine.generated.toolchain.ComponentSpecificationMember;
import com.siemens.spine.generated.toolchain.ComponentStateHistory;
import com.siemens.spine.generated.toolchain.ConnectionPoint;
import com.siemens.spine.generated.toolchain.DecompositionAttributes;
import com.siemens.spine.generated.toolchain.Group;
import com.siemens.spine.generated.toolchain.GroupItemData;
import com.siemens.spine.generated.toolchain.ListStatus;
import com.siemens.spine.generated.toolchain.MarkAsDoneData;
import com.siemens.spine.generated.toolchain.Position;
import com.siemens.spine.generated.toolchain.Project;
import com.siemens.spine.generated.toolchain.RoleOperations;
import com.siemens.spine.generated.toolchain.State;
import com.siemens.spine.generated.toolchain.ToolChainLogicPort;
import com.siemens.spine.generated.toolchain.Type;
import com.siemens.spine.generated.toolchain.UserRole;
import com.siemens.spine.generated.toolchain.Version;
import com.siemens.spine.logic.exception.ForbiddenException;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.resource.interceptor.annotation.SpineRequiresPermission;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.inject.spi.CDI;
import javax.jws.WebService;
import javax.xml.datatype.XMLGregorianCalendar;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> Pham
 * @version 1.0
 * @since 22/12/2022
 */
@WebService(serviceName = "ToolChainLogic", portName = "ToolChainLogic_PortPort",
        targetNamespace = "http://toolchain.siemens.pl")
@Slf4j
public class SpineSOAP implements ToolChainLogicPort {

    private final ProjectWS projectWS = CDI.current().select(ProjectWS.class).get();
    private final ComponentWS componentWS = CDI.current().select(ComponentWS.class).get();
    private final GroupWS groupWS = CDI.current().select(GroupWS.class).get();
    private final ConnectionPointWS connectionPointWS = CDI.current().select(ConnectionPointWS.class).get();
    private final ChangeGroupWS changeGroupWS = CDI.current().select(ChangeGroupWS.class).get();
    private final BomWS bomWS = CDI.current().select(BomWS.class).get();
    private final IdentityAndAccessWS identityAndAccessWS = CDI.current().select(IdentityAndAccessWS.class).get();
    private final TypeWS typeWS = CDI.current().select(TypeWS.class).get();

    @Override
    public List<ComponentNeighborConnections> getNeighborConnections(String token,
                                                                     String projectName,
                                                                     List<String> componentIds,
                                                                     ComponentNeighborConnections componentNeighborConnections) {
        return connectionPointWS.getNeighborConnection(token, projectName, componentIds);
    }

    @Override
    public boolean setNeighborConnections(String token,
                                          String projectName,
                                          List<ComponentNeighborConnections> componentNeighborConnections) {
        return connectionPointWS.setNeighborConnection(token, projectName, componentNeighborConnections);
    }

    @Override
    @SpineRequiresPermission(value = Constants.Permission.TYPE_VIEW)
    public Type getType(String token, String projectName, Type type) {
        try {
            return typeWS.getType(token, projectName, type);
        } catch (ForbiddenException ex) {
            log.error("Permission denied: {}", ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean setSimulationState(String arg0, String arg1, String arg2, List<String> arg3) {
        return false;
    }

    @Override
    public String getAllTypes(String token, String projectName, Type type) {
        return projectWS.getAllTypes(token, projectName, type);
    }

    @Override
    public Integer createChangeGroup(String token, ChangeGroup changeGroup) {
        return changeGroupWS.createChangeGroup(token, changeGroup);
    }

    @Override
    public Component getComponent(String arg0, String arg1, Component arg2) {
        return null;
    }

    @Override
    public List<String> getComponentsByUniqueIds(String token, String projectName, List<Long> ids,
                                                 Component component) {
        try {
            return componentWS.getComponentsByUniqueIds(token, projectName, ids);
        } catch (ForbiddenException ex) {
            log.error("Permission denied: {}", ex.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public boolean createTypes(String token, String projectName, List<Type> types) {
        try {
            return typeWS.createTypes(token, projectName, types);
        } catch (ForbiddenException ex) {
            log.error("Permission denied: {}", ex.getMessage());
            return false;
        }
    }

    @Override
    public BoMGetDataResult getBoMListData(String arg0, String arg1, List<String> arg2,
                                           boolean arg3, BoMGetDataResult arg4) {
        log.info("Get bom list data");
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public List<Project> getAllProjects(String token, Project project) {
        try {
            return projectWS.getAllProjects(token, project);
        } catch (Exception ex) {
            log.error("Permission denied: {}", ex.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public boolean markAsDone(String token, String projectName, List<MarkAsDoneData> data) {
        try {
            return componentWS.markAsDone(token, projectName, data);
        } catch (ForbiddenException ex) {
            log.error("Permission denied: {}", ex.getMessage());
            return false;
        }
    }

    @Override
    public List<String> createComponents(String token, String projectName, String drawing, String arg3,
                                         List<Component> components) {
        try {
            return componentWS.createComponents(token, projectName, drawing, arg3, components);
        } catch (ForbiddenException ex) {
            log.error("Permission denied: {}", ex.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public List<Long> getComponentsUniqueIds(String token, String projectName, Group axgroup) {
        try {
            return componentWS.getComponentsUniqueIds(token, projectName, axgroup);
        } catch (ForbiddenException ex) {
            log.error("Permission denied: {}", ex.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public List<RoleOperations> getRoleOperations(String token, List<String> roleNames,
                                                  RoleOperations roleOperations) {
        class LocalRoleOperations extends RoleOperations {

            public void setOperations(List<String> operations) {
                super.operations = operations;
            }

        }

        return roleNames.stream().map(role -> {
            LocalRoleOperations roleOperations1 = new LocalRoleOperations();
            roleOperations1.setOperations(Arrays.asList("COMPONENT_DELETE", "COMPONENT_MODIFY", "COMPONENT_VIEW"));
            roleOperations1.setRole(role);
            return (RoleOperations) roleOperations1;
        }).toList();
    }

    @Override
    public boolean setComponentsStatusInList(String token, String project, String operationName, String state,
                                             List<String> componentUniqueIds) {
        try {
            return componentWS.setComponentsStatusInList(token, project, operationName, state, componentUniqueIds);
        } catch (Exception ex) {
            log.error("Permission denied: {}", ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean setStates(String token, String projectName, List<State> states) {
        try {
            return componentWS.setStates(token, projectName, states);
        } catch (Exception ex) {
            log.error("Permission denied: {}", ex.getMessage());
            return false;
        }
    }

    @Override
    public List<State> getStates(String token, String projectName, List<String> axids, State state) {
        try {
            return componentWS.getCurrentStates(token, projectName, axids, state);
        } catch (ForbiddenException ex) {
            log.error("Permission denied: {}", ex.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public boolean setEmulationState(String arg0, String arg1, String arg2, List<String> arg3) {
        log.info("Set emulation state");
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public List<Component> getComponents(String arg0, String arg1, Group arg2, Component arg3) {
        log.info("Get components");
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public boolean setProject(String token, Project projectInfo) {
        try {
            return projectWS.setProject(token, projectInfo);
        } catch (ForbiddenException ex) {
            log.error("Permission denied: {}", ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean isOperationPermitted(String arg0, String arg1, String arg2) {
        return false;
    }

    @Override
    public List<DecompositionAttributes> getDecompositionAttributes(String token,
                                                                    List<String> sapNumbers,
                                                                    DecompositionAttributes attributes) {
        try {
            return componentWS.getDecompositionAttributes(token, sapNumbers, attributes);
        } catch (ForbiddenException ex) {
            log.error("Permission denied: {}", ex.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public boolean createDecompositionAttributes(String token, List<DecompositionAttributes> attributes) {
        try {
            return componentWS.createDecompositionAttributes(token, attributes);
        } catch (ForbiddenException ex) {
            log.error("Permission denied: {}", ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean setCalculationState(String arg0, String arg1, String arg2, List<String> arg3) {
        return false;
    }

    @Override
    public boolean setPosition(String arg0, String arg1, String arg2, Position arg3) {
        return false;
    }

    @Override
    public boolean deleteComponents(String token, String projectName, List<String> uniqueIds) {
        try {
            return componentWS.markAsDeleted(token, projectName, uniqueIds);
        } catch (Exception ex) {
            log.error("Permission denied: {}", ex.getMessage());
            return false;
        }
    }

    @Override
    public List<UserRole> getUserRoles(String token, String username, UserRole userRole) {
        try {
            return identityAndAccessWS.getUserRoles(token, username, userRole);
        } catch (Exception ex) {
            log.error("Permission denied: {}", ex.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public List<GroupItemData> getGroupItems(String token, String projectName, Group group,
                                             GroupItemData groupItemData) {
        try {
            return groupWS.getGroupItems(token, projectName, group, groupItemData);
        } catch (Exception ex) {
            log.error("Exception occurred when get group items", ex);
            return Collections.emptyList();
        }
    }

    @Override
    public BoMGetDataResult getBomListDataByUniqueIds(String token, String projectName,
                                                      List<Long> axids,
                                                      BoMGetDataResult boMGetDataResult) {
        try {
            return bomWS.getBomListDataByUniqueIds(token, projectName, axids, boMGetDataResult);
        } catch (Exception ex) {
            log.error("Exception occurred when get bom list data by unique ids", ex);
            return null;
        }
    }

    /**
     * This function will get list of componentId by condition.
     * In case the condition is _getChangeReason, this function will return list of componentId_changeGroupName changeGroupDescription
     *
     * @param token
     * @param projectName          It is project id
     * @param specificationMembers It is condiditon
     * @param start                The offset when query data
     * @param end                  The limit value  when query data
     * @return
     */
    @Override
    public List<String> getComponentsBySpecification(String token,
                                                     String projectName,
                                                     List<ComponentSpecificationMember> specificationMembers,
                                                     int start,
                                                     int end) {
        try {
            return componentWS.getComponentsBySpecification(token, projectName, specificationMembers, start, end);
        } catch (ForbiddenException | SpineException e) {
            log.error("Exception occurred when get components by specification", e);
            return Collections.emptyList();
        }
    }

    @Override
    public Position getPosition(String arg0, String arg1, String arg2, Position arg3) {
        log.info("Get position");
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public State getState(String token, String projectName, String uniqueId, State state) {
        try {
            List<State> response = componentWS.getCurrentStates(token, projectName, List.of(uniqueId), state);
            return (response == null || response.isEmpty()) ? null : response.get(0);
        } catch (Exception e) {
            log.error("Exception occurred when get state", e);
            return null;
        }
    }

    @Override
    public List<Version> getVersionHistory(Version arg0) {
        return null;
    }

    @Override
    public List<ListStatus> getChangeListStates(String token,
                                                String projectName,
                                                List<String> uniqueIds,
                                                ListStatus status) {
        try {
            return componentWS.getChangeListStates(token, projectName, uniqueIds, status);
        } catch (Exception e) {
            log.error("Exception occurred when get change list states", e);
            return Collections.emptyList();
        }
    }

    @Override
    public boolean setState(String token, String projectName, String uniqueId, State state) {
        try {
            return componentWS.setState(token, projectName, uniqueId, state);
        } catch (Exception e) {
            log.error("Exception occurred when set state", e);
            return false;
        }
    }

    @Override
    public boolean setDriveAssignmentData(String arg0, String arg1,
                                          List<ComponentDriveAssignmentData> arg2) {
        try {
            return componentWS.setDriveAssignmentData(arg0, arg1, arg2);
        } catch (Exception e) {
            log.error("Exception occurred when set drive assignment data", e);
            return false;
        }
    }

    @Override
    public List<State> getAllStates(String token, String projectName, Group group, State state) {
        try {
            return componentWS.getAllStates(token, projectName, group);
        } catch (Exception e) {
            log.error("Exception occurred when get all states", e);
            return Collections.emptyList();
        }
    }

    @Override
    public Version getVersion() {
        Version version = new Version();
        version.setInfo("SPINE-CORE");
        version.setNumber("15.0");
        return version;
    }

    @Override
    public List<Attribute> calculateDrive(String arg0, String arg1, Component arg2,
                                          Attribute arg3) {
        log.info("Calculate drive");
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public List<ConnectionPoint> getConnectionPoints(String arg0, String arg1, String arg2,
                                                     ConnectionPoint arg3) {
        log.info("Get connection points");
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public boolean renameGroup(String token, String projectName, GroupItemData groupItemData, String newName) {
        try {
            return groupWS.renameGroup(token, projectName, groupItemData, newName);
        } catch (Exception e) {
            log.error("Exception occurred when rename group", e);
            return false;
        }
    }

    @Override
    public List<Long> getUniqueIdsFromList(String token, String projectName, String listTypes,
                                           List<Group> group, boolean getFullData) {
        try {
            return componentWS.getUniqueIdsFromList(token, projectName, listTypes, group, getFullData);
        } catch (Exception e) {
            log.error("Exception occurred when get unique ids from list", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<Long> getBomListUniqueIds(String token, String projectName, List<String> groupData,
                                          boolean getFullData) {
        try {
            return bomWS.getBomListUniqueIds(token, projectName, groupData, getFullData);
        } catch (Exception e) {
            log.error("Exception occurred when get bom list unique ids", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<XMLGregorianCalendar> getComponentLastModDate(String token, String projectName, List<Long> axids) {
        try {
            return componentWS.getComponentLastModDate(token, projectName, axids);
        } catch (Exception e) {
            log.error("Exception occurred when get component last mod date", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ComponentDriveAssignmentData> getDriveAssignmentData(String token, String projectName,
                                                                     List<String> componentIds) {
        try {
            return componentWS.getDriveAssignmentData(token, projectName, componentIds);
        } catch (Exception e) {
            log.error("Exception occurred when get drive assignment data", e);
            return Collections.emptyList();
        }
    }

    @Override
    public boolean deleteGroup(String token, String projectName, GroupItemData group, boolean force) {
        try {
            return groupWS.deleteGroup(token, projectName, group, force);
        } catch (Exception e) {
            log.error("Exception occurred when delete group", e);
            return false;
        }
    }

    @Override
    public List<ChangeGroup> getAllChangeGroupsInProject(String token, String projectName,
                                                         ChangeGroup arg2) {
        try {
            return changeGroupWS.getAllChangeGroupsInProject(token, projectName);
        } catch (Exception e) {
            log.error("Exception occurred when get all change groups in project", e);
            return Collections.emptyList();
        }
    }

    @Override
    public boolean setConnectionPoints(String arg0, String arg1, String arg2,
                                       List<ConnectionPoint> arg3) {
        return false;
    }

    @Override
    public boolean setSimulationStateForDrawing(String token,
                                                String projectName,
                                                String simulationState,
                                                String drawing) {
        try {
            return componentWS.setSimulationStateForDrawing(token, projectName, simulationState, drawing);
        } catch (Exception e) {
            log.error("Exception occurred when set simulation state for drawing", e);
            return false;
        }
    }

    @Override
    public boolean setCalculationStateForDrawing(String token,
                                                 String projectName,
                                                 String calculationState,
                                                 String drawing) {
        try {
            return componentWS.setCalculationStateForDrawing(token, projectName, calculationState, drawing);
        } catch (Exception e) {
            log.error("Exception occurred when set calculation state for drawing", e);
            return false;
        }
    }

    @Override
    public List<Long> generateComponentUniqueIds(String token, int amount) {
        try {
            return componentWS.generateUniqueIds(token, amount);
        } catch (Exception e) {
            log.error("Exception occurred when generate component unique ids", e);
            return Collections.emptyList();
        }
    }

    @Override
    public boolean setITState(String arg0, String arg1, String arg2, List<String> arg3) {
        return false;
    }

    @Override
    public boolean setBOMState(String token, String projectName, String bomState, List<String> componentIds) {
        try {
            return componentWS.setBOMState(token, projectName, bomState,
                    componentIds.stream().map(Long::parseLong).toList());
        } catch (Exception e) {
            log.error("Exception occurred when set bom state", e);
            return false;
        }
    }

    @Override
    public List<Long> getValuesFromNamedCounter(String token, int amount, String counterName) {
        try {
            return projectWS.getValuesFromNamedCounter(token, amount, counterName);
        } catch (Exception e) {
            log.error("Exception occurred when get values from named counter", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ComponentStateHistory> getComponentsStateHistory(String token,
                                                                 String projectName,
                                                                 List<Long> componentIds) {
        try {
            return componentWS.getComponentsStateHistory(token, projectName, componentIds);
        } catch (Exception e) {
            log.error("Exception occurred when get components state history", e);
            return Collections.emptyList();
        }
    }

}
