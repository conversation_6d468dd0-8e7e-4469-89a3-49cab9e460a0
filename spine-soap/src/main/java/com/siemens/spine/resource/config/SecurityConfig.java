package com.siemens.spine.resource.config;

import lombok.Getter;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

@ApplicationScoped
@Getter
public class SecurityConfig {

    @Inject
    @ConfigProperty(name = "security.jersey.enabled", defaultValue = "true")
    private boolean enable;

    @Inject
    @ConfigProperty(name = "security.providers.0.oidc.issuer")
    private String issuer;

    @Inject
    @ConfigProperty(name = "security.providers.0.oidc.audience", defaultValue = "spine-core")
    private String audience;

}
