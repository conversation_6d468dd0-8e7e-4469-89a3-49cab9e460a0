package com.siemens.spine.resource.rest;

import com.siemens.spine.db.constant.Constants;
import com.siemens.spine.db.constant.ProjectState;
import com.siemens.spine.generated.toolchain.Project;
import com.siemens.spine.logic.dto.request.ProjectRequestDTO;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.service.ComponentService;
import com.siemens.spine.logic.service.ProjectService;
import com.siemens.spine.resource.interceptor.annotation.SecurityHolder;
import com.siemens.spine.resource.interceptor.annotation.SpineRequiresPermission;
import io.helidon.security.annotations.Authenticated;

import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 21/12/2022
 */
@Path("/projects")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Authenticated
@SecurityHolder
public class ProjectResource {

    @Inject
    private ProjectService projectService;

    @Inject
    private ComponentService componentService;

    @POST
    public Response getProjects(@QueryParam("token") String token, Project project) {
        return Response.ok(projectService.getAllProjectForRest()).build();
    }

    @POST
    @Path("/create")
    @SpineRequiresPermission(value = Constants.Permission.ADMIN_CREATE_NEW_PROJECT)
    public Response createProject(ProjectRequestDTO request) throws SpineException {
        return Response.ok(projectService.createProject(request)).build();
    }

    @GET
    @Path("/state")
    public Response getListStateProject() {
        return Response.ok(projectService.getListStateProject()).build();
    }

    @GET
    @Path("/type")
    public Response getListTypesProject() {
        return Response.ok(projectService.getListTypesProject()).build();
    }

    @PUT
    @Path("/move-forward")
    @SpineRequiresPermission(value = Constants.Permission.PROJECT_CHANGE_TO_EXECUTION_PHASE)
    public Response changeProjectState(
            @QueryParam("project_id") Long projectId,
            @QueryParam("status") ProjectState status) throws SpineException {
        return Response.ok(projectService.changeProjectState(projectId, status)).build();
    }

    @GET
    @Path("/unfinished-material")
    @SpineRequiresPermission(value = Constants.Permission.COMPONENT_VIEW)
    public Response getUnfinishedMaterial(@QueryParam("project_id") Long projectId) throws SpineException {
        return Response.ok(componentService.getUnfinishedMaterial(projectId)).build();
    }

}
