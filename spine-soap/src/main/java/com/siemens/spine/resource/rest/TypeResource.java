package com.siemens.spine.resource.rest;

import com.siemens.spine.db.constant.Constants;
import com.siemens.spine.logic.dto.TypeDto;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.service.TypeService;
import com.siemens.spine.resource.interceptor.annotation.SecurityHolder;
import com.siemens.spine.resource.interceptor.annotation.SpineRequiresPermission;
import io.helidon.security.annotations.Authenticated;

import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 21/12/2022
 */
@Path("/types")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Authenticated
@SecurityHolder
public class TypeResource {

    @Inject
    private TypeService typeService;

    @GET
    @SpineRequiresPermission(value = Constants.Permission.TYPE_VIEW)
    public Response getListTypesProject(@QueryParam("project_id") Long projectId) {
        return Response.ok(typeService.getAllTypeByProjectId(projectId)).build();
    }

    @GET
    @Path("/metadata")
    public Response getMetadata() {
        return Response.ok(typeService.getTypeMetadata()).build();
    }

    @PUT
    @Path("/{id}")
    @SpineRequiresPermission(value = Constants.Permission.TYPE_MODIFY)
    public Response update(
            @PathParam("id") String typeId,
            TypeDto modifiedType
    ) throws SpineException {
        return Response.ok(typeService.update(typeId, modifiedType)).build();
    }

    @PUT
    @Path("/delete")
    @SpineRequiresPermission(value = Constants.Permission.TYPE_DELETE)
    public Response deleteProjectByType(List<String> ids) throws SpineException {
        typeService.deleteTypeByIds(ids);
        return Response.ok().build();
    }

    @POST
    @SpineRequiresPermission(value = Constants.Permission.TYPE_MODIFY)
    public Response create(TypeDto type) throws SpineException {
        typeService.create(type);
        return Response.ok().build();
    }

}