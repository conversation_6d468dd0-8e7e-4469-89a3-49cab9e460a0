package com.siemens.spine.resource.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.siemens.spine.logic.exception.UnauthorizedException;
import com.siemens.spine.logic.security.AuthService;
import com.siemens.spine.logic.security.SecurityConfig;
import com.siemens.spine.resource.interceptor.annotation.SoapSecure;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Priority;
import javax.inject.Inject;
import javax.interceptor.AroundInvoke;
import javax.interceptor.Interceptor;
import javax.interceptor.InvocationContext;
import java.io.Serializable;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * <AUTHOR> <PERSON>
 */
@Priority(1)
@Interceptor
@SoapSecure
@Slf4j
public class SoapAuthenticationInterceptor {

    @Inject
    private SecurityConfig securityConfig;

    @Inject
    private AuthService authService;

    @AroundInvoke
    public Object validateToken(InvocationContext invocationContext) throws Exception {
        if (!securityConfig.isEnable()) {
            return invocationContext.proceed();
        }

        String tokenParam = SoapSecure.DEFAULT_TOKEN_PARAM;

        Method method = invocationContext.getMethod();
        Class<?> declaredClass = method.getDeclaringClass();

        // Use the token param defined from the class if it's appeared
        SoapSecure classAnnotation = declaredClass.getAnnotation(SoapSecure.class);
        if (classAnnotation != null) {
            tokenParam = classAnnotation.tokenParam();
        }

        // Prioritize the token param defined from the method if it's appeared
        SoapSecure methodAnnotation = method.getDeclaredAnnotation(SoapSecure.class);
        if (methodAnnotation != null) {
            tokenParam = methodAnnotation.tokenParam();
        }

        // Get the value of token from method parameter
        Parameter[] params = method.getParameters();
        String tokenObj = null;
        for (int i = 0; i < params.length; i++) {
            if (params[i].getName().equals(tokenParam)) {
                Object obj = invocationContext.getParameters()[i];
                tokenObj = obj == null ? null : obj.toString();
                break;
            }
        }

        //TODO: enable the block below to validate token from SOAP APIs
        if (tokenObj == null) {
            log.error("No secured field was found with name {} in the method {}", tokenParam, method.getName());
            throw new UnauthorizedException();
        } else {
            String token;
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                SoapSecurityToken data = objectMapper.readValue(tokenObj, SoapSecurityToken.class);
                token = data.getToken();
            } catch (Exception e) {
                log.warn("Could not deserialize the token to the object. Try authenticate with raw string");
                token = tokenObj;
            }

            authService.validateToken(token);
        }

        return invocationContext.proceed();
    }

    /**
     * This object is map with AX4 - SecurityToken
     */
    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    static class SoapSecurityToken implements Serializable {

        String username;

        String method;

        String token;

    }

}
