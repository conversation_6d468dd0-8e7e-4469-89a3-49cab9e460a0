package com.siemens.spine.resource.interceptor.annotation;

import javax.interceptor.InterceptorBinding;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Save the authentication info from RestAPI to current thread
 * See {@link com.siemens.spine.logic.security.SecurityContextHolder}
 */
@InterceptorBinding
@Retention(RetentionPolicy.RUNTIME)
@Target({ ElementType.TYPE })
public @interface SecurityHolder {

}
