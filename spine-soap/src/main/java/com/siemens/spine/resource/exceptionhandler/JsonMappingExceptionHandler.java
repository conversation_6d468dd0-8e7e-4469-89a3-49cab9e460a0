package com.siemens.spine.resource.exceptionhandler;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.siemens.spine.logic.dto.BaseErrorDto;
import io.netty.handler.codec.http.HttpResponseStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import javax.ws.rs.ext.Provider;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Provider
@Slf4j
public class JsonMappingExceptionHandler implements ExceptionMapper<JsonMappingException> {

    @Override
    public Response toResponse(JsonMappingException e) {
        log.error("", e);

        List<String> invalidFields = new ArrayList<>();
        List<JsonMappingException.Reference> references = e.getPath();
        for (JsonMappingException.Reference reference : references) {
            if (!StringUtils.isEmpty(reference.getFieldName())) {
                invalidFields.add(reference.getFieldName());
            }
        }

        String msg = invalidFields.isEmpty() ?
                e.getMessage() :
                String.format("Invalid value of %s", StringUtils.join(invalidFields));
        BaseErrorDto error = BaseErrorDto.builder()
                .timestamp(System.currentTimeMillis())
                .errorMessage(msg)
                .build();

        return Response.status(HttpResponseStatus.BAD_REQUEST.code())
                .entity(error)
                .build();
    }

}
