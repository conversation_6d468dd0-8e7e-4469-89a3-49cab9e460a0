package com.siemens.spine.resource;

import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.joran.JoranConfigurator;
import ch.qos.logback.core.joran.spi.JoranException;
import io.helidon.microprofile.cdi.Main;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.slf4j.bridge.SLF4JBridgeHandler;

import java.io.InputStream;
import java.util.logging.LogManager;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 29/12/2022
 */
@Slf4j
public class SpineCoreApplication {

    public static void main(String[] args) {
        log.info("Server spine is starting...");

        // Configure logging based on environment
        configureLogging();

        Main.main(args);

        log.info("Server Spinecon is running...");
    }

    /**
     * Configure logging based on the active profile.
     * Uses logback-prod.xml for production and logback.xml for development.
     */
    private static void configureLogging() {
        // Reset JUL logging
        LogManager.getLogManager().reset();
        SLF4JBridgeHandler.removeHandlersForRootLogger();
        SLF4JBridgeHandler.install();

        // Determine which config file to use based on environment
        String configFile;
        String activeProfile = System.getProperty("profile", System.getenv().getOrDefault("PROFILE", "dev"));

        if ("prod".equalsIgnoreCase(activeProfile)) {
            configFile = "/logback-prod.xml";
            log.info("Using production logging configuration");
        } else {
            configFile = "/logback.xml";
            log.info("Using development logging configuration: {}", activeProfile);
        }

        // Load and apply the configuration
        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
        try {
            JoranConfigurator configurator = new JoranConfigurator();
            configurator.setContext(context);
            context.reset();

            InputStream configStream = SpineCoreApplication.class.getResourceAsStream(configFile);
            if (configStream != null) {
                configurator.doConfigure(configStream);
                configStream.close();
            } else {
                log.warn("Could not find logging configuration file: {}", configFile);
            }
        } catch (JoranException | java.io.IOException e) {
            log.error("Error configuring Logback", e);
        }
    }

}
