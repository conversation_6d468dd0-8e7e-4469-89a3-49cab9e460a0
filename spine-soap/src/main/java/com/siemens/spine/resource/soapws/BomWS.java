package com.siemens.spine.resource.soapws;

import com.siemens.spine.db.constant.Constants;
import com.siemens.spine.generated.toolchain.BoMGetDataResult;
import com.siemens.spine.logic.exception.ForbiddenException;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.security.SecurityCheck;
import com.siemens.spine.logic.service.BomService;
import com.siemens.spine.resource.interceptor.annotation.SoapSecure;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.Collections;
import java.util.List;

@ApplicationScoped
@Slf4j
public class BomWS extends AuthzWS {

    private final BomService bomService;

    @Inject
    public BomWS(SecurityCheck securityCheck, BomService bomService) {
        super(securityCheck);
        this.bomService = bomService;
    }

    @SoapSecure
    public BoMGetDataResult getBomListDataByUniqueIds(String token,
                                                      String projectName,
                                                      List<Long> axids,
                                                      BoMGetDataResult boMGetDataResult) {
        try {
            securityCheck.checkPermissions(projectName, Constants.Permission.COMPONENT_VIEW,
                    Constants.Permission.COMPONENT_VIEW_DECOMPOSITION, Constants.Permission.TYPE_VIEW,
                    Constants.Permission.TODOLIST_SAPBOM);
            return bomService.getBomListDataByUniqueIds(projectName, axids);
        } catch (SpineException | ForbiddenException e) {
            log.error("Exception occurred when get bom list data by unique ids", e);
            return null;
        }
    }

    @SoapSecure
    public List<Long> getBomListUniqueIds(String token, String projectName, List<String> groupData,
                                          boolean getFullData) {
        try {
            securityCheck.checkPermissions(projectName, Constants.Permission.COMPONENT_VIEW,
                    Constants.Permission.COMPONENT_VIEW_DECOMPOSITION, Constants.Permission.TYPE_VIEW,
                    Constants.Permission.TODOLIST_SAPBOM);
            return bomService.getBomListUniqueIds(projectName, groupData, getFullData);
        } catch (SpineException | ForbiddenException e) {
            log.error("Exception occurred when get bom list unique ids", e);
            return Collections.emptyList();
        }
    }

}
