package com.siemens.spine.resource.rest;

import com.siemens.spine.db.constant.Constants;
import com.siemens.spine.logic.dto.request.GroupRequestDto;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.service.GroupService;
import com.siemens.spine.resource.interceptor.annotation.SecurityHolder;
import com.siemens.spine.resource.interceptor.annotation.SpineRequiresPermission;
import io.helidon.security.annotations.Authenticated;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 10/01/2022
 */
@Path("/groups")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Authenticated
@SecurityHolder
@SpineRequiresPermission
public class GroupResource {

    private final GroupService groupService;

    @Inject
    public GroupResource(GroupService groupService) {
        this.groupService = groupService;
    }

    @GET
    @SpineRequiresPermission(value = Constants.Permission.GROUPS_VIEW)
    public Response getGroups(@QueryParam("project_id") Long projectId) {
        return Response.ok(groupService.findByProjectId(projectId)).build();
    }

    @PUT
    @Path("/assign-delivery-date")
    @SpineRequiresPermission(value = Constants.Permission.GROUPS_ASSIGN_DELIVERY_DATE)
    public Response assignDeliveryDate(
            @QueryParam("project_id") Long projectId,
            GroupRequestDto groupRequestDto) throws SpineException {
        return Response.ok(groupService.updateListGroup(projectId, groupRequestDto)).build();
    }

    @DELETE
    @SpineRequiresPermission(value = Constants.Permission.GROUPS_DELETE)
    public Response delete(@QueryParam("group_ids") String listGroupIdStr) throws SpineException {
        List<Long> groupIds = null;
        if (!StringUtils.isEmpty(listGroupIdStr)) {
            groupIds = Arrays.stream(listGroupIdStr.split(","))
                    .map(String::trim)
                    .map(Long::valueOf)
                    .toList();
        }
        groupService.deleteListGroup(groupIds);
        return Response.ok().build();
    }

}
