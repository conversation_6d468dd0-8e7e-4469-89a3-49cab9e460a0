package com.siemens.spine.resource.exceptionhandler;

import com.siemens.spine.logic.dto.ValidationErrorInfo;
import io.netty.handler.codec.http.HttpResponseStatus;
import lombok.extern.slf4j.Slf4j;

import javax.ws.rs.core.Response;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 21/7/2025
 **/
@Slf4j
public class IllegalArgumentExceptionHandler extends AbstractExceptionHandler<IllegalArgumentException> {

    @Override
    public Response toResponse(IllegalArgumentException exception) {
        log.warn("exception occurred. message={}", exception.getMessage());

        ValidationErrorInfo validationErrorInfo = new ValidationErrorInfo();
        validationErrorInfo.setTimestamp(System.currentTimeMillis());
        validationErrorInfo.setErrorMessage(exception.getMessage());

        return Response
                .status(Response.Status.BAD_REQUEST)
                .entity(validationErrorInfo)
                .build();
    }

    @Override
    protected int getHttpStatus() {
        return HttpResponseStatus.BAD_REQUEST.code();
    }

}
