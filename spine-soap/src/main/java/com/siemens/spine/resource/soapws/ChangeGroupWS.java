package com.siemens.spine.resource.soapws;

import com.siemens.spine.db.constant.Constants.Permission;
import com.siemens.spine.db.repository.filter.ChangeGroupFilter;
import com.siemens.spine.generated.toolchain.ChangeGroup;
import com.siemens.spine.logic.dto.ChangeGroupDto;
import com.siemens.spine.logic.exception.ForbiddenException;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.mapper.ChangeGroupMapper;
import com.siemens.spine.logic.security.SecurityCheck;
import com.siemens.spine.logic.service.ChangeGroupService;
import com.siemens.spine.resource.interceptor.annotation.SoapSecure;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.BadRequestException;
import java.util.List;

@ApplicationScoped
@Slf4j
public class ChangeGroupWS extends AuthzWS {

    private final ChangeGroupService changeGroupService;

    @Inject
    public ChangeGroupWS(SecurityCheck securityCheck, ChangeGroupService changeGroupService) {
        super(securityCheck);
        this.changeGroupService = changeGroupService;
    }

    @SoapSecure
    public Integer createChangeGroup(String token, ChangeGroup changeGroup) {

        try {
            securityCheck.checkPermission(changeGroup.getProjectKey(), Permission.COMPONENT_MOVE_BACKWARD);
            ChangeGroupDto dto = ChangeGroupDto.builder()
                    .id(changeGroup.getId() == null ? null : changeGroup.getId().longValue())
                    .name(changeGroup.getName())
                    .description(changeGroup.getDescription()).build();
            ChangeGroupDto createdCG = changeGroupService.create(changeGroup.getProjectKey(), dto);
            return createdCG.getId().intValue();
        } catch (SpineException | ForbiddenException e) {
            throw new BadRequestException(e);
        }
    }

    @SoapSecure
    public List<ChangeGroup> getAllChangeGroupsInProject(String token, String projectName) {
        ChangeGroupFilter filter = ChangeGroupFilter.builder().projectName(projectName).build();
        List<ChangeGroupDto> dtos = changeGroupService.getChangeGroups(filter);

        return ChangeGroupMapper.INSTANCE.toSoapChangeGroups(dtos);
    }

}
