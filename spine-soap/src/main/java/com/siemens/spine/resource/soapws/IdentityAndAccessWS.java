package com.siemens.spine.resource.soapws;

import com.siemens.spine.generated.toolchain.UserRole;
import com.siemens.spine.logic.service.IamService;
import com.siemens.spine.resource.interceptor.annotation.SoapSecure;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.List;

@ApplicationScoped
public class IdentityAndAccessWS {

    private final IamService userRoleService;

    @Inject
    public IdentityAndAccessWS(IamService userRoleService) {
        this.userRoleService = userRoleService;
    }

    @SoapSecure
    public List<UserRole> getUserRoles(String token, String username, UserRole userRole) {
        return userRoleService.getUserRoles();
    }

}
