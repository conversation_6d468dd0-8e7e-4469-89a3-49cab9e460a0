package com.siemens.spine.resource.exceptionhandler;

import com.siemens.spine.logic.exception.UnauthorizedException;
import io.netty.handler.codec.http.HttpResponseStatus;

import javax.ws.rs.ext.Provider;

/**
 * <AUTHOR>
 */
@Provider
public class Unauthorized<PERSON>and<PERSON> extends AbstractExceptionHandler<UnauthorizedException> {

    @Override
    protected int getHttpStatus() {
        return HttpResponseStatus.UNAUTHORIZED.code();
    }

}
