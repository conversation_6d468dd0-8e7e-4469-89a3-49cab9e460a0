package com.siemens.spine.resource.interceptor;

import com.siemens.spine.logic.security.SecurityCheck;
import com.siemens.spine.logic.security.SecurityConfig;
import com.siemens.spine.resource.interceptor.annotation.SpineRequiresPermission;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Priority;
import javax.inject.Inject;
import javax.interceptor.AroundInvoke;
import javax.interceptor.Interceptor;
import javax.interceptor.InvocationContext;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.HttpHeaders;
import java.lang.reflect.Method;
import java.net.URLDecoder;
import java.nio.charset.Charset;

/**
 * Check permission for rest api
 *
 * <AUTHOR>
 * @version 1.0
 * @since 8/3/2023
 */
@Priority(InterceptorPriority.PERMISSION_ALLOWED_PRIORITY)
@Interceptor
@Slf4j
@SpineRequiresPermission
public class SpineRequiresPermissionInterceptor {

    @Inject
    private SecurityConfig securityConfig;

    @Context
    private HttpHeaders httpHeaders;

    @Inject
    private SecurityCheck securityCheck;

    @AroundInvoke
    public Object allowPermission(InvocationContext invocationContext) throws Exception {
        if (!securityConfig.isEnable()) {
            return invocationContext.proceed();
        }
        String xPid = httpHeaders.getHeaderString("X-PID");
        if (StringUtils.isNotEmpty(xPid)) {
            xPid = URLDecoder.decode(xPid, Charset.defaultCharset());
        }
        Method method = invocationContext.getMethod();
        SpineRequiresPermission annotation = method.getAnnotation(SpineRequiresPermission.class);
        String permission = annotation.value();
        securityCheck.checkPermission(xPid, permission);
        return invocationContext.proceed();
    }

}
