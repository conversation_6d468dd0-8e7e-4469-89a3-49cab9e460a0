package com.siemens.spine.resource.exceptionhandler;

import com.siemens.spine.logic.exception.SpineException;
import io.netty.handler.codec.http.HttpResponseStatus;

import javax.ws.rs.ext.Provider;

/**
 * Exception handler for SpineException
 */
@Provider
public class SpineExceptionHandler extends AbstractExceptionHandler<SpineException> {

    @Override
    protected int getHttpStatus() {
        return HttpResponseStatus.INTERNAL_SERVER_ERROR.code();
    }

}
