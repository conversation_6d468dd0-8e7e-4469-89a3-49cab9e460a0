package com.siemens.spine.resource.interceptor.annotation;

import javax.enterprise.util.Nonbinding;
import javax.interceptor.InterceptorBinding;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@InterceptorBinding
@Target({ ElementType.TYPE, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
public @interface SpineRolesAllowed {

    /**
     * List of the user roles (in string) that allowed to execute the function
     * Multiple roles have to be separated by a comma. Eg. "ADMIN, OPERATOR"
     *
     * @return
     */
    @Nonbinding
    String value() default "";

}
