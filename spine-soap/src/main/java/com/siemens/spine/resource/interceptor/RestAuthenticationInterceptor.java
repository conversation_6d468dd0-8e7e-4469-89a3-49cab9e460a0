package com.siemens.spine.resource.interceptor;

import com.siemens.spine.logic.security.Authentication;
import com.siemens.spine.logic.security.KeycloakJwtAuthenticationConverter;
import com.siemens.spine.logic.security.SecurityConfig;
import com.siemens.spine.logic.security.SecurityContextHolder;
import com.siemens.spine.resource.interceptor.annotation.SecurityHolder;
import io.helidon.security.jwt.SignedJwt;
import io.netty.handler.codec.http.HttpHeaderNames;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Priority;
import javax.inject.Inject;
import javax.interceptor.AroundInvoke;
import javax.interceptor.Interceptor;
import javax.interceptor.InvocationContext;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.HttpHeaders;

/**
 * Because Helidon Security have not supported to hold the authentication,
 * so this interceptor will save the authentication into security holder
 * Expectation: The request have to pass the Helidon security authentication (using {@link io.helidon.security.annotations.Authenticated}
 *
 * <AUTHOR> <PERSON>uyen
 */
@Priority(InterceptorPriority.AUTHENTICATION_PRIORITY)
@Interceptor
@SecurityHolder
@Slf4j
public class RestAuthenticationInterceptor {

    @Inject
    private SecurityConfig securityConfig;

    @Context
    private HttpHeaders headers;

    @Inject
    private KeycloakJwtAuthenticationConverter converter;

    @AroundInvoke
    public Object saveAuthentication(InvocationContext invocationContext) throws Exception {
        if (!securityConfig.isEnable()) {
            return invocationContext.proceed();
        }

        String token = headers.getHeaderString(HttpHeaderNames.AUTHORIZATION.toString());
        if (StringUtils.isEmpty(token)) {
            return invocationContext.proceed();
        }

        SignedJwt signedJwt;
        try {
            signedJwt = SignedJwt.parseToken(token.substring(7));

            // save the authentication into security holder
            Authentication authentication = converter.parseJwt(signedJwt.getJwt());
            SecurityContextHolder.setAuthentication(authentication);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return invocationContext.proceed();
    }

}
