package com.siemens.spine.resource.soapws;

import com.siemens.spine.db.constant.Constants.Permission;
import com.siemens.spine.generated.toolchain.ComponentNeighborConnections;
import com.siemens.spine.logic.exception.ForbiddenException;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.security.SecurityCheck;
import com.siemens.spine.logic.service.ConnectionPointService;
import com.siemens.spine.resource.interceptor.annotation.SoapSecure;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;
import java.util.List;

@ApplicationScoped
@Slf4j
public class ConnectionPointWS extends AuthzWS {

    private final ConnectionPointService neighborConnectionService;

    @Inject
    public ConnectionPointWS(SecurityCheck securityCheck,
                             ConnectionPointService neighborConnectionService) {
        super(securityCheck);
        this.neighborConnectionService = neighborConnectionService;
    }

    @SoapSecure
    public List<ComponentNeighborConnections> getNeighborConnection(String token,
                                                                    String projectName,
                                                                    List<String> componentIds) {
        try {
            securityCheck.checkPermission(projectName, Permission.COMPONENT_VIEW);
            List<Long> listComponentId = componentIds.stream().map(Long::parseLong).toList();
            return neighborConnectionService.getNeighborConnection(projectName, listComponentId);
        } catch (SpineException | ForbiddenException e) {
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    @Transactional(TxType.REQUIRED)
    @SoapSecure
    public boolean setNeighborConnection(String token, String projectName,
                                         List<ComponentNeighborConnections> componentNeighborConnections) {
        try {
            securityCheck.checkPermission(projectName, Permission.COMPONENT_MODIFY);
            return neighborConnectionService.setNeighborConnection(projectName, componentNeighborConnections);
        } catch (SpineException | ForbiddenException e) {
            throw new IllegalArgumentException(e.getMessage());
        }
    }

}

