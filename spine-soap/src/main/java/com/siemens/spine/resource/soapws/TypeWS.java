package com.siemens.spine.resource.soapws;

import com.siemens.spine.db.constant.Constants;
import com.siemens.spine.db.constant.TypeAttribute;
import com.siemens.spine.generated.toolchain.Attribute;
import com.siemens.spine.generated.toolchain.Type;
import com.siemens.spine.logic.exception.ForbiddenException;
import com.siemens.spine.logic.security.SecurityCheck;
import com.siemens.spine.logic.service.TypeService;
import com.siemens.spine.resource.interceptor.annotation.SoapSecure;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.siemens.spine.db.constant.Constants.Permission.TYPE_CREATION;

/**
 * <AUTHOR> <PERSON>am
 * @version 1.0
 * @since 23/2/2023
 */
@ApplicationScoped
@Slf4j
public class TypeWS extends AuthzWS {

    private final TypeService typeService;

    @Inject
    public TypeWS(SecurityCheck securityCheck, TypeService typeService) {
        super(securityCheck);
        this.typeService = typeService;
    }

    @SoapSecure
    public boolean createTypes(String token, String projectName, List<Type> types) throws ForbiddenException {
        securityCheck.checkPermission(projectName, TYPE_CREATION);
        if (projectName == null) {
            log.error("Project name is undefined");
            return false;
        }

        if (types == null || types.isEmpty()) {
            log.error("No type was entered from request");
            return false;
        }

        try {
            typeService.createTypes(projectName, types);
            return true;
        } catch (Exception ex) {
            log.error(ex.getMessage());
            return false;
        }
    }

    @SoapSecure
    public Type getType(String token, String projectName, Type type) throws ForbiddenException {
        securityCheck.checkPermission(projectName, Constants.Permission.TYPE_VIEW);
        if (projectName == null) {
            log.error("Project name is undefined");
            return null;
        }

        if (type == null || type.getAttributes() == null || type.getAttributes().isEmpty()) {
            log.error("The type attribute was not defined");
            return null;
        }

        List<Attribute> typeAttributes = type.getAttributes();
        Map<String, String> attributeMap = mapAttribute(typeAttributes);
        String typeId = attributeMap.get(TypeAttribute.TYPE_ID.getName());
        if (typeId == null) {
            log.error("Type id could not be found from attribute");
            return null;
        }

        log.info("Find the type of project {} by the id {}", projectName, typeId);
        return typeService.getTypeById(projectName, typeId);
    }

    private Map<String, String> mapAttribute(List<Attribute> typeAttributes) {
        Map<String, String> map = new HashMap<>();
        for (Attribute attr : typeAttributes) {
            if (attr.getName() != null && attr.getValue() != null) {
                map.put(attr.getName(), attr.getValue());
            }
        }

        return map;
    }

}
