package com.siemens.spine.resource.soapws;

import com.siemens.spine.generated.toolchain.Project;
import com.siemens.spine.generated.toolchain.Type;
import com.siemens.spine.logic.dto.request.ProjectRequestDTO;
import com.siemens.spine.logic.exception.ForbiddenException;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.security.SecurityCheck;
import com.siemens.spine.logic.service.CounterService;
import com.siemens.spine.logic.service.ProjectService;
import com.siemens.spine.logic.service.TypeService;
import com.siemens.spine.resource.interceptor.annotation.SoapSecure;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.BadRequestException;
import java.util.List;

import static com.siemens.spine.db.constant.Constants.Permission.ADMIN_CREATE_NEW_PROJECT;

@ApplicationScoped
@Slf4j
public class ProjectWS extends AuthzWS {

    private final ProjectService projectService;

    private final TypeService typeService;

    private final CounterService counterService;

    @Inject
    public ProjectWS(SecurityCheck securityCheck,
                     ProjectService projectService,
                     TypeService typeService,
                     CounterService counterService) {
        super(securityCheck);
        this.projectService = projectService;
        this.typeService = typeService;
        this.counterService = counterService;
    }

    @SoapSecure
    public boolean setProject(String token, Project projectInfo) throws ForbiddenException {
        securityCheck.checkPermission(null, ADMIN_CREATE_NEW_PROJECT);
        if (projectInfo == null) {
            log.error("Input project could not be null");
            return false;
        }

        ProjectRequestDTO requestDTO = ProjectRequestDTO.builder()
                .projectName(projectInfo.getProjectID())
                .sapProjectKey(projectInfo.getSapProjectKey())
                .customerName(projectInfo.getCustomerName())
                .siteName(projectInfo.getSiteName())
                .type(projectInfo.getType())
                .mainProjectState(projectInfo.getMainProjectState())
                .build();

        try {
            projectService.createProject(requestDTO);
            return true;
        } catch (SpineException e) {
            log.error("Could not save project from SOAP API", e);
            return false;
        }
    }

    @SoapSecure
    public List<Project> getAllProjects(String token, Project project) {
        return (List<Project>) projectService.getAllProjects();
    }

    @SoapSecure
    public String getAllTypes(String token, String projectName, Type type) {
        return typeService.getAllTypes(projectName);
    }

    @SoapSecure
    public List<Long> getValuesFromNamedCounter(String token, int amount, String counterName) {
        try {
            return counterService.retrieveNextValuesFromCounter(amount, counterName);
        } catch (SpineException e) {
            log.error("Could not get value from counter", e);
            throw new BadRequestException(e);
        }
    }

}
