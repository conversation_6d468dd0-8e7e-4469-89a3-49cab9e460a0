package com.siemens.spine.resource.config;

import com.fasterxml.jackson.databind.ObjectMapper;

import javax.inject.Inject;
import javax.ws.rs.ext.ContextResolver;
import javax.ws.rs.ext.Provider;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 22/12/2022
 */
@Provider
public class CustomJacksonMapperProvider implements ContextResolver<ObjectMapper> {

    private final ObjectMapper mapper;

    @Inject
    public CustomJacksonMapperProvider(ObjectMapper objectMapper) {
        mapper = objectMapper;
    }

    @Override
    public ObjectMapper getContext(Class<?> type) {
        return mapper;
    }

}