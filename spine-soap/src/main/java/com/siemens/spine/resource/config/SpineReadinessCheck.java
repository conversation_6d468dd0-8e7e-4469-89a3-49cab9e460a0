package com.siemens.spine.resource.config;

import org.eclipse.microprofile.health.HealthCheck;
import org.eclipse.microprofile.health.HealthCheckResponse;
import org.eclipse.microprofile.health.Readiness;

import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.context.Initialized;
import javax.enterprise.event.Observes;
import java.time.Duration;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17/3/2023
 */
@Readiness
@ApplicationScoped
public class SpineReadinessCheck implements HealthCheck {

    private AtomicLong readyTime = new AtomicLong(0);

    @Override
    public HealthCheckResponse call() {
        return HealthCheckResponse.named("ReadinessCheckSpine")
                .state(isReady())
                .withData("time", readyTime.get())
                .build();
    }

    public void onStartUp(
            @Observes @Initialized(ApplicationScoped.class) Object init) {
        readyTime = new AtomicLong(System.currentTimeMillis());
    }

    /**
     * Become ready after 3 seconds
     *
     * @return true if application ready
     */
    private boolean isReady() {
        return Duration.ofMillis(System.currentTimeMillis() - readyTime.get()).getSeconds() >= 3;
    }

}
