package com.siemens.spine.resource.rest;

import com.siemens.spine.db.repository.paging.Page;
import com.siemens.spine.db.repository.paging.PageRequest;
import com.siemens.spine.db.repository.paging.Pageable;
import com.siemens.spine.db.repository.paging.Sort;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.service.RestEntityService;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.DefaultValue;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Optional;

/**
 * REST resource for dynamic CRUD operations on entities.
 * This resource provides generic REST endpoints for entities that are marked
 * for REST exposure through the {@link com.siemens.spine.db.repository.elcon.RestRepository} annotation.
 *
 * <p>All operations are delegated to the service layer to maintain proper
 * separation of concerns and encapsulate business logic.</p>
 *
 * <AUTHOR> Pham
 * @since 1.0
 */
@Path("/data-rest")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Slf4j
public class RestRepositoryResource {

    private final RestEntityService restEntityService;

    /**
     * Constructor for dependency injection.
     * Uses constructor injection instead of field injection for better testability
     * and to ensure all dependencies are properly initialized.
     *
     * @param restEntityService Service for handling REST entity operations
     */
    @Inject
    public RestRepositoryResource(RestEntityService restEntityService) {
        this.restEntityService = Objects.requireNonNull(restEntityService,
                "RestEntityService cannot be null");
    }

    /**
     * Retrieves all entities for the specified entity path.
     *
     * @param entityPath The entity path identifier
     * @return Response containing list of entities or error status
     */
    @GET
    @Path("/{entityPath}")
    public Response getPaginatedEntities(@PathParam("entityPath") String entityPath,
                                         @QueryParam("page") @DefaultValue("0") int page,
                                         @QueryParam("size") @DefaultValue("20") int size,
                                         @QueryParam("sort") String sort) {
        log.debug("GET request for all entities with path: {}", entityPath);

        try {
            Pageable pageable = createPageable(page, size, sort);
            Page<Object> entities = restEntityService.getPaginatedEntities(entityPath, pageable);
            log.debug("Retrieved {} entities for path: {}", entities.getSize(), entityPath);
            return Response.ok(entities).build();
        } catch (SpineException e) {
            return handleSpineException(e, "Failed to retrieve entities for path: " + entityPath);
        } catch (Exception e) {
            return handleUnexpectedException(e, "Unexpected error retrieving entities for path: " + entityPath);
        }
    }

    /**
     * Retrieves a single entity by its ID.
     *
     * @param entityPath The entity path identifier
     * @param idStr      The string representation of the entity ID
     * @return Response containing the entity or error status
     */
    @GET
    @Path("/{entityPath}/{id}")
    public Response getEntityById(@PathParam("entityPath") String entityPath,
                                  @PathParam("id") String idStr) {
        log.debug("GET request for entity with path: {} and ID: {}", entityPath, idStr);

        try {
            Optional<Object> entity = restEntityService.getEntityById(entityPath, idStr);
            if (entity.isPresent()) {
                log.debug("Found entity with ID: {} for path: {}", idStr, entityPath);
                return Response.ok(entity.get()).build();
            } else {
                log.debug("Entity not found with ID: {} for path: {}", idStr, entityPath);
                return Response.status(Response.Status.NOT_FOUND)
                        .entity("Entity not found with ID: " + idStr)
                        .build();
            }
        } catch (SpineException e) {
            return handleSpineException(e, "Failed to retrieve entity with ID: " + idStr);
        } catch (Exception e) {
            return handleUnexpectedException(e, "Unexpected error retrieving entity with ID: " + idStr);
        }
    }

    /**
     * Creates a new entity from JSON data.
     *
     * @param entityPath The entity path identifier
     * @param jsonBody   The JSON representation of the entity
     * @param uriInfo    URI information for location header generation
     * @return Response containing the created entity with location header or error status
     */
    @POST
    @Path("/{entityPath}")
    public Response createEntity(@PathParam("entityPath") String entityPath,
                                 String jsonBody,
                                 @Context UriInfo uriInfo) {
        log.debug("POST request to create entity with path: {}", entityPath);

        if (jsonBody == null || jsonBody.trim().isEmpty()) {
            log.warn("Empty JSON body provided for entity creation");
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("Request body cannot be empty")
                    .build();
        }

        try {
            Object savedEntity = restEntityService.createEntity(entityPath, jsonBody);

            Object entityId = restEntityService.extractEntityId(savedEntity);
            String entityIdStr = String.valueOf(entityId);
            String encodedEntityId = URLEncoder.encode(entityIdStr, StandardCharsets.UTF_8);

            URI location = uriInfo.getAbsolutePathBuilder()
                    .path(encodedEntityId)
                    .build();

            log.debug("Successfully created entity with ID: {} for path: {}", entityId, entityPath);
            return Response.created(location).entity(savedEntity).build();
        } catch (SpineException e) {
            return handleSpineException(e, "Failed to create entity for path: " + entityPath);
        } catch (Exception e) {
            return handleUnexpectedException(e, "Unexpected error creating entity for path: " + entityPath);
        }
    }

    /**
     * Updates an existing entity with new data from JSON.
     *
     * @param entityPath The entity path identifier
     * @param idStr      The string representation of the entity ID
     * @param jsonBody   The JSON representation of the updated entity
     * @return Response containing the updated entity or error status
     */
    @PUT
    @Path("/{entityPath}/{id}")
    public Response updateEntity(@PathParam("entityPath") String entityPath,
                                 @PathParam("id") String idStr,
                                 String jsonBody) {
        log.debug("PUT request to update entity with path: {} and ID: {}", entityPath, idStr);

        if (jsonBody == null || jsonBody.trim().isEmpty()) {
            log.warn("Empty JSON body provided for entity update");
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("Request body cannot be empty")
                    .build();
        }

        try {
            Object updatedEntity = restEntityService.updateEntity(entityPath, idStr, jsonBody);
            log.debug("Successfully updated entity with ID: {} for path: {}", idStr, entityPath);
            return Response.ok(updatedEntity).build();
        } catch (SpineException e) {
            return handleSpineException(e, "Failed to update entity with ID: " + idStr);
        } catch (Exception e) {
            return handleUnexpectedException(e, "Unexpected error updating entity with ID: " + idStr);
        }
    }

    /**
     * Deletes an entity by its ID.
     *
     * @param entityPath The entity path identifier
     * @param idStr      The string representation of the entity ID
     * @return Response with no content status or error status
     */
    @DELETE
    @Path("/{entityPath}/{id}")
    public Response deleteEntity(@PathParam("entityPath") String entityPath,
                                 @PathParam("id") String idStr) {
        log.debug("DELETE request for entity with path: {} and ID: {}", entityPath, idStr);

        try {
            restEntityService.deleteEntity(entityPath, idStr);
            log.debug("Successfully deleted entity with ID: {} for path: {}", idStr, entityPath);
            return Response.noContent().build();
        } catch (SpineException e) {
            return handleSpineException(e, "Failed to delete entity with ID: " + idStr);
        } catch (Exception e) {
            return handleUnexpectedException(e, "Unexpected error deleting entity with ID: " + idStr);
        }
    }

    /**
     * Handles SpineException by logging and creating appropriate error response.
     * Centralizes error handling for consistent response format.
     *
     * @param e          The SpineException that occurred
     * @param contextMsg Additional context message for logging
     * @return Response with appropriate error status and message
     */
    private Response handleSpineException(SpineException e, String contextMsg) {
        log.warn("{}: {}", contextMsg, e.getMessage());

        // Check if it's a "not found" type error
        if (e.getMessage().contains("not found") || e.getMessage().contains("Entity path not found")) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity(e.getMessage())
                    .build();
        }

        // For other SpineExceptions, return bad request
        return Response.status(Response.Status.BAD_REQUEST)
                .entity(e.getMessage())
                .build();
    }

    /**
     * Handles unexpected exceptions by logging and creating internal server error response.
     * Provides fallback error handling for unexpected situations.
     *
     * @param e          The unexpected exception that occurred
     * @param contextMsg Additional context message for logging
     * @return Response with internal server error status
     */
    private Response handleUnexpectedException(Exception e, String contextMsg) {
        log.error("{}: {}", contextMsg, e.getMessage(), e);
        return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity("Internal server error occurred")
                .build();
    }

    private Pageable createPageable(int page, int size, String sort) {
        Sort sortObj = Sort.unsorted();
        if (sort != null && !sort.isEmpty()) {
            String[] sortParams = sort.split(",");
            String property = sortParams[0];
            Sort.Direction direction = sortParams.length > 1 &&
                    "desc".equalsIgnoreCase(sortParams[1]) ?
                    Sort.Direction.DESC : Sort.Direction.ASC;
            sortObj = Sort.by(direction, property);
        }
        return PageRequest.of(page, size, sortObj);
    }

}