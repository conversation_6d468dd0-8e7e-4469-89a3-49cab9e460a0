<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>spine-core</artifactId>
        <groupId>com.siemens.spine</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>spine-soap</artifactId>
    <packaging>jar</packaging>

    <!-- Inheriting Java version from parent -->
    <properties>
        <mainClass>com.siemens.spine.resource.SpineCoreApplication</mainClass>
        <version.plugin.hibernate-enhance>${version.hibernate}</version.plugin.hibernate-enhance>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.siemens.spine</groupId>
            <artifactId>spine-logic</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.metro</groupId>
            <artifactId>helidon-mp</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.common</groupId>
            <artifactId>helidon-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.config</groupId>
            <artifactId>helidon-config-yaml-mp</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.microprofile.bundles</groupId>
            <artifactId>helidon-microprofile-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.microprofile</groupId>
            <artifactId>helidon-microprofile-cors</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.media</groupId>
            <artifactId>helidon-media-jsonb</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.microprofile.scheduling</groupId>
            <artifactId>helidon-microprofile-scheduling</artifactId>
        </dependency>

        <dependency>
            <groupId>io.helidon.microprofile</groupId>
            <artifactId>helidon-microprofile-security</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.microprofile</groupId>
            <artifactId>helidon-microprofile-oidc</artifactId>
        </dependency>

        <dependency>
            <groupId>io.helidon.microprofile.health</groupId>
            <artifactId>helidon-microprofile-health</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jboss</groupId>
            <artifactId>jandex</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-all</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.helidon.integrations.cdi</groupId>
            <artifactId>helidon-integrations-cdi-jpa</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.helidon.integrations.cdi</groupId>
            <artifactId>helidon-integrations-cdi-jta-weld</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.helidon.integrations.cdi</groupId>
            <artifactId>helidon-integrations-cdi-datasource-hikaricp</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.helidon.integrations.cdi</groupId>
            <artifactId>helidon-integrations-cdi-hibernate</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!--Using jackson-->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jersey.media</groupId>
            <artifactId>jersey-media-json-binding</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.eclipse</groupId>
                    <artifactId>yasson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.jaxrs</groupId>
            <artifactId>jackson-jaxrs-base</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jersey.media</groupId>
            <artifactId>jersey-media-json-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.jaxrs</groupId>
            <artifactId>jackson-jaxrs-json-provider</artifactId>
        </dependency>

        <!--Slf4j log-->
        <dependency>
            <groupId>io.helidon.logging</groupId>
            <artifactId>helidon-logging-slf4j</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.hsqldb/hsqldb -->
        <dependency>
            <groupId>org.hsqldb</groupId>
            <artifactId>hsqldb</artifactId>
            <version>2.7.1</version>
            <scope>test</scope>
        </dependency>

        <!-- Jakarta XML dependencies -->
        <dependency>
            <groupId>jakarta.xml.ws</groupId>
            <artifactId>jakarta.xml.ws-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.jws</groupId>
            <artifactId>jakarta.jws-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.activation</groupId>
            <artifactId>jakarta.activation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>jakarta.transaction</groupId>
            <artifactId>jakarta.transaction-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.vladmihalcea</groupId>
            <artifactId>hibernate-types-52</artifactId>
            <version>2.21.1</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>xml-maven-plugin</artifactId>
                <version>1.0.2</version>
                <executions>
                    <execution>
                        <id>prepare-test</id>
                        <goals>
                            <goal>transform</goal>
                        </goals>
                        <phase>process-test-resources</phase>
                        <configuration>
                            <transformationSets>
                                <transformationSet>
                                    <dir>${project.build.outputDirectory}/META-INF</dir>
                                    <outputDir>${project.build.outputDirectory}/META-INF</outputDir>
                                    <includes>persistence.xml</includes>
                                    <stylesheet>${project.basedir}/src/main/resources/stylesheet.xsl</stylesheet>
                                    <parameters>
                                        <parameter>
                                            <name>hibernate-dialect</name>
                                            <value>org.hibernate.dialect.HSQLDialect</value>
                                        </parameter>
                                        <parameter>
                                            <name>hbm2ddl-auto</name>
                                            <value>update</value>
                                        </parameter>
                                    </parameters>
                                </transformationSet>
                            </transformationSets>
                        </configuration>
                    </execution>
                    <execution>
                        <id>prepare-app-execution</id>
                        <goals>
                            <goal>transform</goal>
                        </goals>
                        <phase>prepare-package</phase>
                        <configuration>
                            <transformationSets>
                                <transformationSet>
                                    <dir>${project.build.outputDirectory}/META-INF</dir>
                                    <outputDir>${project.build.outputDirectory}/META-INF</outputDir>
                                    <includes>persistence.xml</includes>
                                    <stylesheet>${project.basedir}/src/main/resources/stylesheet.xsl</stylesheet>
                                    <parameters>
                                        <parameter>
                                            <name>hibernate-dialect</name>
                                            <value>org.hibernate.dialect.PostgreSQL10Dialect</value>
                                        </parameter>
                                        <parameter>
                                            <name>hbm2ddl-auto</name>
                                            <value>none</value>
                                        </parameter>
                                    </parameters>
                                </transformationSet>
                            </transformationSets>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-libs</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/libs</outputDirectory>
                            <overWriteReleases>false</overWriteReleases>
                            <overWriteSnapshots>false</overWriteSnapshots>
                            <overWriteIfNewer>true</overWriteIfNewer>
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jboss.jandex</groupId>
                <artifactId>jandex-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>make-index</id>
                        <goals>
                            <goal>jandex</goal>
                        </goals>
                        <phase>process-classes</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jvnet.jaxb2.maven2</groupId>
                <artifactId>maven-jaxb2-plugin</artifactId>
                <version>${version.plugin.jaxb}</version>
                <executions>
                    <execution>
                        <id>Generate persistence.xml Java objects</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generatePackage>io.helidon.archetypes.tests.jaxb</generatePackage>
                            <markGenerated>true</markGenerated>
                            <schemas>
                                <schema>
                                    <dependencyResource>
                                        <groupId>jakarta.persistence</groupId>
                                        <artifactId>jakarta.persistence-api</artifactId>
                                        <resource>javax/persistence/persistence_2_2.xsd</resource>
                                    </dependencyResource>
                                </schema>
                            </schemas>
                            <strict>false</strict>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.hibernate.orm.tooling</groupId>
                <artifactId>hibernate-enhance-maven-plugin</artifactId>
                <version>${version.plugin.hibernate-enhance}</version>
                <executions>
                    <execution>
                        <configuration>
                            <failOnError>true</failOnError>
                            <enableLazyInitialization>true</enableLazyInitialization>
                            <enableDirtyTracking>true</enableDirtyTracking>
                        </configuration>
                        <goals>
                            <goal>enhance</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>io.helidon.build-tools</groupId>
                <artifactId>helidon-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>third-party-license-report</id>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${version.plugin.compiler}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <compilerArgs>
                        <!-- This arg is necessary to retrieve the raw parameter name of a method -->
                        <arg>-parameters</arg>
                        <!-- MapStruct compiler arguments -->
                        <arg>-Amapstruct.defaultComponentModel=cdi</arg>
                        <arg>-Amapstruct.unmappedTargetPolicy=IGNORE</arg>
                    </compilerArgs>
                    <annotationProcessorPaths>
                        <!-- IMPORTANT: Lombok must come before MapStruct in the processor order -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${version.lombok}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${version.lombok-mapstruct-binding}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${version.mapstruct}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
