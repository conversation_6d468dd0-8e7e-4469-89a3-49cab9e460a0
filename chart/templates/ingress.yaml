{{- $ingress := .Values.ingress.kong -}}
{{- if $ingress.enabled -}}
{{ range $name, $rule := .Values.ingress.kong.rules }}
{{- if hasKey $rule "path" }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $.Values.fullName }}-{{ $name }}
  annotations:
    konghq.com/preserve-host: "false"
  {{- if hasKey $rule "stripPath" }}
    konghq.com/strip-path: "{{ $rule.stripPath }}"
  {{- end }}
spec:
  ingressClassName: kong
  rules:
    - http:
        paths:
          - backend:
              service:
                name: {{ $.Values.fullName }}
                port:
                  number: {{ $.Values.service.port }}
            path: {{ $rule.path }}
            pathType: {{ $rule.pathType | default "ImplementationSpecific" }}
---
{{- end -}}
{{- end -}}
{{- end }}
