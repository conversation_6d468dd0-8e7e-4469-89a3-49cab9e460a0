apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.fullName }}
  labels:
    {{- include "spine-core.labels" . | nindent 4 }}
    app: spine-core
    version: {{ .Values.dockerVersionName }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app: spine-core
    version: {{ .Values.dockerVersionName }}
