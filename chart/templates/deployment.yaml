apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.fullName }}
  labels:
    SecretsOperator: "true"
    SecretsOperatorUsers: "spine"
    CreateDB: "spine"
    {{- include "spine-core.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: spine-core
  template:
    metadata:
      labels:
        app: spine-core
        version: {{ .Values.dockerVersionName }}
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          {{- if eq "" .Values.branchName }}
          image: "{{ .Values.image }}:{{ .Values.dockerVersionName }}"
          {{- else }}
          image: "{{ .Values.image }}_{{ .Values.branchName }}:{{ .Values.dockerVersionName }}"
          {{- end }}
          imagePullPolicy: {{ if (.Values.pullPolicy) }}{{ .Values.pullPolicy }}{{- else }}IfNotPresent{{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          ports:
            - name: http
              containerPort: 8080
          env:
            - name: JAVA_OPTIONS
              value: "-Xmx4096m -Djavax.net.ssl.trustStore=/mnt/KoerberRoot.jks -Djavax.net.ssl.trustStorePassword=changeMe"
          livenessProbe:
            httpGet:
              path: /health/live
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health/live
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
          volumeMounts:
            - name: keycloak-keystore
              mountPath: /mnt/
      volumes:
        - name: keycloak-keystore
          secret:
            secretName: keycloak-keystore
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
