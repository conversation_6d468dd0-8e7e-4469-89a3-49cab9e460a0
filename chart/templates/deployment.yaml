apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.fullName }}
  labels:
    SecretsOperator: "true"
    SecretsOperatorUsers: "spine"
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: spine-core-frontend
  template:
    metadata:
      labels:
        app: spine-core-frontend
        version: {{ .Values.dockerVersionName }}
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          env:
            {{- range $name, $value := .Values.env }} 
            - name: {{ $name }}
              value: {{ $value }}
            {{- end}}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          {{- if eq "" .Values.branchName }}
          image: "{{ .Values.image }}:{{ .Values.dockerVersionName }}"
          {{- else }}
          image: "{{ .Values.image }}_{{ .Values.branchName }}:{{ .Values.dockerVersionName }}"
          {{- end }}
          imagePullPolicy: {{ if (.Values.pullPolicy) }}{{ .Values.pullPolicy }}{{- else }}IfNotPresent{{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          ports:
            - name: http
              containerPort: 80
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}

