fullName: "spine-core-frontend"

image: dkp-harbor.kscl.dev/spine-core-replacement/spine-core-frontend/spine-core-frontend
dockerVersionName: "latest"

service:
  type: ClusterIP
  port: 8080

replicaCount: 1

imagePullSecrets: []

podSecurityContext: {}
securityContext: {}

# if branchname is set, the image name will be suffixed with _<branchName>. Needed for gitlab-ci builds on branches
branchName: ""

# Overwrite the pull policy if needed. Default is IfNotPresent
# pullPolicy: IfNotPresent

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: "1"
    memory: 2Gi
  requests:
    cpu: 100m
    memory: 500Mi

nodeSelector: {}

tolerations: []

affinity: {}

env:
  API_URL: ""
  KEYCLOAK_URL: ""
  KEYCLOAK_REALM: ""
  KEYCLOAK_CLIENT_ID: ""

ingress:
  kong:
    enabled: true
    rules:
      spine-core:
        path: /spine-core-frontend
        pathType: Prefix
        stripPath: true

