fullName: "spine-core"

image: dkp-harbor.kscl.dev/spine-core-replacement/spine-core/spine-soap
dockerVersionName: "latest"

service:
  type: ClusterIP
  port: 8080

replicaCount: 1

imagePullSecrets: []

podSecurityContext: {}
securityContext: {}

# if branchname is set, the image name will be suffixed with _<branchName>. Needed for gitlab-ci builds on branches
branchName: ""

# Overwrite the pull policy if needed. Default is IfNotPresent
# pullPolicy: IfNotPresent

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: "2"
    memory: 4Gi
  requests:
    cpu: 200m
    memory: 1Gi

nodeSelector: {}

tolerations: []

affinity: {}

ingress:
  kong:
    enabled: true
    rules:
      spine-core:
        path: /spine-core
        pathType: Prefix
        stripPath: true
