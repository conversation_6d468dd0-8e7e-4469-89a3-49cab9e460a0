import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ConnectionPointsExternalComponent } from './pages/external/connection-points-external/connection-points-external.component';
import { DecompositionsComponent } from './pages/external/decompositions/decompositions.component';
import { StatusHistoryExternalComponent } from './pages/external/status-history-external/status-history-external.component';
import { AuthGuard } from './shared/utils/app.guard';

const routes: Routes = [
  {
    path: '',
    loadChildren: () =>
      import('./layout/layout.module').then((m) => m.LayoutModule),
      canActivate: [AuthGuard]
  },
  {
    path: 'decompositions',
    component: DecompositionsComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'status-history',
    component: StatusHistoryExternalComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'connection-points',
    component: ConnectionPointsExternalComponent,
    canActivate: [AuthGuard]
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
