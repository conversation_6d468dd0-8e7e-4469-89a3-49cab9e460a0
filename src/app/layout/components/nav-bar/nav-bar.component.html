<nav class="navbar navbar-expand-lg header-center pt-0 pb-0 navbar-customize">
    <div class="container-fluid header-center">
        <div class="collapse navbar-collapse header-center">
            <ul class="navbar-nav header-center">

                <li class="nav-item dropdown" [class.active-menu]="router.isActive('/projects', false)">
                    <a class="nav-link" href="#" role="button" [routerLink]="['/projects']">
                        Projects
                    </a>
                </li>

                <li class="nav-item dropdown" href="#" [class.nav-item-readonly]="!hasProjectSelected"
                    [class.active-menu]="router.isActive('/components', false)">
                    <a class="nav-link" role="button" [style.pointer-events]="hasProjectSelected ?'auto':'none'"
                        style="pointer-events: none;" [routerLink]="['/components']">
                        Components
                    </a>
                </li>

                <li class="nav-item dropdown" href="#" [class.nav-item-readonly]="!hasProjectSelected"
                    [class.active-menu]="router.isActive('/groups', false)">
                    <a class="nav-link" role="button" [style.pointer-events]="hasProjectSelected ?'auto':'none'"
                        style="pointer-events: none;" [routerLink]="['/groups']">
                        Groups
                    </a>
                </li>
                <li class="nav-item dropdown" href="#" [class.nav-item-readonly]="!hasProjectSelected"
                    [class.active-menu]="router.isActive('/types', false)">
                    <a class="nav-link" role="button" [style.pointer-events]="hasProjectSelected ?'auto':'none'"
                        style="pointer-events: none;" [routerLink]="['/types']">
                        Types
                    </a>
                </li>
                <li class="nav-item dropdown" [class.nav-item-readonly]="!hasProjectSelected"
                    [class.active-menu]="router.isActive('/administration', false)">
                    <a class="nav-link" role="button" [style.pointer-events]="hasProjectSelected ?'auto':'none'"
                        style="pointer-events: none;" [routerLink]="['/administration']">
                        Administration
                    </a>
                </li>

            </ul>
        </div>
    </div>
</nav>