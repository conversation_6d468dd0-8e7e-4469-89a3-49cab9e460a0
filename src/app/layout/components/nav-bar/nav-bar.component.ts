import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { PROJECT_ITEM_SELECTED } from 'src/app/shared/constants/common';
import { ProjectSelectedService } from 'src/app/shared/services/store/project.service';

@Component({
  selector: 'app-nav-bar',
  templateUrl: './nav-bar.component.html',
  styleUrls: ['./nav-bar.component.scss']
})
export class NavBarComponent implements OnInit {

  eventProjectSelectedSubscription: Subscription;
  hasProjectSelected: boolean = false;

  constructor(
    public router: Router,
    private readonly projectSelectedService: ProjectSelectedService,
    ) { }

  ngOnInit(): void {
    this.checkSelectedProject();
    // event when updating new main project
    this.eventProjectSelectedSubscription = this.projectSelectedService.getProjectSelected().subscribe(
      res => {
        if(res) {
          this.checkSelectedProject();
        }
      }
    )
  }

  checkSelectedProject() {
    const projectItem = localStorage.getItem(PROJECT_ITEM_SELECTED);
    if(projectItem) {
      this.hasProjectSelected = true;
    }
  }

  ngOnDestroy() {
    if (this.eventProjectSelectedSubscription) {
      this.eventProjectSelectedSubscription.unsubscribe();
    }
  }

}
