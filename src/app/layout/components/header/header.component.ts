import { APP_BASE_HREF } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';
import { Subscription } from 'rxjs';
import { PROJECT_ITEM_SELECTED, USER_LOGGED_IN_KEY } from 'src/app/shared/constants/common';
import { UserAccount } from 'src/app/shared/interface/account/user.interface';
import { ProjectModel } from 'src/app/shared/interface/projects/project.interface';
import { ProjectSelectedService } from 'src/app/shared/services/store/project.service';
import { environment } from 'src/environments/environment';


@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent implements OnInit {

  user: UserAccount | undefined;
  projectItemSelected: ProjectModel;
  projectIdSelected: string;
  projectVal: string = "Select main project";
  eventProjectSelectedSubscription: Subscription;

  constructor(
    private readonly keycloakService: KeycloakService,
    private readonly projectSelectedService: ProjectSelectedService,
    private readonly router: Router,
    @Inject(APP_BASE_HREF) public baseHref:string
  ) { }

  ngOnInit(): void {
    this.initializeUserOptions();
    this.initializeProjectSelected();

    // event when updating new main project
    this.eventProjectSelectedSubscription = this.projectSelectedService.getProjectSelected().subscribe(
      res => {
        if(res) {
          this.initializeProjectSelected();
        }
      }
    )
  }

  initializeUserOptions() {
    const userObject = localStorage.getItem(USER_LOGGED_IN_KEY);
    if(userObject) {
      this.user = JSON.parse(userObject);
    }
  }

  initializeProjectSelected() {
    const projectItem = localStorage.getItem(PROJECT_ITEM_SELECTED);
    if(projectItem) {
      this.projectItemSelected = JSON.parse(projectItem);
      this.projectVal = this.projectItemSelected.project_name + ' (' + this.projectItemSelected.sap_project_key + ')';
    }
  }

  logout() {
    localStorage.removeItem(USER_LOGGED_IN_KEY);
    localStorage.removeItem(PROJECT_ITEM_SELECTED);
    this.keycloakService.logout(window.location.origin + this.baseHref);
  }

  onclickLogo() {
    location.href = window.location.origin + this.baseHref;
  }

  goToProjectScreen() {
    this.router.navigate(['projects']);
  }

  ngOnDestroy() {
    if (this.eventProjectSelectedSubscription) {
      this.eventProjectSelectedSubscription.unsubscribe();
    }
  }

}
