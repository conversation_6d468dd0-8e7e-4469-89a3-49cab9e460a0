import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { USER_LOGGED_IN_KEY } from '../shared/constants/common';
import { UserAccount } from '../shared/interface/account/user.interface';
import { AdministrationService } from '../shared/services/api/administration.service';
import { LoadingService } from '../shared/services/store/loading.service';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LayoutComponent implements OnInit {
  userLoggedIn: UserAccount;

  constructor(
    private readonly adminService: AdministrationService,
    readonly loadingService: LoadingService
  ) {}

  async ngOnInit(): Promise<void> {
    await this.initializeUserOptions();
  }

  async initializeUserOptions(): Promise<void> {
    this.loadingService.setLoadingStatus(true);
    try {
      this.userLoggedIn = await this.adminService.getUserInfo().toPromise();
      if (this.userLoggedIn) {
        localStorage.setItem(
          USER_LOGGED_IN_KEY,
          JSON.stringify(this.userLoggedIn)
        );
      }
    } finally {
      setTimeout(() => {
        this.loadingService.setLoadingStatus(false);
      }, 100);
    }
  }
}
