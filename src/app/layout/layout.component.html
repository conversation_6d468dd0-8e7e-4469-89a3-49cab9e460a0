<ng-container *ngIf="userLoggedIn">
    <app-header></app-header>
    <app-content></app-content>
    <app-footer></app-footer>
</ng-container>

<div *ngIf="loadingService.getLoadingStatus() | async"
    class="loading d-flex flex-column justify-content-center align-items-center">
    <div class="spinner-border text-primary loading-spinner" role="status">
    </div>
    <div class="loading-title">Loading...</div>
</div>