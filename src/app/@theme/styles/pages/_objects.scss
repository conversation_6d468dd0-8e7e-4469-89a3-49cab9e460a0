.objects {
    &-container {
        margin: 12px 0 5px 0;
        background-color: transparent;
        max-width: 100%;
        height: auto;

        ul {
            button {
                color: $color-gray-77;
            }
            
            button:hover {
                color: $color-text;
            }

            .active {
                font-weight: 500;
                text-shadow: .25px 0px .1px,
                -.25px 0px .1px;
            }
        }
    }

    &-table {
        thead tr th {
            min-width: 150px !important;
        }

        tbody tr td {
            min-width: 150px !important;
        }
    }
}

.components {
    &-action {
        &-button {
            padding: 5px 15px;
            border: 1px solid $color-text;
            margin-right: 15px;
            border-radius: 5px;
            background-color: $color-white;

            &:hover {
                color: $color-white;
                background-color: $color-green-00;
                cursor: pointer;
            }

            &-disabled {
                background-color: $color-gray-f2;
                font-style: italic;
                &:hover {
                    color: inherit;
                    background-color: $color-gray-f2;
                    cursor: not-allowed;
                }
            }
        }
    }
}