.container-content {
    min-height: 100%;
    height: auto;
    background-color: $color-gray-fc;
    margin: 12px 12px 5px 12px;
    border-radius: 4px;
}

.content {
    max-width: 100%;
    height: auto;
    margin: 12px 12px 5px 12px;

    &-search {
        width: 25%;
        // min-height: 100%;
        margin-right: 15px;
        background-color: $color-gray-fc;
        border-radius: 4px;
        padding: 15px 20px;
    }

    &-value {
        width: 75%;
        // min-height: 100%;
        background-color: $color-white;
        border-radius: 4px;
        padding: 15px 20px;

        &-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }
    }
}

.vertical-content {
    max-width: 100%;
    height: auto;
    margin: 15px 0 5px 0;

    &-search {
        width: 100%;
        min-height: 70px;
        background-color: $color-gray-fc;
        border-radius: 4px;
        margin-bottom: 15px;
        padding: 15px 20px;

        &-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 15px;

            &-icon {
                font-size: 15px;
                margin-right: 10px;
            }
        }

        &-title:hover {
            color: $color-blue-BB;
        }

        &-title[aria-expanded=true] .bi-chevron-double-right {
            display: none;
         }
        
        &-title[aria-expanded=false] .bi-chevron-double-down {
            display: none;
        }
    }

    &-value {
        width: 100%;
        min-height: 100%;
        background-color: $color-gray-fc;
        border-radius: 4px;
        padding: 15px 20px;

        &-grid-group {
            border: 1px solid $color-gray-c7;
        }

        &-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;

            &-icon {
                font-size: 16px;
                margin-right: 10px;
            }
        }

        &-limit-title {
            font-size: 14px;
            font-weight: 500;
            margin-right: 5px;
            color: $color-gray-77;
        }


        &-table {
            max-height: 300px !important;
            overflow-y: scroll !important;
        }

        &-sub-title {
            font-size: $default-font-size;
            color: $color-gray-77;
        }
    }
}