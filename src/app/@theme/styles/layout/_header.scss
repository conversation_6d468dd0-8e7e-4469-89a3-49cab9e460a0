.header {
    background-color: $color-gray-fc;
    width: 100%;
    height: 70px;
    padding: 0 25px !important;

    &-text-logo {
        font-size: 29px;
        font-weight: 900;
        cursor: default;
        color: $color-blue-8a;
    }

    &-logo {
        width: 150px;
        height: 50px;
        margin-left: -25px;

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }

    &-nav {
        width: auto;
        height: auto;
    }

    &-account {
        width: auto;
        height: auto;

        &-icon {
            border-radius: 50%;
            font-size: 19px;
            width: 35px;
            height: 35px;
            color: $color-white;
            background-color: $color-blue-8a;
            margin-right: 10px;
        }

        &-expand {
            width: 270px;
            height: auto;
            background-color: $color-white;
            font-size: $default-font-size !important;
            color: $color-text !important;
            padding: 10px 15px !important;
            font-family: sans-serif !important;

            &-infor {
                line-height: 2;
                margin-bottom: 10px;
            }

            &-line {
                width: 80%;
                border-bottom: 1px solid $color-gray-d2;
            }

            &-logout {
                cursor: pointer;
                padding: 5px 0;
                color: $color-gray-d2;
            }

            &-logout:hover {
                color: #000;
            }
        }
    }

    &-project {
        position: relative;
        border: 0.5px solid $color-gray-cc;
        padding: 10px 35px;
        margin-right: 25px;
        border-radius: 25px;
        font-style: italic;
        font-size: 12px;
        color: $color-gray-77;
        font-weight: 600;
        cursor: pointer;
    }

    &-project:hover {
        border: 1px solid $color-gray-77;
        color: #000;
        cursor: pointer;
        background-color: rgba(96, 119, 187, 0.4);

        &::before {
            display: none;
        }
    }

    &-project::before {
        content: "Project";
        position: absolute;
        top: -10px;
        left: 5px;
        font-size: 12px;
        color: $color-gray-77;
        font-weight: 200;
        font-style: initial;
        z-index: 10;
        background-color: white;
    }
}