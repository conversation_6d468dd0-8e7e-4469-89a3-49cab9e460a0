.ag-header-viewport, .ag-pinned-left-header {
    background-color: $color-blue-heading-grid;
}

.grid {
    &-side-bar {
        max-width: 350px;
        width: auto;
        height: 660px;
        border: 0.5px solid $color-gray-d2;
        
        &-heading {
            border-left: 0.5px solid $color-gray-d2;
            background-color: $color-blue-heading-grid;
            width: 42px;
            
            :hover {
                font-weight: 500;
                color: $color-black-1f;
            }

            &-title {
                writing-mode: vertical-lr;
                padding: 25px 10px;
                cursor: pointer;


                &-active {
                    font-weight: 500;
                    color: $color-black-1f;
                }
            }
        }
    }

    &-tool-panel {
        width: 100%;
        text-align: center;

        &-heading {
            width: 100%;
            min-height: 96px;
            color: $color-black-1f;
            font-weight: 600;
            font-size: 13px;
            background-color: $color-blue-heading-grid;
        }

        &-content {
            padding: 15px;
            overflow-y: auto;
        }

        &-button {
            display: inline-block;
            width: 100%;
            padding: 10px 0;
            border-radius: 14px;
            color: $color-white;
            transition: all .2s;
            position: relative;
            overflow: hidden;
            z-index: 1;
            margin-bottom: 10px;
            &:after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: $color-green-BB;
                border-radius: 10px;
                z-index: -2;
            }
            &:before {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                width: 0%;
                height: 100%;
                background-color: darken($color-green-BB, 5%);
                transition: all .2s;
                border-radius: 10px;
                z-index: -1;
            }
            &:hover {
                color: $color-white;
                cursor: pointer;
                &:before {
                    width: 100%;
                }
            }
        }
    }

    &-link {
        color: $color-blue;
        font-weight: 600;
    }

    &-link:hover {
        text-decoration: underline;
        cursor: pointer;
    }

    &-item-deleted {
        background-color: rgba(128, 128, 128, .4);
        font-style: italic;
        opacity: 0.7;
    }
}