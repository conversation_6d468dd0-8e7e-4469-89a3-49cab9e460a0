.spine-button {
    border: none;
    padding: 5px 20px;
    color: $color-white;
    border-radius: 5px;

    span {
        margin-right: 10px;
        font-size: 16px;
    }

    &-search {
        background-color: $color-green-BB;    
    }

    &-reset {
        background-color: $color-blue-BB;
    }
}

.spine-button:hover {
    opacity: 0.9 !important;
}

.reload-icon {
    font-size: 22px;
	transition: transform .4s ease-in-out;
}

.reload-icon:hover {
	transform: rotate(180deg);
	cursor: pointer;
}

.button-to-do {
    font-size: 13px;
    padding: 2px 30px;
    border-radius: 25px;
    border: 0.5px solid $color-gray-cc;
    color: $color-gray-77;
    font-weight: 600;

    &:hover {
        border: 1px solid $color-gray-77;
        color: #000;
        cursor: pointer;
        background-color: rgba(96, 119, 187, 0.4);
    }
}