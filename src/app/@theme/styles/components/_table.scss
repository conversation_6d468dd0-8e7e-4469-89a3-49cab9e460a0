.styled-table {
    border-collapse: collapse;
    font-size: 0.9em;
    font-family: sans-serif !important;
    min-width: 400px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
    width: 100%;
    height: auto;

    table-layout: auto;

    tbody {
        display:block;
        height:570px;
        overflow:auto;
    }

    thead tr {
        display: block;
        width: auto;
        background-color: $color-blue-BB;
        color: $color-white;
        text-align: left;
    }
    // thead, tbody tr {
        // display:table;
        // width:100%;
        // table-layout:auto;

        // th {
            // width: auto;
        // }
    // }

}

// .styled-table thead tr {
//     background-color: $color-blue-BB;
//     color: $color-white;
//     text-align: left;
// }

// .styled-table tbody tr {
//     text-align: left;
// }

.styled-table th,
.styled-table td {
    padding: 12px 15px;
}

.styled-table tbody tr {
    border-bottom: 1px solid #dddddd;
}

.styled-table tbody tr:hover {
    font-weight: 500;
    color: $color-blue-8a; 
    background-color: $color-gray-dd !important;
}

.styled-table tbody tr:nth-of-type(even) {
    background-color: $color-gray-f2;
}

.text-no-result {
    margin: 10px;
    font-style: italic;
    font-size: 16px;
    color: $color-gray-77;
    font-weight: 500;
}



