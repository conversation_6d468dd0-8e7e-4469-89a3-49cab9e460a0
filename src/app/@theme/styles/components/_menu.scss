.menu_button {
    color: $color-text;
    border: 0;
    background-color: transparent;
    display: flex;
    align-items: center;
    font-size: $default-font-size;

    span {
        margin-left: 10px;
    }
}

.navbar-customize {

    .nav-item:hover {
        background-color: $color-gray-ed;
        // cursor: pointer;
    }

    .nav-item {
        padding: 0 25px;
        height: 100%;
        display: flex;
        align-items: center;
        
        .dropdown-item {
            color: $color-text;
            font-size: $default-font-size;
        }
    
        .dropdown-menu {
            line-height: 2;
        }
    
        &-readonly {
            font-style: italic;

            a {
                color: $color-gray-d2;
            }

            &:hover {
                background-color: transparent !important;
                a {
                    cursor: default !important;
                }
            }
        }
    }

    
}


.active-menu {
    border-bottom: 2px solid $color-blue-8a;

    a {
        font-weight: 600;
    }
}