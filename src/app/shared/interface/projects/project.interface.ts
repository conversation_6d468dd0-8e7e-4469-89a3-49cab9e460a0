export interface ProjectModel {
    project_id?: number
    project_name: string
    sap_project_key: string
    customer_name: string
    site_name: string
    type: string
    main_project_state: string
}

export interface ToDoModel {
    component_id: number
    description: string
    id: number
    material_type: string
    mps_prefix: string
    position_number: any
    quantity: number
    sap_material_number: string
    source: string
    standard_material: boolean
    unit_in_sap: string
    derived_from: any
}