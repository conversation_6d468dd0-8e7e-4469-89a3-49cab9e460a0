import { KeyV<PERSON>ueModel, SkippedReasonModel } from "./response.objects"

export interface AllowedStateRequest {
    component_ids: number[]
    project_id: number
}

export interface MoveBackwardRequest {
    component_ids: number[]
    new_state: any
    change_group: any
    project_id: number
}

export interface MoveBackwardResponse {
    total_skipped: number
    skipped_reason: SkippedReasonModel
    new_states: MoveBackwardNewState[]
}

export interface MoveBackwardNewState {
    id: number
    state : KeyValueModel
    username: string
    remark: string
    status_date: string
    component_id: number
    change_group: any
}