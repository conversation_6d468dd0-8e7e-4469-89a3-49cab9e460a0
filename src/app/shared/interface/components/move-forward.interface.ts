import { KeyValueModel, SkippedReasonModel } from "./response.objects"

export interface MoveForwardRequest {
    component_ids: number[]
    project_id: number
}

export interface MoveForwardResponse {
    total_skipped: number
    skipped_reason: SkippedReasonModel
    new_states: MoveForwardNewState[]
}

interface MoveForwardNewState {
    state: KeyValueModel
    username: string
    status_date: string
    component_id: number
}