import { KeyV<PERSON>ueModel, SkippedReasonModel } from "./response.objects"

export interface UnlockComponentsRequest {
    component_ids: number[]
    type_num: any
    project_id: number
    change_group: any
}

export interface UnlockComponentResponse {
    total_skipped: number
    skipped_reason: SkippedReasonModel
    new_states: UnlockComponentNewState[]
}

export interface UnlockComponentNewState {
    id: number
    state : KeyValueModel
    username: string
    status_date: string
    component_id: number
}