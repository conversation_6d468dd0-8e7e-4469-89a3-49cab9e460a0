export interface ComponentModel {
  unique_id: number;
  amount_buffer_elec: number;
  amount_buffer_mech: number;
  break_type: string;
  buffer_size: number;
  comment: string;
  curve_angle: number;
  curve_direction: string;
  curve_radius: number;
  drive_position: string;
  drive_station: string;
  height: number;
  length_total: number;
  load: number;
  motor_controller: string;
  motor_direction: string;
  motor_position: string;
  parent: string;
  plant_domain: string;
  pos_no: string;
  reference: string;
  rotation: number;
  rotation3_d: number;
  screen: string;
  section1_angle: number;
  section1_length: number;
  section2_angle: number;
  section2_length: number;
  section3_angle: number;
  section3_length: number;
  section4_angle: number;
  section4_length: number;
  slave_drive: string;
  slope: number;
  speed: string;
  start_stop_cycles: number;
  supplier: string;
  throughput: number;
  type: string;
  unit_reversible: boolean;
  usage: string;
  version: number;
  virtual: string;
  width: number;
  installation_hours: number;
  engineering_hours: number;
  color_group: string;
  akz: string;
  customer_akz: string;
  auto_cad_layer: string;
  user1: string;
  user2: string;
  user3: string;
  user4: string;
  user5: string;
  drive_side: string;
  angle_corr: string;
  level_start: string;
  level_end: string;
  control_nr: string;
  vault_instance: string;
  drawing_version: string;
  drawing_revision: string;
  outfit3_d: string;
  parcel_type_id: string;
  order_plant: string;
  design_plant: string;
  storage_conveyor: boolean;
  drive_pulley_diameter: number;
  drive_shaft_diameter: number;
  acceleration: number;
  current_state: {
    value: any;
    name: string;
  };
  component_status: {
    unique_id: number;
    emulation_state: number;
    emulation_state_date: string;
    it_state: number;
    it_state_date: string;
    simulation_state: number;
    simulation_state_date: string;
    calculation_state: number;
    calculation_state_date: string;
    bom_state: number;
    bom_state_date: string;
    row_num: any;
    sap_id: any;
    simulation_component_last_mod_date: string;
    object_xml_export_state: number;
    object_xml_export_state_date: string;
    electric_synchronization_state: number;
    electric_synchronization_date: string;
  };
  group_mapping: {
    drawing: string;
    delivery_batch: string;
    building_section: string;
    calculation_area: string;
    plc_area: string;
    sequence_group: string;
    line_name: string;
    screen: string;
    installation_section: string;
    project_view: any;
    estop_group: string;
  };
  connection_point_dtos: any;
  position_dto: any;
  decomposition_dtos: any;
  sys_mod_date: string;
}

export interface DecompositionModel {
  index: number;
  source?: string;
  mode?: any;
  change_description: string;
  derived_from: any;
  description: string;
  material_type: any;
  mpsprefix: any;
  position_number: number;
  quantity: number;
  sap_material_number: string;
  source_internal: string;
  standard_material: boolean;
  unit_in_sap: any;
  zoptions: any;
}

export interface ConnectionPointsModel {
  id: number;
  name: string;
  type: string;
  x: number;
  y: number;
  z: number;
  neighbor?: {
    assignment: string;
    gap: number;
    manual: boolean;
    neighbor_component_id: number;
    neighbor_connection_name: string;
    neighbor_type: string;
  };
}

export interface DecompositionResponse {
  mode: any;
  source: string;
  decompositions: DecompositionModel[];
}

export interface StatusHistoryResponse {
  change_group?: {
    description?: string;
    id: number;
    name: string;
  };
  component_id: number;
  id: number;
  remark?: string;
  state: {
    value: string;
    name: string;
  };
  status_date: string;
  username: string;
}

export interface ChangeGroupModel {
  id?: number;
  name: string;
  description: string;
}

export interface SkippedReasonModel {
  INVALID_STATUS: number;
  MARKED_AS_DELETED: number;
  OTHER: number;
}

export interface KeyValueModel {
  value: string;
  name: string;
}
