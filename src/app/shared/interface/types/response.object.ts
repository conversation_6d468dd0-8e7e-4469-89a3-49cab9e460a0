export interface TypeModel {
  type_id: string;
  main_type: string;
  sub_type: string;
  type_version: string;
  description: string;
  mech_material_cost_kpi: number;
  engineering_me_hkpi: number;
  engineering_hw_hpki: number;
  engineering_plc_hkpi: number;
  engineering_it_hkpi: number;
  project_id?: any;
  project_name?: string;
  url1?: string;
  url2?: string;
  url3?: string;
  url4?: string;
  url5?: string;
  type_variant: string;
  default_slope?: string;
  default_drive_count: number;
  version_number?: number;
  supplier: string;
  engineering_scada_hkpi: number;
  mps: string;
  installation_hkpi: number;
  plc_commissioning_hkpi: number;
  belt_mass: number;
  required_acceleration: number;
  drive_pulley_diameter: number;
  friction_factor: number;
  drive_shaft_diameter: number;
  efficiency_gearbox: number;
  type_aggregator: string;
  project_type_classification: string;
  hierarchy_type: string;
  outdated: boolean;
  solution_id: any;
  project_specific: boolean;
  original_type?: string;
  sap_material_number?: number;
  special_type?: string;
  creation_date?: string;
  modification_date?: string;
  engineering_hw_one_off_costs: number;
  engineering_it_one_off_costs: number;
  engineering_me_one_off_costs: number;
  engineering_plc_one_off_costs: number;
  engineering_scada_one_off_costs: number;
}

export interface TypeDeleteResponse {
  fail: number;
  success: number;
}

export interface MetaDataTypeResponse {
  project_type_classification: string[];
  type_aggregator: string[];
}

export type TypesValue = 'Specific' | 'Generic';

export const HierarchyType = ['Carousel', 'Conveyor', 'FunctionGroup', 'InductionGroup', 'OutletGroup', 'Simple', 'Sorter'];