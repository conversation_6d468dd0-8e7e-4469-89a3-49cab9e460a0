import { environment } from "src/environments/environment";

export const API_URL = {
    // PROJECT
    GET_ALL_PROJECT: `${environment.apiUrl}/projects`,
    GET_ALL_PROJECT_TYPES: `${environment.apiUrl}/projects/type`,
    GET_ALL_PROJECT_STATES: `${environment.apiUrl}/projects/state`,
    CREATE_PROJECT: `${environment.apiUrl}/projects/create`,
    MOVE_PROJECT_STATUS: `${environment.apiUrl}/projects/move-forward`,
    UNFINISHED_MATERIAL: `${environment.apiUrl}/projects/unfinished-material`,

    //COMPONENT
    GET_COMPONENT: `${environment.apiUrl}/components`,
    COMPONENT_SET_OBJECT_TO_DELETE: `${environment.apiUrl}/components/mark-deleted`,
    COMPONENT_ELIMINATE_OBJECT_FROM_DATABASE: `${environment.apiUrl}/components/delete`,
    MOVE_FORWARD_COMPONENTS: `${environment.apiUrl}/components/move-forward`,
    ALLOWED_STATE_COMPONENTS: `${environment.apiUrl}/components/move-backward/allowed-state`,
    MOVE_BACKWARD_COMPONENTS: `${environment.apiUrl}/components/move-backward`,
    UNLOCK_COMPONENTS: `${environment.apiUrl}/components/undelete`,

    // GROUP
    GET_GROUPS: `${environment.apiUrl}/groups`,
    CHANGE_GROUP: `${environment.apiUrl}/change-group`,
    ASSIGN_DELIVERY_DATE: `${environment.apiUrl}/groups/assign-delivery-date`,

    // TYPE
    GET_TYPES: `${environment.apiUrl}/types`,
    DELETE_TYPES: `${environment.apiUrl}/types/delete`,
    META_DATA_TYPES: `${environment.apiUrl}/types/metadata`,
    
    // ADMINISTRATION
    GET_USER_INFO: `${environment.apiUrl}/iam/user-info`,
    GET_MAPPING: `${environment.apiUrl}/iam`,
    GET_AD_GROUPS: `${environment.apiUrl}/iam/user-group`,
    GET_ROLES: `${environment.apiUrl}/iam/role`,
    GET_ACCESS_ROLE: `${environment.apiUrl}/iam/role/project`,
    CREATE_MAPPING: `${environment.apiUrl}/iam/create`,
    UPDATE_MAPPING: `${environment.apiUrl}/iam/update`,
    DELETE_MAPPING: `${environment.apiUrl}/iam/delete`,
}