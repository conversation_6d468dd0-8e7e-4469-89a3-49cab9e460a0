export const getDateFromDateString = (params: string, format?: string, includeTime?: boolean) => {
  if (!params || params === '') {
    return '';
  }
  const date = new Date(params);

  if(format === 'dd-mm-yyyy') {
    return (
      str_pad(date.getDate()) + '-' + str_pad((date.getMonth() + 1)) + '-' + date.getFullYear() + ( includeTime ? (' ' + str_pad(date.getHours()) + ':' + str_pad(date.getMinutes())) : '' )
    );
  }
  
  if(format === 'yyyy-mm-dd') {
    return (
      date.getFullYear() + '-' + str_pad((date.getMonth() + 1)) + '-' + str_pad(date.getDate()) + ( includeTime ? (' ' + str_pad(date.getHours()) + ':' + str_pad(date.getMinutes())) : '' )
    );
  }

  return '';

};

export const getTimeFromDateString = (params: string) => {
  if (!params || params === '') {
    return '';
  }
  const date = new Date(params);

  return str_pad(date.getHours()) + ':' + str_pad(date.getMinutes());
};

export function str_pad(n: any) {
  return String("0" + n).slice(-2);
}
