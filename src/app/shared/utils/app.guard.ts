import { APP_BASE_HREF } from '@angular/common';
import { Inject, Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import {
  KeycloakAuthGuard,
  KeycloakEventType,
  KeycloakService,
} from 'keycloak-angular';
import { ExpiredService } from '../services/toasts/expired.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard extends KeycloakAuthGuard {
  constructor(
    protected override readonly router: Router,
    protected readonly keycloak: KeycloakService,
    @Inject(APP_BASE_HREF) public baseHref: string,
    private readonly expiredService: ExpiredService
  ) {
    super(router, keycloak);

    // Listen event when token is expired
    this.keycloak.keycloakEvents$.subscribe({
      next: (e) => {
        if (e.type === KeycloakEventType.OnTokenExpired) {
          this.keycloak.updateToken(20).catch((error) => {});
        } else if (e.type === KeycloakEventType.OnAuthRefreshError) {
          this.expiredService.initExpiredDialog();
        }
      },
    });
  }

  public async isAccessAllowed(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<boolean> {
    // Force the user to log in if currently unauthenticated.
    if (!this.authenticated) {
      await this.keycloak.login({
        redirectUri: window.location.origin + this.baseHref,
      });
    }

    // Get the roles required from the route.
    const requiredRoles = route.data['roles'];

    // Allow the user to proceed if no additional roles are required to access the route.
    if (!(requiredRoles instanceof Array) || requiredRoles.length === 0) {
      return true;
    }

    // Allow the user to proceed if all the required roles are present.
    return requiredRoles.every((role) => this.roles.includes(role));
  }
}
