import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { SkippedReasonModel } from '../../interface/components/response.objects';
import { StatusTypeInterface } from '../../interface/status/status.type';

@Injectable({
  providedIn: 'root',
})
export class ToastsService {
  constructor(private readonly toastr: ToastrService) {}

  showToastr(
    type: StatusTypeInterface,
    message: string,
    title: string,
    timeOut: number = 5000
  ) {
    if (type === 'success') {
      this.toastr.success(message, title, {
        timeOut: timeOut,
        progressBar: true,
      });
    } else if (type === 'warning') {
      this.toastr.warning(message, title, {
        timeOut: timeOut,
        progressBar: true,
      });
    } else if (type === 'error') {
      this.toastr.error(message, title, {
        timeOut: timeOut,
        progressBar: true,
      });
    }
  }

  showCustomForComponentToast(
    type: StatusTypeInterface,
    title: string,
    timeOut: number,
    reason: SkippedReasonModel,
    totalSkipped: number
  ) {
    let message = `Could not update ${totalSkipped} component(s) because of:`;
    if (reason.MARKED_AS_DELETED && reason.MARKED_AS_DELETED > 0) {
      message +=
        '<div>' + 'Already marked as deleted: ' + reason.MARKED_AS_DELETED + '</div>';
    }
    if (reason.INVALID_STATUS && reason.INVALID_STATUS > 0) {
      message += '<div>' + 'Invalid status: ' + reason.INVALID_STATUS + '</div>';
    }
    if (reason.OTHER && reason.OTHER) {
      message += '<div>' + 'Other: ' + reason.OTHER + '</div>';
    }

    if (type === 'success') {
      this.toastr.success(message, title, {
        timeOut: timeOut,
        progressBar: true,
        enableHtml: true,
      });
    } else if (type === 'warning') {
      this.toastr.warning(message, title, {
        timeOut: timeOut,
        progressBar: true,
        enableHtml: true,
      });
    } else if (type === 'error') {
      this.toastr.error(message, title, {
        timeOut: timeOut,
        progressBar: true,
        enableHtml: true,
      });
    }
  }
}
