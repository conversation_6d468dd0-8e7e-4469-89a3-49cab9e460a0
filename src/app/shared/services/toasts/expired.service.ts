import { APP_BASE_HREF } from '@angular/common';
import { Inject, Injectable } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';
import { ConfirmationDialogService } from '../../components/confirmation-dialog/confirmation-dialog.service';
import {
  PROJECT_ITEM_SELECTED,
  USER_LOGGED_IN_KEY,
} from '../../constants/common';

@Injectable({
  providedIn: 'root',
})
export class ExpiredService {
  constructor(
    protected readonly keycloak: KeycloakService,
    private readonly confirmationDialogService: ConfirmationDialogService,
    @Inject(APP_BASE_HREF) public baseHref: string
  ) {}

  initExpiredDialog() {
    this.confirmationDialogService
      .confirm(
        'Session Expired',
        'Your session has expired. Please login to start a new session',
        false,
        'OK'
      )
      .then((confirmed) => {
        if (confirmed) {
          localStorage.removeItem(USER_LOGGED_IN_KEY);
          localStorage.removeItem(PROJECT_ITEM_SELECTED);
          this.keycloak.logout(window.location.origin + this.baseHref);
        }
      })
      .catch((error) => {});
  }
}
