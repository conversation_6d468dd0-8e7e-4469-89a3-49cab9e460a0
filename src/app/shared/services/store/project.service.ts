import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ProjectSelectedService {
  private readonly eventProjectId$: Subject<boolean> =
    new BehaviorSubject<boolean>(false);

  getProjectSelected() {
    return this.eventProjectId$.asObservable();
  }

  setProjectSelected(data: boolean) {
    this.eventProjectId$.next(data);
  }
}
