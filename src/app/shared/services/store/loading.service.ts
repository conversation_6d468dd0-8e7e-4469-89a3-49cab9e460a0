import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class LoadingService {
  private readonly eventLoading$: BehaviorSubject<boolean> =
    new BehaviorSubject<boolean>(false);

  getLoadingStatus() {
    return this.eventLoading$.asObservable();
  }

  setLoadingStatus(data: boolean) {
    this.eventLoading$.next(data);
  }
}
