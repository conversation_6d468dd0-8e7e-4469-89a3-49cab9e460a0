import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { API_URL } from '../../constants/APIs';
import { headers } from '../../headers/request-options';

@Injectable({
  providedIn: 'root',
})
export class ProjectsService {
  constructor(private readonly httpClient: HttpClient) {}

  public getListAllProject(params: any): Observable<any[]> {
    return this.httpClient.post<any[]>(`${API_URL.GET_ALL_PROJECT}`, params, headers);
  }

  public getListAllProjectTypes(): Observable<any[]> {
    return this.httpClient.get<any[]>(`${API_URL.GET_ALL_PROJECT_TYPES}`, headers);
  }

  public getListAllProjectStates(): Observable<any[]> {
    return this.httpClient.get<any[]>(`${API_URL.GET_ALL_PROJECT_STATES}`, headers);
  }

  public createProject(rerBody: any): Observable<any> {
    return this.httpClient.post<any>(`${API_URL.CREATE_PROJECT}`,rerBody , headers);
  }

  public moveStatus(projectId: number, status: string): Observable<any> {
    return this.httpClient.put<any>(`${API_URL.MOVE_PROJECT_STATUS}?project_id=${projectId}&status=${status}`, {} , headers);
  }

  public getUnfinishedMaterial(projectId?: number): Observable<any[]> {
    return this.httpClient.get<any[]>(`${API_URL.UNFINISHED_MATERIAL}?project_id=${projectId}`, headers);
  }
}
