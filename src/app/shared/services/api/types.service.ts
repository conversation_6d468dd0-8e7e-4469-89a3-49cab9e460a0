import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { API_URL } from '../../constants/APIs';
import { headers } from '../../headers/request-options';
import { TypeModel } from '../../interface/types/response.object';

@Injectable({
  providedIn: 'root',
})
export class TypesService {
  constructor(private readonly httpClient: HttpClient) {}

  public getListAllTypes(projectId: number): Observable<any[]> {
    return this.httpClient.get<any[]>(`${API_URL.GET_TYPES}?project_id=${projectId}`, headers);
  }

  public getListMetaDataTypes(): Observable<any> {
    return this.httpClient.get<any>(`${API_URL.META_DATA_TYPES}`, headers);
  }

  public editTypes(reqBody?: TypeModel | null | undefined): Observable<any> {
    return this.httpClient.put<any>(`${API_URL.GET_TYPES}/${reqBody?.type_id}`, reqBody, headers);
  }

  public createGenericType(reqBody?: TypeModel | null | undefined): Observable<any> {
    return this.httpClient.post<any>(`${API_URL.GET_TYPES}`, reqBody, headers);
  }

  public deleteTypes(reqBody: any[]): Observable<any> {
    return this.httpClient.put<any>(`${API_URL.DELETE_TYPES}`, reqBody, headers);
  }
}
