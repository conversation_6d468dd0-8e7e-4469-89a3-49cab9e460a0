import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { API_URL } from '../../constants/APIs';
import { headers } from '../../headers/request-options';
import { AllowedStateRequest } from '../../interface/components/move-backward.interface';
import { MoveForwardRequest } from '../../interface/components/move-forward.interface';

@Injectable({
  providedIn: 'root',
})
export class ComponentsService {
  constructor(private readonly httpClient: HttpClient) {}

  public getComponentByCriteria(projectId?: number): Observable<any> {
    return this.httpClient.get<any>(`${API_URL.GET_COMPONENT}?project_id=${projectId}`, headers);
  }

  public getDecompositionsByComponentId(componentId: string | number): Observable<any[]> {
    return this.httpClient.get<any[]>(`${API_URL.GET_COMPONENT}/${componentId}/decompositions`, headers);
  }

  public getConnectionPointsByComponentId(componentId: string | number): Observable<any[]> {
    return this.httpClient.get<any[]>(`${API_URL.GET_COMPONENT}/${componentId}/connection-points`, headers);
  }

  public moveForwardComponents(reqBody: MoveForwardRequest): Observable<any> {
    return this.httpClient.put<any>(`${API_URL.MOVE_FORWARD_COMPONENTS}`, reqBody, headers);
  }

  public getAllowedStateByComponentIds(reqBody: AllowedStateRequest): Observable<any[]> {
    return this.httpClient.post<any[]>(`${API_URL.ALLOWED_STATE_COMPONENTS}`, reqBody, headers);
  }

  public getListChangeGroupByProjectId(projectId?: number): Observable<any[]> {
    return this.httpClient.get<any[]>(`${API_URL.CHANGE_GROUP}?project_id=${projectId}`, headers);
  }

  public moveBackwardComponents(reqBody: any): Observable<any> {
    return this.httpClient.put<any>(`${API_URL.MOVE_BACKWARD_COMPONENTS}`, reqBody, headers);
  }

  public unlockComponents(reqBody: any): Observable<any> {
    return this.httpClient.put<any>(`${API_URL.UNLOCK_COMPONENTS}`, reqBody, headers);
  }

  public setObjectToDelete(componentIds: any[]): Observable<any> {
    return this.httpClient.put<any>(`${API_URL.COMPONENT_SET_OBJECT_TO_DELETE}`, componentIds, headers);
  } 

  public eliminateObjectFromDatabase(componentIds: any[]): Observable<any> {
    return this.httpClient.put<any>(`${API_URL.COMPONENT_ELIMINATE_OBJECT_FROM_DATABASE}`, componentIds, headers);
  } 

  public getStatusHistoryByComponentId(componentId: string | number): Observable<any[]> {
    return this.httpClient.get<any[]>(`${API_URL.GET_COMPONENT}/${componentId}/state-history`, headers);
  }
}
