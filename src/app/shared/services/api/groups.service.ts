import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { API_URL } from '../../constants/APIs';
import { headers } from '../../headers/request-options';

@Injectable({
  providedIn: 'root',
})
export class GroupsService {
  constructor(private readonly httpClient: HttpClient) {}

  public getListAllGroups(projectId: number): Observable<any[]> {
    return this.httpClient.get<any[]>(`${API_URL.GET_GROUPS}?project_id=${projectId}`, headers);
  }

  public assignDeliveryDate(reqBody: any, projectId: number): Observable<any[]> {
    return this.httpClient.put<any[]>(`${API_URL.ASSIGN_DELIVERY_DATE}?project_id=${projectId}`, reqBody, headers);
  }

  public deleteGroup(groupId: number): Observable<any> {
    return this.httpClient.delete<any>(`${API_URL.GET_GROUPS}?group_ids=${groupId}`, headers);
  }
}
