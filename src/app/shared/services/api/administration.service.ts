import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { API_URL } from '../../constants/APIs';
import { headers } from '../../headers/request-options';

@Injectable({
  providedIn: 'root',
})
export class AdministrationService {
  constructor(private readonly httpClient: HttpClient) {}

  public getUserInfo(): Observable<any> {
    return this.httpClient.get<any>(`${API_URL.GET_USER_INFO}`, headers);
  }

  public getListAllMapping(projectId: number): Observable<any[]> {
    return this.httpClient.get<any[]>(`${API_URL.GET_MAPPING}?project_id=${projectId}`, headers);
  }

  public getListADGroup(): Observable<any[]> {
    return this.httpClient.get<any[]>(`${API_URL.GET_AD_GROUPS}`, headers);
  }

  public getListRoles(): Observable<any[]> {
    return this.httpClient.get<any[]>(`${API_URL.GET_ROLES}`, headers);
  }

  public getAccessRole(projectId?: number): Observable<any> {
    return this.httpClient.get<any>(`${API_URL.GET_ACCESS_ROLE}/${projectId}`, headers);
  }

  public createMapping(rerBody: any): Observable<any> {
    return this.httpClient.post<any>(`${API_URL.CREATE_MAPPING}`,rerBody , headers);
  }

  public updateMapping(rerBody: any, mappingId?: number): Observable<any> {
    return this.httpClient.put<any>(`${API_URL.UPDATE_MAPPING}?group_role_id=${mappingId}`,rerBody , headers);
  }

  public deleteMapping(rerBody: any): Observable<any> {
    return this.httpClient.put<any>(`${API_URL.DELETE_MAPPING}`, rerBody, headers);
  }
}
