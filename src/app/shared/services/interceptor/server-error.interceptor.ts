import { APP_BASE_HREF } from '@angular/common';
import {
  Http<PERSON><PERSON>,
  <PERSON>ttp<PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ExpiredService } from '../toasts/expired.service';
import { ToastsService } from '../toasts/toasts.service';

@Injectable()
export class ServerErrorInterceptor implements HttpInterceptor {
  constructor(
    public router: Router,
    @Inject(APP_BASE_HREF) public baseHref: string,
    private readonly toastsService: ToastsService,
    private readonly expiredService: ExpiredService
  ) {}

  errorTimer: any;
  lastErrorMessage: any;

  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(
      catchError((error: any) => {
        if (error.status === 401 || error.statusText === 'Unauthorized') {
          this.expiredService.initExpiredDialog();
        } else if (
          (error.status === 400 || error.status === 403) &&
          error.error.error_message
        ) {
          this.lastErrorMessage = error.error.error_message;
          if (!this.errorTimer) {
            this.errorTimer = setTimeout(() => {
              this.toastsService.showToastr(
                'error',
                this.lastErrorMessage,
                '',
                2500
              );
              this.errorTimer = null;
              this.lastErrorMessage = null;
            }, 100);
          }
        } else {
          this.toastsService.showToastr(
            'error',
            'An error occurred!',
            '',
            2500
          );
        }

        return of(error);
      })
    );
  }
}
