import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { PROJECT_ITEM_SELECTED } from '../../constants/common';
import { ProjectModel } from '../../interface/projects/project.interface';

@Injectable()
export class RequestHeaderInterceptor implements HttpInterceptor {
  constructor() {}
  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    const projectSelected = localStorage.getItem(PROJECT_ITEM_SELECTED);
    if (projectSelected) {
      const objProjectSelected: ProjectModel = JSON.parse(projectSelected);

      if (objProjectSelected) {
        // Clone the request and add new header parameters
        const modifiedReq = req.clone({
          setHeaders: {
            'X-PID': encodeURIComponent(objProjectSelected.project_name),
          },
        });

        // Pass the modified request to the next interceptor or to the backend
        return next.handle(modifiedReq);
      } else {
        // Pass the original request to the next interceptor or to the backend
        return next.handle(req);
      }
    }

    return next.handle(req);
  }
}
