import { Directive, ElementRef, HostListener } from '@angular/core';

@Directive({
  selector: '[appNumericOnly]',
})
export class NumericOnlyDirective {
  constructor(private readonly el: ElementRef) {}

  @HostListener('keypress', ['$event'])
  onKeyPress(event: KeyboardEvent) {
    const input = this.el.nativeElement as HTMLInputElement;
    const currentValue = input.value;
    const newValue = currentValue + event.key;
    const isValid = /^[0-9]*\.?[0-9]*$/.test(newValue); // Check if the new value is a valid number with optional decimal point
    if (!isValid) {
      event.preventDefault(); // Prevent input of invalid characters
    }
  }
}
