import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ConfirmationDialogComponent } from './confirmation-dialog/confirmation-dialog.component';
import { ConfirmationDialogService } from './confirmation-dialog/confirmation-dialog.service';
import { PaginationComponent } from './pagination/pagination.component';
import { SpineAutocompleteComponent } from './spine-autocomplete/spine-autocomplete.component';
import { SplashScreenComponent } from './splash-screen/splash-screen.component';
import { SplashScreenService } from './splash-screen/splash-screen.service';

@NgModule({
  imports: [CommonModule, ReactiveFormsModule, FormsModule],
  exports: [
    SpineAutocompleteComponent,
    PaginationComponent,
    ConfirmationDialogComponent,
    SplashScreenComponent
  ],
  declarations: [
    SpineAutocompleteComponent,
    PaginationComponent,
    ConfirmationDialogComponent,
    SplashScreenComponent,
  ],
  providers: [ConfirmationDialogService, SplashScreenService],
})
export class SharedComponentsModule {}
