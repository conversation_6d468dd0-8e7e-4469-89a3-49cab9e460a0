import { Component, OnInit, OnChanges, Input, Output, EventEmitter, SimpleChanges } from '@angular/core';
import { IPagination } from '../../interface/pagination/pagination';


@Component({
  // tslint:disable-next-line:component-selector
  selector: 'cpd-pagination',
  templateUrl: './pagination.component.html'
})
export class PaginationComponent implements OnInit {
  @Input() props: IPagination;

  @Output() onChangePage = new EventEmitter<string>();

  constructor() {

  }

  ngOnInit(): void {

  }

  ngOnChanges(changes: SimpleChanges): void {

  }

  plusStr(val1: any, val2: any) {
    return Number(val1) + Number(val2);
  }

  convertToNumber(value: any){
    return Number(value);
  }

  next(): void {
    this.onChangePage.emit('next');
  }

  prev(): void {
    this.onChangePage.emit('prev');
  }
}
