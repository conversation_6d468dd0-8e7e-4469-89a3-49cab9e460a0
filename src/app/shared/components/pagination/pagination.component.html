<div *ngIf="props" class="paginator d-flex align-items-center">
  <span *ngIf="props.total > 0" class="page-counter small mr-10 font-style-italic fw-600">
    Number of results: {{convertToNumber(props.total)}} ({{plusStr(props.offset , 1)}} - {{ plusStr(props.offset,
    props.limit) <= convertToNumber(props.total) ? (convertToNumber(props.total) < convertToNumber(props.limit) ?
      props.total : plusStr(props.offset , props.limit)) : props.total }}) </span>
      <ul class="pagination pagination-sm mb-0 ml-2">
        <li class="page-item" [ngClass]="{'disabled': convertToNumber(props.offset) == 0}">
          <button class="page-link" href="#" aria-label="Previous" [disabled]="convertToNumber(props.offset) == 0"
            (click)="prev()">
            <span aria-hidden="true" class="mr-5">&laquo;</span>
            <span class="sr-only small">Previous</span>
          </button>
        </li>
        <li class="page-item" [ngClass]="{'disabled': plusStr(props.offset, props.limit) >= convertToNumber(props.total)}">
          <button class="page-link" href="#" aria-label="Next"
            [disabled]="plusStr(props.offset , props.limit)  >= convertToNumber(props.total)" (click)="next()">
            <span aria-hidden="true" class="mr-5">&raquo;</span>
            <span class="sr-only small">Next</span>
          </button>
        </li>
      </ul>
</div>