import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationDialogComponent } from './confirmation-dialog.component';

@Injectable()
export class ConfirmationDialogService {
  constructor(private readonly modalService: NgbModal) {}

  public confirm(
    title: string,
    message: string,
    showCloseBtn: boolean = true,
    btnOkText?: string,
    btnCancelText?: string,
    dialogSize: 'sm' | 'lg' | 'xl' = 'sm'

  ): Promise<boolean> {
    const modalRef = this.modalService.open(ConfirmationDialogComponent, {
      size: dialogSize,
      backdrop: 'static',
      keyboard: false
    });
    modalRef.componentInstance.title = title;
    modalRef.componentInstance.message = message;
    modalRef.componentInstance.btnOkText = btnOkText;
    modalRef.componentInstance.btnCancelText = btnCancelText;
    modalRef.componentInstance.showCloseBtn = showCloseBtn;

    return modalRef.result;
  }
}
