import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { APP_INITIALIZER, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { KeycloakAngularModule, KeycloakService } from 'keycloak-angular';
import { ToastrModule } from 'ngx-toastr';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { SharedComponentsModule } from './shared/components/components.module';
import { initializeKeycloak } from './shared/utils/keycloak.init';
import { DecompositionsComponent } from './pages/external/decompositions/decompositions.component';
import { AgGridModule } from 'ag-grid-angular';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { StatusHistoryExternalComponent } from './pages/external/status-history-external/status-history-external.component';
import { APP_BASE_HREF, PlatformLocation } from '@angular/common';
import { getBaseHref } from './shared/utils/base-href.helper';
import { ConnectionPointsExternalComponent } from './pages/external/connection-points-external/connection-points-external.component';
import { ServerErrorInterceptor } from './shared/services/interceptor/server-error.interceptor';
import { RequestHeaderInterceptor } from './shared/services/interceptor/request-header.interceptor';

@NgModule({
  declarations: [AppComponent, DecompositionsComponent, StatusHistoryExternalComponent, ConnectionPointsExternalComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    HttpClientModule,
    AppRoutingModule,
    KeycloakAngularModule,
    SharedComponentsModule,
    ToastrModule.forRoot(),
    AgGridModule,
    NgbModule
  ],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: initializeKeycloak,
      multi: true,
      deps: [KeycloakService],
    },
    {
      provide: APP_BASE_HREF,
      useFactory: getBaseHref,
      deps: [PlatformLocation]
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: ServerErrorInterceptor,
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: RequestHeaderInterceptor,
      multi: true
    }
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
