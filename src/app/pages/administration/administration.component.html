<div class="vertical-content container-fluid projects-container">

    <div class="vertical-content-search mb-20">
        <div class="d-flex justify-content-between">

            <div class="vertical-content-search-title cursor-pointer d-flex align-items-center"
                data-bs-toggle="collapse" [attr.data-bs-target]="'#mappingCollapse'" aria-expanded="false"
                [attr.aria-controls]="'mappingCollapse'">
                <span class="vertical-content-search-title-icon">
                    <i class="bi bi-chevron-double-right"></i>
                    <i class="bi bi-chevron-double-down"></i>
                </span>
                <span class="fw-600">Actions</span>
            </div>
        </div>

        <div class="collapse multi-collapse" [attr.id]="'mappingCollapse'">
            <div class="components-action d-flex">
                <div class="components-action-button" [class.components-action-button-disabled]="!isAdmin"
                    [attr.data-bs-toggle]=" isAdmin ? 'modal' : '' "
                    [attr.data-bs-target]=" isAdmin ? '#mappingModal' : '' " (click)="openMappingModal('add')"><i
                        class="bi bi-folder-plus mr-5"></i>Add</div>
                <div class="components-action-button"
                    [class.components-action-button-disabled]="(!isAdmin || disabledAction)"
                    [attr.data-bs-toggle]="(!isAdmin || disabledAction) ? '' : 'modal' "
                    [attr.data-bs-target]="(!isAdmin || disabledAction) ? '' : '#mappingModal' "
                    (click)="openMappingModal('edit')">
                    <i class="bi bi-folder-plus mr-5"></i>Edit
                </div>
                <div class="components-action-button"
                    [class.components-action-button-disabled]="(!isAdmin || disabledAction)"
                    (click)="onclickDeleteMapping()"><i class="bi bi-folder-plus mr-5"></i>Delete</div>
            </div>
        </div>
    </div>

    <div class="vertical-content-value projects-content d-flex flex-column">
        <div class="vertical-content-value-title d-flex justify-content-between">
            <span>
                User management
            </span>
            <div class="d-flex align-items-center">
                <div class="reload-icon mr-10" title="Refresh" (click)="doRefreshButton()">
                    <i class="bi bi-arrow-clockwise"></i>
                </div>
            </div>
        </div>
        <div class="vertical-content-value-grid-group">
            <ag-grid-angular style="width: 100%;" class="ag-theme-alpine projects-grid" [columnDefs]="columnDefs"
                [enableCellTextSelection]="true" [isRowSelectable]="isRowSelectable" [getRowId]="getRowId"
                [defaultColDef]="defaultColDef" [rowData]="mappingData$ | async" [rowSelection]="'single'"
                (selectionChanged)="onSelectionChanged($event)" [animateRows]="true"
                (filterChanged)="onFilterChanged($event)"></ag-grid-angular>

            <div class="d-flex justify-content-between p-17">
                <div></div>
                <div class="vertical-content-value-sub-title">Total: <span class="fw-600">{{displayedRowCount}}</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Create Mapping -->
<app-mapping-dialog (refreshGridEvent)="receiveRefreshGrid($event)" [typeAction]="typeAction"
    [selectedMappingItem]="selectedMappingItem" [resetMappingForm]="resetMappingForm"></app-mapping-dialog>