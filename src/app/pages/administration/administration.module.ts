import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedComponentsModule } from 'src/app/shared/components/components.module';
import { AgGridModule } from 'ag-grid-angular';
import { AdministrationComponent } from './administration.component';
import { MappingDialogComponent } from './mapping-dialog/mapping-dialog.component';

const routes: Routes = [
  {
    path: '',
    component: AdministrationComponent,
  },
];

@NgModule({
  declarations: [
    AdministrationComponent,
    MappingDialogComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedComponentsModule,
    FormsModule,
    ReactiveFormsModule,
    AgGridModule
  ]
})
export class AdministrationModule { }
