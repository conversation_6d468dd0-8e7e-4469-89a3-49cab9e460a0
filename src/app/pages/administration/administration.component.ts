import { Component, OnInit, ViewChild } from '@angular/core';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef, IsRowSelectable } from 'ag-grid-community';
import { BehaviorSubject, finalize, Subscription } from 'rxjs';
import { ConfirmationDialogService } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.service';
import {
  ACTION_MODAL,
  PROJECT_ITEM_SELECTED,
  USER_LOGGED_IN_KEY,
} from 'src/app/shared/constants/common';
import { UserAccount } from 'src/app/shared/interface/account/user.interface';
import { AdminMappingModel } from 'src/app/shared/interface/administration/admin.interface';
import { ProjectModel } from 'src/app/shared/interface/projects/project.interface';
import { AdministrationService } from 'src/app/shared/services/api/administration.service';
import { LoadingService } from 'src/app/shared/services/store/loading.service';
import { ToastsService } from 'src/app/shared/services/toasts/toasts.service';

@Component({
  selector: 'app-administration',
  templateUrl: './administration.component.html',
  styleUrls: ['./administration.component.scss'],
})
export class AdministrationComponent implements OnInit {
  getMappingSubscription: Subscription;
  deleteMappingSubscription: Subscription;

  displayedRowCount: number = 0;
  selectedProjectItem: ProjectModel;
  typeAction: ACTION_MODAL;
  resetMappingForm: boolean = false;
  disabledAction: boolean = true;
  selectedMappingItem: AdminMappingModel | null | undefined;
  isAdmin: boolean = false;
  user: UserAccount | undefined;

  // grid config
  // Each Column Definition results in one Column.
  public columnDefs: ColDef[] = [
    {
      field: 'index',
      headerName: '',
      headerCheckboxSelection: false,
      checkboxSelection: true,
      showDisabledCheckboxes: true,
      filter: false,
      width: 55,
      cellStyle: {
        borderRight: '1px solid #e8ebed',
      },
      suppressMovable: true,
      pinned: 'left'
    },
    {
      field: 'user_group',
      headerName: 'AD Group',
      width: 950,
      sortable: true,
      sort: 'asc',
    },
    {
      field: 'role_name',
      headerName: 'SPINE role',
      width: 850,
    },
  ];

  // DefaultColDef sets props common to all Columns
  public defaultColDef: ColDef = {
    width: 200,
    sortable: true,
    filter: 'agTextColumnFilter',
    floatingFilter: true,
    resizable: true,
    filterParams: {
      trimInput: true,
      debounceMs: 1000,
    },
  };

  public isRowSelectable: IsRowSelectable = (params: any) => {
    return params.data && params.data.id && this.isAdmin;
  };

  getRowId = (TData: any) =>
    `${TData.data?.project_id}-${TData.data?.user_group}` as any;

  // Data that gets displayed in the grid
  mappingData$: BehaviorSubject<AdminMappingModel[]> = new BehaviorSubject<
    AdminMappingModel[]
  >([]);

  // For accessing the Grid's API
  @ViewChild(AgGridAngular) agGrid!: AgGridAngular;

  constructor(
    private readonly adminService: AdministrationService,
    private readonly loadingService: LoadingService,
    private readonly toastsService: ToastsService,
    private readonly confirmationDialogService: ConfirmationDialogService
  ) {}

  ngOnInit(): void {
    const projectItem = localStorage.getItem(PROJECT_ITEM_SELECTED);
    if (projectItem) {
      this.selectedProjectItem = JSON.parse(projectItem);
    }

    const userObject = localStorage.getItem(USER_LOGGED_IN_KEY);
    if (userObject) {
      this.user = JSON.parse(userObject);
      if (this.user && this.user.admin) {
        this.isAdmin = true;
      }
    }
    this.getListMapping();
  }

  getListMapping() {
    this.loadingService.setLoadingStatus(true);
    this.getMappingSubscription = this.adminService
      .getListAllMapping(Number(this.selectedProjectItem.project_id))
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 300);
        })
      )
      .subscribe((res: AdminMappingModel[]) => {
        if (res) {
          this.mappingData$.next(res);
          this.displayedRowCount = res.length;
        }
      });
  }

  openMappingModal(type: ACTION_MODAL) {
    if (!this.isAdmin) {
      return;
    }

    if (type === 'edit' && !this.selectedMappingItem) {
      return;
    }

    this.typeAction = type;
    this.resetMappingForm = !this.resetMappingForm;
    this.onSelectionChanged();
  }

  receiveRefreshGrid(event: boolean) {
    if (event) {
      this.getListMapping();
    }
  }

  doRefreshButton() {
    this.agGrid.api.setFilterModel(null);
    this.agGrid.api.onFilterChanged();
    this.agGrid.api.deselectAll();
    this.getListMapping();
  }

  onSelectionChanged(event?: any) {
    const selectedRows = this.agGrid.api.getSelectedRows();
    if (selectedRows.length > 0) {
      this.disabledAction = false;
      this.selectedMappingItem = selectedRows[0];
    } else {
      this.disabledAction = true;
      this.selectedMappingItem = null;
    }
  }

  onclickDeleteMapping() {
    if (!this.isAdmin || !this.selectedMappingItem) {
      return;
    }

    if (
      this.selectedMappingItem?.id === null ||
      this.selectedMappingItem?.id === undefined
    ) {
      return;
    }

    this.confirmationDialogService
      .confirm(
        'Confirmation',
        'Do you really want to delete this mapping?',
        true,
        'OK',
        'Cancel'
      )
      .then((confirmed) => {
        if (confirmed) {
          this.doDeleteMapping();
        }
      })
      .catch((error) => {});
  }

  doDeleteMapping() {
    this.loadingService.setLoadingStatus(true);
    const request = [this.selectedMappingItem?.id];
    this.deleteMappingSubscription = this.adminService
      .deleteMapping(request)
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 300);
        })
      )
      .subscribe((res: any) => {
        this.toastsService.showToastr(
          'success',
          'Delete mapping successfully',
          '',
          2500
        );
        this.getListMapping();
      });
  }

  onFilterChanged(event: any) {
    this.displayedRowCount = this.agGrid.api.getDisplayedRowCount();
  }

  ngOnDestroy(): void {
    if (this.getMappingSubscription) {
      this.getMappingSubscription.unsubscribe();
    }

    if (this.deleteMappingSubscription) {
      this.deleteMappingSubscription.unsubscribe();
    }
  }
}
