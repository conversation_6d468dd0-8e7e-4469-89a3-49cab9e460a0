import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize, Subscription } from 'rxjs';
import {
  ACTION_MODAL,
  PROJECT_ITEM_SELECTED,
} from 'src/app/shared/constants/common';
import { AdminMappingModel } from 'src/app/shared/interface/administration/admin.interface';
import { ProjectModel } from 'src/app/shared/interface/projects/project.interface';
import { AdministrationService } from 'src/app/shared/services/api/administration.service';
import { LoadingService } from 'src/app/shared/services/store/loading.service';
import { ToastsService } from 'src/app/shared/services/toasts/toasts.service';

@Component({
  selector: 'app-mapping-dialog',
  templateUrl: './mapping-dialog.component.html',
  styleUrls: ['./mapping-dialog.component.scss'],
})
export class MappingDialogComponent implements OnInit, OnChanges {
  mappingForm: FormGroup;
  isSubmit: boolean = false;
  listADGroups: any[];
  listRoles: any[];
  selectedProjectItem: ProjectModel;

  createMappingSubscription: Subscription;
  updateMappingSubscription: Subscription;
  getADGroupsSubscription: Subscription;
  getRolesSubscription: Subscription;

  @Input('resetMappingForm') resetMappingForm: boolean;
  @Input('typeAction') typeAction: ACTION_MODAL;
  @Input('selectedMappingItem') selectedMappingItem:
    | AdminMappingModel
    | null
    | undefined;
  @Output() refreshGridEvent = new EventEmitter<boolean>();

  get f() {
    return this.mappingForm.controls;
  }

  constructor(
    private readonly loadingService: LoadingService,
    private readonly toastsService: ToastsService,
    private readonly adminService: AdministrationService,
    private readonly fb: FormBuilder
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes && changes['resetMappingForm']) {
      this.initForm();
      this.isSubmit = false;
    }

    if (this.typeAction === 'edit') {
      this.mappingForm = this.fb.group({
        user_group: [
          this.selectedMappingItem?.user_group,
          Validators.compose([Validators.required]),
        ],
        role_name: [
          this.selectedMappingItem?.role_name,
          Validators.compose([Validators.required]),
        ],
      });
    }
  }

  ngOnInit(): void {
    this.getListADGroups();
    this.getListRoles();
    this.initForm();
    this.isSubmit = false;

    const projectItem = localStorage.getItem(PROJECT_ITEM_SELECTED);
    if (projectItem) {
      this.selectedProjectItem = JSON.parse(projectItem);
    }
  }

  getListADGroups() {
    this.loadingService.setLoadingStatus(true);
    this.getADGroupsSubscription = this.adminService
      .getListADGroup()
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 300);
        })
      )
      .subscribe((res) => {
        if (res) {
          this.listADGroups = res;
        }
      });
  }

  getListRoles() {
    this.loadingService.setLoadingStatus(true);
    this.getRolesSubscription = this.adminService
      .getListRoles()
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 300);
        })
      )
      .subscribe((res) => {
        if (res) {
          this.listRoles = res;
        }
      });
  }

  initForm() {
    this.mappingForm = this.fb.group({
      user_group: [null, Validators.compose([Validators.required])],
      role_name: [null, Validators.compose([Validators.required])],
    });
  }

  onSubmitAddMapping() {
    this.isSubmit = true;
    if (!this.mappingForm.invalid && this.typeAction) {
      if (this.typeAction === 'edit') {
        this.updateMapping();
      } else {
        this.createMapping();
      }
    }
  }

  createMapping() {
    const reqBody = {
      user_group: this.f['user_group'].value,
      project_id: this.selectedProjectItem.project_id,
      role_name: this.f['role_name'].value,
    };

    this.loadingService.setLoadingStatus(true);
    this.createMappingSubscription = this.adminService
      .createMapping(reqBody)
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 300);
        })
      )
      .subscribe((res) => {
        if (res) {
          this.refreshGridEvent.emit(true);
          document.getElementById('mappingDialogCloseButtonModal')?.click();

          this.toastsService.showToastr(
            'success',
            'Create mapping successfully',
            '',
            2500
          );
        }
      });
  }

  updateMapping() {
    if(this.selectedMappingItem?.id === null || this.selectedMappingItem?.id === undefined) {
      return;
    }

    const reqBody = {
      user_group: this.f['user_group'].value,
      project_id: this.selectedProjectItem.project_id,
      role_name: this.f['role_name'].value,
    };

    this.loadingService.setLoadingStatus(true);
    this.updateMappingSubscription = this.adminService
      .updateMapping(reqBody, this.selectedMappingItem?.id)
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 300);
        })
      )
      .subscribe((res: AdminMappingModel) => {
        if (res) {
          this.refreshGridEvent.emit(true);
          document.getElementById('mappingDialogCloseButtonModal')?.click();

          this.toastsService.showToastr(
            'success',
            'Update mapping successfully',
            '',
            2500
          );
        }
      });
  }

  onChangeADGroups(event: any) {
    if (this.typeAction === 'add') {
      const roleValue = this.f['role_name'].value;
      if (!roleValue) {
        const adGroupValue = this.f['user_group'].value;
        if (adGroupValue) {
          if (adGroupValue.toUpperCase().includes('SENIOR')) {
            this.f['role_name'].setValue('SENIOR');
          } else if (adGroupValue.toUpperCase().includes('USER')) {
            this.f['role_name'].setValue('USER');
          } else if (adGroupValue.toUpperCase().includes('READ')) {
            this.f['role_name'].setValue('GUEST');
          } else if (adGroupValue.toUpperCase().includes('ADMIN')) {
            this.f['role_name'].setValue('ADMIN');
          } else {
            this.f['role_name'].setValue('GUEST');
          }
        }
      }
    }
  }

  cancelModal() {
    this.initForm();
    this.isSubmit = false;
  }

  ngOnDestroy(): void {
    if (this.getADGroupsSubscription) {
      this.getADGroupsSubscription.unsubscribe();
    }

    if (this.getRolesSubscription) {
      this.getRolesSubscription.unsubscribe();
    }

    if (this.createMappingSubscription) {
      this.createMappingSubscription.unsubscribe();
    }

    if (this.updateMappingSubscription) {
      this.updateMappingSubscription.unsubscribe();
    }
  }
}
