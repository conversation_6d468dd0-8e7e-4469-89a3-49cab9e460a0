<div class="modal fade" id="mappingModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="mappingLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form [formGroup]="mappingForm" (ngSubmit)="onSubmitAddMapping()" style="width: 100%;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-blue-8a fw-600" id="mappingLabel">{{typeAction === 'add' ? 'Add' : 'Update'}} mapping</h5>
                    <button id="mappingDialogCloseButtonModal" type="button" class="btn-close"
                        data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-20">
                        <label class="form-label fw-500 text-gray-77">AD Group <span class="text-red">*</span></label>
                        <select class="form-select" aria-label="AD Group selection" name="user_group" (change)="onChangeADGroups($event)"
                            formControlName="user_group">
                            <option *ngFor="let item of listADGroups;let i = index" [value]="item">{{item}}
                            </option>
                        </select>
                        <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
                                validation: 'required',
                                message: 'AD Group is required',
                                control: mappingForm.controls['user_group']
                              }"></ng-container>
                    </div>
                    <div class="mb-20">
                        <label class="form-label fw-500 text-gray-77">SPINE role <span class="text-red">*</span></label>
                        <select class="form-select" aria-label="Role selection" name="role_name"
                            formControlName="role_name">
                            <option *ngFor="let item of listRoles;let i = index" [value]="item">{{item}}
                            </option>
                        </select>
                        <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
                                validation: 'required',
                                message: 'SPINE role is required',
                                control: mappingForm.controls['role_name']
                              }"></ng-container>
                    </div>

                </div>
                <div class="modal-footer">
                    <button type="submit" class="components-action-button">{{typeAction === 'add' ? 'Create' : 'Update'}}</button>
                    <div class="components-action-button" data-bs-dismiss="modal" (click)="cancelModal()">Cancel</div>
                </div>
            </div>
        </form>
    </div>
</div>

<ng-template #formError let-control="control" let-message="message" let-validation="validation">
    <ng-container *ngIf="
      (control.hasError(validation) && (control.dirty || control.touched)) ||
      (control.hasError(validation) && isSubmit)
    ">
        <div class="validate-field">
            {{ message }}
        </div>
    </ng-container>
</ng-template>

<!-- <ng-template #valiDateError let-control="control" let-message="message">
    <ng-container *ngIf="!control.hasError('required') && !isValidDate">
        <div class="validate-field">
            {{ message }}
        </div>
    </ng-container>
</ng-template> -->