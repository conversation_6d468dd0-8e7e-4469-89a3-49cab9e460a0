import { APP_BASE_HREF } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  Inject,
  ViewChild,
} from '@angular/core';
import { ColDef, ICellRendererParams } from 'ag-grid-community';
import { BehaviorSubject, Subscription } from 'rxjs';
import {
  ComponentModel,
  StatusHistoryResponse,
} from 'src/app/shared/interface/components/response.objects';
import { ComponentsService } from 'src/app/shared/services/api/components.service';
import {
  getDateFromDateString,
  getTimeFromDateString,
} from 'src/app/shared/utils/time.helper';
import tippy, { hideAll } from 'tippy.js';

@Component({
  selector: 'app-status-history',
  templateUrl: './status-history.component.html',
  styleUrls: ['./status-history.component.scss'],
})
export class StatusHistoryComponent implements AfterViewInit {
  params: any;
  rowData: ComponentModel;
  isOpen: boolean = false;
  tippyInstance: any;
  public cellValue!: string;

  // Data that gets displayed in the grid
  statusHistorySubscription: Subscription;
  statusHistoryData$: BehaviorSubject<StatusHistoryResponse[]> =
    new BehaviorSubject<StatusHistoryResponse[]>([]);

  @ViewChild('content') container: any;

  @ViewChild('trigger') button: any;

  // grid config
  // Each Column Definition results in one Column.
  public columnDefs: ColDef[] = [
    {
      field: 'status',
      headerName: 'Status',
      width: 280,
      valueGetter: (params) => {
        return (
          '(' +
          params?.data?.state?.value +
          ')' +
          ' ' +
          params?.data?.state?.name
        );
      },
    },
    {
      field: 'date',
      headerName: 'Date',
      valueGetter: (params) => {
        return getDateFromDateString(params?.data?.status_date, 'dd-mm-yyyy');
      },
      width: 150,
    },
    {
      field: 'time',
      headerName: 'Time',
      valueGetter: (params) => {
        return getTimeFromDateString(params?.data?.status_date);
      },
      width: 100,
    },
    {
      field: 'change_group',
      headerName: 'Change Group',
      valueGetter: (params) => {
        return params?.data?.change_group?.name;
      },
      width: 200,
    },
    {
      field: 'remark',
      headerName: 'Reason',
      width: 200,
      valueGetter: (params) => {
        return params?.data?.change_group?.description &&
          params?.data?.change_group?.description !== ''
          ? params?.data?.change_group?.description
          : '';
      },
    },
    { field: 'username', headerName: 'User', width: 300 },
  ];

  // DefaultColDef sets props common to all Columns
  public defaultColDef: ColDef = {
    width: 150,
    sortable: true,
    filter: 'agTextColumnFilter',
    resizable: true,
    filterParams: {
      trimInput: true,
      debounceMs: 1000,
    },
  };

  constructor(
    private readonly changeDetector: ChangeDetectorRef,
    private readonly componentsService: ComponentsService,
    @Inject(APP_BASE_HREF) public baseHref: string
  ) {}

  ngAfterViewInit(): void {
    this.tippyInstance = tippy(this.button.nativeElement);
    this.tippyInstance.disable();
  }

  agInit(params: any) {
    this.params = params;
    this.rowData = params?.data;
    this.cellValue = this.getValueToDisplay(params);
  }

  getValueToDisplay(params: ICellRendererParams) {
    return params.valueFormatted ? params.valueFormatted : params.value;
  }

  configureTippyInstance() {
    this.tippyInstance.enable();
    this.tippyInstance.show();

    this.tippyInstance.setProps({
      trigger: 'manual',
      placement: 'right',
      arrow: false,
      interactive: true,
      appendTo: document.body,
      hideOnClick: false,
      onShow: (instance: any) => {
        hideAll({ exclude: instance });
      },
      onClickOutside: (instance: any, event: any) => {
        this.isOpen = false;
        instance.unmount();
      },
    });
  }

  togglePopup() {
    this.isOpen = !this.isOpen;
    this.changeDetector.detectChanges();
    if (this.isOpen) {
      this.configureTippyInstance();
      this.getStatusHistory();
      this.tippyInstance.setContent(this.container.nativeElement);
    } else {
      this.tippyInstance.unmount();
    }
  }

  getStatusHistory() {
    this.statusHistorySubscription = this.componentsService
      .getStatusHistoryByComponentId(this.rowData?.unique_id)
      .subscribe((res: StatusHistoryResponse[]) => {
        if (res) {
          this.statusHistoryData$.next(res);
        }
      });
  }

  openInTab() {
    if (!this.cellValue || !this.isOpen) {
      return;
    }

    this.togglePopup();

    window.open(
      `${window.location.origin}${this.baseHref}status-history?componentId=${this.rowData?.unique_id}`,
      '_blank',
      'location=yes,height=370,width=970,top=100,left=100,scrollbars=yes,status=yes'
    );
  }

  closeDialog() {
    this.isOpen = false;
    this.tippyInstance.unmount();
  }

  ngOnDestroy(): void {
    if (this.statusHistorySubscription) {
      this.statusHistorySubscription.unsubscribe();
    }
  }
}
