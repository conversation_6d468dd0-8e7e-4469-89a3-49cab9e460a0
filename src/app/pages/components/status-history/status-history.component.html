<div>
    <div>
        <span #trigger data-action="toggle" class="modal-component-icon-external mr-10" title="List status history">
            <i data-action="toggle" class="bi bi-info-circle"></i>
        </span>
        <span>{{ cellValue }}</span>
    </div>
  
    <div #content *ngIf="isOpen" class="modal-component">
        <div class="modal-component-heading d-flex justify-content-between align-items-center">
            Status History for Component {{ rowData.unique_id }}
            <div class="cursor-pointer reload-icon" title="Close" (click)="closeDialog()"><i class="bi bi-x"></i></div>
        </div>

        <ag-grid-angular 
            style="width: 720px; height: 230px; font-size: 12px;" 
            class="ag-theme-alpine"
            [rowData]="statusHistoryData$ | async" 
            [columnDefs]="columnDefs"
            [enableCellTextSelection]="true"
            [defaultColDef]="defaultColDef"
            [rowSelection]="'single'"
            [animateRows]="true"
            >
        </ag-grid-angular>

        <div class="modal-component-link" (click)="openInTab()">
            Open in Tab
        </div>
        
    </div>
</div>
  