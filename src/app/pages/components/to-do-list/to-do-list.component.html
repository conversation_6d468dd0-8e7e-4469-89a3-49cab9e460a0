<div class="modal fade" id="toDoListModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="toDoListLabel" aria-hidden="true">
    <div class="modal-dialog" style="min-width: 70%;">

        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-blue-8a fw-600" id="toDoListLabel">Unfinished Materials</h5>
                <div class="d-flex align-items-center">
                    <div class="cursor-pointer mr-10" (click)="exportExcel()" title="Export excel"><i
                            class="bi bi-file-spreadsheet-fill"></i></div>
                    <div class="reload-icon mr-10" title="Refresh" (click)="doRefreshButton()">
                        <i class="bi bi-arrow-clockwise"></i>
                    </div>
                    <div class="cursor-pointer font-size-20" title="Close" data-bs-dismiss="modal">
                        <i class="bi bi-x-lg"></i>
                    </div>
                </div>
            </div>
            <div class="modal-body">

                <div class="vertical-content-value-grid-group">
                    <ag-grid-angular style="width: 100%;" class="ag-theme-alpine projects-grid"
                        [columnDefs]="columnDefs" [defaultColDef]="defaultColDef" [rowData]="toDoData$ | async"
                        (filterChanged)="onFilterChanged($event)" [rowSelection]="'single'" [animateRows]="true"
                        [enableCellTextSelection]="true"></ag-grid-angular>

                    <div class="d-flex justify-content-between p-17">
                        <div></div>
                        <div class="vertical-content-value-sub-title">Total: <span
                                class="fw-600">{{displayedRowCount}}</span>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </div>
</div>