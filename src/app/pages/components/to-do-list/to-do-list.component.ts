import { Component, OnInit, ViewChild } from '@angular/core';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';
import Excel from 'exceljs';
import FileSaver from 'file-saver';
import { BehaviorSubject, finalize, Subscription } from 'rxjs';
import { PROJECT_ITEM_SELECTED } from 'src/app/shared/constants/common';
import {
  ProjectModel,
  ToDoModel,
} from 'src/app/shared/interface/projects/project.interface';
import { ProjectsService } from 'src/app/shared/services/api/projects.service';
import { LoadingService } from 'src/app/shared/services/store/loading.service';
import { ToastsService } from 'src/app/shared/services/toasts/toasts.service';
import { numberSortComparator } from 'src/app/shared/utils/sort-grid.helper';

@Component({
  selector: 'app-to-do-list',
  templateUrl: './to-do-list.component.html',
  styleUrls: ['./to-do-list.component.scss'],
})
export class ToDoListComponent implements OnInit {
  getToDoSubscription: Subscription;

  selectedProjectItem: ProjectModel | null | undefined;
  skipFlag: boolean = false;
  displayedRowCount: number = 0;

  // grid config
  // Each Column Definition results in one Column.
  public columnDefs: ColDef[] = [
    {
      field: 'sap_material_number',
      headerName: 'SAP',
      sortable: true,
      width: 200,
      sort: 'asc',
      comparator: numberSortComparator,
    },
    {
      field: 'description',
      headerName: 'Description',
      width: 300,
    },
    {
      field: 'derived_from',
      headerName: 'Derived From',
      width: 200,
    },
    {
      field: 'material_type',
      headerName: 'Material Type',
    },
    {
      field: 'mps_prefix',
      headerName: 'MPS Prefix',
    },
    {
      field: 'source',
      headerName: 'Source',
    },
    {
      field: 'quantity',
      headerName: 'Elements',
    },
  ];

  // DefaultColDef sets props common to all Columns
  public defaultColDef: ColDef = {
    width: 150,
    sortable: true,
    filter: 'agTextColumnFilter',
    floatingFilter: true,
    resizable: true,
    filterParams: {
      trimInput: true,
      debounceMs: 1000,
    },
  };

  // Data that gets displayed in the grid
  toDoData$: BehaviorSubject<ToDoModel[]> = new BehaviorSubject<ToDoModel[]>(
    []
  );

  // For accessing the Grid's API
  @ViewChild(AgGridAngular) agGrid!: AgGridAngular;

  constructor(
    private readonly projectService: ProjectsService,
    private readonly loadingService: LoadingService,
    private readonly toastsService: ToastsService
  ) {}

  ngOnInit(): void {
    const projectItem = localStorage.getItem(PROJECT_ITEM_SELECTED);
    if (projectItem) {
      this.selectedProjectItem = JSON.parse(projectItem);
    }
    this.getToDoList();
  }

  getToDoList() {
    if (this.skipFlag) {
      this.loadingService.setLoadingStatus(true);
    }
    this.getToDoSubscription = this.projectService
      .getUnfinishedMaterial(Number(this.selectedProjectItem?.project_id))
      .pipe(
        finalize(() => {
          if (this.skipFlag) {
            setTimeout(() => {
              this.loadingService.setLoadingStatus(false);
            }, 300);
          }
          this.skipFlag = true;
        })
      )
      .subscribe((res) => {
        if (res) {
          this.toDoData$.next(res);
          this.displayedRowCount = res.length;
        }
      });
  }

  onFilterChanged(event: any) {
    this.displayedRowCount = this.agGrid.api.getDisplayedRowCount();
  }

  doRefreshButton() {
    this.agGrid.api.setFilterModel(null);
    this.agGrid.api.onFilterChanged();
    this.agGrid.api.deselectAll();
    this.getToDoList();
  }

  exportExcel() {
    if (this.displayedRowCount === 0) {
      this.toastsService.showToastr('warning', 'No data to export!', '', 2500);
      return;
    }

    this.loadingService.setLoadingStatus(true);
    const fileName = 'unfinished-materials';
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet('Unfinished Materials');

    // Define custom header for the worksheet
    worksheet.columns = [
      { header: 'SAP', key: 'sap_material_number', width: 30 },
      { header: 'Description', key: 'description', width: 50 },
      { header: 'Derived From', key: 'derived_from', width: 30 },
      { header: 'Material Type', key: 'material_type', width: 20 },
      { header: 'MPS Prefix', key: 'mps_prefix', width: 20 },
      { header: 'Source', key: 'source', width: 20 },
      { header: 'Elements', key: 'quantity', width: 20 },
    ];

    // Style the header
    const header = worksheet.getRow(1);
    header.eachCell((cell, number) => {
      cell.font = {
        bold: true,
      };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '6077bbb3' },
      };
    });

    // Add data to the worksheet
    this.agGrid.api.forEachNodeAfterFilterAndSort((node, index) => {
      const rowData = node.data;
      worksheet.addRow({
        sap_material_number: rowData.sap_material_number,
        description: rowData.description,
        derived_from: rowData.derived_from,
        material_type: rowData.material_type,
        mps_prefix: rowData.mps_prefix,
        source: rowData.source,
        quantity: rowData.quantity,
      });
    });

    // Generate the Excel file
    workbook.xlsx
      .writeBuffer()
      .then((data) => {
        const blob = new Blob([data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        FileSaver.saveAs(blob, `${fileName}.xlsx`);
        this.loadingService.setLoadingStatus(false);
      })
      .catch((error) => {
        this.loadingService.setLoadingStatus(false);
      });
  }

  ngOnDestroy(): void {
    if (this.getToDoSubscription) {
      this.getToDoSubscription.unsubscribe();
    }
  }
}
