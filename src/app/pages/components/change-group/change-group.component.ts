import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TYPE_MODAL } from 'src/app/shared/constants/common';
import { ChangeGroupModel } from 'src/app/shared/interface/components/response.objects';

@Component({
  selector: 'app-change-group',
  templateUrl: './change-group.component.html',
  styleUrls: ['./change-group.component.scss'],
})
export class ChangeGroupComponent implements OnInit, OnChanges {
  submitCheckChangeGroupForm: boolean = false;
  changeGroupForm: FormGroup;

  @Input('name') changeGroupName: string = '';
  @Input('description') changeGroupDescription: string;
  @Input('typeModal') typeModal: TYPE_MODAL;
  @Output() changeGroupEvent = new EventEmitter<ChangeGroupModel>();

  get f() {
    return this.changeGroupForm.controls;
  }

  constructor(private readonly fb: FormBuilder) {}

  ngOnChanges(changes: SimpleChanges) {
    this.initChangeGroupForm();
  }

  ngOnInit(): void {}

  initChangeGroupForm() {
    this.changeGroupForm = this.fb.group({
      changeGroupName: [
        this.changeGroupName,
        Validators.compose([Validators.required]),
      ],
      changeGroupDesc: [this.changeGroupDescription, Validators.compose([])],
    });
  }

  onSubmitChangeGroup() {
    this.submitCheckChangeGroupForm = true;

    if (!this.changeGroupForm.invalid) {
      this.changeGroupName = this.f['changeGroupName'].value;
      this.changeGroupDescription = this.f['changeGroupDesc'].value;
      // force send event
      if(this.changeGroupDescription.trim() === '') {
        this.changeGroupDescription = ' ';
      }
      this.changeGroupEvent.emit({
        name: this.changeGroupName,
        description: this.changeGroupDescription,
      });
    }
  }
}
