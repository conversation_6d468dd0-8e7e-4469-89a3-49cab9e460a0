<div class="modal fade" id="changeGroupModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="changeGroupLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form [formGroup]="changeGroupForm" (ngSubmit)="onSubmitChangeGroup()" style="width: 100%;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-blue-8a fw-600" id="changeGroupLabel">Change group</h5>
                    <button class="btn-close" [attr.data-bs-target]="typeModal==='moveBackward' ? '#moveBackwardModal' : typeModal==='unlockComponents' ? '#unlockComponentsModal' : ''"
                    data-bs-toggle="modal" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-20">
                        <label class="form-label fw-500 text-gray-77">Name <span class="text-red">*</span></label>
                        <input class="form-control" type="text" name="changeGroupName" maxlength="254"
                            formControlName="changeGroupName" />
                        <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
                          validation: 'required',
                          message: 'Change group name is required',
                          control: changeGroupForm.controls['changeGroupName']
                        }"></ng-container>
                    </div>
                    <div>
                        <label class="form-label fw-500 text-gray-77">Description</label>
                        <textarea class="form-control" rows="5" name="changeGroupDesc" maxlength="254"
                            formControlName="changeGroupDesc"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="components-action-button"
                        [attr.data-bs-target]="changeGroupForm.controls['changeGroupName'].hasError('required') ? null : (typeModal==='moveBackward' ? '#moveBackwardModal' : typeModal==='unlockComponents' ? '#unlockComponentsModal' : '')"
                        [attr.data-bs-toggle]="changeGroupForm.controls['changeGroupName'].hasError('required') ? null : 'modal'"
                        [attr.data-bs-dismiss]="changeGroupForm.controls['changeGroupName'].hasError('required') ? null : 'modal'">{{changeGroupName
                        && changeGroupName !== '' ?
                        'Update': 'Create'}}</button>
                    <div class="components-action-button"
                        [attr.data-bs-target]="typeModal==='moveBackward' ? '#moveBackwardModal' : typeModal==='unlockComponents' ? '#unlockComponentsModal' : ''"
                        data-bs-toggle="modal" data-bs-dismiss="modal">Cancel</div>
                </div>
            </div>
        </form>
    </div>
</div>

<ng-template #formError let-control="control" let-message="message" let-validation="validation">
    <ng-container *ngIf="
      (control.hasError(validation) && (control.dirty || control.touched)) ||
      (control.hasError(validation) && submitCheckChangeGroupForm)
    ">
        <div class="validate-field">
            {{ message }}
        </div>
    </ng-container>
</ng-template>