import { APP_BASE_HREF } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  Inject,
  ViewChild,
} from '@angular/core';
import { ColDef, ICellRendererParams } from 'ag-grid-community';
import { BehaviorSubject, Subscription } from 'rxjs';
import {
  DecompositionModel,
  DecompositionResponse,
} from 'src/app/shared/interface/components/response.objects';
import { ComponentsService } from 'src/app/shared/services/api/components.service';
import { numberSortComparator } from 'src/app/shared/utils/sort-grid.helper';
import tippy, { hideAll } from 'tippy.js';

@Component({
  selector: 'app-component-unique-id',
  templateUrl: './component-unique-id.component.html',
  styleUrls: ['./component-unique-id.component.scss'],
})
export class ComponentUniqueIdComponent implements AfterViewInit {
  params: any;
  isOpen: boolean = false;
  tippyInstance: any;
  public cellValue!: string;

  // Data that gets displayed in the grid
  decompositionsSubscription: Subscription;
  decompositionsData$: BehaviorSubject<DecompositionModel[]> =
    new BehaviorSubject<DecompositionModel[]>([]);

  @ViewChild('content') container: any;

  @ViewChild('trigger') button: any;

  // grid config
  // Each Column Definition results in one Column.
  public columnDefs: ColDef[] = [
    {
      field: 'index',
      headerName: 'Item',
    },
    {
      field: 'position_number',
      headerName: 'Position',
      sortable: true,
      sort: 'asc',
      comparator: numberSortComparator,
    },
    {
      field: 'sap_material_number',
      headerName: 'Sap Material Number',
      width: 200,
    },
    { field: 'description', headerName: 'Description', width: 200 },
    { field: 'quantity', headerName: 'Quantity' },
    { field: 'unit_in_sap', headerName: 'Unit In Sap' },
    { field: 'derived_from', headerName: 'Derived From' },
    { field: 'standard_material', headerName: 'Standard Material', width: 200 },
    {
      field: 'change_description',
      headerName: 'Change Description',
      width: 200,
    },
    { field: 'mpsprefix', headerName: 'MPS Prefix' },
    { field: 'source', headerName: 'Source' },
    { field: 'zoptions', headerName: 'Zoptions' },
  ];

  // DefaultColDef sets props common to all Columns
  public defaultColDef: ColDef = {
    width: 150,
    sortable: true,
    filter: 'agTextColumnFilter',
    resizable: true,
    filterParams: {
      trimInput: true,
      debounceMs: 1000,
    },
  };

  constructor(
    private readonly changeDetector: ChangeDetectorRef,
    private readonly componentsService: ComponentsService,
    @Inject(APP_BASE_HREF) public baseHref: string
  ) {}

  ngAfterViewInit(): void {
    this.tippyInstance = tippy(this.button.nativeElement);
    this.tippyInstance.disable();
  }

  agInit(params: any) {
    this.params = params;
    this.cellValue = this.getValueToDisplay(params);
  }

  getValueToDisplay(params: ICellRendererParams) {
    return params.valueFormatted ? params.valueFormatted : params.value;
  }

  configureTippyInstance() {
    this.tippyInstance.enable();
    this.tippyInstance.show();

    this.tippyInstance.setProps({
      trigger: 'manual',
      placement: 'right',
      arrow: false,
      interactive: true,
      appendTo: document.body,
      hideOnClick: false,
      onShow: (instance: any) => {
        hideAll({ exclude: instance });
      },
      onClickOutside: (instance: any, event: any) => {
        this.isOpen = false;
        instance.unmount();
      },
    });
  }

  togglePopup() {
    this.isOpen = !this.isOpen;
    this.changeDetector.detectChanges();
    if (this.isOpen) {
      this.configureTippyInstance();
      this.getDecomposition();
      this.tippyInstance.setContent(this.container.nativeElement);
    } else {
      this.tippyInstance.unmount();
    }
  }

  getDecomposition() {
    this.decompositionsSubscription = this.componentsService
      .getDecompositionsByComponentId(this.cellValue)
      .subscribe((res) => {
        if (res) {
          const decompositonsRes: DecompositionResponse[] = res;
          // convert data

          const listDecompositions: DecompositionModel[] = [];
          let index = 0;

          decompositonsRes.forEach((item: DecompositionResponse) => {
            const decompositions: DecompositionModel[] = item.decompositions;
            decompositions.forEach((comp: DecompositionModel) => {
              comp.index = index + 1;
              comp.mode = item.mode;
              comp.source = item.source;
              index += 1;

              listDecompositions.push(comp);
            });
          });

          this.decompositionsData$.next(listDecompositions);
        }
      });
  }

  openInTab() {
    if (!this.cellValue || !this.isOpen) {
      return;
    }

    this.togglePopup();

    window.open(
      `${window.location.origin}${this.baseHref}decompositions?componentId=${this.cellValue}`,
      '_blank',
      'location=yes,height=370,width=970,top=100,left=100,scrollbars=yes,status=yes'
    );
  }

  closeDialog() {
    this.isOpen = false;
    this.tippyInstance.unmount();
  }

  ngOnDestroy(): void {
    if (this.decompositionsSubscription) {
      this.decompositionsSubscription.unsubscribe();
    }
  }
}
