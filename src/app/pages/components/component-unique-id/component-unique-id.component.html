<div>
    <div>
        <span class="mr-10">{{ cellValue }}</span>
        <span #trigger data-action="toggle" class="modal-component-icon-external" title="List Decompositions">
            <i data-action="toggle" class="bi bi-box-arrow-up-right"></i>
        </span>
    </div>

    <div #content *ngIf="isOpen" class="modal-component">
        <div class="modal-component-heading d-flex justify-content-between align-items-center">
            Decompositions for Component {{ cellValue }}
            <div class="cursor-pointer reload-icon" title="Close" (click)="closeDialog()"><i class="bi bi-x"></i></div>
        </div>

        <ag-grid-angular style="width: 720px; height: 230px; font-size: 12px;" class="ag-theme-alpine"
            [rowData]="decompositionsData$ | async" [columnDefs]="columnDefs" [defaultColDef]="defaultColDef"
            [enableCellTextSelection]="true" [rowSelection]="'single'" [animateRows]="true">
        </ag-grid-angular>

        <div class="modal-component-link" (click)="openInTab()">
            Open in Tab
        </div>
    </div>
</div>