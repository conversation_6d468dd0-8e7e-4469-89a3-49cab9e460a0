import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { AgGridAngular } from 'ag-grid-angular';
import { catchError, finalize, Subscription } from 'rxjs';
import { PROJECT_ITEM_SELECTED } from 'src/app/shared/constants/common';
import { ROLLBACK_OPTIONS } from 'src/app/shared/constants/config';
import {
  ChangeGroupModel,
  ComponentModel
} from 'src/app/shared/interface/components/response.objects';
import { UnlockComponentResponse, UnlockComponentsRequest } from 'src/app/shared/interface/components/unlock.interface';
import { ProjectModel } from 'src/app/shared/interface/projects/project.interface';
import { ComponentsService } from 'src/app/shared/services/api/components.service';
import { LoadingService } from 'src/app/shared/services/store/loading.service';
import { ToastsService } from 'src/app/shared/services/toasts/toasts.service';

@Component({
  selector: 'app-unlock-components',
  templateUrl: './unlock-components.component.html',
  styleUrls: ['./unlock-components.component.scss'],
})
export class UnlockComponentsComponent implements OnInit, OnChanges {
  unlockComponentsSubscription: Subscription;
  selectedProjectItem: ProjectModel;

  @Input('name') changeGroupName: string = '';
  @Input('description') changeGroupDescription: string = '';
  @Input('groupSelected') changeGroupSelected: string = '';
  @Input('listChangeGroup') listChangeGroup: ChangeGroupModel[] = [];
  @Input('agGrid') agGrid!: AgGridAngular;
  @Input('resetUnlockComponentsForm') resetUnlockComponentsForm: boolean;
  @Output() refreshGridEvent = new EventEmitter<boolean>();
  @Output() removeChangeGroupEvent = new EventEmitter<boolean>();

  rollbackOptions = ROLLBACK_OPTIONS;
  rollbackSelected: any = ROLLBACK_OPTIONS[0]['value'];

  constructor(
    private readonly componentsService: ComponentsService,
    private readonly loadingService: LoadingService,
    private readonly toastsService: ToastsService
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes && changes['resetUnlockComponentsForm']) {
      this.rollbackSelected = ROLLBACK_OPTIONS[0]['value'];
      this.changeGroupName = '';
      this.changeGroupSelected = '';
    }
  }

  ngOnInit(): void {
    const projectItem = localStorage.getItem(PROJECT_ITEM_SELECTED);
    if (projectItem) {
      this.selectedProjectItem = JSON.parse(projectItem);
    }
  }

  onRollBackChange(event: any) {
    this.rollbackSelected = event.target.value;
  }

  removeChangeGroup() {
    this.changeGroupName = '';
    this.changeGroupSelected = '';
    this.changeGroupDescription = '';
    this.removeChangeGroupEvent.emit(true);
  }

  doUnlockComponents() {

    if ((!this.changeGroupName || this.changeGroupName ==='') && (this.changeGroupSelected === '' || !this.changeGroupSelected)) {
      return;
    }

    const selectedRows: ComponentModel[] = this.agGrid.api.getSelectedRows();
    const listComponentIds: number[] = [];
    selectedRows.forEach((item) => {
      listComponentIds.push(Number(item.unique_id));
    });

    let reqBody: UnlockComponentsRequest | undefined | null;

    if (
      (!this.changeGroupSelected || this.changeGroupSelected === '') &&
      this.changeGroupName === ''
    ) {
      // no change group
      reqBody = {
        component_ids: listComponentIds,
        type_num: this.rollbackSelected,
        project_id: Number(this.selectedProjectItem.project_id),
        change_group: null,
      };
    } else if (this.changeGroupSelected !== '' && this.changeGroupName === '') {
      // select exist change group
      const selectedChangeGroup = this.listChangeGroup.find(
        (item) => item.id === Number(this.changeGroupSelected)
      );
      reqBody = {
        component_ids: listComponentIds,
        type_num: this.rollbackSelected,
        project_id: Number(this.selectedProjectItem.project_id),
        change_group: selectedChangeGroup,
      };
    } else if (!this.changeGroupName || this.changeGroupName !== '') {
      // new change group
      reqBody = {
        component_ids: listComponentIds,
        type_num: this.rollbackSelected,
        project_id: Number(this.selectedProjectItem.project_id),
        change_group: {
          id: null,
          name: this.changeGroupName,
          description: this.changeGroupDescription,
        },
      };
    }
    this.loadingService.setLoadingStatus(true);
    this.unlockComponentsSubscription = this.componentsService
      .unlockComponents(reqBody)
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 100);
        })
      )
      .subscribe(
        (res: UnlockComponentResponse) => {
          if (res) {
            //update cell new value
            if (res.new_states) {
              if (res.new_states.length > 100) {
                this.refreshGridEvent.emit(true);
              } else {
                res.new_states.forEach((item) => {
                  let rowNode = this.agGrid.api.getRowNode(
                    item.component_id.toString()
                  );
                  rowNode?.setDataValue('current_state', item.state);
                  rowNode?.setDataValue('sys_mod_date', item.status_date);
                });
              }
            }

            document
              .getElementById('unlockComponentsCloseButtonModal')
              ?.click();

              if (res.new_states && res.new_states.length > 0) {
                this.toastsService.showToastr(
                  'success',
                  'Status update successful',
                  '',
                  2500
                );
              }
  
              if (res.total_skipped > 0) {
                this.toastsService.showCustomForComponentToast(
                  'warning',
                  '',
                  15000,
                  res?.skipped_reason,
                  res.total_skipped
                );
              }
          }
        }
      );
  }

  onChangeGroupSelected(event: any) {
    const itemSelected = this.listChangeGroup.find(item => item.id === Number(this.changeGroupSelected));
    if(itemSelected) {
      this.changeGroupDescription = itemSelected.description;
    }else {
      this.changeGroupDescription = '';
    }
  }

  ngOnDestroy(): void {
    if (this.unlockComponentsSubscription) {
      this.unlockComponentsSubscription.unsubscribe();
    }
  }
}
