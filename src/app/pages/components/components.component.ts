import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { AgGridAngular } from 'ag-grid-angular';
import { BodyScrollEvent, ColDef, ColGroupDef } from 'ag-grid-community';
import Excel from 'exceljs';
import FileSaver from 'file-saver';
import { BehaviorSubject, finalize, Subscription } from 'rxjs';
import { ConfirmationDialogService } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.service';
import {
  PROJECT_ITEM_SELECTED,
  SENIOR_ROLE,
  TYPE_MODAL,
  USER_LOGGED_IN_KEY,
} from 'src/app/shared/constants/common';
import { UserAccount } from 'src/app/shared/interface/account/user.interface';
import { MarkAsDeleteResponse } from 'src/app/shared/interface/components/delete.interface';
import { AllowedStateRequest } from 'src/app/shared/interface/components/move-backward.interface';
import {
  MoveForwardRequest,
  MoveForwardResponse,
} from 'src/app/shared/interface/components/move-forward.interface';
import {
  ChangeGroupModel,
  ComponentModel,
  KeyValueModel,
} from 'src/app/shared/interface/components/response.objects';
import { ProjectModel } from 'src/app/shared/interface/projects/project.interface';
import { AdministrationService } from 'src/app/shared/services/api/administration.service';
import { ComponentsService } from 'src/app/shared/services/api/components.service';
import { LoadingService } from 'src/app/shared/services/store/loading.service';
import { ToastsService } from 'src/app/shared/services/toasts/toasts.service';
import { getDateFromDateString } from 'src/app/shared/utils/time.helper';
import { ComponentUniqueIdComponent } from './component-unique-id/component-unique-id.component';
import { ConnectionPointsComponent } from './connection-points/connection-points.component';
import { StatusHistoryComponent } from './status-history/status-history.component';

@Component({
  selector: 'app-components',
  templateUrl: './components.component.html',
  styleUrls: ['./components.component.scss'],
})
export class ComponentsComponent implements OnInit {
  objectsSubscription: Subscription;
  accessRoleSubscription: Subscription;
  moveForwardSubscription: Subscription;
  allowedStateSubscription: Subscription;
  listChangeGroupSubscription: Subscription;
  setStatusToDeleteSubscription: Subscription;
  eliminateObjectFromDatabaseSubscription: Subscription;

  disabledClosedProject: boolean = false;
  disabledAction: boolean = true;
  disabledMoveBackward: boolean = false;
  disabledEliminateObject: boolean = true;

  user: UserAccount | undefined;
  listProject: ProjectModel[] = [];
  selectedProjectItem: ProjectModel;
  listAllowedState: KeyValueModel[] = [];
  listChangeGroup: ChangeGroupModel[] = [];
  allowedStateSelected: any;
  changeGroupSelected: string = '';
  resetMoveBackwardForm: boolean = false;
  resetUnlockComponentsForm: boolean = false;
  unlockComponentsDescription: string = '';
  changeGroupName: string = '';
  changeGroupDescription: string = '';
  countSelectedRows: number = 0;
  displayedRowCount: number = 0;

  typeModal: TYPE_MODAL;

  // Grid options
  // Each Column Definition results in one Column.
  public columnDefs: (ColDef | ColGroupDef)[] = [
    {
      field: 'index',
      headerName: '',
      headerCheckboxSelection: true,
      checkboxSelection: true,
      showDisabledCheckboxes: true,
      width: 55,
      headerCheckboxSelectionFilteredOnly: true,
      filter: false,
      cellStyle: {
        borderRight: '1px solid #e8ebed',
      },
      suppressMovable: true,
      pinned: 'left',
    },
    {
      field: 'unique_id',
      headerName: 'Component Unique ID',
      width: 230,
      cellClass: 'grid-link',
      cellRenderer: ComponentUniqueIdComponent,
      editable: false,
      colId: 'unique_id',
      sortable: true,
      sort: 'asc',
    },
    {
      field: 'current_state',
      headerName: 'Status',
      width: 330,
      cellClass: 'grid-link',
      cellRenderer: StatusHistoryComponent,
      editable: false,
      colId: 'current_state',
      valueGetter: (params: any) => {
        return params?.data?.current_state?.name;
      },
    },
    { field: 'comment', headerName: 'Comment', width: 250 },
    { field: 'type', headerName: 'Type ID', width: 270 },
    {
      field: 'sys_mod_date',
      headerName: 'Modification Date',
      width: 200,
      valueGetter: (params) => {
        return getDateFromDateString(params?.data?.sys_mod_date, 'dd-mm-yyyy', true);
      },
    },
    {
      headerName: 'Groups',
      marryChildren: true,
      children: [
        {
          field: 'delivery_batch',
          headerName: 'Delivery Batch',
          valueGetter: (params: any) => {
            return params?.data?.group_mapping?.delivery_batch;
          },
        },
        {
          field: 'calculation_area',
          headerName: 'Calculation Area',
          columnGroupShow: 'closed',
          width: 200,
          valueGetter: (params: any) => {
            return params?.data?.group_mapping?.calculation_area;
          },
        },
        {
          field: 'estop_group',
          headerName: 'Estop Group',
          columnGroupShow: 'closed',
          valueGetter: (params: any) => {
            return params?.data?.group_mapping?.estop_group;
          },
        },
        {
          field: 'plc_area',
          headerName: 'PLC Area',
          columnGroupShow: 'closed',
          valueGetter: (params: any) => {
            return params?.data?.group_mapping?.plc_area;
          },
        },
        {
          field: 'screen',
          headerName: 'Screen',
          columnGroupShow: 'closed',
          valueGetter: (params: any) => {
            return params?.data?.group_mapping?.screen;
          },
        },
        {
          field: 'sequence_group',
          headerName: 'Sequence Group',
          columnGroupShow: 'closed',
          width: 200,
          valueGetter: (params: any) => {
            return params?.data?.group_mapping?.sequence_group;
          },
        },
        {
          field: 'line_name',
          headerName: 'Line Name',
          columnGroupShow: 'closed',
          valueGetter: (params: any) => {
            return params?.data?.group_mapping?.line_name;
          },
        },
        {
          field: 'building_section',
          headerName: 'Building Section',
          columnGroupShow: 'closed',
          width: 200,
          valueGetter: (params: any) => {
            return params?.data?.group_mapping?.building_section;
          },
        },
        {
          field: 'installation_section',
          headerName: 'Installation Section',
          columnGroupShow: 'closed',
          width: 200,
          valueGetter: (params: any) => {
            return params?.data?.group_mapping?.installation_section;
          },
        },
        {
          field: 'drawing',
          headerName: 'Drawing',
          columnGroupShow: 'closed',
          width: 250,
          valueGetter: (params: any) => {
            return params?.data?.group_mapping?.drawing;
          },
        },
      ],
    },
    { field: 'pos_no', headerName: 'Pos No', width: 200 },
    { field: 'color_group', headerName: 'Color Group' },
    { field: 'length_total', headerName: 'Total Length' },
    { field: 'width', headerName: 'Width' },
    { field: 'speed', headerName: 'Speed' },
    { field: 'load', headerName: 'Load' },
    { field: 'throughput', headerName: 'Throughput' },
    { field: 'motor_controller', headerName: 'Motor Controller', width: 200 },
    { field: 'unit_reversible', headerName: 'Unit Reversible' },
    { field: 'plant_domain', headerName: 'Plant Domain' },
    {
      field: 'amount_buffer_elec',
      headerName: 'Amount Buffer Electric',
      width: 200,
    },
    {
      field: 'amount_buffer_mech',
      headerName: 'Amount Buffer Mechanic',
      width: 220,
    },
    { field: 'buffer_size', headerName: 'Buffer Size' },
    {
      headerName: '',
      marryChildren: true,
      children: [
        { field: 'drive_position', headerName: 'Drive Position' },
        {
          field: 'motor_direction',
          headerName: 'Motor Direction',
          width: 200,
          columnGroupShow: 'closed',
        },
      ],
    },
    {
      headerName: 'Curve',
      marryChildren: true,
      children: [
        { field: 'curve_angle', headerName: 'Curve Angle' },
        {
          field: 'curve_direction',
          headerName: 'Curve Direction',
          columnGroupShow: 'closed',
        },
        {
          field: 'curve_radius',
          headerName: 'Curve Radius',
          columnGroupShow: 'closed',
        },
      ],
    },
    { field: 'break_type', headerName: 'Break Type' },
    {
      headerName: 'Section 1',
      marryChildren: true,
      children: [
        { field: 'section1_angle', headerName: 'Angle' },
        {
          field: 'section1_length',
          headerName: 'Length',
          columnGroupShow: 'closed',
        },
      ],
    },
    {
      headerName: 'Section 2',
      marryChildren: true,
      children: [
        { field: 'section2_angle', headerName: 'Angle' },
        {
          field: 'section2_length',
          headerName: 'Length',
          columnGroupShow: 'closed',
        },
      ],
    },
    {
      headerName: 'Section 3',
      marryChildren: true,
      children: [
        { field: 'section3_angle', headerName: 'Angle' },
        {
          field: 'section3_length',
          headerName: 'Length',
          columnGroupShow: 'closed',
        },
      ],
    },
    {
      headerName: 'Section 4',
      marryChildren: true,
      children: [
        { field: 'section4_angle', headerName: 'Angle' },
        {
          field: 'section4_length',
          headerName: 'Length',
          columnGroupShow: 'closed',
        },
      ],
    },
    {
      field: '',
      headerName: 'Connection Points',
      cellRenderer: ConnectionPointsComponent,
      editable: false,
      width: 200,
      colId: 'connection_points',
      valueGetter: (params: any) => {
        return params?.data?.unique_id;
      },
    },
    {
      field: 'bom_state',
      headerName: 'BOM Status',
      valueGetter: (params: any) => {
        return params?.data?.component_status?.bom_state;
      },
    },
    { field: 'reference', headerName: 'Reference Status', width: 250 },
    {
      headerName: 'Rotation',
      marryChildren: true,
      children: [
        { field: 'rotation', headerName: 'Rotation' },
        {
          field: 'rotation3_d',
          headerName: 'Rotation 3D',
          columnGroupShow: 'closed',
        },
      ],
    },
  ];

  // DefaultColDef sets props common to all Columns
  public defaultColDef: ColDef = {
    width: 150,
    sortable: true,
    filter: 'agTextColumnFilter',
    floatingFilter: true,
    resizable: true,
    filterParams: {
      trimInput: true,
      debounceMs: 1000,
    },
  };

  getRowStyle(params: any) {
    const currentState = params?.data?.current_state;
    if (currentState && currentState?.value == '0') {
      return {
        'background-color': 'rgba(128, 128, 128, .4)',
        'font-style': 'italic',
        opacity: '0.7',
      };
    } else {
      return {
        'background-color': 'unset',
        'font-style': 'unset',
        opacity: 'unset',
      };
    }
  }

  // Data that gets displayed in the grid
  componentData$: BehaviorSubject<ComponentModel[]> = new BehaviorSubject<
    ComponentModel[]
  >([]);

  getRowId = (TData: any) => TData.data?.unique_id;

  // For accessing the Grid's API
  @ViewChild(AgGridAngular) agGrid!: AgGridAngular;

  instanceDecompostion: any;

  onCellClicked(params: any) {
    const rowNode = params.node;
    if (!rowNode.isSelected()) {
      rowNode.setSelected(true);
    }

    if (
      params.column.getColId() === 'unique_id' ||
      params.column.getColId() === 'current_state' ||
      params.column.getColId() === 'connection_points'
    ) {
      const cellRendererInstances = params.api.getCellRendererInstances({
        rowNodes: [params.node],
        columns: [params.column],
      });
      if (cellRendererInstances.length > 0) {
        this.instanceDecompostion = cellRendererInstances[0];
        this.instanceDecompostion.togglePopup();
      }
    }
  }

  constructor(
    private readonly componentsService: ComponentsService,
    private readonly adminService: AdministrationService,
    private readonly loadingService: LoadingService,
    private readonly router: Router,
    private readonly toastsService: ToastsService,
    private readonly confirmationDialogService: ConfirmationDialogService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.getAccessEliminateObject();
  }

  initForm() {
    const projectItem = localStorage.getItem(PROJECT_ITEM_SELECTED);
    if (projectItem) {
      this.selectedProjectItem = JSON.parse(projectItem);
      if (
        this.selectedProjectItem &&
        this.selectedProjectItem.main_project_state === 'CLOSED'
      ) {
        this.disabledClosedProject = true;
      }
    }

    setTimeout(() => {
      this.doSearchComponentsByCriteria();
    }, 0);
  }

  getAccessEliminateObject() {
    const userObject = localStorage.getItem(USER_LOGGED_IN_KEY);
    if (userObject) {
      this.user = JSON.parse(userObject);
      if (this.user && this.user.admin) {
        // Check if user has ADMIN role
        this.disabledEliminateObject = false;
      } else {
        // Check if user has SENIOR role
        this.accessRoleSubscription = this.adminService
          .getAccessRole(this.selectedProjectItem?.project_id)
          .subscribe((res) => {
            if (res && res.includes(SENIOR_ROLE)) {
              this.disabledEliminateObject = false;
            }
          });
      }
    }
  }

  doSearchComponentsByCriteria() {
    this.loadingService.setLoadingStatus(true);
    this.objectsSubscription = this.componentsService
      .getComponentByCriteria(this.selectedProjectItem?.project_id)
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 100);
        })
      )
      .subscribe((res) => {
        if (res) {
          this.componentData$.next(res);
          this.displayedRowCount = res.length;
        }
      });
  }

  onFilterChanged(event: any) {
    this.displayedRowCount = this.agGrid.api.getDisplayedRowCount();
  }

  getProjectId(projectValue: string) {
    const projectName = projectValue.split(' ')[0];
    if (!projectName || projectName === '') {
      return null;
    }
    // AP_CN_SYS_CN_ShenzhenAirport
    const projectId = this.listProject.find(
      (item) =>
        item.project_name && item.project_name.trim() === projectName.trim()
    )?.project_id;
    return projectId;
  }

  onBodyScroll(event: BodyScrollEvent) {
    if (
      event &&
      this.instanceDecompostion &&
      this.instanceDecompostion.isOpen
    ) {
      this.instanceDecompostion.togglePopup();
    }
  }

  onSelectionChanged(event: any) {
    const selectedRows = this.agGrid.api.getSelectedNodes();
    this.countSelectedRows = selectedRows.length;
    if (selectedRows.length > 0) {
      this.disabledAction = false;
    } else {
      this.disabledAction = true;
    }
  }

  moveForward() {
    if (this.disabledClosedProject || this.disabledAction) {
      return;
    }

    const selectedData: ComponentModel[] = this.agGrid.api.getSelectedRows();
    if (selectedData.length > 0) {
      const arrUniqueId: number[] = [];
      selectedData.forEach((item) => {
        arrUniqueId.push(Number(item.unique_id));
      });

      const reqBody: MoveForwardRequest = {
        component_ids: arrUniqueId,
        project_id: Number(this.selectedProjectItem.project_id),
      };

      this.loadingService.setLoadingStatus(true);
      this.moveForwardSubscription = this.componentsService
        .moveForwardComponents(reqBody)
        .pipe(
          finalize(() => {
            setTimeout(() => {
              this.loadingService.setLoadingStatus(false);
            }, 100);
          })
        )
        .subscribe((res: MoveForwardResponse) => {
          if (res) {
            //update cell new value
            this.handleAfterUpdateState(res);
          }
        });
    } else {
      this.toastsService.showToastr(
        'warning',
        'Component selection is required!',
        '',
        2500
      );
    }
  }

  moveBackward() {
    if (this.disabledClosedProject || this.disabledAction) {
      return;
    }

    // reset variables of modal move backward
    this.resetMoveBackwardForm = !this.resetMoveBackwardForm;
    this.typeModal = 'moveBackward';
    this.removeChangeGroup();

    const selectedRows: ComponentModel[] = this.agGrid.api.getSelectedRows();
    let componentIds: number[] = [];
    selectedRows.forEach((item) => {
      componentIds.push(Number(item.unique_id));
    });

    if (componentIds.length === 0) {
      return;
    }

    this.getListAllowedState(componentIds);
    this.getListChangeGroup();
  }

  unlockComponents() {
    if (this.disabledClosedProject || this.disabledAction) {
      return;
    }

    // reset variables of modal undelete components
    this.resetUnlockComponentsForm = !this.resetUnlockComponentsForm;

    this.typeModal = 'unlockComponents';
    this.removeChangeGroup();
    this.getListChangeGroup();
  }

  handleAfterUpdateState(res: MoveForwardResponse) {
    if (res.new_states) {
      if (res.new_states.length > 100) {
        this.doSearchComponentsByCriteria();
      } else {
        res.new_states.forEach((item) => {
          let rowNode = this.agGrid.api.getRowNode(
            item.component_id.toString()
          );
          rowNode?.setDataValue('current_state', item.state);
          rowNode?.setDataValue('sys_mod_date', item.status_date);
        });
      }
    }
    if (res.new_states && res.new_states.length > 0) {
      this.toastsService.showToastr(
        'success',
        'Status update successful',
        '',
        2500
      );
    }

    if (res.total_skipped > 0) {
      this.toastsService.showCustomForComponentToast(
        'warning',
        '',
        15000,
        res?.skipped_reason,
        res.total_skipped
      );
    }
  }

  getListAllowedState(componentIds: number[]) {
    const reqBody: AllowedStateRequest = {
      component_ids: componentIds,
      project_id: Number(this.selectedProjectItem.project_id),
    };
    this.loadingService.setLoadingStatus(true);
    this.allowedStateSubscription = this.componentsService
      .getAllowedStateByComponentIds(reqBody)
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 100);
        })
      )
      .subscribe((res: KeyValueModel[]) => {
        if (res) {
          this.listAllowedState = res;
          if (this.listAllowedState.length > 0) {
            this.allowedStateSelected = this.listAllowedState[0].value;
            this.disabledMoveBackward = false;
          } else {
            this.disabledMoveBackward = true;
          }
        }
      });
  }

  getListChangeGroup() {
    this.loadingService.setLoadingStatus(true);
    this.listChangeGroupSubscription = this.componentsService
      .getListChangeGroupByProjectId(this.selectedProjectItem?.project_id)
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 100);
        })
      )
      .subscribe((res: ChangeGroupModel[]) => {
        if (res) {
          this.listChangeGroup = res;
        }
      });
  }

  setObjectStatusToDelete() {
    if (this.disabledClosedProject || this.disabledAction) {
      return;
    }

    const selectedRows: ComponentModel[] = this.agGrid.api.getSelectedRows();
    let componentIds: number[] = [];
    selectedRows.forEach((item) => {
      componentIds.push(Number(item.unique_id));
    });

    if (componentIds.length === 0) {
      return;
    }

    this.confirmationDialogService
      .confirm(
        'Confirmation',
        'Do you really want to set object status to delete?',
        true,
        'OK',
        'Cancel'
      )
      .then((confirmed) => {
        if (confirmed) {
          this.doSetStatusToDelete(componentIds);
        }
      })
      .catch((error) => {});
  }

  doSetStatusToDelete(componentIds: any[]) {
    this.loadingService.setLoadingStatus(true);
    this.setStatusToDeleteSubscription = this.componentsService
      .setObjectToDelete(componentIds)
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 100);
        })
      )
      .subscribe((res: MarkAsDeleteResponse) => {
        if (res) {
          //update cell new value
          this.handleAfterUpdateState(res);
        }
      });
  }

  eliminateObject() {
    if (
      this.disabledClosedProject ||
      this.disabledAction ||
      this.disabledEliminateObject
    ) {
      return;
    }

    const selectedRows: ComponentModel[] = this.agGrid.api.getSelectedRows();
    let componentIds: number[] = [];
    selectedRows.forEach((item) => {
      componentIds.push(Number(item.unique_id));
    });

    if (componentIds.length === 0) {
      return;
    }

    this.confirmationDialogService
      .confirm(
        'Confirmation',
        'Do you really want to eliminate object from database?',
        true,
        'OK',
        'Cancel'
      )
      .then((confirmed) => {
        if (confirmed) {
          this.doEliminateObject(componentIds);
        }
      })
      .catch((error) => {});
  }

  doEliminateObject(componentIds: any[]) {
    this.loadingService.setLoadingStatus(true);
    this.eliminateObjectFromDatabaseSubscription = this.componentsService
      .eliminateObjectFromDatabase(componentIds)
      .pipe(
        finalize(() => {
          this.loadingService.setLoadingStatus(false);
        })
      )
      .subscribe((res: MarkAsDeleteResponse) => {
        this.toastsService.showToastr(
          'success',
          'Status update successful',
          '',
          2500
        );
        setTimeout(() => {
          this.doRefreshButton();
        }, 100);
      });
  }

  goToProjectScreen() {
    this.router.navigate(['projects']);
  }

  removeChangeGroup() {
    this.changeGroupName = '';
    this.changeGroupDescription = '';
  }

  receiveRemoveChangeGroup(event: boolean) {
    if (event) {
      this.removeChangeGroup();
    }
  }

  receiveRefreshGrid(event: boolean) {
    if (event) {
      this.doSearchComponentsByCriteria();
    }
  }

  receiveChangeGroup(event: ChangeGroupModel) {
    if (event) {
      this.changeGroupSelected = '';
      this.changeGroupName = event.name;
      this.changeGroupDescription = event.description;
    }
  }

  doRefreshButton() {
    this.disabledAction = true;
    this.agGrid.api.setFilterModel(null);
    this.agGrid.api.onFilterChanged();
    this.agGrid.api.deselectAll();
    this.doSearchComponentsByCriteria();
  }

  exportExcel() {
    if (this.displayedRowCount === 0) {
      this.toastsService.showToastr('warning', 'No data to export!', '', 2500);
      return;
    }

    this.loadingService.setLoadingStatus(true);
    const fileName = 'components';
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet('Components');

    // Define custom header for the worksheet
    worksheet.columns = [
      { header: 'Component Unique ID', key: 'unique_id', width: 30 },
      { header: 'Status', key: 'current_state', width: 50 },
      { header: 'Comment', key: 'comment', width: 30 },
      { header: 'Type ID', key: 'type', width: 30 },
      { header: 'Modification Date', key: 'sys_mod_date', width: 30 },
      { header: 'Delivery Batch', key: 'delivery_batch', width: 30 },
      { header: 'Calculation Area', key: 'calculation_area', width: 30 },
      { header: 'Estop Group', key: 'estop_group', width: 30 },
      { header: 'PLC Area', key: 'plc_area', width: 30 },
      { header: 'Screen', key: 'screen', width: 30 },
      { header: 'Sequence Group', key: 'sequence_group', width: 30 },
      { header: 'Line Name', key: 'line_name', width: 30 },
      { header: 'Building Section', key: 'building_section', width: 30 },
      {
        header: 'Installation Section',
        key: 'installation_section',
        width: 30,
      },
      { header: 'Drawing', key: 'drawing', width: 30 },
      { header: 'Pos No', key: 'pos_no', width: 30 },
      { header: 'Color Group', key: 'color_group', width: 30 },
      { header: 'Total Length', key: 'length_total', width: 30 },
      { header: 'Width', key: 'width', width: 30 },
      { header: 'Speed', key: 'speed', width: 30 },
      { header: 'Load', key: 'load', width: 30 },
      { header: 'Throughput', key: 'throughput', width: 30 },
      { header: 'Motor Controller', key: 'motor_controller', width: 30 },
      { header: 'Unit Reversible', key: 'unit_reversible', width: 30 },
      { header: 'Plant Domain', key: 'plant_domain', width: 30 },
      {
        header: 'Amount Buffer Electric',
        key: 'amount_buffer_elec',
        width: 30,
      },
      {
        header: 'Amount Buffer Mechanic',
        key: 'amount_buffer_mech',
        width: 30,
      },
      { header: 'Buffer Size', key: 'buffer_size', width: 30 },
      { header: 'Drive Position', key: 'drive_position', width: 30 },
      { header: 'Motor Direction', key: 'motor_direction', width: 30 },
      { header: 'Curve Angle', key: 'curve_angle', width: 30 },
      { header: 'Curve Direction', key: 'curve_direction', width: 30 },
      { header: 'Curve Radius', key: 'curve_radius', width: 30 },
      { header: 'Break Type', key: 'break_type', width: 30 },
      { header: 'Section 1 Angle', key: 'section1_angle', width: 30 },
      { header: 'Section 1 Length', key: 'section1_length', width: 30 },
      { header: 'Section 2 Angle', key: 'section2_angle', width: 30 },
      { header: 'Section 2 Length', key: 'section2_length', width: 30 },
      { header: 'Section 3 Angle', key: 'section3_angle', width: 30 },
      { header: 'Section 3 Length', key: 'section3_length', width: 30 },
      { header: 'Section 4 Angle', key: 'section4_angle', width: 30 },
      { header: 'Section 4 Length', key: 'section4_length', width: 30 },
      { header: 'BOM Status', key: 'bom_state', width: 30 },
      { header: 'Reference Status', key: 'reference', width: 30 },
      { header: 'Rotation', key: 'rotation', width: 30 },
      { header: 'Rotation 3D', key: 'rotation3_d', width: 30 },
    ];

    // Style the header
    const header = worksheet.getRow(1);
    header.eachCell((cell, number) => {
      cell.font = {
        bold: true,
      };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '6077bbb3' },
      };
    });

    // Add data to the worksheet
    this.agGrid.api.forEachNodeAfterFilterAndSort((node, index) => {
      const rowData = node.data;
      worksheet.addRow({
        unique_id: rowData.unique_id,
        current_state: rowData?.current_state?.name,
        comment: rowData.comment,
        type: rowData.type,
        sys_mod_date: getDateFromDateString(
          rowData?.sys_mod_date,
          'dd-mm-yyyy',
          true
        ),
        delivery_batch: rowData?.group_mapping?.delivery_batch,
        calculation_area: rowData?.group_mapping?.calculation_area,
        estop_group: rowData?.group_mapping?.estop_group,
        plc_area: rowData?.group_mapping?.plc_area,
        screen: rowData?.group_mapping?.screen,
        sequence_group: rowData?.group_mapping?.sequence_group,
        line_name: rowData?.group_mapping?.line_name,
        building_section: rowData?.group_mapping?.building_section,
        installation_section: rowData?.group_mapping?.installation_section,
        drawing: rowData?.group_mapping?.drawing,
        pos_no: rowData.pos_no,
        color_group: rowData.color_group,
        length_total: rowData.length_total,
        width: rowData.width,
        speed: rowData.speed,
        load: rowData.load,
        throughput: rowData.throughput,
        motor_controller: rowData.motor_controller,
        unit_reversible: rowData.unit_reversible,
        plant_domain: rowData.plant_domain,
        amount_buffer_elec: rowData.amount_buffer_elec,
        amount_buffer_mech: rowData.amount_buffer_mech,
        buffer_size: rowData.buffer_size,
        drive_position: rowData.drive_position,
        motor_direction: rowData.motor_direction,
        curve_angle: rowData.curve_angle,
        curve_direction: rowData.curve_direction,
        curve_radius: rowData.curve_radius,
        break_type: rowData.break_type,
        section1_angle: rowData.section1_angle,
        section1_length: rowData.section1_length,
        section2_angle: rowData.section2_angle,
        section2_length: rowData.section2_length,
        section3_angle: rowData.section3_angle,
        section3_length: rowData.section3_length,
        section4_angle: rowData.section4_angle,
        section4_length: rowData.section4_length,
        bom_state: rowData?.component_status?.bom_state,
        reference: rowData.reference,
        rotation: rowData.rotation,
        rotation3_d: rowData.rotation3_d,
      });
    });

    // Generate the Excel file
    workbook.xlsx
      .writeBuffer()
      .then((data) => {
        const blob = new Blob([data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        FileSaver.saveAs(blob, `${fileName}.xlsx`);
        this.loadingService.setLoadingStatus(false);
      })
      .catch((error) => {
        this.loadingService.setLoadingStatus(false);
      });
  }

  ngOnDestroy(): void {
    if (this.objectsSubscription) {
      this.objectsSubscription.unsubscribe();
    }

    if (this.moveForwardSubscription) {
      this.moveForwardSubscription.unsubscribe();
    }

    if (this.allowedStateSubscription) {
      this.allowedStateSubscription.unsubscribe();
    }

    if (this.setStatusToDeleteSubscription) {
      this.setStatusToDeleteSubscription.unsubscribe();
    }

    if (this.listChangeGroupSubscription) {
      this.listChangeGroupSubscription.unsubscribe();
    }

    if (this.eliminateObjectFromDatabaseSubscription) {
      this.eliminateObjectFromDatabaseSubscription.unsubscribe();
    }

    if (this.accessRoleSubscription) {
      this.accessRoleSubscription.unsubscribe();
    }
  }
}
