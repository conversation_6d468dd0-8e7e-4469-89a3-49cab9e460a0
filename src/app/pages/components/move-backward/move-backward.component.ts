import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { AgGridAngular } from 'ag-grid-angular';
import { finalize, Subscription } from 'rxjs';
import { PROJECT_ITEM_SELECTED } from 'src/app/shared/constants/common';
import { DefaultResponseModel } from 'src/app/shared/interface/common/response.interface';
import { MoveBackwardRequest, MoveBackwardResponse } from 'src/app/shared/interface/components/move-backward.interface';
import {
  ChangeGroupModel,
  ComponentModel
} from 'src/app/shared/interface/components/response.objects';
import { ProjectModel } from 'src/app/shared/interface/projects/project.interface';
import { ComponentsService } from 'src/app/shared/services/api/components.service';
import { LoadingService } from 'src/app/shared/services/store/loading.service';
import { ToastsService } from 'src/app/shared/services/toasts/toasts.service';

@Component({
  selector: 'app-move-backward',
  templateUrl: './move-backward.component.html',
  styleUrls: ['./move-backward.component.scss'],
})
export class MoveBackwardComponent implements OnInit, OnChanges {
  moveBackwardSubscription: Subscription;
  selectedProjectItem: ProjectModel;

  @Input('agGrid') agGrid!: AgGridAngular;
  @Input('listAllowedState') listAllowedState: DefaultResponseModel[] = [];
  @Input('allowedStateSelected') allowedStateSelected: any;
  @Input('name') changeGroupName: string = '';
  @Input('description') changeGroupDescription: string = '';
  @Input('groupSelected') changeGroupSelected: string = '';
  @Input('listChangeGroup') listChangeGroup: ChangeGroupModel[] = [];
  @Input('resetMoveBackwardForm') resetMoveBackwardForm: boolean;
  @Input('disabledMoveBackward') disabledMoveBackward: boolean = false;

  @Output() removeChangeGroupEvent = new EventEmitter<boolean>();
  @Output() refreshGridEvent = new EventEmitter<boolean>();

  constructor(
    private readonly componentsService: ComponentsService,
    private readonly loadingService: LoadingService,
    private readonly toastsService: ToastsService
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes && changes['resetMoveBackwardForm']) {
      this.changeGroupDescription = '';
      this.changeGroupName = '';
      this.changeGroupSelected = '';
      if (this.listAllowedState.length > 0) {
        this.allowedStateSelected = this.listAllowedState[0].value;
      } else {
        this.allowedStateSelected = null;
      }
    }
  }

  ngOnInit(): void {
    const projectItem = localStorage.getItem(PROJECT_ITEM_SELECTED);
    if (projectItem) {
      this.selectedProjectItem = JSON.parse(projectItem);
    }
  }

  doMoveBackwards() {
    if (this.disabledMoveBackward || ((!this.changeGroupName || this.changeGroupName ==='') && (this.changeGroupSelected === '' || !this.changeGroupSelected))) {
      return;
    }

    const selectedRows: ComponentModel[] = this.agGrid.api.getSelectedRows();
    const listComponentIds: number[] = [];
    selectedRows.forEach((item) => {
      listComponentIds.push(Number(item.unique_id));
    });

    let reqBody: MoveBackwardRequest | undefined | null;

    if (
      (!this.changeGroupSelected || this.changeGroupSelected === '') &&
      this.changeGroupName === ''
    ) {
      // no change group
      reqBody = {
        component_ids: listComponentIds,
        new_state: this.allowedStateSelected,
        change_group: null,
        project_id: Number(this.selectedProjectItem.project_id)
      };
    } else if (
      this.changeGroupSelected !== '' &&
      (this.changeGroupName === '' || !this.changeGroupName)
    ) {
      // select exist change group
      const selectedChangeGroup = this.listChangeGroup.find(
        (item) => item.id === Number(this.changeGroupSelected)
      );
      reqBody = {
        component_ids: listComponentIds,
        new_state: this.allowedStateSelected,
        change_group: selectedChangeGroup,
        project_id: Number(this.selectedProjectItem.project_id)
      };
    } else if (!this.changeGroupName || this.changeGroupName !== '') {
      // new change group
      reqBody = {
        component_ids: listComponentIds,
        new_state: this.allowedStateSelected,
        change_group: {
          id: null,
          name: this.changeGroupName,
          description: this.changeGroupDescription,
        },
        project_id: Number(this.selectedProjectItem.project_id)
      };
    }
    this.loadingService.setLoadingStatus(true);
    this.moveBackwardSubscription = this.componentsService
      .moveBackwardComponents(reqBody)
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 300);
        })
      )
      .subscribe(
        (res: MoveBackwardResponse) => {
          if (res) {
            //update cell new value
            if (res.new_states) {
              if (res.new_states.length > 100) {
                this.refreshGridEvent.emit(true);
              } else {
                res.new_states.forEach((item) => {
                  let rowNode = this.agGrid.api.getRowNode(
                    item.component_id.toString()
                  );
                  rowNode?.setDataValue('current_state', item.state);
                  rowNode?.setDataValue('sys_mod_date', item.status_date);
                });
              }
            }

            document.getElementById('backWardCloseButtonModal')?.click();

            if (res.new_states && res.new_states.length > 0) {
              this.toastsService.showToastr(
                'success',
                'Status update successful',
                '',
                2500
              );
            }

            if (res.total_skipped > 0) {
              this.toastsService.showCustomForComponentToast(
                'warning',
                '',
                15000,
                res?.skipped_reason,
                res.total_skipped
              );
            }
          }
        }
      );
  }

  onChangeGroupSelected(event: any) {
    const itemSelected = this.listChangeGroup.find(item => item.id === Number(this.changeGroupSelected));
    if(itemSelected) {
      this.changeGroupDescription = itemSelected.description;
    }else {
      this.changeGroupDescription = '';
    }
  }

  removeChangeGroup() {
    this.changeGroupName = '';
    this.changeGroupSelected = '';
    this.changeGroupDescription = '';
    this.removeChangeGroupEvent.emit(true);
  }

  ngOnDestroy(): void {
    if (this.moveBackwardSubscription) {
      this.moveBackwardSubscription.unsubscribe();
    }
  }
}
