<div class="modal fade" id="moveBackwardModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="moveBackwardLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-blue-8a fw-600" id="moveBackwardLabel">Move backward</h5>
                <button id="backWardCloseButtonModal" type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-20">
                    <label class="form-label fw-500 text-gray-77">Allowed states: <span class="text-red">*</span></label>
                    <select class="form-select" aria-label="Allowed states selectiton"
                        [(ngModel)]="allowedStateSelected">
                        <option *ngFor="let item of listAllowedState;let i = index" [value]="item.value">{{item.name}}
                        </option>
                    </select>
                    <div *ngIf="listAllowedState.length === 0" class="note-error-message">No status available for
                        selected component(s)</div>
                </div>

                <div class="mb-20" *ngIf="changeGroupName">
                    <label class="form-label fw-500 text-gray-77 mr-15">New change group: <span class="text-red">*</span></label>
                    <span class="font-style-italic text-black mr-15">{{changeGroupName}}</span>
                    <span class="icon-remove" title="Remove new change group" (click)="removeChangeGroup()"><i
                            class="bi bi-x-circle"></i></span>
                    <div class="note-infor-message">New change group will be saved when moving backward</div>
                </div>

                <div *ngIf="!changeGroupName || changeGroupName ===''" class="mb-20">
                    <label class="form-label fw-500 text-gray-77">Change group: <span class="text-red">*</span></label>
                    <select class="form-select" aria-label="Change group selectiton" [(ngModel)]="changeGroupSelected" (change)="onChangeGroupSelected($event)">
                        <option value="" selected="true"></option>
                        <option *ngFor="let item of listChangeGroup;let i = index" [value]="item.id">[id={{item.id}}]
                            {{item.name}}
                        </option>
                    </select>

                    <div *ngIf="changeGroupSelected === '' || !changeGroupSelected" class="note-error-message">Please select a change group or create a new one</div>
                </div>

                <div>
                    <label class="form-label fw-500 text-gray-77">Description: </label>
                    <textarea class="form-control" rows="5" [(ngModel)]="changeGroupDescription" disabled
                        maxlength="254"></textarea>
                </div>


            </div>
            <div class="modal-footer">
                <div class="components-action-button" [class.components-action-button-disabled]="disabledMoveBackward || ((!changeGroupName || changeGroupName ==='') && (changeGroupSelected === '' || !changeGroupSelected))"
                    (click)="doMoveBackwards()">Move backwards</div>
                <div class="components-action-button" [class.components-action-button-disabled]="disabledMoveBackward"
                    [attr.data-bs-target]="disabledMoveBackward ? null : '#changeGroupModal'"
                    [attr.data-bs-toggle]="disabledMoveBackward ? null : 'modal'"
                    [attr.data-bs-dismiss]="disabledMoveBackward ? null : 'modal'">
                    {{changeGroupName && changeGroupName !== '' ? 'Update change group': 'New change group'}}
                </div>
            </div>
        </div>
    </div>
</div>