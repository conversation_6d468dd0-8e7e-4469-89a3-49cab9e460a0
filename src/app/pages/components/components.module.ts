import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { SharedComponentsModule } from 'src/app/shared/components/components.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ComponentsComponent } from './components.component';
import { AgGridModule } from 'ag-grid-angular';
import { ComponentUniqueIdComponent } from './component-unique-id/component-unique-id.component';
import { StatusHistoryComponent } from './status-history/status-history.component';
import { ChangeGroupComponent } from './change-group/change-group.component';
import { MoveBackwardComponent } from './move-backward/move-backward.component';
import { UnlockComponentsComponent } from './unlock-components/unlock-components.component';
import { ConnectionPointsComponent } from './connection-points/connection-points.component';
import { ToDoListComponent } from './to-do-list/to-do-list.component';

const routes: Routes = [
  {
    path: '',
    component: ComponentsComponent,
  },
];

@NgModule({
  declarations: [
    ComponentsComponent,
    ComponentUniqueIdComponent,
    StatusHistoryComponent,
    ChangeGroupComponent,
    MoveBackwardComponent,
    UnlockComponentsComponent,
    ConnectionPointsComponent,
    ToDoListComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedComponentsModule,
    FormsModule,
    ReactiveFormsModule,
    AgGridModule
  ]
})
export class ComponentsModule { }
