<div class="d-flex vertical-content flex-column container-fluid">
    <div class="vertical-content-search">
        <div class="d-flex justify-content-between">

            <div class="vertical-content-search-title cursor-pointer d-flex align-items-center"
                data-bs-toggle="collapse" [attr.data-bs-target]="'#objectsCollapse'" aria-expanded="true"
                [attr.aria-controls]="'objectsCollapse'">
                <span class="vertical-content-search-title-icon">
                    <i class="bi bi-chevron-double-right"></i>
                    <i class="bi bi-chevron-double-down"></i>
                </span>
                <span class="fw-600">Actions</span>
            </div>
        </div>

        <div class="collapse show multi-collapse" [attr.id]="'objectsCollapse'">
            <div class="components-action d-flex">
                <div class="components-action-button"
                    [class.components-action-button-disabled]="disabledClosedProject || disabledAction"
                    (click)="moveForward()"><i class="bi bi-forward mr-5"></i>Move forward</div>
                <div class="components-action-button d-flex align-items-center"
                    [class.components-action-button-disabled]="(disabledClosedProject || disabledAction)"
                    [attr.data-bs-toggle]="(disabledClosedProject || disabledAction) ? '' : 'modal' "
                    [attr.data-bs-target]=" (disabledClosedProject || disabledAction) ? '' : '#moveBackwardModal' "
                    (click)="moveBackward()">
                    <div class="ronate-180 mr-5">
                        <i class="bi bi-forward"></i>
                    </div>
                    Move backward
                </div>
                <div class="components-action-button"
                    [class.components-action-button-disabled]="(disabledClosedProject || disabledAction)"
                    [attr.data-bs-toggle]=" (disabledClosedProject || disabledAction) ? '' : 'modal' "
                    [attr.data-bs-target]=" (disabledClosedProject || disabledAction) ? '' : '#unlockComponentsModal' "
                    (click)="unlockComponents()"><i class="bi bi-unlock mr-5"></i>Undelete</div>
                <div class="components-action-button"
                    [class.components-action-button-disabled]="(disabledClosedProject || disabledAction)"
                    (click)="setObjectStatusToDelete()"><i class="bi bi-archive mr-5"></i>Mark as Deleted
                </div>
                <div class="components-action-button"
                    [class.components-action-button-disabled]="(disabledClosedProject || disabledAction || disabledEliminateObject)"
                    (click)="eliminateObject()"><i class="bi bi-database-gear mr-5"></i>Senior user: Eliminate
                    object from database</div>
            </div>
        </div>

    </div>

    <div class="vertical-content-value">

        <div class="vertical-content-value-title d-flex justify-content-between">
            <span>
                Components
            </span>
            <div class="d-flex align-items-center">
                <div class="mr-20 button-to-do" data-bs-toggle="modal" data-bs-target="#toDoListModal">Unfinished Materials</div>
                <div class="reload-icon mr-10" title="Refresh" (click)="doRefreshButton()">
                    <i class="bi bi-arrow-clockwise"></i>
                </div>
                <!-- <div class="cursor-pointer mr-10"><i class="bi bi-journal-album"></i></div> -->
                <div class="cursor-pointer" (click)="exportExcel()" title="Export excel"><i
                        class="bi bi-file-spreadsheet-fill"></i></div>
                <!-- <div class="cursor-pointer"><i class="bi bi-gear"></i></div> -->
            </div>

        </div>

        <div class="vertical-content-value-grid-group">
            <ag-grid-angular style="width: 100%;" class="ag-theme-alpine projects-grid" [getRowId]="getRowId"
                [enableCellTextSelection]="true" [columnDefs]="columnDefs" [defaultColDef]="defaultColDef"
                [rowData]="componentData$| async" [rowSelection]="'multiple'" [animateRows]="true"
                [suppressRowClickSelection]="true" (bodyScroll)="onBodyScroll($event)"
                (cellClicked)="onCellClicked($event)" [getRowStyle]="getRowStyle"
                (selectionChanged)="onSelectionChanged($event)"
                (filterChanged)="onFilterChanged($event)"></ag-grid-angular>

            <div class="d-flex justify-content-between p-17">
                <div class="vertical-content-value-sub-title">Selected: <span
                        class="fw-600">{{countSelectedRows}}</span></div>
                <div class="vertical-content-value-sub-title">Total: <span class="fw-600">{{displayedRowCount}}</span>
                </div>
            </div>
        </div>

    </div>
</div>

<!-- Modal Move Backward -->
<app-move-backward [agGrid]="agGrid" [allowedStateSelected]="allowedStateSelected" [listAllowedState]="listAllowedState"
    [name]="changeGroupName" [description]="changeGroupDescription" [groupSelected]="changeGroupSelected"
    [listChangeGroup]="listChangeGroup" [resetMoveBackwardForm]="resetMoveBackwardForm"
    [disabledMoveBackward]="disabledMoveBackward" (removeChangeGroupEvent)="receiveRemoveChangeGroup($event)"
    (refreshGridEvent)="receiveRefreshGrid($event)"></app-move-backward>

<!-- Modal Undelete components -->
<app-unlock-components [agGrid]="agGrid" [name]="changeGroupName" [description]="changeGroupDescription"
    [groupSelected]="changeGroupSelected" [listChangeGroup]="listChangeGroup"
    (removeChangeGroupEvent)="receiveRemoveChangeGroup($event)" [resetUnlockComponentsForm]="resetUnlockComponentsForm"
    (refreshGridEvent)="receiveRefreshGrid($event)"></app-unlock-components>

<!-- Modal Change Group -->
<app-change-group [name]="changeGroupName" [description]="changeGroupDescription" [typeModal]="typeModal"
    (changeGroupEvent)="receiveChangeGroup($event)"></app-change-group>

<!-- Modal To Do List -->
<app-to-do-list></app-to-do-list>