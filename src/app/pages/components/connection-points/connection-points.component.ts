import { APP_BASE_HREF } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  Inject,
  ViewChild,
} from '@angular/core';
import { ColDef, ColGroupDef, ICellRendererParams } from 'ag-grid-community';
import { BehaviorSubject, Subscription } from 'rxjs';
import { ConnectionPointsModel } from 'src/app/shared/interface/components/response.objects';
import { ComponentsService } from 'src/app/shared/services/api/components.service';
import tippy, { hideAll } from 'tippy.js';

@Component({
  selector: 'app-connection-points',
  templateUrl: './connection-points.component.html',
  styleUrls: ['./connection-points.component.scss'],
})
export class ConnectionPointsComponent implements AfterViewInit {
  params: any;
  isOpen: boolean = false;
  tippyInstance: any;
  public cellValue!: string;

  // Data that gets displayed in the grid
  connectionPointsSubscription: Subscription;
  connectionPointsData$: BehaviorSubject<ConnectionPointsModel[]> =
    new BehaviorSubject<ConnectionPointsModel[]>([]);

  @ViewChild('content') container: any;

  @ViewChild('trigger') button: any;

  // grid config
  // Each Column Definition results in one Column.
  public columnDefs: (ColDef | ColGroupDef)[] = [
    {
      field: 'id',
      headerName: 'ID',
      sort: 'asc'
    },
    { field: 'name', headerName: 'Name' },
    {
      field: 'type',
      headerName: 'Type',
    },
    { field: 'x', headerName: 'X point' },
    { field: 'y', headerName: 'Y point' },
    { field: 'z', headerName: 'Z point' },
    {
      headerName: 'Neighbor',
      marryChildren: true,
      children: [
        {
          field: 'neighbor_component_id',
          headerName: 'Neighbor component',
          width: 200,
          cellClass: 'grid-link',
          cellRenderer: this.neighborIdRenderer,
        },
        {
          field: 'neighbor_connection_name',
          headerName: 'Neighbor name',
          columnGroupShow: 'closed',
          width: 170,
          valueGetter: (params) => {
            return params?.data?.neighbor?.neighbor_connection_name;
          },
        },
        {
          field: 'neighbor_type',
          headerName: 'Neighbor type',
          columnGroupShow: 'closed',
          width: 170,
          valueGetter: (params) => {
            return params?.data?.neighbor?.neighbor_type;
          },
        },
        {
          field: 'gap',
          headerName: 'Gap',
          columnGroupShow: 'closed',
          valueGetter: (params) => {
            return params?.data?.neighbor?.gap;
          },
        },
        {
          field: 'assignment',
          headerName: 'Assignment',
          columnGroupShow: 'closed',
          valueGetter: (params) => {
            return params?.data?.neighbor?.assignment;
          },
        },
        {
          field: 'manual',
          headerName: 'Is manual',
          columnGroupShow: 'closed',
          valueGetter: (params) => {
            return params?.data?.neighbor?.manual;
          },
        },
      ],
    },
  ];

  // DefaultColDef sets props common to all Columns
  public defaultColDef: ColDef = {
    width: 125,
    sortable: true,
    filter: 'agTextColumnFilter',
    resizable: true,
    filterParams: {
      trimInput: true,
      debounceMs: 1000,
    },
  };

  constructor(
    private readonly changeDetector: ChangeDetectorRef,
    private readonly componentsService: ComponentsService,
    @Inject(APP_BASE_HREF) public baseHref: string
  ) {}

  ngAfterViewInit(): void {
    this.tippyInstance = tippy(this.button.nativeElement);
    this.tippyInstance.disable();
  }

  agInit(params: any) {
    this.params = params;
    this.cellValue = this.getValueToDisplay(params);
  }

  getValueToDisplay(params: ICellRendererParams) {
    return params.valueFormatted ? params.valueFormatted : params.value;
  }

  configureTippyInstance() {
    this.tippyInstance.enable();
    this.tippyInstance.show();

    this.tippyInstance.setProps({
      trigger: 'manual',
      placement: 'right',
      arrow: false,
      interactive: true,
      appendTo: document.body,
      hideOnClick: false,
      onShow: (instance: any) => {
        hideAll({ exclude: instance });
      },
      onClickOutside: (instance: any, event: any) => {
        this.isOpen = false;
        instance.unmount();
      },
    });
  }

  togglePopup() {
    this.isOpen = !this.isOpen;
    this.changeDetector.detectChanges();
    if (this.isOpen) {
      this.configureTippyInstance();
      this.getConnectionPoints();
      this.tippyInstance.setContent(this.container.nativeElement);
    } else {
      this.tippyInstance.unmount();
    }
  }

  getConnectionPoints() {
    this.connectionPointsSubscription = this.componentsService
      .getConnectionPointsByComponentId(this.cellValue)
      .subscribe((res) => {
        if (res) {
          this.connectionPointsData$.next(res);
        }
      });
  }

  openInTab() {
    if (!this.cellValue || !this.isOpen) {
      return;
    }

    this.togglePopup();

    window.open(
      `${window.location.origin}${this.baseHref}connection-points?componentId=${this.cellValue}`,
      '_blank',
      'location=yes,height=370,width=970,top=100,left=100,scrollbars=yes,status=yes'
    );
  }

  onCellClicked(params: any) {
    if (
      params.column.getColId() === 'neighbor_component_id' &&
      params.data &&
      params.data?.neighbor
    ) {
      window.open(
        `${window.location.origin}${this.baseHref}connection-points?componentId=${params.data?.neighbor?.neighbor_component_id}`,
        '_blank',
        'location=yes,height=370,width=970,top=100,left=100,scrollbars=yes,status=yes'
      );
    }
  }

  neighborIdRenderer(params: any) {
    if (params?.data?.neighbor?.neighbor_component_id) {
      return (
        params?.data?.neighbor?.neighbor_component_id +
        '<span><i class="bi bi-box-arrow-up-right ml-10"></i></span>'
      );
    } else {
      return '';
    }
  }

  closeDialog() {
    this.isOpen = false;
    this.tippyInstance.unmount();
  }

  ngOnDestroy(): void {
    if (this.connectionPointsSubscription) {
      this.connectionPointsSubscription.unsubscribe();
    }
  }
}
