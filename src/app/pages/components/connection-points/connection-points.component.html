<div>
    <div>
        <span #trigger data-action="toggle" class="modal-component-icon-external" title="List Connection Points">
            <i data-action="toggle" class="bi bi-circle-fill text-black"></i>
            <i data-action="toggle" class="bi bi-arrow-right-short text-black"></i>
            <i data-action="toggle" class="bi bi-circle-fill text-black"></i>
        </span>
    </div>
  
    <div #content *ngIf="isOpen" class="modal-component">
        <div class="modal-component-heading d-flex justify-content-between align-items-center">
            Connection points for Component {{ cellValue }}
            <div class="cursor-pointer reload-icon" title="Close" (click)="closeDialog()"><i class="bi bi-x"></i></div>
        </div>

        <ag-grid-angular 
            style="width: 720px; height: 230px; font-size: 12px;" 
            class="ag-theme-alpine"
            [rowData]="connectionPointsData$ | async" 
            [columnDefs]="columnDefs"
            [enableCellTextSelection]="true"
            [defaultColDef]="defaultColDef"
            [rowSelection]="'single'"
            [animateRows]="true"
            (cellClicked)="onCellClicked($event)"
            >
        </ag-grid-angular>

        <div class="modal-component-link" (click)="openInTab()">
            Open in Tab
        </div>
        
    </div>
</div>
  