<div class="modal fade" id="createProjectModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="createProjectLabel" aria-hidden="true">
    <div class="modal-dialog" style="min-width: 40%;">
        <form [formGroup]="projectForm" (ngSubmit)="onSubmitCreateProject()" style="width: 100%;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-blue-8a fw-600" id="createProjectLabel">Create project</h5>
                    <button id="createProjectButtonModal" type="button" class="btn-close" data-bs-dismiss="modal"
                        aria-label="Close" (click)="cancelModal()"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-20" style="width: 100%;">
                        <label class="form-label fw-500 text-gray-77">Project Name <span class="text-red">*</span></label>
                        <input type="text" class="form-control" name="project_name" maxlength="254"
                            formControlName="project_name">
                        <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
                            validation: 'required',
                            message: 'Project name is required',
                            control: projectForm.controls['project_name']
                          }"></ng-container>
                    </div>


                    <div class="d-flex mb-20 justify-content-between" style="width: 100%;">
                        <div style="min-width: 32%;">
                            <label class="form-label fw-500 text-gray-77">Key <span class="text-red">*</span></label>
                            <input type="text" class="form-control" name="sap_project_key" maxlength="254"
                                formControlName="sap_project_key">
                            <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
                                validation: 'required',
                                message: 'Key is required',
                                control: projectForm.controls['sap_project_key']
                              }"></ng-container>
                        </div>
                        <div style="min-width: 32%;">
                            <label class="form-label fw-500 text-gray-77">Type <span class="text-red">*</span></label>
                            <select class="form-select" aria-label="Type selection" name="type" formControlName="type">
                                <option *ngFor="let item of projectTypes;let i = index" [value]="item">{{item}}
                                </option>
                            </select>
                            <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
                                validation: 'required',
                                message: 'Type is required',
                                control: projectForm.controls['type']
                              }"></ng-container>
                        </div>
                        <div style="min-width: 32%;">
                            <label class="form-label fw-500 text-gray-77">Status <span class="text-red">*</span></label>
                            <input type="text" class="form-control" name="main_project_state" value="OFFER" disabled>
                        </div>
                    </div>

                    <div class="d-flex mb-20 justify-content-between" style="width: 100%;">
                        <div style="width: 49%;">
                            <label class="form-label fw-500 text-gray-77">Customer</label>
                            <input type="text" class="form-control" name="customer_name" maxlength="254"
                                formControlName="customer_name">
                            <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
                                validation: 'required',
                                message: 'Customer name is required',
                                control: projectForm.controls['customer_name']
                              }"></ng-container>
                        </div>
                        <div style="width: 49%;">
                            <label class="form-label fw-500 text-gray-77">Site</label>
                            <input type="text" class="form-control" name="site_name" maxlength="254"
                                formControlName="site_name">
                            <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
                                validation: 'required',
                                message: 'Site name is required',
                                control: projectForm.controls['site_name']
                              }"></ng-container>
                        </div>
                    </div>


                </div>
                <div class="modal-footer">
                    <button class="components-action-button" type="submit">Create</button>
                    <div class="components-action-button" data-bs-dismiss="modal" (click)="cancelModal()">Cancel</div>
                </div>
            </div>
        </form>
    </div>
</div>

<ng-template #formError let-control="control" let-message="message" let-validation="validation">
    <ng-container *ngIf="
      (control.hasError(validation) && (control.dirty || control.touched)) ||
      (control.hasError(validation) && isSubmit)
    ">
        <div class="validate-field">
            {{ message }}
        </div>
    </ng-container>
</ng-template>