import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize, Subscription } from 'rxjs';
import { ProjectModel } from 'src/app/shared/interface/projects/project.interface';
import { ProjectsService } from 'src/app/shared/services/api/projects.service';
import { LoadingService } from 'src/app/shared/services/store/loading.service';
import { ToastsService } from 'src/app/shared/services/toasts/toasts.service';

@Component({
  selector: 'app-create-project',
  templateUrl: './create-project.component.html',
  styleUrls: ['./create-project.component.scss'],
})
export class CreateProjectComponent implements OnInit {
  projectTypes: any[];
  projectStates: any[];
  projectForm: FormGroup;
  isSubmit: boolean = false;

  projectTypesSubscription: Subscription;
  projectStatesSubscription: Subscription;
  createProjectSubscription: Subscription;

  @Output() refreshGridEvent = new EventEmitter<boolean>();

  get f() {
    return this.projectForm.controls;
  }

  constructor(
    private readonly projectsService: ProjectsService,
    private readonly loadingService: LoadingService,
    private readonly toastsService: ToastsService,
    private readonly fb: FormBuilder
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.isSubmit = false;
    this.getListProjectTypes();
    this.getListProjectStates();
  }

  initForm() {
    this.projectForm = this.fb.group({
      project_name: [null, Validators.compose([Validators.required])],
      sap_project_key: [null, Validators.compose([Validators.required])],
      customer_name: [null, Validators.compose([])],
      site_name: [null, Validators.compose([])],
      type: [null, Validators.compose([Validators.required])],
    });
  }

  getListProjectTypes() {
    this.loadingService.setLoadingStatus(true);
    this.projectTypesSubscription = this.projectsService
      .getListAllProjectTypes()
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 300);
        })
      )
      .subscribe(
        (res) => {
          if (res) {
            this.projectTypes = res;
          }
        }
      );
  }

  getListProjectStates() {
    this.loadingService.setLoadingStatus(true);
    this.projectStatesSubscription = this.projectsService
      .getListAllProjectStates()
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 300);
        })
      )
      .subscribe(
        (res) => {
          if (res) {
            this.projectStates = res;
          }
        }
      );
  }

  onSubmitCreateProject() {
    this.isSubmit = true;
    if (!this.projectForm.invalid) {
      const reqBody: ProjectModel = {
        project_name: this.f['project_name'].value,
        sap_project_key: this.f['sap_project_key'].value,
        customer_name: this.f['customer_name'].value,
        site_name: this.f['site_name'].value,
        type: this.f['type'].value,
        main_project_state: 'OFFER'
      };

      this.loadingService.setLoadingStatus(true);
      this.createProjectSubscription = this.projectsService
        .createProject(reqBody)
        .pipe(
          finalize(() => {
            setTimeout(() => {
              this.loadingService.setLoadingStatus(false);
            }, 300);
          })
        )
        .subscribe(
          (res) => {
            if (res) {
              this.refreshGridEvent.emit(true);
              document.getElementById('createProjectButtonModal')?.click();

              this.toastsService.showToastr(
                'success',
                'Create project successfully',
                '',
                2500
              );
            }
          }
        );
    }
  }

  cancelModal() {
    this.initForm();
    this.isSubmit = false;
  }

  ngOnDestroy(): void {
    if (this.projectTypesSubscription) {
      this.projectTypesSubscription.unsubscribe();
    }

    if (this.projectStatesSubscription) {
      this.projectStatesSubscription.unsubscribe();
    }

    if (this.createProjectSubscription) {
      this.createProjectSubscription.unsubscribe();
    }
  }
}
