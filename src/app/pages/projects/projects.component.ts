import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';
import Excel from 'exceljs';
import FileSaver from 'file-saver';
import { BehaviorSubject, finalize, Subscription } from 'rxjs';
import {
  PROJECT_ITEM_SELECTED,
  USER_LOGGED_IN_KEY,
} from 'src/app/shared/constants/common';
import { UserAccount } from 'src/app/shared/interface/account/user.interface';
import { ProjectModel } from 'src/app/shared/interface/projects/project.interface';
import { ProjectsService } from 'src/app/shared/services/api/projects.service';
import { LoadingService } from 'src/app/shared/services/store/loading.service';
import { ProjectSelectedService } from 'src/app/shared/services/store/project.service';
import { ToastsService } from 'src/app/shared/services/toasts/toasts.service';

@Component({
  selector: 'app-projects',
  templateUrl: './projects.component.html',
  styleUrls: ['./projects.component.scss'],
})
export class ProjectsComponent implements OnInit {
  projectStatisticsSubscription: Subscription;
  moveStatusSubscription: Subscription;
  submitCheck: boolean = false;
  selectedProjectItem: ProjectModel | null | undefined;
  disableMoveToExecution: boolean = true;
  disableMoveToClosed: boolean = true;
  displayedRowCount: number = 0;
  user: UserAccount | undefined;
  isAdmin: boolean = false;

  // grid config
  // Each Column Definition results in one Column.
  public columnDefs: ColDef[] = [
    {
      field: 'index',
      headerName: '',
      headerCheckboxSelection: false,
      checkboxSelection: true,
      showDisabledCheckboxes: true,
      filter: false,
      width: 55,
      cellStyle: {
        borderRight: '1px solid #e8ebed',
      },
      suppressMovable: true,
      pinned: 'left'
    },
    {
      field: 'project_name',
      headerName: 'Project Name',
      cellClass: 'projects-content-name',
      width: 400,
      sortable: true,
      sort: 'asc',
    },
    {
      field: 'sap_project_key',
      headerName: 'Key',
      width: 200,
    },
    {
      field: 'customer_name',
      headerName: 'Customer',
      width: 350,
    },
    {
      field: 'site_name',
      headerName: 'Site',
      width: 400,
    },
    {
      field: 'type',
      headerName: 'Type',
      width: 200,
    },
    {
      field: 'main_project_state',
      headerName: 'Status',
      width: 250,
    },
  ];

  // DefaultColDef sets props common to all Columns
  public defaultColDef: ColDef = {
    width: 200,
    sortable: true,
    filter: 'agTextColumnFilter',
    floatingFilter: true,
    resizable: true,
    filterParams: {
      trimInput: true,
      debounceMs: 1000,
    },
  };

  getRowId = (TData: any) => TData.data?.project_id;

  // Data that gets displayed in the grid
  projectData$: BehaviorSubject<ProjectModel[]> = new BehaviorSubject<
    ProjectModel[]
  >([]);

  // For accessing the Grid's API
  @ViewChild(AgGridAngular) agGrid!: AgGridAngular;

  constructor(
    private readonly projectsService: ProjectsService,
    private readonly loadingService: LoadingService,
    private readonly projectSelectedService: ProjectSelectedService,
    private readonly toastsService: ToastsService,
    public router: Router
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.getAllProject();
  }

  onGridReady = (params: any) => {
    var stateFilter = params.api.getFilterInstance('main_project_state')!;
    stateFilter.setModel({
      condition2: {
        type: 'contains',
        filter: 'EXECUTION',
      },
      operator: 'OR',
      condition1: {
        type: 'contains',
        filter: 'OFFER',
      },
    });
    params.api.onFilterChanged();
  };

  initForm() {
    const projectItem = localStorage.getItem(PROJECT_ITEM_SELECTED);
    if (projectItem) {
      this.selectedProjectItem = JSON.parse(projectItem);
    }

    const userObject = localStorage.getItem(USER_LOGGED_IN_KEY);
    if (userObject) {
      this.user = JSON.parse(userObject);
      if (this.user && this.user.admin) {
        this.isAdmin = true;
      }
    }
  }

  getAllProject() {
    this.loadingService.setLoadingStatus(true);
    const params = {};
    this.projectStatisticsSubscription = this.projectsService
      .getListAllProject(params)
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 300);
        })
      )
      .subscribe((res) => {
        if (res) {
          this.projectData$.next(res);
          this.displayedRowCount = res.length;
        }
      });
  }

  onSelectionChanged(event: any) {
    const selectedRows = this.agGrid.api.getSelectedRows();
    if (selectedRows.length > 0) {
      if (selectedRows[0]['main_project_state'] === 'OFFER') {
        this.disableMoveToExecution = false;
        this.disableMoveToClosed = false;
      }

      if (selectedRows[0]['main_project_state'] === 'EXECUTION') {
        this.disableMoveToExecution = true;
        this.disableMoveToClosed = false;
      }

      if (
        this.selectedProjectItem?.project_id !== selectedRows[0]['project_id']
      ) {
        this.toastsService.showToastr(
          'success',
          'Successfully selected project',
          '',
          1500
        );
      }

      this.selectedProjectItem = selectedRows[0];
      localStorage.setItem(
        PROJECT_ITEM_SELECTED,
        JSON.stringify(selectedRows[0])
      );
      this.projectSelectedService.setProjectSelected(true);
    } else {
      this.disableMoveToExecution = true;
      this.disableMoveToClosed = true;
    }
  }

  clearSelection(): void {
    this.agGrid.api.deselectAll();
  }

  doRefreshButton() {
    this.agGrid.api.setFilterModel(null);
    var stateFilter = this.agGrid.api.getFilterInstance('main_project_state')!;
    stateFilter.setModel({
      condition2: {
        type: 'contains',
        filter: 'EXECUTION',
      },
      operator: 'OR',
      condition1: {
        type: 'contains',
        filter: 'OFFER',
      },
    });
    this.agGrid.api.onFilterChanged();
    this.agGrid.api.deselectAll();
    this.getAllProject();
  }

  receiveRefreshGrid(event: boolean) {
    if (event) {
      this.getAllProject();
    }
  }

  onFilterChanged(event: any) {
    this.displayedRowCount = this.agGrid.api.getDisplayedRowCount();
  }

  moveStatus(type: 'EXECUTION' | 'CLOSED') {
    if (type === 'EXECUTION' && this.disableMoveToExecution) {
      return;
    }

    if (type === 'CLOSED' && this.disableMoveToClosed) {
      return;
    }

    this.loadingService.setLoadingStatus(true);
    this.moveStatusSubscription = this.projectsService
      .moveStatus(
        Number(this.selectedProjectItem?.project_id),
        type
      )
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 300);
        })
      )
      .subscribe((res: ProjectModel) => {
        if (res && res?.project_id) {
          if (type === 'EXECUTION') {
            let rowNode = this.agGrid.api.getRowNode(res.project_id.toString());
            rowNode?.setDataValue('main_project_state', res.main_project_state);
            this.disableMoveToExecution = true;
            this.disableMoveToClosed = false;
          } else {
            this.disableMoveToExecution = true;
            this.disableMoveToClosed = true;
            this.getAllProject();
          }

          this.toastsService.showToastr(
            'success',
            'Status update successful',
            '',
            2500
          );
        }
      });
  }

  exportExcel() {
    if (this.displayedRowCount === 0) {
      this.toastsService.showToastr('warning', 'No data to export!', '', 2500);
      return;
    }

    this.loadingService.setLoadingStatus(true);
    const fileName = 'projects';
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet('Projects');

    // Define custom header for the worksheet
    worksheet.columns = [
      { header: 'Project Name', key: 'project_name', width: 50 },
      { header: 'Key', key: 'sap_project_key', width: 20 },
      { header: 'Customer', key: 'customer_name', width: 50 },
      { header: 'Site', key: 'site_name', width: 50 },
      { header: 'Type', key: 'type', width: 20 },
      { header: 'Status', key: 'main_project_state', width: 20 },
    ];

    // Style the header
    const header = worksheet.getRow(1);
    header.eachCell((cell, number) => {
      cell.font = {
        bold: true,
      };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '6077bbb3' },
      };
    });

    // Add data to the worksheet
    this.agGrid.api.forEachNodeAfterFilterAndSort((node, index) => {
      const rowData = node.data;
      worksheet.addRow({
        project_name: rowData.project_name,
        sap_project_key: rowData.sap_project_key,
        customer_name: rowData.customer_name,
        site_name: rowData.site_name,
        type: rowData.type,
        main_project_state: rowData.main_project_state,
      });
    });

    // Generate the Excel file
    workbook.xlsx
      .writeBuffer()
      .then((data) => {
        const blob = new Blob([data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        FileSaver.saveAs(blob, `${fileName}.xlsx`);
        this.loadingService.setLoadingStatus(false);
      })
      .catch((error) => {
        this.loadingService.setLoadingStatus(false);
      });
  }

  ngOnDestroy(): void {
    if (this.projectStatisticsSubscription) {
      this.projectStatisticsSubscription.unsubscribe();
    }

    if (this.moveStatusSubscription) {
      this.moveStatusSubscription.unsubscribe();
    }
  }
}
