<div class="vertical-content container-fluid projects-container">

    <div class="vertical-content-search mb-20">
        <div class="d-flex justify-content-between">

            <div class="vertical-content-search-title cursor-pointer d-flex align-items-center"
                data-bs-toggle="collapse" [attr.data-bs-target]="'#projectCollapse'" aria-expanded="false"
                [attr.aria-controls]="'projectCollapse'">
                <span class="vertical-content-search-title-icon">
                    <i class="bi bi-chevron-double-right"></i>
                    <i class="bi bi-chevron-double-down"></i>
                </span>
                <span class="fw-600">Actions</span>
            </div>

            <!-- <div class="cursor-pointer">
                <span class="mr-5"><i class="bi bi-chat-right-text"></i></span>
                <span class="fw-600">Feedback</span>
            </div> -->
        </div>

        <div class="collapse multi-collapse" [attr.id]="'projectCollapse'">
            <div class="components-action d-flex">
                <div class="components-action-button" [class.components-action-button-disabled]="!isAdmin"
                    [attr.data-bs-toggle]=" isAdmin ? 'modal' : '' "
                    [attr.data-bs-target]=" isAdmin ? '#createProjectModal' : '' "><i
                        class="bi bi-folder-plus mr-5"></i>Create project</div>
                <div class="components-action-button" [class.components-action-button-disabled]="disableMoveToExecution"
                    (click)="moveStatus('EXECUTION')"><i class="bi bi-forward mr-5"></i>Move to EXECUTION</div>
                <div class="components-action-button" [class.components-action-button-disabled]="disableMoveToClosed"
                    (click)="moveStatus('CLOSED')"><i class="bi bi-forward mr-5"></i>Move to CLOSED</div>
            </div>
        </div>

    </div>

    <div class="vertical-content-value projects-content d-flex flex-column">
        <div class="vertical-content-value-title d-flex justify-content-between">
            <span>
                List Projects
            </span>
            <div class="d-flex align-items-center">
                <div class="reload-icon mr-10" title="Refresh" (click)="doRefreshButton()">
                    <i class="bi bi-arrow-clockwise"></i>
                </div>
                <div class="cursor-pointer" (click)="exportExcel()" title="Export excel"><i
                        class="bi bi-file-spreadsheet-fill"></i></div>
            </div>
        </div>
        <div class="vertical-content-value-grid-group">
            <ag-grid-angular style="width: 100%;" class="ag-theme-alpine projects-grid" [columnDefs]="columnDefs"
                [getRowId]="getRowId" [defaultColDef]="defaultColDef" [rowData]="projectData$ | async"
                [rowSelection]="'single'" [animateRows]="true" [enableCellTextSelection]="true"
                (selectionChanged)="onSelectionChanged($event)" (gridReady)="onGridReady($event)"
                (filterChanged)="onFilterChanged($event)"></ag-grid-angular>

            <div class="d-flex justify-content-between p-17">
                <div></div>
                <div class="vertical-content-value-sub-title">Total: <span class="fw-600">{{displayedRowCount}}</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Create Project -->
<app-create-project (refreshGridEvent)="receiveRefreshGrid($event)"></app-create-project>