<div class="modal-component-tab">

    <div class="modal-component-tab-heading d-flex align-items-center justify-content-between">
        <div class="header-text-logo mr-100">
            SPINE
        </div>

        <div class="modal-component-tab-heading-title">
            Connection points for Component {{componentId}}
        </div>

    </div>

    <div class="vertical-content-value-grid-group m-15">
        <ag-grid-angular class="ag-theme-alpine" [rowData]="connectionPointsData$ | async" domLayout="autoHeight"
            [enableCellTextSelection]="true" [columnDefs]="columnDefs" [defaultColDef]="defaultColDef"
            [rowSelection]="'single'" [animateRows]="true" (filterChanged)="onFilterChanged($event)"
            (cellClicked)="onCellClicked($event)">
        </ag-grid-angular>

        <div class="d-flex justify-content-between p-17">
            <div></div>
            <div class="vertical-content-value-sub-title">Total: <span class="fw-600">{{displayedRowCount}}</span></div>
        </div>
    </div>
</div>