import { APP_BASE_HREF } from '@angular/common';
import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef, ColGroupDef } from 'ag-grid-community';
import { BehaviorSubject, Subscription } from 'rxjs';
import { ConnectionPointsModel } from 'src/app/shared/interface/components/response.objects';
import { ComponentsService } from 'src/app/shared/services/api/components.service';

@Component({
  selector: 'app-connection-points-external',
  templateUrl: './connection-points-external.component.html',
  styleUrls: ['./connection-points-external.component.scss'],
})
export class ConnectionPointsExternalComponent implements OnInit {
  componentId: string;
  connectionPointsSubscription: Subscription;
  connectionPointsData$: BehaviorSubject<ConnectionPointsModel[]> =
    new BehaviorSubject<ConnectionPointsModel[]>([]);
  displayedRowCount: number = 0;

  // grid config
  // Each Column Definition results in one Column.
  public columnDefs: (ColDef | ColGroupDef)[] = [
    {
      field: 'id',
      headerName: 'ID',
      sort: 'asc'
    },
    { field: 'name', headerName: 'Name' },
    {
      field: 'type',
      headerName: 'Type',
    },
    { field: 'x', headerName: 'X point' },
    { field: 'y', headerName: 'Y point' },
    { field: 'z', headerName: 'Z point' },
    {
      headerName: 'Neighbor',
      marryChildren: true,
      children: [
        {
          field: 'neighbor_component_id',
          headerName: 'Neighbor component',
          width: 200,
          cellClass: 'grid-link',
          cellRenderer: this.neighborIdRenderer,
        },
        {
          field: 'neighbor_connection_name',
          headerName: 'Neighbor name',
          columnGroupShow: 'closed',
          width: 170,
          valueGetter: (params) => {
            return params?.data?.neighbor?.neighbor_connection_name;
          },
        },
        {
          field: 'neighbor_type',
          headerName: 'Neighbor type',
          columnGroupShow: 'closed',
          width: 170,
          valueGetter: (params) => {
            return params?.data?.neighbor?.neighbor_type;
          },
        },
        {
          field: 'gap',
          headerName: 'Gap',
          columnGroupShow: 'closed',
          valueGetter: (params) => {
            return params?.data?.neighbor?.gap;
          },
        },
        {
          field: 'assignment',
          headerName: 'Assignment',
          columnGroupShow: 'closed',
          valueGetter: (params) => {
            return params?.data?.neighbor?.assignment;
          },
        },
        {
          field: 'manual',
          headerName: 'Is manual',
          columnGroupShow: 'closed',
          valueGetter: (params) => {
            return params?.data?.neighbor?.manual;
          },
        },
      ],
    },
  ];

  // DefaultColDef sets props common to all Columns
  public defaultColDef: ColDef = {
    width: 150,
    sortable: true,
    filter: 'agTextColumnFilter',
    resizable: true,
  };

  // For accessing the Grid's API
  @ViewChild(AgGridAngular) agGrid!: AgGridAngular;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly componentsService: ComponentsService,
    @Inject(APP_BASE_HREF) public baseHref: string
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      if (params) {
        this.componentId = params['componentId'];
        if (this.componentId) {
          this.getConnectionPoints();
        }
      }
    });
  }

  getConnectionPoints() {
    this.connectionPointsSubscription = this.componentsService
      .getConnectionPointsByComponentId(this.componentId)
      .subscribe((res) => {
        if (res) {
          this.connectionPointsData$.next(res);
          this.displayedRowCount = res.length;
        }
      });
  }

  onFilterChanged(event: any) {
    this.displayedRowCount = this.agGrid.api.getDisplayedRowCount();
  }

  onCellClicked(params: any) {
    if (
      params.column.getColId() === 'neighbor_component_id' &&
      params.data &&
      params.data?.neighbor
    ) {
      window.open(
        `${window.location.origin}${this.baseHref}connection-points?componentId=${params.data?.neighbor?.neighbor_component_id}`,
        '_blank',
        'location=yes,height=370,width=970,top=100,left=100,scrollbars=yes,status=yes'
      );
    }
  }

  neighborIdRenderer(params: any) {
    if (params?.data?.neighbor?.neighbor_component_id) {
      return (
        params?.data?.neighbor?.neighbor_component_id +
        '<span><i class="bi bi-box-arrow-up-right ml-10"></i></span>'
      );
    } else {
      return '';
    }
  }

  ngOnDestroy(): void {
    if (this.connectionPointsSubscription) {
      this.connectionPointsSubscription.unsubscribe();
    }
  }
}
