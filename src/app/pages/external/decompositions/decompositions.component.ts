import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';
import { BehaviorSubject, Subscription } from 'rxjs';
import {
  DecompositionModel,
  DecompositionResponse,
} from 'src/app/shared/interface/components/response.objects';
import { ComponentsService } from 'src/app/shared/services/api/components.service';
import { numberSortComparator } from 'src/app/shared/utils/sort-grid.helper';

@Component({
  selector: 'app-decompositions',
  templateUrl: './decompositions.component.html',
  styleUrls: ['./decompositions.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class DecompositionsComponent implements OnInit {
  componentId: string;
  decompositionsSubscription: Subscription;
  decompositionsData$: BehaviorSubject<DecompositionModel[]> =
    new BehaviorSubject<DecompositionModel[]>([]);
  displayedRowCount: number = 0;

  // grid config
  // Each Column Definition results in one Column.
  public columnDefs: ColDef[] = [
    {
      field: 'index',
      headerName: 'Item',
    },
    {
      field: 'position_number',
      headerName: 'Position',
      width: 200,
      sortable: true,
      sort: 'asc',
      comparator: numberSortComparator,
    },
    {
      field: 'sap_material_number',
      headerName: 'Sap Material Number',
      width: 275,
    },
    { field: 'description', headerName: 'Description', width: 275 },
    { field: 'quantity', headerName: 'Quantity' },
    { field: 'unit_in_sap', headerName: 'Unit In Sap' },
    { field: 'derived_from', headerName: 'Derived From' },
    { field: 'standard_material', headerName: 'Standard Material', width: 200 },
    {
      field: 'change_description',
      headerName: 'Change Description',
      width: 200,
    },
    { field: 'mpsprefix', headerName: 'MPS Prefix' },
    { field: 'source', headerName: 'Source' },
    { field: 'zoptions', headerName: 'Zoptions' },
  ];

  // DefaultColDef sets props common to all Columns
  public defaultColDef: ColDef = {
    width: 150,
    sortable: true,
    filter: 'agTextColumnFilter',
    resizable: true,
    filterParams: {
      trimInput: true,
      debounceMs: 1000,
    },
  };

  // For accessing the Grid's API
  @ViewChild(AgGridAngular) agGrid!: AgGridAngular;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly componentsService: ComponentsService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      if (params) {
        this.componentId = params['componentId'];
        if (this.componentId) {
          this.getDecomposition();
        }
      }
    });
  }

  getDecomposition() {
    this.decompositionsSubscription = this.componentsService
      .getDecompositionsByComponentId(this.componentId)
      .subscribe((res) => {
        if (res) {
          const decompositonsRes: DecompositionResponse[] = res;
          // convert data

          const listDecompositions: DecompositionModel[] = [];
          let index = 0;

          decompositonsRes.forEach((item: DecompositionResponse) => {
            const decompositions: DecompositionModel[] = item.decompositions;
            decompositions.forEach((comp: DecompositionModel) => {
              comp.index = index + 1;
              comp.mode = item.mode;
              comp.source = item.source;
              index += 1;

              listDecompositions.push(comp);
            });
          });

          this.decompositionsData$.next(listDecompositions);
          this.displayedRowCount = res.length;
        }
      });
  }

  onFilterChanged(event: any) {
    this.displayedRowCount = this.agGrid.api.getDisplayedRowCount();
  }

  ngOnDestroy(): void {
    if (this.decompositionsSubscription) {
      this.decompositionsSubscription.unsubscribe();
    }
  }
}
