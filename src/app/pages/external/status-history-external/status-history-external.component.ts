import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';
import { BehaviorSubject, Subscription } from 'rxjs';
import { StatusHistoryResponse } from 'src/app/shared/interface/components/response.objects';
import { ComponentsService } from 'src/app/shared/services/api/components.service';
import {
  getDateFromDateString,
  getTimeFromDateString,
} from 'src/app/shared/utils/time.helper';

@Component({
  selector: 'app-status-history-external',
  templateUrl: './status-history-external.component.html',
  styleUrls: ['./status-history-external.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class StatusHistoryExternalComponent implements OnInit {
  componentId: string;
  statusHistorySubscription: Subscription;
  statusHistoryData$: BehaviorSubject<StatusHistoryResponse[]> =
    new BehaviorSubject<StatusHistoryResponse[]>([]);
  displayedRowCount: number = 0;

  // grid config
  // Each Column Definition results in one Column.
  public columnDefs: ColDef[] = [
    {
      field: 'status',
      headerName: 'Status',
      width: 370,
      valueGetter: (params) => {
        return (
          '(' +
          params?.data?.state?.value +
          ')' +
          ' ' +
          params?.data?.state?.name
        );
      },
    },
    {
      field: 'date',
      headerName: 'Date',
      valueGetter: (params) => {
        return getDateFromDateString(params?.data?.status_date, 'dd-mm-yyyy');
      },
    },
    {
      field: 'time',
      headerName: 'Time',
      valueGetter: (params) => {
        return getTimeFromDateString(params?.data?.status_date);
      },
    },
    {
      field: 'change_group',
      headerName: 'Change Group',
      valueGetter: (params) => {
        return params?.data?.change_group?.name;
      },
      width: 350,
    },
    {
      field: 'remark',
      headerName: 'Reason',
      width: 300,
      valueGetter: (params) => {
        return params?.data?.change_group?.description &&
          params?.data?.change_group?.description !== ''
          ? params?.data?.change_group?.description
          : '';
      },
    },
    { field: 'username', headerName: 'User', width: 350 },
  ];

  // DefaultColDef sets props common to all Columns
  public defaultColDef: ColDef = {
    width: 250,
    sortable: true,
    filter: 'agTextColumnFilter',
    resizable: true,
    filterParams: {
      trimInput: true,
      debounceMs: 1000,
    },
  };

  // For accessing the Grid's API
  @ViewChild(AgGridAngular) agGrid!: AgGridAngular;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly componentsService: ComponentsService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      if (params) {
        this.componentId = params['componentId'];
        if (this.componentId) {
          this.getStatusHistory();
        }
      }
    });
  }

  getStatusHistory() {
    this.statusHistorySubscription = this.componentsService
      .getStatusHistoryByComponentId(this.componentId)
      .subscribe((res: StatusHistoryResponse[]) => {
        if (res) {
          this.statusHistoryData$.next(res);
          this.displayedRowCount = res.length;
        }
      });
  }

  onFilterChanged(event: any) {
    this.displayedRowCount = this.agGrid.api.getDisplayedRowCount();
  }

  ngOnDestroy(): void {
    if (this.statusHistorySubscription) {
      this.statusHistorySubscription.unsubscribe();
    }
  }
}
