import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { AgGridModule } from 'ag-grid-angular';
import { SharedComponentsModule } from 'src/app/shared/components/components.module';
import { GroupsComponent } from './groups.component';
import { AssignDeliveryDateComponent } from './assign-delivery-date/assign-delivery-date.component';
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';

const routes: Routes = [
  {
    path: '',
    component: GroupsComponent,
  },
];

@NgModule({
  declarations: [GroupsComponent, AssignDeliveryDateComponent],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedComponentsModule,
    FormsModule,
    ReactiveFormsModule,
    AgGridModule,
    NgbDatepickerModule
  ],
})
export class GroupsModule {}
