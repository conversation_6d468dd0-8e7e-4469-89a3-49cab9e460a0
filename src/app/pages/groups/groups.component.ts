import { Component, OnInit, ViewChild } from '@angular/core';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';
import Excel from 'exceljs';
import FileSaver from 'file-saver';
import { BehaviorSubject, finalize, Subscription } from 'rxjs';
import { ConfirmationDialogService } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.service';
import { PROJECT_ITEM_SELECTED } from 'src/app/shared/constants/common';
import { GroupModel } from 'src/app/shared/interface/groups/response.object';
import { ProjectModel } from 'src/app/shared/interface/projects/project.interface';
import { GroupsService } from 'src/app/shared/services/api/groups.service';
import { LoadingService } from 'src/app/shared/services/store/loading.service';
import { ToastsService } from 'src/app/shared/services/toasts/toasts.service';
import { getDateFromDateString } from 'src/app/shared/utils/time.helper';

@Component({
  selector: 'app-groups',
  templateUrl: './groups.component.html',
  styleUrls: ['./groups.component.scss'],
})
export class GroupsComponent implements OnInit {
  getGroupsSubscription: Subscription;
  deleteGroupSubscription: Subscription;

  selectedProjectItem: ProjectModel;
  disabledAction: boolean = true;
  selectedGroupItem: GroupModel | null | undefined;
  displayedRowCount: number = 0;

  // grid config
  // Each Column Definition results in one Column.
  public columnDefs: ColDef[] = [
    {
      field: 'index',
      headerName: '',
      headerCheckboxSelection: false,
      checkboxSelection: true,
      showDisabledCheckboxes: true,
      filter: false,
      width: 55,
      cellStyle: {
        borderRight: '1px solid #e8ebed',
      },
      suppressMovable: true,
      pinned: 'left'
    },
    {
      colId: 'id',
      field: 'id',
      headerName: 'ID',
      width: 240,
      sortable: true,
      sort: 'asc',
    },
    { field: 'name', headerName: 'Name', width: 700 },
    { field: 'grouptype', headerName: 'Type', width: 350 },
    {
      field: 'deliverydate',
      headerName: 'Delivery Date',
      width: 500,
      valueGetter: (params) => {
        return getDateFromDateString(params?.data?.deliverydate, 'dd-mm-yyyy', true);
      },
    },
  ];

  // DefaultColDef sets props common to all Columns
  public defaultColDef: ColDef = {
    width: 300,
    sortable: true,
    filter: 'agTextColumnFilter',
    floatingFilter: true,
    resizable: true,
    filterParams: {
      trimInput: true,
      debounceMs: 1000,
    },
  };

  getRowId = (TData: any) => TData.data?.id as any;

  // Data that gets displayed in the grid
  groupsData$: BehaviorSubject<GroupModel[]> = new BehaviorSubject<
    GroupModel[]
  >([]);

  // For accessing the Grid's API
  @ViewChild(AgGridAngular) agGrid!: AgGridAngular;

  constructor(
    private readonly groupsService: GroupsService,
    private readonly loadingService: LoadingService,
    private readonly toastsService: ToastsService,
    private readonly confirmationDialogService: ConfirmationDialogService
  ) {}

  ngOnInit(): void {
    const projectItem = localStorage.getItem(PROJECT_ITEM_SELECTED);
    if (projectItem) {
      this.selectedProjectItem = JSON.parse(projectItem);
    }

    this.getListGroups();
  }

  getListGroups() {
    this.loadingService.setLoadingStatus(true);
    this.getGroupsSubscription = this.groupsService
      .getListAllGroups(Number(this.selectedProjectItem.project_id))
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 300);
        })
      )
      .subscribe((res: GroupModel[]) => {
        if (res) {
          this.groupsData$.next(res);
          this.displayedRowCount = res.length;
        }
      });
  }

  onFilterChanged(event: any) {
    this.displayedRowCount = this.agGrid.api.getDisplayedRowCount();
  }

  doRefreshButton() {
    this.agGrid.api.setFilterModel(null);
    this.agGrid.api.onFilterChanged();
    this.agGrid.api.deselectAll();
    this.getListGroups();
  }

  onclickAssignDeliveryDate() {
    this.onSelectionChanged();
  }

  receiveRefreshGrid(event: boolean) {
    if (event) {
      this.getListGroups();
    }
  }

  onSelectionChanged(event?: any) {
    const selectedRows = this.agGrid.api.getSelectedRows();
    if (selectedRows.length > 0) {
      this.disabledAction = false;
      this.selectedGroupItem = selectedRows[0];
    } else {
      this.disabledAction = true;
      this.selectedGroupItem = null;
    }
  }

  deleteGroup() {
    if (!this.selectedGroupItem) {
      return;
    }

    this.confirmationDialogService
      .confirm(
        'Confirmation',
        'Do you really want to delete this group?',
        true,
        'OK',
        'Cancel'
      )
      .then((confirmed) => {
        if (confirmed) {
          this.doDeleteGroup();
        }
      })
      .catch((error) => {});
  }

  doDeleteGroup() {
    this.loadingService.setLoadingStatus(true);
    this.deleteGroupSubscription = this.groupsService
      .deleteGroup(Number(this.selectedGroupItem?.id))
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 300);
        })
      )
      .subscribe((res: any) => {
        this.toastsService.showToastr(
          'success',
          'Delete group successfully',
          '',
          2500
        );
        this.getListGroups();
      });
  }

  exportExcel() {
    if (this.displayedRowCount === 0) {
      this.toastsService.showToastr('warning', 'No data to export!', '', 2500);
      return;
    }
    this.loadingService.setLoadingStatus(true);
    const fileName = 'groups';
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet('Groups');

    // Define custom header for the worksheet
    worksheet.columns = [
      { header: 'ID', key: 'id', width: 40 },
      { header: 'Name', key: 'name', width: 40 },
      { header: 'Type', key: 'grouptype', width: 40 },
      { header: 'Delivery Date', key: 'deliverydate', width: 40 },
    ];

    // Style the header
    const header = worksheet.getRow(1);
    header.eachCell((cell, number) => {
      cell.font = {
        bold: true,
      };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '6077bbb3' },
      };
    });

    // Add data to the worksheet
    this.agGrid.api.forEachNodeAfterFilterAndSort((node, index) => {
      const rowData = node.data;
      worksheet.addRow({
        id: rowData.id,
        name: rowData.name,
        grouptype: rowData.grouptype,
        deliverydate: getDateFromDateString(
          rowData?.deliverydate,
          'dd-mm-yyyy',
          true
        ),
      });
    });

    // Generate the Excel file
    workbook.xlsx
      .writeBuffer()
      .then((data) => {
        const blob = new Blob([data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        FileSaver.saveAs(blob, `${fileName}.xlsx`);
        this.loadingService.setLoadingStatus(false);
      })
      .catch((error) => {
        this.loadingService.setLoadingStatus(false);
      });
  }

  ngOnDestroy(): void {
    if (this.getGroupsSubscription) {
      this.getGroupsSubscription.unsubscribe();
    }

    if (this.deleteGroupSubscription) {
      this.deleteGroupSubscription.unsubscribe();
    }
  }
}
