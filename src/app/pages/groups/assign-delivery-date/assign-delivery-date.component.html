<div class="modal fade" id="assignDeliveryDateModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="assignDeliveryDateLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form [formGroup]="assignForm" (ngSubmit)="onSubmit()" style="width: 100%;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-blue-8a fw-600" id="assignDeliveryDateLabel">Change group</h5>
                    <button id="assignDeliveryDateCloseButtonModal" type="button" class="btn-close"
                        data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-10">
                        <label class="form-label fw-500 text-gray-77 mr-10">ID: </label>
                        <span class="fw-600">{{selectedGroupItem?.id}}</span>
                    </div>

                    <div class="mb-10">
                        <label class="form-label fw-500 text-gray-77 mr-10">Name: </label>
                        <span class="fw-600">{{selectedGroupItem?.name}}</span>
                    </div>

                    <div class="mb-10">
                        <label class="form-label fw-500 text-gray-77 mr-10">Type: </label>
                        <span class="fw-600">{{selectedGroupItem?.grouptype}}</span>
                    </div>

                    <div class="mb-20">
                        <label class="form-label fw-500 text-gray-77">Comment: </label>
                        <textarea class="form-control" rows="3" name="comment" maxlength="254"
                            formControlName="comment"></textarea>
                    </div>

                    <div class="mb-20">
                        <label class="form-label fw-500 text-gray-77">Delivery date: </label>
                        <div class="input-group" style="width:50%">
                            <input
                                class="form-control"
                                placeholder="dd-mm-yyyy"
                                name="deliverydate"
                                formControlName="deliverydate"
                                ngbDatepicker
                                #d="ngbDatepicker"
                                (ngModelChange)="onChooseDate($event)"
                            />
                            <button class="btn btn-outline-secondary bi bi-calendar3" (click)="d.toggle()" type="button"></button>
                        </div>
                        <!-- <input type="date" id="deliveryDate" name="deliverydate" formControlName="deliverydate"
                            class="form-control" style="width: 50%;"> -->
                        <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
                            validation: 'required',
                            message: 'Delivery date is required',
                            control: assignForm.controls['deliverydate']
                          }"></ng-container>
                        <ng-container [ngTemplateOutlet]="valiDateError" [ngTemplateOutletContext]="{
                            message: 'Invalid delivery date',
                            control: assignForm.controls['deliverydate']
                          }"></ng-container>
                    </div>

                </div>
                <div class="modal-footer">
                    <button type="submit" class="components-action-button">Modify</button>
                    <div class="components-action-button" data-bs-toggle="modal" data-bs-dismiss="modal">Cancel</div>
                </div>
            </div>
        </form>
    </div>
</div>

<ng-template #formError let-control="control" let-message="message" let-validation="validation">
    <ng-container *ngIf="
      (control.hasError(validation) && (control.dirty || control.touched)) ||
      (control.hasError(validation) && isSubmit)
    ">
        <div class="validate-field">
            {{ message }}
        </div>
    </ng-container>
</ng-template>

<ng-template #valiDateError let-control="control" let-message="message">
    <ng-container *ngIf="!control.hasError('required') && !isValidDate">
        <div class="validate-field">
            {{ message }}
        </div>
    </ng-container>
</ng-template>