import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';
import { AgGridAngular } from 'ag-grid-angular';
import moment from 'moment';
import { finalize, Subscription } from 'rxjs';
import { PROJECT_ITEM_SELECTED } from 'src/app/shared/constants/common';
import { GroupModel } from 'src/app/shared/interface/groups/response.object';
import { ProjectModel } from 'src/app/shared/interface/projects/project.interface';
import { GroupsService } from 'src/app/shared/services/api/groups.service';
import { CustomDateParserFormatter } from 'src/app/shared/services/datepicker/format-datepicker.service';
import { LoadingService } from 'src/app/shared/services/store/loading.service';
import { ToastsService } from 'src/app/shared/services/toasts/toasts.service';
import { getDateFromDateString, str_pad } from 'src/app/shared/utils/time.helper';

@Component({
  selector: 'app-assign-delivery-date',
  templateUrl: './assign-delivery-date.component.html',
  styleUrls: ['./assign-delivery-date.component.scss'],
  providers: [
    { provide: NgbDateParserFormatter, useClass: CustomDateParserFormatter },
  ],
})
export class AssignDeliveryDateComponent implements OnInit, OnChanges {
  assignForm: FormGroup;
  isSubmit: boolean = false;
  selectedProjectItem: ProjectModel;
  assignDeliveryDateSubscription: Subscription;
  isValidDate: boolean = true;

  @Input('agGrid') agGrid!: AgGridAngular;
  @Input('selectedGroupItem') selectedGroupItem: GroupModel | null | undefined;
  @Output() refreshGridEvent = new EventEmitter<boolean>();

  get f() {
    return this.assignForm.controls;
  }

  constructor(
    private readonly loadingService: LoadingService,
    private readonly toastsService: ToastsService,
    private readonly fb: FormBuilder,
    private readonly groupsService: GroupsService
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes && changes['selectedGroupItem']) {
      this.initForm();
    }
  }

  ngOnInit(): void {
    this.isSubmit = false;
    const projectItem = localStorage.getItem(PROJECT_ITEM_SELECTED);
    if (projectItem) {
      this.selectedProjectItem = JSON.parse(projectItem);
    }
  }

  initForm() {
    this.isValidDate = true;
    let deliveryDate = this.selectedGroupItem
      ? this.selectedGroupItem.deliverydate
      : null;

    this.assignForm = this.fb.group({
      comment: [
        this.selectedGroupItem ? this.selectedGroupItem.comment : null,
        Validators.compose([]),
      ],
      deliverydate: [
        deliveryDate
          ? {
              year: new Date(deliveryDate).getFullYear(),
              month: new Date(deliveryDate).getMonth() + 1,
              day: new Date(deliveryDate).getDate(),
            }
          : null,
        Validators.compose([Validators.required]),
      ],
    });
  }

  onSubmit() {
    this.isSubmit = true;

    if(!this.isValidDatepicker()) {
      this.isValidDate = false;
      return;
    }else {
      this.isValidDate = true;
    }
    
    if (!this.assignForm.invalid) {
      const date = this.f['deliverydate'].value;
      const reqBody = {
        comment: this.f['comment'].value,
        deliverydate: date?.['year'] +'-'+str_pad(date?.['month'])+'-'+ str_pad(date?.['day']),
        ids: [this.selectedGroupItem?.id],
      };

      this.loadingService.setLoadingStatus(true);
      this.assignDeliveryDateSubscription = this.groupsService
        .assignDeliveryDate(
          reqBody,
          Number(this.selectedProjectItem.project_id)
        )
        .pipe(
          finalize(() => {
            setTimeout(() => {
              this.loadingService.setLoadingStatus(false);
            }, 300);
          })
        )
        .subscribe(
          (res: GroupModel[]) => {
            if (res) {
              //update cell new value
              this.refreshGridEvent.emit(true);

              document
                .getElementById('assignDeliveryDateCloseButtonModal')
                ?.click();

              this.toastsService.showToastr(
                'success',
                'Status update successful',
                '',
                2500
              );
            }
          }
        );
    }
  }

  isValidDatepicker() {
    const date = this.f['deliverydate'].value;
    if(!date) {
      return false;
    }
    return moment(str_pad(date?.['day'])+'-'+str_pad(date?.['month'])+'-'+date?.['year'], 'DD-MM-YYYY', true).isValid();
  }

  onChooseDate(date: any) {
    this.isValidDate = this.isValidDatepicker();
  }

  ngOnDestroy(): void {
    if (this.assignDeliveryDateSubscription) {
      this.assignDeliveryDateSubscription.unsubscribe();
    }
  }
}
