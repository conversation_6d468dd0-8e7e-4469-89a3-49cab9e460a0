import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  Renderer2,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subscription, finalize } from 'rxjs';
import {
  HierarchyType,
  MetaDataTypeResponse,
  TypeModel,
} from 'src/app/shared/interface/types/response.object';
import { TypesService } from 'src/app/shared/services/api/types.service';
import { LoadingService } from 'src/app/shared/services/store/loading.service';
import { ToastsService } from 'src/app/shared/services/toasts/toasts.service';
import { uppercaseFirstChar } from 'src/app/shared/utils/character.helper';

@Component({
  selector: 'app-type-form',
  templateUrl: './type-form.component.html',
  styleUrls: ['./type-form.component.scss'],
})
export class TypeFormComponent implements OnInit, OnChanges, AfterViewInit {
  editTypeSubscription: Subscription;
  createTypeSubscription: Subscription;
  getMetaDataTypesSubscription: Subscription;

  @Input('typeItem') selectedTypeItem: TypeModel | null | undefined;
  @Input('resetForm') resetForm: boolean;
  @Input('editableGenericType') editableGenericType: boolean;
  @Input('editableSpecificType') editableSpecificType: boolean;
  @Input('triggerCreateAction') triggerCreateAction: boolean = false;
  @Output() refreshGridEvent = new EventEmitter<TypeModel | null>();

  @ViewChild('scrollSection') scrollSection: ElementRef;
  @ViewChild('contentModal') contentModal: ElementRef;

  typeForm: FormGroup;
  isSubmit: boolean = false;
  showSubmitBtn: boolean = false;
  listTypeAggregator: any[];
  listHierarchyType = HierarchyType;
  listTypeClassification: any[];
  doCreate: boolean = false;

  constructor(
    private readonly fb: FormBuilder,
    private readonly loadingService: LoadingService,
    private readonly typesService: TypesService,
    private readonly toastsService: ToastsService,
    private readonly renderer: Renderer2
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes && changes['resetForm']) {
      this.doCreate = false;
      this.initForm();
      this.isSubmit = false;
    }
    if (changes && changes['triggerCreateAction']) {
      this.doCreate = true;
      this.initForm();
      this.isSubmit = false;
    }
  }

  ngOnInit(): void {
    this.initForm();
    this.getListMetaDataTypes();
  }

  ngAfterViewInit() {
    const contentModalElement = this.contentModal.nativeElement;

    // Attach an event handler to the shown.bs.modal event of the modal element
    this.renderer.listen(contentModalElement, 'shown.bs.modal', () => {
      this.scrollToTop();
    });
  }

  scrollToTop() {
    const scrollSectionElement = this.scrollSection.nativeElement;
    const currentScrollTop = scrollSectionElement.scrollTop;
    const targetScrollTop = 0;
    const duration = 500;
    const startTime = performance.now();

    const scrollAnimation = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easing = this.easeInOutQuad(progress);
      const scrollTo =
        currentScrollTop + (targetScrollTop - currentScrollTop) * easing;
      this.renderer.setProperty(scrollSectionElement, 'scrollTop', scrollTo);

      if (elapsed < duration) {
        requestAnimationFrame(scrollAnimation);
      }
    };
    requestAnimationFrame(scrollAnimation);
  }

  // Easing function for smooth scrolling
  easeInOutQuad(progress: number): number {
    return progress < 0.5
      ? 2 * progress * progress
      : -1 + (4 - 2 * progress) * progress;
  }

  getListMetaDataTypes() {
    this.getMetaDataTypesSubscription = this.typesService
      .getListMetaDataTypes()
      .subscribe((res: MetaDataTypeResponse) => {
        if (res) {
          this.listTypeAggregator = res.type_aggregator;
          this.listTypeClassification = res.project_type_classification;
        }
      });
  }

  get f() {
    return this.typeForm.controls;
  }

  initForm() {
    if (this.doCreate) {
      this.typeForm = this.fb.group({
        type_id: [null, Validators.required],
        main_type: [null, Validators.compose([])],
        sub_type: [null, Validators.compose([])],
        type_version: [null, Validators.compose([])],
        description: [null, Validators.compose([])],
        mech_material_cost_kpi: [null, Validators.compose([])],
        engineering_me_hkpi: [null, Validators.compose([])],
        engineering_hw_hpki: [null, Validators.compose([])],
        engineering_plc_hkpi: [null, Validators.compose([])],
        engineering_it_hkpi: [null, Validators.compose([])],
        project_id: [null, Validators.compose([])],
        project_name: [null, Validators.compose([])],
        url1: [null, Validators.compose([])],
        url2: [null, Validators.compose([])],
        url3: [null, Validators.compose([])],
        url4: [null, Validators.compose([])],
        url5: [null, Validators.compose([])],
        type_variant: [null, Validators.compose([])],
        default_slope: [null, Validators.compose([])],
        default_drive_count: [null, Validators.compose([])],
        version_number: [null, Validators.compose([])],
        supplier: [null, Validators.compose([])],
        engineering_scada_hkpi: [null, Validators.compose([])],
        mps: [null, Validators.compose([])],
        installation_hkpi: [null, Validators.compose([])],
        plc_commissioning_hkpi: [null, Validators.compose([])],
        belt_mass: [null, Validators.compose([])],
        required_acceleration: [null, Validators.compose([])],
        drive_pulley_diameter: [null, Validators.compose([])],
        friction_factor: [null, Validators.compose([])],
        drive_shaft_diameter: [null, Validators.compose([])],
        efficiency_gearbox: [null, Validators.compose([])],
        type_aggregator: [null, Validators.required],
        project_type_classification: [
          {
            value: 'generic',
            disabled: true,
          },
        ],
        hierarchy_type: [null, Validators.required],
        outdated: [false, Validators.compose([])],
        solution_id: [null, Validators.compose([])],
        project_specific: [false, Validators.compose([])],
        original_type: [null, Validators.compose([])],
        sap_material_number: [null, Validators.compose([])],
        special_type: [null, Validators.compose([])],
        creation_date: [null, Validators.compose([])],
        modification_date: [null, Validators.compose([])],
        engineering_hw_one_off_costs: [null, Validators.compose([])],
        engineering_it_one_off_costs: [null, Validators.compose([])],
        engineering_me_one_off_costs: [null, Validators.compose([])],
        engineering_plc_one_off_costs: [null, Validators.compose([])],
        engineering_scada_one_off_costs: [null, Validators.compose([])],
      });
    } else {
      this.typeForm = this.fb.group({
        type_id: [this.selectedTypeItem?.type_id, Validators.required],
        main_type: [this.selectedTypeItem?.main_type, Validators.compose([])],
        sub_type: [this.selectedTypeItem?.sub_type, Validators.compose([])],
        type_version: [
          this.selectedTypeItem?.type_version,
          Validators.compose([]),
        ],
        description: [
          this.selectedTypeItem?.description,
          Validators.compose([]),
        ],
        mech_material_cost_kpi: [
          this.selectedTypeItem?.mech_material_cost_kpi,
          Validators.compose([]),
        ],
        engineering_me_hkpi: [
          this.selectedTypeItem?.engineering_me_hkpi,
          Validators.compose([]),
        ],
        engineering_hw_hpki: [
          this.selectedTypeItem?.engineering_hw_hpki,
          Validators.compose([]),
        ],
        engineering_plc_hkpi: [
          this.selectedTypeItem?.engineering_plc_hkpi,
          Validators.compose([]),
        ],
        engineering_it_hkpi: [
          this.selectedTypeItem?.engineering_it_hkpi,
          Validators.compose([]),
        ],
        project_id: [this.selectedTypeItem?.project_id, Validators.compose([])],
        project_name: [
          this.selectedTypeItem?.project_name,
          Validators.compose([]),
        ],
        url1: [this.selectedTypeItem?.url1, Validators.compose([])],
        url2: [this.selectedTypeItem?.url2, Validators.compose([])],
        url3: [this.selectedTypeItem?.url3, Validators.compose([])],
        url4: [this.selectedTypeItem?.url4, Validators.compose([])],
        url5: [this.selectedTypeItem?.url5, Validators.compose([])],
        type_variant: [
          this.selectedTypeItem?.type_variant,
          Validators.compose([]),
        ],
        default_slope: [
          this.selectedTypeItem?.default_slope,
          Validators.compose([]),
        ],
        default_drive_count: [
          this.selectedTypeItem?.default_drive_count,
          Validators.compose([]),
        ],
        version_number: [
          this.selectedTypeItem?.version_number,
          Validators.compose([]),
        ],
        supplier: [this.selectedTypeItem?.supplier, Validators.compose([])],
        engineering_scada_hkpi: [
          this.selectedTypeItem?.engineering_scada_hkpi,
          Validators.compose([]),
        ],
        mps: [this.selectedTypeItem?.mps, Validators.compose([])],
        installation_hkpi: [
          this.selectedTypeItem?.installation_hkpi,
          Validators.compose([]),
        ],
        plc_commissioning_hkpi: [
          this.selectedTypeItem?.plc_commissioning_hkpi,
          Validators.compose([]),
        ],
        belt_mass: [this.selectedTypeItem?.belt_mass, Validators.compose([])],
        required_acceleration: [
          this.selectedTypeItem?.required_acceleration,
          Validators.compose([]),
        ],
        drive_pulley_diameter: [
          this.selectedTypeItem?.drive_pulley_diameter,
          Validators.compose([]),
        ],
        friction_factor: [
          this.selectedTypeItem?.friction_factor,
          Validators.compose([]),
        ],
        drive_shaft_diameter: [
          this.selectedTypeItem?.drive_shaft_diameter,
          Validators.compose([]),
        ],
        efficiency_gearbox: [
          this.selectedTypeItem?.efficiency_gearbox,
          Validators.compose([]),
        ],
        type_aggregator: [
          this.selectedTypeItem?.type_aggregator,
          Validators.required,
        ],
        project_type_classification: [
          {
            value: this.selectedTypeItem?.project_type_classification,
            disabled: true,
          },
        ],
        hierarchy_type: [
          uppercaseFirstChar(this.selectedTypeItem?.hierarchy_type),
          Validators.required,
        ],
        outdated: [this.selectedTypeItem?.outdated, Validators.compose([])],
        solution_id: [
          this.selectedTypeItem?.solution_id,
          Validators.compose([]),
        ],
        project_specific: [
          this.selectedTypeItem?.project_specific,
          Validators.compose([]),
        ],
        original_type: [
          this.selectedTypeItem?.original_type,
          Validators.compose([]),
        ],
        sap_material_number: [
          this.selectedTypeItem?.sap_material_number,
          Validators.compose([]),
        ],
        special_type: [
          this.selectedTypeItem?.special_type,
          Validators.compose([]),
        ],
        creation_date: [
          this.selectedTypeItem?.creation_date,
          Validators.compose([]),
        ],
        modification_date: [
          this.selectedTypeItem?.modification_date,
          Validators.compose([]),
        ],
        engineering_hw_one_off_costs: [
          this.selectedTypeItem?.engineering_hw_one_off_costs,
          Validators.compose([]),
        ],
        engineering_it_one_off_costs: [
          this.selectedTypeItem?.engineering_it_one_off_costs,
          Validators.compose([]),
        ],
        engineering_me_one_off_costs: [
          this.selectedTypeItem?.engineering_me_one_off_costs,
          Validators.compose([]),
        ],
        engineering_plc_one_off_costs: [
          this.selectedTypeItem?.engineering_plc_one_off_costs,
          Validators.compose([]),
        ],
        engineering_scada_one_off_costs: [
          this.selectedTypeItem?.engineering_scada_one_off_costs,
          Validators.compose([]),
        ],
      });
    }

    if (this.selectedTypeItem?.project_specific) {
      if (this.editableSpecificType) {
        this.showSubmitBtn = true;
        setTimeout(() => {
          this.typeForm.enable();
        }, 0);
      } else {
        this.showSubmitBtn = false;
        setTimeout(() => {
          this.typeForm.disable();
        }, 0);
      }
    } else {
      if (this.editableGenericType) {
        this.showSubmitBtn = true;
        setTimeout(() => {
          this.typeForm.enable();
        }, 0);
      } else {
        this.showSubmitBtn = false;
        setTimeout(() => {
          this.typeForm.disable();
        }, 0);
      }
    }
  }

  onSubmitEditForm() {
    this.isSubmit = true;
    if (!this.typeForm.invalid) {
      if (this.doCreate) {
        const reqBody: TypeModel = {
          ...this.typeForm.value,
        };

        this.loadingService.setLoadingStatus(true);
        this.createTypeSubscription = this.typesService
          .createGenericType(reqBody)
          .pipe(
            finalize(() => {
              setTimeout(() => {
                this.loadingService.setLoadingStatus(false);
              }, 300);
            })
          )
          .subscribe((res) => {
            this.toastsService.showToastr(
              'success',
              'Create new generic type successfully',
              '',
              2500
            );

            this.refreshGridEvent.emit(null);

            document.getElementById('editTypeButtonModal')?.click();
          });
      } else {
        const reqBody: TypeModel = {
          type_id: this.typeForm.controls['type_id'].value,
          ...this.typeForm.value,
        };
        this.loadingService.setLoadingStatus(true);
        this.editTypeSubscription = this.typesService
          .editTypes(reqBody)
          .pipe(
            finalize(() => {
              setTimeout(() => {
                this.loadingService.setLoadingStatus(false);
              }, 300);
            })
          )
          .subscribe((res: TypeModel) => {
            if (res) {
              this.toastsService.showToastr(
                'success',
                'Update type successfully',
                '',
                2500
              );

              this.refreshGridEvent.emit(res);

              document.getElementById('editTypeButtonModal')?.click();
            }
          });
      }
    } else {
      this.scrollToTop();
      this.toastsService.showToastr(
        'warning',
        'Please fill out all required fields',
        '',
        2500
      );
    }
  }

  ngOnDestroy(): void {
    if (this.editTypeSubscription) {
      this.editTypeSubscription.unsubscribe();
    }

    if (this.createTypeSubscription) {
      this.createTypeSubscription.unsubscribe();
    }

    if (this.getMetaDataTypesSubscription) {
      this.getMetaDataTypesSubscription.unsubscribe();
    }
  }
}
