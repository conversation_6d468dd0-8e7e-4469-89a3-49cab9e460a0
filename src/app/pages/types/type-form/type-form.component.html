<div #contentModal class="modal fade" id="editTypeModal" data-bs-backdrop="static" data-bs-keyboard="false"
    tabindex="-1" aria-labelledby="editTypeLabel" aria-hidden="true" style="overflow: unset;">
    <div class="modal-dialog modal-dialog-custom">
        <form [formGroup]="typeForm" (ngSubmit)="onSubmitEditForm()" style="width: 100%;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-blue-8a fw-600" id="editTypeLabel">{{doCreate ? 'Create New Generic Type' : 'Update Type'}}</h5>
                    <button id="editTypeButtonModal" type="button" class="btn-close" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div #scrollSection class="modal-dialog-custom-scroll">
                    <div class="modal-body">
                        <div class="mb-20" style="width: 100%;">
                            <label class="form-label fw-500 text-gray-77">Type </label>
                            <input type="text" class="form-control" name="project_specific" [value]="typeForm.controls['project_specific'].value ? 'Project Specific Types' : 'Generic Types'" [disabled]="true">
                        </div>
    
                        <div class="mb-20" style="width: 100%;">
                            <label class="form-label fw-500 text-gray-77">Type ID <span class="text-red">*</span></label>
                            <input *ngIf="!doCreate" type="text" class="form-control" name="type_id" [value]="typeForm.controls['type_id'].value" [disabled]="true">
                            <input *ngIf="doCreate" type="text" class="form-control" name="type_id" formControlName="type_id" maxlength="254">
                            <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
                                validation: 'required',
                                message: 'Type ID is required',
                                control: typeForm.controls['type_id']
                              }"></ng-container>
                        </div>
    
                        <div class="d-flex justify-content-center">
                            <div class="line-form"></div>
                        </div>
    
                        <div class="d-flex mb-20 justify-content-between" style="width: 100%;">
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Original Type </label>
                                <input type="text" class="form-control" name="original_type" formControlName="original_type" maxlength="254">
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Type Aggregator <span
                                        class="text-red">*</span></label>
                                <select class="form-select" aria-label="Type Aggregator" name="type" formControlName="type_aggregator">
                                    <option *ngFor="let item of listTypeAggregator;let i = index" [value]="item">{{item}}
                                    </option>
                                </select>
                                <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
                                    validation: 'required',
                                    message: 'Type Aggregator is required',
                                    control: typeForm.controls['type_aggregator']
                                  }"></ng-container>
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Type Classification <span
                                        class="text-red">*</span></label>
                                <select class="form-select" aria-label="Type Classification" name="type" [value]="typeForm.controls['project_type_classification'].value" [disabled]="true">
                                    <option *ngFor="let item of listTypeClassification;let i = index" [value]="item">{{item}}
                                    </option>
                                </select>
                            </div>
                        </div>
    
                        <div class="d-flex mb-20 justify-content-between" style="width: 100%;">
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Main Type </label>
                                <input type="text" class="form-control" name="main_type" formControlName="main_type" maxlength="254">
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Sub Type </label>
                                <input type="text" class="form-control" name="sub_type" formControlName="sub_type" maxlength="254">
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Special Type </label>
                                <input type="text" class="form-control" name="special_type" formControlName=special_type maxlength="254">
                            </div>
                        </div>
    
                        <div class="d-flex mb-20 justify-content-between" style="width: 100%;">
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Hierarchy Type <span class="text-red">*</span>
                                </label>
                                <select class="form-select" aria-label="Type Aggregator" name="type" formControlName="hierarchy_type">
                                    <option *ngFor="let item of listHierarchyType;let i = index" [value]="item">{{item}}
                                    </option>
                                </select>
                                <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
                                    validation: 'required',
                                    message: 'Hierarchy Type is required',
                                    control: typeForm.controls['hierarchy_type']
                                  }"></ng-container>
                            </div>

                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Type Version </label>
                                <input type="text" class="form-control" name="type_version" formControlName="type_version" maxlength="254">
                            </div>

                            <div style="min-width: 32%;">
    
                            </div>
                        </div>

    
                        <div class="mb-20" style="width: 100%;">
                            <label class="form-label fw-500 text-gray-77">Description </label>
                            <input type="text" class="form-control" name="description" formControlName="description" maxlength="254">
                        </div>
    
                        <div class="d-flex justify-content-center">
                            <div class="line-form"></div>
                        </div>
    
                        <div class="d-flex mb-20 justify-content-between" style="width: 100%;">
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Mech Material Cost KPI </label>
                                <input type="number" class="form-control" name="mech_material_cost_kpi" formControlName="mech_material_cost_kpi" appNumericOnly>
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Engineering ME H KPI </label>
                                <input type="number" class="form-control" name="engineering_me_hkpi" formControlName="engineering_me_hkpi" appNumericOnly>
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Engineering HW H KPI </label>
                                <input type="number" class="form-control" name="engineering_hw_hpki" formControlName="engineering_hw_hpki" appNumericOnly>
                            </div>
                        </div>
    
                        <div class="d-flex mb-20 justify-content-between" style="width: 100%;">
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Engineering PLC H KPI </label>
                                <input type="number" class="form-control" name="engineering_plc_hkpi" formControlName="engineering_plc_hkpi" appNumericOnly>
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Engineering IT H KPI </label>
                                <input type="number" class="form-control" name="engineering_it_hkpi" formControlName="engineering_it_hkpi" appNumericOnly>
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Installation H KPI </label>
                                <input type="number" class="form-control" name="installation_hkpi" formControlName="installation_hkpi" appNumericOnly>
                            </div>
                        </div>
    
                        <div class="d-flex mb-20 justify-content-between" style="width: 100%;">
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">PLC Commissioning H KPI </label>
                                <input type="number" class="form-control" name="plc_commissioning_hkpi" formControlName="plc_commissioning_hkpi" appNumericOnly>
                            </div>
                            <div style="min-width: 66%;">
                                <label class="form-label fw-500 text-gray-77">MPS </label>
                                <input type="text" class="form-control" name="mps" formControlName="mps" maxlength="254">
                            </div>
                        </div>
    
                        <div class="d-flex justify-content-center">
                            <div class="line-form"></div>
                        </div>
    
                        <div class="d-flex mb-20 justify-content-between" style="width: 100%;">
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Type Variant </label>
                                <input type="text" class="form-control" name="type_variant" formControlName="type_variant" maxlength="254">
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Default Slope </label>
                                <input type="text" class="form-control" name="default_slope" formControlName="default_slope" maxlength="254">
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Default Drive Count </label>
                                <input type="number" class="form-control" name="default_drive_count" formControlName="default_drive_count" appNumericOnly>
                            </div>
                        </div>
    
                        <div class="d-flex mb-20 justify-content-between" style="width: 100%;">
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Version Number </label>
                                <input type="number" class="form-control" name="version_number" formControlName="version_number" appNumericOnly>
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Supplier </label>
                                <input type="text" class="form-control" name="supplier" formControlName="supplier" maxlength="254">
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Engineering SCADA H KPI </label>
                                <input type="number" class="form-control" name="engineering_scada_hkpi" formControlName="engineering_scada_hkpi" appNumericOnly>
                            </div>
                        </div>
    
                        <div class="mb-20" style="width: 100%;">
                            <label class="form-label fw-500 text-gray-77">SAP Material Number </label>
                            <input type="text" class="form-control" name="sap_material_number" formControlName="sap_material_number">
                        </div>
    
                        <div class="d-flex justify-content-center">
                            <div class="line-form"></div>
                        </div>
    
                        <div class="d-flex mb-20 justify-content-between" style="width: 100%;">
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">URL 1 </label>
                                <input type="text" class="form-control" name="url1" formControlName="url1" maxlength="254">
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">URL 2 </label>
                                <input type="text" class="form-control" name="url2" formControlName="url2" maxlength="254">
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">URL 3 </label>
                                <input type="text" class="form-control" name="url3" formControlName="url3" maxlength="254">
                            </div>
    
                        </div>
    
                        <div class="d-flex mb-20 justify-content-between" style="width: 100%;">
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">URL 4 </label>
                                <input type="text" class="form-control" name="url4" formControlName="url4" maxlength="254">
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">URL 5 </label>
                                <input type="text" class="form-control" name="url5" formControlName="url5" maxlength="254">
                            </div>
                            <div style="min-width: 32%;">
    
                            </div>
    
                        </div>
    
                        <div class="d-flex justify-content-center">
                            <div class="line-form"></div>
                        </div>
    
                        <div class="d-flex mb-20 justify-content-between" style="width: 100%;">
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Belt Mass Mer M2 </label>
                                <input type="number" class="form-control" name="belt_mass" formControlName="belt_mass" appNumericOnly>
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Required Acceleration </label>
                                <input type="number" class="form-control" name="required_acceleration" formControlName="required_acceleration" appNumericOnly>
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Diameter Of Drive Pulley </label>
                                <input type="number" class="form-control" name="drive_pulley_diameter" formControlName="drive_pulley_diameter" appNumericOnly>
                            </div>
    
                        </div>
    
                        <div class="d-flex mb-20 justify-content-between" style="width: 100%;">
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Friction Factor </label>
                                <input type="number" class="form-control" name="friction_factor" formControlName="friction_factor" appNumericOnly>
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Drive Shaft Diameter </label>
                                <input type="number" class="form-control" name="drive_shaft_diameter" formControlName="drive_shaft_diameter" appNumericOnly>
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Efficiency Gearbox </label>
                                <input type="number" class="form-control" name="efficiency_gearbox" formControlName="efficiency_gearbox" appNumericOnly>
                            </div>
    
                        </div>
    
                        <div class="d-flex justify-content-center">
                            <div class="line-form"></div>
                        </div>
    
                        <div class="d-flex mb-20 justify-content-between" style="width: 100%;">
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Engineering ME One Off Costs </label>
                                <input type="number" class="form-control" name="engineering_me_one_off_costs" formControlName="engineering_me_one_off_costs" appNumericOnly>
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Engineering HW One Off Costs </label>
                                <input type="number" class="form-control" name="engineering_hw_one_off_costs" formControlName="engineering_hw_one_off_costs" appNumericOnly>
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Engineering PLC One Off Costs </label>
                                <input type="number" class="form-control" name="engineering_plc_one_off_costs" formControlName="engineering_plc_one_off_costs" appNumericOnly>
                            </div>
    
                        </div>
    
                        <div class="d-flex mb-20 justify-content-between" style="width: 100%;">
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Engineering IT One Off Costs </label>
                                <input type="number" class="form-control" name="engineering_it_one_off_costs" formControlName="engineering_it_one_off_costs" appNumericOnly>
                            </div>
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77">Engineering SCADA One Off Costs </label>
                                <input type="number" class="form-control" name="engineering_scada_one_off_costs" formControlName="engineering_scada_one_off_costs" appNumericOnly>
                            </div>
                            <div style="min-width: 32%;">
    
                            </div>
    
                        </div>
    
                        <div class="d-flex mb-20" style="width: 100%;">
                            <div style="min-width: 32%;">
                                <label class="form-label fw-500 text-gray-77 mr-10">Outdated </label>
                                <input class="form-check-input" type="checkbox" formControlName="outdated" aria-label="Checkbox for Outdated">
                            </div>
                        </div>
    
    
    
                    </div>
                    <div class="modal-footer">
                        <button *ngIf="!doCreate && showSubmitBtn" class="components-action-button" type="submit">Update</button>
                        <button *ngIf="doCreate" class="components-action-button" type="submit">Create</button>
                        <div class="components-action-button" data-bs-dismiss="modal">Cancel</div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<ng-template #formError let-control="control" let-message="message" let-validation="validation">
    <ng-container *ngIf="
      (control.hasError(validation) && (control.dirty || control.touched)) ||
      (control.hasError(validation) && isSubmit)
    ">
        <div class="validate-field">
            {{ message }}
        </div>
    </ng-container>
</ng-template>