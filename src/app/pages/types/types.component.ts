import { Component, OnInit, ViewChild } from '@angular/core';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef, ColGroupDef } from 'ag-grid-community';
import Excel from 'exceljs';
import FileSaver from 'file-saver';
import { BehaviorSubject, Subscription, finalize } from 'rxjs';
import { ConfirmationDialogService } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.service';
import {
  PROJECT_ITEM_SELECTED,
  SENIOR_ROLE,
  USER_LOGGED_IN_KEY,
} from 'src/app/shared/constants/common';
import { UserAccount } from 'src/app/shared/interface/account/user.interface';
import { ProjectModel } from 'src/app/shared/interface/projects/project.interface';
import {
  TypeModel,
  TypesValue,
} from 'src/app/shared/interface/types/response.object';
import { AdministrationService } from 'src/app/shared/services/api/administration.service';
import { TypesService } from 'src/app/shared/services/api/types.service';
import { LoadingService } from 'src/app/shared/services/store/loading.service';
import { ToastsService } from 'src/app/shared/services/toasts/toasts.service';
import { getDateFromDateString } from 'src/app/shared/utils/time.helper';

@Component({
  selector: 'app-types',
  templateUrl: './types.component.html',
  styleUrls: ['./types.component.scss'],
})
export class TypesComponent implements OnInit {
  getTypesSubscription: Subscription;
  deleteTypesSubscription: Subscription;
  accessRoleSubscription: Subscription;

  selectedProjectItem: ProjectModel;
  selectedTypeItem: TypeModel | null | undefined;
  disabledAction: boolean = true;
  displayedRowCount: number = 0;
  resetForm: boolean = false;
  currenType: TypesValue = 'Specific';
  listTypes: TypeModel[] = [];
  editableGenericType: boolean = false;
  editableSpecificType: boolean = false;
  user: UserAccount | undefined;
  isAdmin: boolean = false;
  triggerCreateAction: boolean = false;

  // grid config
  // Each Column Definition results in one Column.
  public columnDefs: (ColDef | ColGroupDef)[] = [
    {
      field: 'index',
      headerName: '',
      headerCheckboxSelection: false,
      checkboxSelection: true,
      showDisabledCheckboxes: true,
      filter: false,
      width: 55,
      cellStyle: {
        borderRight: '1px solid #e8ebed',
      },
      suppressMovable: true,
      pinned: 'left',
    },
    {
      field: '',
      headerName: 'Action',
      filter: false,
      width: 90,
      cellStyle: {
        borderRight: '1px solid #e8ebed',
      },
      cellRenderer: (params: any) => {
        if (params?.data?.project_specific) {
          return `
          <div class="d-flex justify-content-center" title=${
            this.editableSpecificType ? 'Edit' : 'View'
          } data-bs-toggle="modal" data-bs-target="#editTypeModal" #trigger>
            <i class="bi ${
              this.editableSpecificType ? 'bi-pencil-square' : 'bi-eye'
            }" style="font-size:18px"></i>
          </div>
          `;
        } else {
          return `
          <div class="d-flex justify-content-center" title=${
            this.editableGenericType ? 'Edit' : 'View'
          } data-bs-toggle="modal" data-bs-target="#editTypeModal" #trigger>
            <i class="bi ${
              this.editableGenericType ? 'bi-pencil-square' : 'bi-eye'
            }" style="font-size:18px"></i>
          </div>
          `;
        }
      },
      colId: 'editAction',
      suppressMovable: true,
      pinned: 'left',
    },
    {
      headerName: 'Type',
      width: 200,
      valueGetter: (params) => {
        return params?.data?.project_specific
          ? 'Project Specific Types'
          : 'Generic Types';
      },
      sortable: true,
      sort: 'desc',
    },
    { field: 'type_id', headerName: 'Type ID', width: 350 },
    {
      headerName: 'Type',
      marryChildren: true,
      children: [
        { field: 'original_type', headerName: 'Original Type', width: 180 },
        {
          field: 'type_aggregator',
          headerName: 'Type Aggregator',
          width: 180,
          columnGroupShow: 'closed',
        },
        {
          field: 'project_type_classification',
          headerName: 'Type Classification',
          width: 180,
          columnGroupShow: 'closed',
        },
        {
          field: 'main_type',
          headerName: 'Main Type',
          columnGroupShow: 'closed',
        },
        {
          field: 'sub_type',
          headerName: 'Sub Type',
          columnGroupShow: 'closed',
        },
        {
          field: 'special_type',
          headerName: 'Special Type',
          columnGroupShow: 'closed',
        },
        {
          field: 'hierarchy_type',
          headerName: 'Hierarchy Type',
          columnGroupShow: 'closed',
        },
      ],
    },
    {
      headerName: 'KPI',
      marryChildren: true,
      children: [
        { field: 'engineering_hw_hpki', headerName: 'ENG HW H KPI' },
        {
          field: 'engineering_it_hkpi',
          headerName: 'ENG IT H KPI',
          columnGroupShow: 'closed',
        },
        {
          field: 'engineering_me_hkpi',
          headerName: 'ENG HE H KPI',
          columnGroupShow: 'closed',
        },
        {
          field: 'engineering_plc_hkpi',
          headerName: 'ENG PLC H KPI',
          columnGroupShow: 'closed',
        },
        {
          field: 'engineering_scada_hkpi',
          headerName: 'ENG SCADA H KPI',
          width: 180,
          columnGroupShow: 'closed',
        },
      ],
    },
    { field: 'description', headerName: 'Description Supplier', width: 350 },
    {
      field: 'mech_material_cost_kpi',
      headerName: 'Mech Material Cost KPI',
      width: 200,
    },
    { field: 'url1', headerName: 'URL', width: 450 },
    { field: 'mps', headerName: 'MPS' },
    {
      headerName: '',
      marryChildren: true,
      children: [
        { field: 'version_number', headerName: 'Version Number', width: 180 },
        {
          field: 'type_variant',
          headerName: 'Type Variant',
          columnGroupShow: 'closed',
        },
        {
          field: 'type_version',
          headerName: 'Type Version',
          columnGroupShow: 'closed',
        },
      ],
    },
    {
      field: 'default_drive_count',
      headerName: 'Default Drive Count',
      width: 200,
    },
    {
      headerName: '',
      marryChildren: true,
      children: [
        { field: 'belt_mass', headerName: 'Belt Mass' },
        {
          field: 'efficiency_gearbox',
          headerName: 'Eff. Gearbox',
          columnGroupShow: 'closed',
        },
        {
          field: 'drive_shaft_diameter',
          headerName: 'Drive Shaft Diameter',
          width: 200,
          columnGroupShow: 'closed',
        },
        {
          field: 'drive_pulley_diameter',
          headerName: 'Drive Pulley Diameter',
          width: 200,
          columnGroupShow: 'closed',
        },
      ],
    },
    {
      headerName: '',
      marryChildren: true,
      children: [
        {
          field: 'creation_date',
          headerName: 'Creation Date',
          valueGetter: (params) => {
            return getDateFromDateString(
              params?.data?.creation_date,
              'dd-mm-yyyy',
              true
            );
          },
        },
        {
          field: 'modification_date',
          headerName: 'Modification Date',
          width: 200,
          valueGetter: (params) => {
            return getDateFromDateString(
              params?.data?.modification_date,
              'dd-mm-yyyy',
              true
            );
          },
        },
      ],
    },
  ];

  // DefaultColDef sets props common to all Columns
  public defaultColDef: ColDef = {
    width: 150,
    sortable: true,
    filter: 'agTextColumnFilter',
    floatingFilter: true,
    resizable: true,
    filterParams: {
      trimInput: true,
      debounceMs: 1000,
    },
  };

  getRowId = (TData: any) => TData.data?.type_id as any;

  // Data that gets displayed in the grid
  typesData$: BehaviorSubject<TypeModel[]> = new BehaviorSubject<TypeModel[]>(
    []
  );
  instanceEditForm: any;
  // For accessing the Grid's API
  @ViewChild(AgGridAngular) agGrid!: AgGridAngular;

  @ViewChild('contentModal') contentModal: any;

  constructor(
    private readonly typesService: TypesService,
    private readonly loadingService: LoadingService,
    private readonly toastsService: ToastsService,
    private readonly confirmationDialogService: ConfirmationDialogService,
    private readonly adminService: AdministrationService
  ) {}

  ngOnInit(): void {
    const projectItem = localStorage.getItem(PROJECT_ITEM_SELECTED);
    if (projectItem) {
      this.selectedProjectItem = JSON.parse(projectItem);
    }

    this.getListTypes();
    this.getAccessEliminateObject();
  }

  onCreateGenericType() {
    this.triggerCreateAction = !this.triggerCreateAction;
  }

  refreshColumnDefs() {
    this.agGrid?.api.setColumnDefs(this.columnDefs);
  }

  receiveRefreshGrid(event: TypeModel | null) {
    if (event === null) {
      this.getListTypes();
    } else {
      let rowNode = this.agGrid.api.getRowNode(event.type_id.toString());

      rowNode?.setData(event);
      this.selectedTypeItem = event;
    }
  }

  onSelectionChanged(event: any) {
    const selectedRows = this.agGrid.api.getSelectedRows();
    if (selectedRows.length > 0) {
      this.disabledAction = false;
      this.selectedTypeItem = selectedRows[0];
    } else {
      this.disabledAction = true;
      this.selectedTypeItem = null;
    }
  }

  onCellClicked(params: any) {
    if (params.column.getColId() === 'editAction') {
      this.resetForm = !this.resetForm;
    }
  }

  getListTypes() {
    this.loadingService.setLoadingStatus(true);
    this.getTypesSubscription = this.typesService
      .getListAllTypes(Number(this.selectedProjectItem.project_id))
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 300);
        })
      )
      .subscribe((res: TypeModel[]) => {
        if (res) {
          this.listTypes = res;

          // filter by Types: Project Specific Types or Generic Types
          this.filterByTypes();
        }
      });
  }

  getAccessEliminateObject() {
    const userObject = localStorage.getItem(USER_LOGGED_IN_KEY);
    if (userObject) {
      this.user = JSON.parse(userObject);
      if (this.user && this.user.admin) {
        // Check if user has ADMIN role
        this.isAdmin = true;
        this.editableGenericType = true;
        this.editableSpecificType = true;

        this.refreshColumnDefs();
      } else {
        this.isAdmin = false;
        // Check if user has SENIOR role
        this.accessRoleSubscription = this.adminService
          .getAccessRole(this.selectedProjectItem?.project_id)
          .subscribe((res) => {
            if (res && res.includes(SENIOR_ROLE)) {
              this.editableGenericType = false;
              this.editableSpecificType = true;

              this.refreshColumnDefs();
            }
          });
      }
    }
  }

  onRadioChange(type: TypesValue) {
    this.currenType = type;
    this.filterByTypes();
  }

  filterByTypes() {
    let filterRes = [];
    if (this.currenType === 'Specific') {
      filterRes = this.listTypes.filter((item) => item?.project_specific);
    } else {
      filterRes = this.listTypes.filter((item) => !item?.project_specific);
    }

    this.typesData$.next(filterRes);
    this.displayedRowCount = filterRes.length;
  }

  onFilterChanged(event: any) {
    this.displayedRowCount = this.agGrid.api.getDisplayedRowCount();
  }

  deleteType() {
    if (!this.selectedTypeItem) {
      return;
    }

    this.confirmationDialogService
      .confirm(
        'Confirmation',
        'Do you really want to delete this type?',
        true,
        'OK',
        'Cancel'
      )
      .then((confirmed) => {
        if (confirmed) {
          this.doDeleteType();
        }
      })
      .catch((error) => {});
  }

  doDeleteType() {
    this.loadingService.setLoadingStatus(true);
    const reqBody = [];
    reqBody.push(this.selectedTypeItem?.type_id);
    this.deleteTypesSubscription = this.typesService
      .deleteTypes(reqBody)
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loadingService.setLoadingStatus(false);
          }, 300);
        })
      )
      .subscribe((res: any) => {
        this.toastsService.showToastr(
          'success',
          'Delete type successfully',
          '',
          2500
        );
        this.getListTypes();
      });
  }

  doRefreshButton() {
    this.agGrid.api.setFilterModel(null);
    this.agGrid.api.onFilterChanged();
    this.agGrid.api.deselectAll();
    this.getListTypes();
  }

  exportExcel() {
    if (this.displayedRowCount === 0) {
      this.toastsService.showToastr('warning', 'No data to export!', '', 2500);
      return;
    }
    this.loadingService.setLoadingStatus(true);
    const fileName = 'types';
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet('Types');

    // Define custom header for the worksheet
    worksheet.columns = [
      { header: 'Type', key: 'project_specific', width: 30 },
      { header: 'Type ID', key: 'type_id', width: 30 },
      { header: 'Original Type', key: 'original_type', width: 30 },
      {
        header: 'Type Aggregator',
        key: 'type_aggregator',
        width: 30,
      },
      {
        header: 'Type Classification',
        key: 'project_type_classification',
        width: 30,
      },
      { header: 'Main Type', key: 'main_type', width: 30 },
      { header: 'SubType', key: 'sub_type', width: 30 },
      { header: 'Special Type', key: 'special_type', width: 30 },
      { header: 'Hierarchy Type', key: 'hierarchy_type', width: 30 },
      { header: 'ENG HW H KPI', key: 'engineering_hw_hpki', width: 30 },
      { header: 'ENG IT H KPI', key: 'engineering_it_hkpi', width: 30 },
      { header: 'ENG HE H KPI', key: 'engineering_me_hkpi', width: 30 },
      { header: 'ENG PLC H KPI', key: 'engineering_plc_hkpi', width: 30 },
      { header: 'ENG SCADA H KPI', key: 'engineering_scada_hkpi', width: 30 },
      { header: 'Description Supplier', key: 'description', width: 30 },
      {
        header: 'Mech Material Cost KPI',
        key: 'mech_material_cost_kpi',
        width: 30,
      },
      { header: 'URL', key: 'url1', width: 30 },
      { header: 'MPS', key: 'mps', width: 30 },
      { header: 'Version Number', key: 'version_number', width: 30 },
      { header: 'Type Variant', key: 'type_variant', width: 30 },
      { header: 'Type Version', key: 'type_version', width: 30 },
      { header: 'Default Drive Count', key: 'default_drive_count', width: 30 },
      { header: 'Belt Mass', key: 'belt_mass', width: 30 },
      { header: 'Eff. Gearbox', key: 'efficiency_gearbox', width: 30 },
      {
        header: 'Drive Shaft Diameter',
        key: 'drive_shaft_diameter',
        width: 30,
      },
      {
        header: 'Drive Pulley Diameter',
        key: 'drive_pulley_diameter',
        width: 30,
      },
      { header: 'Creation Date', key: 'creation_date', width: 30 },
      { header: 'Modification Date', key: 'modification_date', width: 30 },
    ];

    // Style the header
    const header = worksheet.getRow(1);
    header.eachCell((cell, number) => {
      cell.font = {
        bold: true,
      };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '6077bbb3' },
      };
    });

    // Add data to the worksheet
    this.agGrid.api.forEachNodeAfterFilterAndSort((node, index) => {
      const rowData = node.data;
      worksheet.addRow({
        project_specific: rowData.project_specific
          ? 'Project Specific Types'
          : 'Generic Types',
        type_id: rowData.type_id,
        original_type: rowData.original_type,
        type_aggregator: rowData.type_aggregator,
        project_type_classification: rowData.project_type_classification,
        main_type: rowData.main_type,
        sub_type: rowData.sub_type,
        special_type: rowData.special_type,
        hierarchy_type: rowData.hierarchy_type,
        engineering_hw_hpki: rowData.engineering_hw_hpki,
        engineering_it_hkpi: rowData.engineering_it_hkpi,
        engineering_me_hkpi: rowData.engineering_me_hkpi,
        engineering_plc_hkpi: rowData.engineering_plc_hkpi,
        engineering_scada_hkpi: rowData.engineering_scada_hkpi,
        description: rowData.description,
        mech_material_cost_kpi: rowData.mech_material_cost_kpi,
        url1: rowData.url1,
        mps: rowData.mps,
        version_number: rowData.version_number,
        type_variant: rowData.type_variant,
        type_version: rowData.type_version,
        default_drive_count: rowData.default_drive_count,
        belt_mass: rowData.belt_mass,
        efficiency_gearbox: rowData.efficiency_gearbox,
        drive_shaft_diameter: rowData.drive_shaft_diameter,
        drive_pulley_diameter: rowData.drive_pulley_diameter,
        creation_date: getDateFromDateString(
          rowData?.creation_date,
          'dd-mm-yyyy',
          true
        ),
        modification_date: getDateFromDateString(
          rowData?.modification_date,
          'dd-mm-yyyy',
          true
        ),
      });
    });

    // Generate the Excel file
    workbook.xlsx
      .writeBuffer()
      .then((data) => {
        const blob = new Blob([data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        FileSaver.saveAs(blob, `${fileName}.xlsx`);
        this.loadingService.setLoadingStatus(false);
      })
      .catch((error) => {
        this.loadingService.setLoadingStatus(false);
      });
  }

  ngOnDestroy(): void {
    if (this.getTypesSubscription) {
      this.getTypesSubscription.unsubscribe();
    }

    if (this.deleteTypesSubscription) {
      this.deleteTypesSubscription.unsubscribe();
    }

    if (this.accessRoleSubscription) {
      this.accessRoleSubscription.unsubscribe();
    }
  }
}
