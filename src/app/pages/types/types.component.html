<div class="vertical-content container-fluid projects-container">

    <div class="vertical-content-search mb-20">
        <div class="d-flex justify-content-between">

            <div class="vertical-content-search-title cursor-pointer d-flex align-items-center"
                data-bs-toggle="collapse" [attr.data-bs-target]="'#groupCollapse'" aria-expanded="false"
                [attr.aria-controls]="'groupCollapse'">
                <span class="vertical-content-search-title-icon">
                    <i class="bi bi-chevron-double-right"></i>
                    <i class="bi bi-chevron-double-down"></i>
                </span>
                <span class="fw-600">Actions</span>
            </div>
        </div>

        <div class="collapse multi-collapse" [attr.id]="'groupCollapse'">
            <div class="components-action d-flex">
                <div class="components-action-button" [class.components-action-button-disabled]="!isAdmin" (click)="onCreateGenericType()"
                    [attr.data-bs-toggle]=" isAdmin ? 'modal' : '' "
                    [attr.data-bs-target]=" isAdmin ? '#editTypeModal' : '' "><i
                        class="bi bi-folder-plus mr-5"></i>Create generic type</div>
                <div class="components-action-button" [class.components-action-button-disabled]="disabledAction"
                    (click)="deleteType()"><i class="bi bi-trash3 mr-5"></i>Delete</div>
            </div>
        </div>

    </div>

    <div class="vertical-content-value projects-content d-flex flex-column">
        <div class="vertical-content-value-title d-flex justify-content-between">
            <div class="d-flex align-items-center">
                <span>
                    List Types
                </span>
                <div class="d-flex types-option">
                    <div class="form-check mr-15">
                        <input class="form-check-input" type="radio" name="typesRadioDefault" id="specificRadio" checked
                            (change)="onRadioChange('Specific')">
                        <label class="form-check-label" for="specificRadio">
                            Project Specific Types
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="typesRadioDefault" id="genericRadio"
                            (change)="onRadioChange('Generic')">
                        <label class="form-check-label" for="genericRadio">
                            Generic Types
                        </label>
                    </div>
                </div>
            </div>
            <div class="d-flex align-items-center">
                <div class="reload-icon mr-10" title="Refresh" (click)="doRefreshButton()">
                    <i class="bi bi-arrow-clockwise"></i>
                </div>
                <div class="cursor-pointer" (click)="exportExcel()" title="Export excel"><i
                        class="bi bi-file-spreadsheet-fill"></i></div>
            </div>
        </div>

        <div class="vertical-content-value-grid-group">
            <ag-grid-angular style="width: 100%;" class="ag-theme-alpine projects-grid" [columnDefs]="columnDefs"
                [enableCellTextSelection]="true" [getRowId]="getRowId" [defaultColDef]="defaultColDef"
                [rowData]="typesData$ | async" [rowSelection]="'single'" (selectionChanged)="onSelectionChanged($event)"
                (cellClicked)="onCellClicked($event)" [animateRows]="true"
                (filterChanged)="onFilterChanged($event)"></ag-grid-angular>
            <div class="d-flex justify-content-between p-17">
                <div></div>
                <div class="vertical-content-value-sub-title">Total: <span class="fw-600">{{displayedRowCount}}</span>
                </div>
            </div>
        </div>
    </div>
</div>

<app-type-form [triggerCreateAction]="triggerCreateAction" [typeItem]="selectedTypeItem" [editableSpecificType]="editableSpecificType" [editableGenericType]="editableGenericType" (refreshGridEvent)="receiveRefreshGrid($event)"
    [resetForm]="resetForm"></app-type-form>