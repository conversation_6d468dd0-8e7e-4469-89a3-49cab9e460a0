import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { AgGridModule } from 'ag-grid-angular';
import { SharedComponentsModule } from 'src/app/shared/components/components.module';
import { TypesComponent } from './types.component';
import { TypeFormComponent } from './type-form/type-form.component';
import { DirectivesModule } from 'src/app/shared/directives/directives.module';

const routes: Routes = [
  {
    path: '',
    component: TypesComponent,
  },
];

@NgModule({
  declarations: [TypesComponent, TypeFormComponent],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedComponentsModule,
    FormsModule,
    ReactiveFormsModule,
    AgGridModule,
    DirectivesModule
  ],
})
export class TypesModule {}
