import { Routes } from '@angular/router';

const Routing: Routes = [
  {
    path: 'projects',

    loadChildren: () =>
      import('./projects/projects.module').then((m) => m.ProjectsModule),
  },
  {
    path: 'components',

    loadChildren: () =>
      import('./components/components.module').then((m) => m.ComponentsModule),
  },
  {
    path: 'groups',

    loadChildren: () =>
      import('./groups/groups.module').then((m) => m.GroupsModule),
  },
  {
    path: 'types',

    loadChildren: () =>
      import('./types/types.module').then((m) => m.TypesModule),
  },
  {
    path: 'administration',

    loadChildren: () =>
      import('./administration/administration.module').then((m) => m.AdministrationModule),
  },
  {
    path: '',
    redirectTo: 'projects',
    pathMatch: 'full',
  },
  {
    path: '**',
    redirectTo: 'error/404',
  },
];

export { Routing };

