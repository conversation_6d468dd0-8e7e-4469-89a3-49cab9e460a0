# Stage 1
FROM node:16-bullseye-slim as node
ARG base_href
WORKDIR /app
COPY package.json /app/
RUN npm install
RUN npm install -g @angular/cli@14.2.13

# Configuration URL
ENV API_URL="https://spine-db-dev.kscl.dev/spine-core/api/v1"
# Configuration Keycloak
ENV KEYCLOAK_URL="https://spine-db-dev.kscl.dev/keycloak/"
ENV KEYCLOAK_REALM="spine"
ENV KEYCLOAK_CLIENT_ID="spine-core"

COPY ./ /app/

RUN ng build --configuration production --aot --base-href="/spine-core-frontend/"

# Stage 2
FROM nginx:alpine-slim
RUN mkdir /usr/share/nginx/html/spine-core-frontend
COPY --from=node /app/dist/spine-ui /usr/share/nginx/html/spine-core-frontend
COPY --from=node /app/nginx.conf /etc/nginx/nginx.conf
COPY --from=node /app/default.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
