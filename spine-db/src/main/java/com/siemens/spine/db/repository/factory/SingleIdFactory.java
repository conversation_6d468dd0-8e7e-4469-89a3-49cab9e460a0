package com.siemens.spine.db.repository.factory;

import com.siemens.spine.db.exception.JpaException;
import com.siemens.spine.db.utils.CompositeKeyUtils;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.Id;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Factory for handling entities with a single @Id field.
 * This is technically not a composite key, but included for completeness
 * and to provide a unified interface for all key types.
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
public class SingleIdFactory extends AbstractCompositeKeyFactory {

    @Override
    public boolean canHandle(Class<?> entityClass) {
        if (entityClass == null) {
            return false;
        }

        List<Field> idFields = extractIdFields(entityClass);
        return idFields.size() == 1;
    }

    @Override
    public CompositeKeyUtils.CompositeKeyType getKeyType() {
        return CompositeKeyUtils.CompositeKeyType.SINGLE_ID;
    }

    @Override
    public List<Field> extractIdFields(Class<?> entityClass) {
        List<Field> idFields = new ArrayList<>();

        if (entityClass == null) {
            return idFields;
        }

        // Find all @Id annotated fields
        Class<?> currentClass = entityClass;
        while (currentClass != null && currentClass != Object.class) {
            for (Field field : currentClass.getDeclaredFields()) {
                if (field.isAnnotationPresent(Id.class)) {
                    idFields.add(field);
                }
            }
            currentClass = currentClass.getSuperclass();
        }

        return idFields;
    }

    @Override
    public CompositeKeyUtils.CompositeKeyData createKeyDataFromMap(Map<String, String> keyValueMap,
                                                                   Class<?> entityClass) throws JpaException {
        List<Field> idFields = extractIdFields(entityClass);

        // For single ID, we expect exactly one key-value pair
        if (keyValueMap.size() != 1) {
            throw new JpaException(String.format(
                    "Single ID entity '%s' expects exactly 1 key-value pair, but got %d",
                    entityClass.getSimpleName(), keyValueMap.size()
            ));
        }

        if (idFields.size() != 1) {
            throw new JpaException(String.format(
                    "Single ID entity '%s' should have exactly 1 @Id field, but found %d",
                    entityClass.getSimpleName(), idFields.size()
            ));
        }

        CompositeKeyUtils.CompositeKeyData keyData = new CompositeKeyUtils.CompositeKeyData();
        keyData.setKeyType(getKeyType());

        Field idField = idFields.get(0);
        String keyName = keyValueMap.keySet().iterator().next();
        String stringValue = keyValueMap.get(keyName);

        // Find matching field (supporting snake_case conversion)
        FieldMatch fieldMatch = findMatchingField(keyName, idFields, entityClass);

        try {
            Object convertedValue = convertToFieldType(stringValue, fieldMatch.field().getType());
            keyData.addComponent(fieldMatch.field().getName(), fieldMatch.field().getType(), convertedValue);

            log.debug("Successfully created single ID key data for entity '{}': {} = {}",
                    entityClass.getSimpleName(), fieldMatch.field().getName(), convertedValue);
        } catch (Exception e) {
            throw new JpaException(String.format(
                    "Failed to convert key value '%s' to type %s for field '%s'",
                    stringValue, fieldMatch.field().getType().getSimpleName(), fieldMatch.field().getName()
            ), e);
        }

        return keyData;
    }

    @Override
    public void validateKeyConfiguration(Class<?> entityClass) throws JpaException {
        List<Field> idFields = extractIdFields(entityClass);

        if (idFields.isEmpty()) {
            throw new JpaException("Entity class " + entityClass.getSimpleName() + " has no @Id fields");
        }

        if (idFields.size() > 1) {
            throw new JpaException(String.format(
                    "Entity class %s has %d @Id fields but should have exactly 1 for single ID strategy",
                    entityClass.getSimpleName(), idFields.size()
            ));
        }

        Field idField = idFields.get(0);
        Class<?> fieldType = idField.getType();

        // Validate that the field type is supported
        if (isSupportedType(fieldType)) {
            throw new JpaException(String.format(
                    "Unsupported ID field type %s for field '%s' in entity %s",
                    fieldType.getSimpleName(), idField.getName(), entityClass.getSimpleName()
            ));
        }
    }

    @Override
    public int getPriority() {
        return 100; // Lowest priority - fallback for single @Id fields
    }

}