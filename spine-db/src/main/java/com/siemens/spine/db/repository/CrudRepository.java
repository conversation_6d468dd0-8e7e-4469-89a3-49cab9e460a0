package com.siemens.spine.db.repository;

import com.siemens.spine.db.repository.paging.Page;
import com.siemens.spine.db.repository.paging.Pageable;

import javax.persistence.criteria.Predicate;
import java.util.Optional;

/**
 * This is the base crud repository for spine/elcon
 *
 * @param <T>  Entity
 * @param <ID> Data type of the entity ID
 */
public interface CrudRepository<T, ID> {

    <S extends T> S save(S entity);

    Optional<T> findById(ID id);

    Page<T> findAll(Pageable pageable);

    void deleteById(ID id);

    boolean existsById(ID id);

    long count(Predicate... predicates);

    Class<T> getDomainType();

}