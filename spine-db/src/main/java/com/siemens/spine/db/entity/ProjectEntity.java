package com.siemens.spine.db.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.util.Objects;

@Entity
@Table(name = "project")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "project_id_seq_gen")
    @SequenceGenerator(name = "project_id_seq_gen", sequenceName = "project_id_seq")
    protected Long projectID;

    protected Double averageObjectLengthForBelt;

    protected Double averageObjectLengthForOOGTray;

    protected Double averageObjectLengthForTray;

    protected Double averageWeightBag;

    protected Double averageWeightOversizeBag;

    protected Double bagWeight;

    protected String beltDriveVendor;

    protected String brakeRelatedZOptionsDol;

    protected String brakeRelatedZOptionsVfd;

    protected Double brakeResistorCycleTime;

    protected String brakingResistor;

    protected String client;

    protected String customerName;

    protected String fieldbus;

    protected Double gearboxServiceFactor;

    protected String generalZOptions;

    protected String increasedVfdSize;

    protected String mainProjectState;

    protected Double maxInputRpmGearbox;

    protected Double maxRelativeHumidity;

    protected Double minGapTimeForBelt;

    protected Double minGapTimeForTray;

    protected String motorEfficiencyClass;

    protected Double motorServiceFactor;

    protected Double motorStarterDriveServiceFactor;

    protected String mountingType;

    protected Double operatingTemperatureHigh;

    protected Double operatingTemperatureLow;

    protected Double oversizeBagWeight;

    protected Double oversizeTrayWeight;

    protected String packageType;

    protected String projectLeader;

    protected Double relativeDutyFactor;

    protected String repairSwitch;

    protected String sapProjectKey;

    protected String siteName;

    protected Double supplyFrequency;

    protected Double supplyVoltage;

    protected String trayDriveVendor;

    protected Double trayStackSize;

    protected Double trayWeight;

    protected String type;

    protected Double vfdAccelerationTorque;

    protected Double vfdDriveServiceFactor;

    protected Double vfdOutputVoltage;

    protected String vfdType;

    protected Double weightOfBelt;

    protected Boolean withSimulation;
    @Column(name = "name1")
    protected String projectName; // probably unused or only used for ORDER BY!
    // From WOADM.COMPANY, alternatively could be kept in a separate table ProjectInSolution
    // From WOADM.COMPANY, alternatively could be kept in a separate table ProjectInSolution
    private Long solutionId;
    // From WOADM.COMPANY, alternatively could be kept in a separate table ProjectInSolution
    // From WOADM.GROUPMEMBER, alternatively could be kept in a separate table ProjectInSolution
    private Long projectsGroupId;

    @Override
    public int hashCode() {
        return Objects.hash(projectID);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ProjectEntity project = (ProjectEntity) o;
        return Objects.equals(projectID, project.projectID);
    }

    public String toString() {
        return "Project {" + "projectID: " + projectID + "}";
    }

}

