package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_plc_area")
public class ElconPlcArea {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "area_id", nullable = false)
    private Long areaId;

    @NotNull
    @Column(name = "project_version_id", nullable = false)
    private Long projectVersionId;

    @Size(max = 255)
    @Column(name = "plc_area_name")
    private String plcAreaName;

    @NotNull
    @Column(name = "sw_relevant", nullable = false)
    private Short swRelevant;

    @Column(name = "sw_library_id")
    private Long swLibraryId;

    @Column(name = "plc_area_nr")
    private Integer plcAreaNr;

}