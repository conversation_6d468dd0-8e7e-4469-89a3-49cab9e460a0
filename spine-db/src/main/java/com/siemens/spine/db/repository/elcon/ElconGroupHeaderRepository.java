package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconGroupHeader;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_group_header")
@ApplicationScoped
public class ElconGroupHeaderRepository extends AbstractRestCRUDRepositoryImpl<ElconGroupHeader, Long> {

    @Override
    protected boolean isNew(ElconGroupHeader entity) {
        return entity.getGroupHeaderId() == null;
    }

    @Override
    public Class<ElconGroupHeader> getDomainType() {
        return ElconGroupHeader.class;
    }
} 