package com.siemens.spine.db.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "counter")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CounterEntity {

    @Id
    protected String name;

    protected long counterValue;

    protected long minCounter;

    protected long maxCounter;

    protected long warning;

}

