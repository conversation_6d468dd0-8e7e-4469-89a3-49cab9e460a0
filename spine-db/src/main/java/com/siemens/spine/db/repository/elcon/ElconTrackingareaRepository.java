package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconTrackingarea;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_trackingarea")
@ApplicationScoped
public class ElconTrackingareaRepository extends AbstractRestCRUDRepositoryImpl<ElconTrackingarea, Long> {

    @Override
    protected boolean isNew(ElconTrackingarea entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconTrackingarea> getDomainType() {
        return ElconTrackingarea.class;
    }
} 