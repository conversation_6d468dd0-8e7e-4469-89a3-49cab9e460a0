package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconSimuMergeProperty;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_simu_merge_property")
@ApplicationScoped
public class ElconSimuMergePropertyRepository extends AbstractRestCRUDRepositoryImpl<ElconSimuMergeProperty, Long> {

    @Override
    protected boolean isNew(ElconSimuMergeProperty entity) {
        return entity.getPropertyId() == null;
    }

    @Override
    public Class<ElconSimuMergeProperty> getDomainType() {
        return ElconSimuMergeProperty.class;
    }
} 