package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_global_cfg_systype_object_kind_mapping")
public class ElconGlobalCfgSystypeObjectKindMapping {

    @Id
    @Column(name = "cfg_id", nullable = false)
    private Long cfgId;

    @Size(max = 255)
    @Column(name = "sys_type")
    private String sysType;

    @Size(max = 255)
    @Column(name = "object_kind")
    private String objectKind;

}