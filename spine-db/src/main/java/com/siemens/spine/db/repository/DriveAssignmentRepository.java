package com.siemens.spine.db.repository;

import com.siemens.spine.db.entity.DriveAssignmentDataEntity;

import java.util.List;

public interface DriveAssignmentRepository extends GenericJpaRepository<DriveAssignmentDataEntity, Long> {

    List<DriveAssignmentDataEntity> findByComponentId(List<Long> componentIds);

    DriveAssignmentDataEntity findByComponentIdAndDriverNumber(Long componentId, Integer driverNumber);

}

