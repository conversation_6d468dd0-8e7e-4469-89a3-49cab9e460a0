package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MapsId;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_network_interface")
public class ElconNetworkInterface {

    @Id
    @Column(name = "interface_id", nullable = false)
    private Long interfaceId;

    @MapsId
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "interface_id", nullable = false)
    private ElconDeviceInterface elconDeviceInterface;

    @Size(max = 15)
    @Column(name = "ip_address", length = 15)
    private String ipAddress;

    @Size(max = 255)
    @Column(name = "subnet_mask")
    private String subnetMask;

    @Column(name = "h_station_id")
    private Long hStationId;

    @Size(max = 15)
    @Column(name = "gateway_ip", length = 15)
    private String gatewayIp;

}