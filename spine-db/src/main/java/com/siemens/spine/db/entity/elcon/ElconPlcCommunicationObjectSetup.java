package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_plc_communication_object_setup")
public class ElconPlcCommunicationObjectSetup {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "line_id", nullable = false)
    private Long lineId;

    @NotNull
    @Column(name = "unit_pairing_id", nullable = false)
    private Long unitPairingId;

    @Column(name = "extension_id_1")
    private Long extensionId1;

    @Column(name = "extension_id_2")
    private Long extensionId2;

    @Column(name = "object_id_1")
    private Long objectId1;

    @Column(name = "object_id_2")
    private Long objectId2;

    @Size(max = 4000)
    @Column(name = "additional_data_1", length = 4000)
    private String additionalData1;

    @Size(max = 4000)
    @Column(name = "additional_data_2", length = 4000)
    private String additionalData2;

}