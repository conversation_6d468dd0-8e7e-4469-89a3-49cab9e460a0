package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_plc_communication")
public class ElconPlcCommunication {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "communication_id", nullable = false)
    private Long communicationId;

    @NotNull
    @Column(name = "plc_area_id_1", nullable = false)
    private Long plcAreaId1;

    @Column(name = "plc_area_id_2")
    private Long plcAreaId2;

    @Size(max = 50)
    @Column(name = "interface_type", length = 50)
    private String interfaceType;

    @Column(name = "port_id_1")
    private Long portId1;

    @Column(name = "port_id_2")
    private Long portId2;

    @Size(max = 50)
    @Column(name = "external_partner_name", length = 50)
    private String externalPartnerName;

}