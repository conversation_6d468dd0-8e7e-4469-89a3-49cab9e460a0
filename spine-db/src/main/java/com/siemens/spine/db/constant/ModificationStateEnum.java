package com.siemens.spine.db.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 9/1/2023
 */
public enum ModificationStateEnum {
    NEW(10, "New"),
    CHANGED(20, "Changed"),
    // TODO not confirm for deleted and recon state
    DELETED(30, "Deleted"),
    RECON(40, "Recon");
    private static final Map<Integer, ModificationStateEnum> MODIFICATION_STATE_ENUM_MAP = new HashMap<>();

    static {
        for (ModificationStateEnum modificationStateEnum : values()) {
            MODIFICATION_STATE_ENUM_MAP.put(modificationStateEnum.getCode(), modificationStateEnum);
        }
    }

    private Integer code;
    private String value;

    ModificationStateEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static ModificationStateEnum resolve(Integer code) {
        return (code != null ? MODIFICATION_STATE_ENUM_MAP.get(code) : null);
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public boolean matches(Integer code) {
        return (this == resolve(code));
    }
}

