package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@Entity
@Table(name = "elcon_device_interface_connection")
public class ElconDeviceInterfaceConnection {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "connection_id", nullable = false)
    private Long connectionId;

    @NotNull
    @Column(name = "src_port_id", nullable = false)
    private Long srcPortId;

    @NotNull
    @Column(name = "dest_port_id", nullable = false)
    private Long destPortId;

}