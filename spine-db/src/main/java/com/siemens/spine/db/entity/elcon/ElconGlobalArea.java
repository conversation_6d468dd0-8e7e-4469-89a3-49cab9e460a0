package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_global_area")
public class ElconGlobalArea {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "global_area_id", nullable = false)
    private Long globalAreaId;

    @NotNull
    @Column(name = "project_version_id", nullable = false)
    private Long projectVersionId;

    @Size(max = 255)
    @Column(name = "global_area_name")
    private String globalAreaName;

    @Size(max = 255)
    @Column(name = "global_area_kind")
    private String globalAreaKind;

}