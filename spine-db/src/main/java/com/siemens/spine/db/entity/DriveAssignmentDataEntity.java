package com.siemens.spine.db.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "drive_assignment_data")
@Audited
public class DriveAssignmentDataEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "drive_assignment_id_seq")
    @SequenceGenerator(name = "drive_assignment_id_seq", sequenceName = "drive_assignment_id_seq")
    @Column(name = "id", nullable = false)
    private long id;

    @Column(name = "driver_number")
    private int driverNumber;

    @Column(length = 1000)
    private String data;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "component_id", referencedColumnName = "id", insertable = false, updatable = false)
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private ComponentEntity component;

}

