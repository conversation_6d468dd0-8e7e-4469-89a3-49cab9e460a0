package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconLoadcheck;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_loadcheck")
@ApplicationScoped
public class ElconLoadcheckRepository extends AbstractRestCRUDRepositoryImpl<ElconLoadcheck, Long> {

    @Override
    protected boolean isNew(ElconLoadcheck entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconLoadcheck> getDomainType() {
        return ElconLoadcheck.class;
    }
} 