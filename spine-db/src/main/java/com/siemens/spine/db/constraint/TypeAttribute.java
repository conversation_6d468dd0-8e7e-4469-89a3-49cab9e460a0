package com.siemens.spine.db.constraint;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum TypeAttribute {

    TYPE_ID("Type_ID", "CHAR"),
    MAIN_TYPE("Main_Type", "CHAR"),
    SUB_TYPE("Sub_Type", "CHAR"),
    PROJECT_TYPE_CLASSIFICATION("ProjectTypeClassification", "CHAR"),
    ORIGINAL_TYPE("OriginalType", "CHAR"),
    TYPE_AGGREGATOR("TypeAggregator", "CHAR"),
    SAP_MATERIAL_NUMBER("SAP_Material_Number", "CHAR"),
    DESCRIPTION("Description", "CHAR"),
    HIERARCHY_TYPE("HierarchyType", "CHAR"),
    SPECIAL_TYPE("Special_Type", "CHAR"),
    SUPPLIER("Supplier", "CHAR"),
    DEFAULT_DRIVE_COUNT("Default_Drive_Count", "INTEGER"),
    TYPE_VERSION("Type_Version", "CHAR"),
    MECH_MATERIAL_COST_KPI("Mech_Material_Cost_KPI", "DOUBLE"),
    ENGINEERING_ME_H_KPI("Engineering_ME_H_KPI", "DOUBLE"),
    ENGINEERING_HW_H_KPI("Engineering_HW_H_KPI", "DOUBLE"),
    ENGINEERING_PLC_H_KPI("Engineering_PLC_H_KPI", "DOUBLE"),
    ENGINEERING_IT_H_KPI("Engineering_IT_H_KPI", "DOUBLE"),
    MPS("MPS", "CHAR"),
    INSTALLATION_H_KPI("Installation_H_KPI", "DOUBLE"),
    PLC_COMMISSION_H_KPI("PLC_Commissioning_H_KPI", "DOUBLE"),
    ENGINEERING_SCADA_H_KPI("Engineering_SCADA_H_KPI", "DOUBLE"),
    URL1("URL1", "CHAR"),
    URL2("URL2", "CHAR"),
    URL3("URL3", "CHAR"),
    URL4("URL4", "CHAR"),
    URL5("URL5", "CHAR"),
    TYPE_VARIANT("Type_Variant", "CHAR"),
    DEFAULT_SLOPE("Default_Slope", "CHAR"),
    VERSION_NUMBER("Version_Number", "CHAR"),
    BELT_MASS("BeltMass", "DOUBLE"),
    REQUIRED_ACCELERATION("RequiredAcceleration", "DOUBLE"),
    DRIVE_PULLEY_DIAMETER("DrivePulleyDiameter", "DOUBLE"),
    FRICTION_FACTOR("FrictionFactor", "DOUBLE"),
    DRIVE_SHAFT_DIAMETER("DriveShaftDiameter", "DOUBLE"),
    EFFICIENCY_GEARBOX("EfficiencyGearbox", "DOUBLE"),
    ENGINEERING_ME_ONE_OFF_COSTS("Engineering_ME_OneOffCosts", "DOUBLE"),
    ENGINEERING_HW_ONE_OFF_COSTS("Engineering_HW_OneOffCosts", "DOUBLE"),
    ENGINEERING_PLC_ONE_OFF_COSTS("Engineering_PLC_OneOffCosts", "DOUBLE"),
    ENGINEERING_IT_ONE_OFF_COSTS("Engineering_IT_OneOffCosts", "DOUBLE"),
    ENGINEERING_SCADA_ONE_OFF_COSTS("Engineering_SCADA_OneOffCosts", "DOUBLE"),
    OUTDATED("Outdated", "BOOLEAN");

    private String name;

    private String type;
}
