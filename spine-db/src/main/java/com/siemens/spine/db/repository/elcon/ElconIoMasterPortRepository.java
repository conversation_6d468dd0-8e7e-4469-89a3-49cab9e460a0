// WARNING: Entity ElconIoMasterPort does not have @Id or @EmbeddedId. Please check entity definition!
package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconIoMasterPort;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_io_master_port")
@ApplicationScoped
public class ElconIoMasterPortRepository extends AbstractRestCRUDRepositoryImpl<ElconIoMasterPort, Long> {

    // WARNING: No @Id or @EmbeddedId found. isNew() always returns false.
    @Override
    protected boolean isNew(ElconIoMasterPort entity) {
        return entity.getIoMasterPortId() == null;
    }

    @Override
    public Class<ElconIoMasterPort> getDomainType() {
        return ElconIoMasterPort.class;
    }
} 