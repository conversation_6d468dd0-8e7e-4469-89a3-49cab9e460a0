package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPanelNeighbour;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_panel_neighbour")
@ApplicationScoped
public class ElconPanelNeighbourRepository extends AbstractRestCRUDRepositoryImpl<ElconPanelNeighbour, Long> {

    @Override
    protected boolean isNew(ElconPanelNeighbour entity) {
        return entity.getNeighbourId() == null;
    }

    @Override
    public Class<ElconPanelNeighbour> getDomainType() {
        return ElconPanelNeighbour.class;
    }
} 