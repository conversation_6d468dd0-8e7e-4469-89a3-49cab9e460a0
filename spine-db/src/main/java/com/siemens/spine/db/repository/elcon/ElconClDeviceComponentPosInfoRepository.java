package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconClDeviceComponentPosInfo;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cl_device_component_pos_info")
@ApplicationScoped
public class ElconClDeviceComponentPosInfoRepository extends AbstractRestCRUDRepositoryImpl<ElconClDeviceComponentPosInfo, Long> {

    @Override
    protected boolean isNew(ElconClDeviceComponentPosInfo entity) {
        return entity.getComponentId() == null;
    }

    @Override
    public Class<ElconClDeviceComponentPosInfo> getDomainType() {
        return ElconClDeviceComponentPosInfo.class;
    }
} 