package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.ProjectComponentVersionEntity;
import com.siemens.spine.db.repository.ProjectComponentVersionRepository;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import java.util.List;
import java.util.Optional;

@ApplicationScoped
public class ProjectComponentVersionRepositoryImpl extends GenericJpaRepositoryImpl<ProjectComponentVersionEntity, Long>
        implements
        ProjectComponentVersionRepository {

    @PersistenceContext
    private EntityManager em;

    public List<ProjectComponentVersionEntity> findByProjectId(Long componentId) {
        return em.createQuery(
                        "SELECT v FROM ProjectComponentVersionEntity v WHERE v.projectId = :cid ORDER BY v.sysCreateDate DESC",
                        ProjectComponentVersionEntity.class)
                .setParameter("cid", componentId)
                .getResultList();
    }

    @Override
    public Optional<ProjectComponentVersionEntity> findTopByProjectIdOrderByRevDesc(Long componentId) {
        try {
            TypedQuery<ProjectComponentVersionEntity> query = em.createQuery(
                    "SELECT v FROM ProjectComponentVersionEntity v WHERE v.projectId = :cid ORDER BY v.rev DESC",
                    ProjectComponentVersionEntity.class);
            query.setParameter("cid", componentId);
            query.setMaxResults(1);  // Limit the result to the top 1, i.e., the highest rev
            List<ProjectComponentVersionEntity> result = query.getResultList();
            return Optional.of(result.get(0));
        } catch (NoResultException e) {
            // Return the first element if present, otherwise null
            return Optional.empty();
        }
    }

    @Override
    public Optional<ProjectComponentVersionEntity> findByProjectIdAndRev(Long projectId, Integer rev) {
        try {
            ProjectComponentVersionEntity entity = em.createQuery(
                            "SELECT v FROM ProjectComponentVersionEntity v WHERE v.projectId = :cid AND v.rev = :rev",
                            ProjectComponentVersionEntity.class)
                    .setParameter("cid", projectId)
                    .setParameter("rev", rev)
                    .getSingleResult();
            return Optional.of(entity);
        } catch (NoResultException e) {
            return Optional.empty();
        }
    }

    @Override
    public Class<ProjectComponentVersionEntity> getDomainType() {
        return ProjectComponentVersionEntity.class;
    }

}

