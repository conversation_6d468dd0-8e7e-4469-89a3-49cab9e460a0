package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_io_master_port")
public class ElconIoMasterPort {

    @Id
    @NotNull
    @Column(name = "io_master_port_id", nullable = false)
    private Long ioMasterPortId;

    @NotNull
    @Column(name = "device_id", nullable = false)
    private Long deviceId;

    @Size(max = 255)
    @Column(name = "type_io")
    private String typeIo;

    @Column(name = "number_io_fd")
    private Short numberIoFd;

    @Column(name = "number_io_public")
    private Short numberIoPublic;

    @Column(name = "io_public")
    private Short ioPublic;

    @Column(name = "extension_id")
    private Long extensionId;

    @Column(name = "number_io_device")
    private Short numberIoDevice;

    @Size(max = 30)
    @Column(name = "name_io_device", length = 30)
    private String nameIoDevice;

    @Size(max = 30)
    @Column(name = "symbol_io_device", length = 30)
    private String symbolIoDevice;

    @Size(max = 255)
    @Column(name = "comment_io_device")
    private String commentIoDevice;

    @Size(max = 20)
    @Column(name = "io_address", length = 20)
    private String ioAddress;

}