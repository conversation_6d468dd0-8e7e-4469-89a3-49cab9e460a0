package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCfgDefaultHlcCommunicationObject;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cfg_default_hlc_communication_object")
@ApplicationScoped
public class ElconCfgDefaultHlcCommunicationObjectRepository extends AbstractRestCRUDRepositoryImpl<ElconCfgDefaultHlcCommunicationObject, Long> {

    @Override
    protected boolean isNew(ElconCfgDefaultHlcCommunicationObject entity) {
        return entity.getId() == null;
    }

    @Override
    public Class<ElconCfgDefaultHlcCommunicationObject> getDomainType() {
        return ElconCfgDefaultHlcCommunicationObject.class;
    }
} 