// WARNING: Entity ElconHlcCommUnit does not have @Id or @EmbeddedId. Please check entity definition!
package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconHlcCommUnit;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_hlc_comm_unit")
@ApplicationScoped
public class ElconHlcCommUnitRepository extends AbstractRestCRUDRepositoryImpl<ElconHlcCommUnit, Long> {

    // WARNING: No @Id or @EmbeddedId found. isNew() always returns false.
    @Override
    protected boolean isNew(ElconHlcCommUnit entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconHlcCommUnit> getDomainType() {
        return ElconHlcCommUnit.class;
    }
} 