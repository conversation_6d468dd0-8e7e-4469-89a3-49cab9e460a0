package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/06/06
 */

@Entity
@Table(name = "query_template")
@Getter
@Setter
@Audited
public class QueryTemplateEntity {

    @Id
    @GeneratedValue
    private Long id;

    @Column(name = "template_name", nullable = false, unique = true)
    private String templateName;

    @Column(name = "sql_template", nullable = false)
    private String sqlTemplate;

    @Column(name = "description")
    private String description;

    @Column(name = "requires_pagination", nullable = false)
    private boolean requiresPagination = false;

    @Column(name = "sys_create_date", updatable = false)
    @CreationTimestamp
    private Timestamp sysCreateDate;

    @Column(name = "sys_mod_date")
    @UpdateTimestamp
    private Timestamp sysModDate;

    @Column(name = "created_by", updatable = false)
    private String createdBy;

    @Column(name = "modified_by")
    private String modifiedBy;

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof QueryTemplateEntity that)) {
            return false;
        }
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

}
