package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.Instant;

@Getter
@Setter
@Entity
@Table(name = "elcon_project_version")
public class ElconProjectVersion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "project_version_id", nullable = false)
    private Long projectVersionId;

    @NotNull
    @Column(name = "project_id", nullable = false)
    private Long projectId;

    @Size(max = 20)
    @NotNull
    @Column(name = "version_key", nullable = false, length = 20)
    private String versionKey;

    @Column(name = "create_date")
    private Instant createDate;

    @NotNull
    @Column(name = "valid_flag", nullable = false)
    private Short validFlag;

    @Size(max = 10)
    @Column(name = "additional_data_prep_state", length = 10)
    private String additionalDataPrepState;

    @Size(max = 1023)
    @Column(name = "description", length = 1023)
    private String description;

    @Size(max = 255)
    @Column(name = "user_name")
    private String userName;

    @Size(max = 1023)
    @Column(name = "simulation_description", length = 1023)
    private String simulationDescription;

    @Size(max = 20)
    @Column(name = "tool_version", length = 20)
    private String toolVersion;

}