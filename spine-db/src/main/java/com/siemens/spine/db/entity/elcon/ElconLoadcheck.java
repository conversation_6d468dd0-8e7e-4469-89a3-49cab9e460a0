package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_loadcheck")
public class ElconLoadcheck {

    @Id
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @Size(max = 31)
    @Column(name = "speed_meas_mode", length = 31)
    private String speedMeasMode;

    @NotNull
    @Column(name = "dist_trg_lc", nullable = false)
    private Long distTrgLc;

    @NotNull
    @Column(name = "dist_belt_blade", nullable = false)
    private Long distBeltBlade;

    @NotNull
    @Column(name = "signal_filter", nullable = false)
    private Long signalFilter;

    @NotNull
    @Column(name = "threshold_fault_on_cf", nullable = false)
    private Integer thresholdFaultOnCf;

    @NotNull
    @Column(name = "turn_camera_image", nullable = false)
    private Short turnCameraImage;

    @NotNull
    @Column(name = "log_cam_image", nullable = false)
    private Short logCamImage;

    @Column(name = "lc_redund_index")
    private Integer lcRedundIndex;

}