package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "elcon_co_neighbour")
public class ElconCoNeighbour {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "neighbour_id", nullable = false)
    private Long neighbourId;

    @NotNull
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @NotNull
    @Column(name = "object_id_neighbour", nullable = false)
    private Long objectIdNeighbour;

    @Column(name = "nr")
    private Long nr;

    @Column(name = "nr_neighbour")
    private Long nrNeighbour;

    @Column(name = "gap")
    private Long gap;

    @Size(max = 255)
    @Column(name = "cp_type")
    private String cpType;

    @Column(name = "coordinate_x")
    private BigDecimal coordinateX;

    @Column(name = "coordinate_y")
    private BigDecimal coordinateY;

    @Column(name = "coordinate_z")
    private BigDecimal coordinateZ;

}