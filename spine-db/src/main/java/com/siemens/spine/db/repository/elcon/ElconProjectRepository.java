package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconProject;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_project")
@ApplicationScoped
public class ElconProjectRepository extends AbstractRestCRUDRepositoryImpl<ElconProject, Long> {

    @Override
    protected boolean isNew(ElconProject entity) {
        return entity.getProjectId() == null;
    }

    @Override
    public Class<ElconProject> getDomainType() {
        return ElconProject.class;
    }
} 