package com.siemens.spine.db.repository.metadata;

import com.siemens.spine.db.exception.JpaException;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import javax.enterprise.context.ApplicationScoped;
import javax.persistence.Entity;
import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * High-performance cache for EntityMetadata.
 * Pre-computes and caches all entity metadata at application startup to eliminate
 * reflection overhead during runtime operations.
 * <p>
 * This singleton cache provides O(1) metadata lookup performance.
 *
 * <AUTHOR>
 * @since 1.0
 */
@ApplicationScoped
@Slf4j
public class EntityMetadataCache {

    private final EntityMetadataFactory metadataFactory;

    // Core metadata caches
    private final ConcurrentMap<Class<?>, EntityMetadata<?>> entityMetadataCacheDelegate = new ConcurrentHashMap<>();
    private final ConcurrentMap<Class<?>, IdMetadata> idMetadataCache = new ConcurrentHashMap<>();

    private volatile boolean initialized = false;

    public EntityMetadataCache() {
        this.metadataFactory = new EntityMetadataFactory();
    }

    /**
     * Initializes the cache by scanning and pre-computing metadata for all entities.
     * Called automatically at application startup.
     */
    @PostConstruct
    public void initializeCache() {
        if (initialized) {
            log.warn("EntityMetadataCache already initialized, skipping");
            return;
        }

        log.info("Initializing EntityMetadataCache...");
        long startTime = System.currentTimeMillis();

        try {
            List<Class<?>> entityClasses = discoverEntityClasses();
            log.info("Discovered {} entity classes for metadata pre-computation", entityClasses.size());

            for (Class<?> entityClass : entityClasses) {
                preComputeEntityMetadata(entityClass);
            }

            int cachedEntityCount = entityClasses.size();
            long initializationTime = System.currentTimeMillis() - startTime;
            this.initialized = true;

            log.info("EntityMetadataCache initialized successfully in {}ms with {} entities cached", initializationTime,
                    cachedEntityCount);

        } catch (Exception e) {
            log.error("Failed to initialize EntityMetadataCache", e);
            throw new JpaException("EntityMetadataCache initialization failed", e);
        }
    }

    /**
     * Gets EntityMetadata for the specified entity class.
     * Returns cached metadata with O(1) lookup performance.
     *
     * @param entityClass The entity class
     * @param <T>         The entity type
     * @return EntityMetadata instance
     * @throws JpaException if entity metadata not found
     */
    @SuppressWarnings("unchecked")
    public <T> EntityMetadata<T> getEntityMetadata(Class<T> entityClass) throws JpaException {
        EntityMetadata<T> metadata = (EntityMetadata<T>) entityMetadataCacheDelegate.get(entityClass);

        if (metadata == null) {
            log.debug("Creating on-demand metadata for {}", entityClass.getSimpleName());
            metadata = metadataFactory.createEntityMetadata(entityClass);
            entityMetadataCacheDelegate.put(entityClass, metadata);
        }

        return metadata;
    }

    /**
     * Gets IdMetadata for the specified entity class.
     *
     * @param entityClass The entity class
     * @return IdMetadata instance
     * @throws JpaException if entity metadata not found
     */
    public IdMetadata getIdMetadata(Class<?> entityClass) throws JpaException {
        return idMetadataCache.computeIfAbsent(entityClass, clazz -> {
            try {
                return getEntityMetadata(clazz).getIdMetadata();
            } catch (JpaException e) {
                log.error("Failed to retrieve IdMetadata for {}: {}", clazz.getSimpleName(), e.getMessage());
                throw e;
            }
        });
    }

    /**
     * Pre-computes and caches metadata for a single entity class.
     */
    private void preComputeEntityMetadata(Class<?> entityClass) throws JpaException {
        log.debug("Pre-computing metadata for {}", entityClass.getSimpleName());
        EntityMetadata<?> metadata = metadataFactory.createEntityMetadata(entityClass);
        entityMetadataCacheDelegate.put(entityClass, metadata);
        idMetadataCache.put(entityClass, metadata.getIdMetadata());

        log.debug("Pre-computed metadata for {} - composite key: {}, attributes: {}", entityClass.getSimpleName(),
                metadata.hasCompositeKey(), metadata.getAllAttributes().size());
    }

    /**
     * Discovers all entity classes in the application classpath.
     * Scans for classes annotated with @Entity.
     */
    private List<Class<?>> discoverEntityClasses() {
        List<Class<?>> entityClasses = new ArrayList<>();

        try {
            String basePackage = "com.siemens.spine.db.entity";
            String packagePath = basePackage.replace('.', '/');

            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            URL packageURL = classLoader.getResource(packagePath);

            if (packageURL != null) {
                File packageDirectory = new File(packageURL.getFile());
                if (packageDirectory.exists() && packageDirectory.isDirectory()) {
                    scanPackageForEntities(packageDirectory, basePackage, entityClasses);
                }
            }

            log.debug("Discovered {} entity classes in package {}", entityClasses.size(), basePackage);

        } catch (Exception e) {
            log.error("Failed to discover entity classes", e);
        }

        return entityClasses;
    }

    /**
     * Recursively scans a package directory for entity classes.
     */
    private void scanPackageForEntities(File directory, String packageName, List<Class<?>> entityClasses) {
        File[] files = directory.listFiles();
        if (files == null) {
            return;
        }

        for (File file : files) {
            if (file.isDirectory()) {
                scanPackageForEntities(file, packageName + "." + file.getName(), entityClasses);
            } else if (file.getName().endsWith(".class")) {
                try {
                    String className = packageName + "." + file.getName().substring(0, file.getName().length() - 6);
                    Class<?> clazz = Class.forName(className);

                    if (clazz.isAnnotationPresent(Entity.class)) {
                        entityClasses.add(clazz);
                        log.debug("Found entity class: {}", clazz.getSimpleName());
                    }
                } catch (ClassNotFoundException e) {
                    log.debug("Could not load class from file: {}", file.getName());
                }
            }
        }
    }

}