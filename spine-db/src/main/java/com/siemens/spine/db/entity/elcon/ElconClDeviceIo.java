package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_cl_device_io")
public class ElconClDeviceIo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "io_id", nullable = false)
    private Long ioId;

    @NotNull
    @Column(name = "device_id", nullable = false)
    private Long deviceId;

    @Size(max = 20)
    @Column(name = "io_kind", length = 20)
    private String ioKind;

    @Column(name = "nr")
    private Integer nr;

    @Size(max = 255)
    @Column(name = "type")
    private String type;

    @Size(max = 255)
    @Column(name = "\"offset\"")
    private String offset;

    @Size(max = 255)
    @Column(name = "terminal")
    private String terminal;

    @Size(max = 255)
    @Column(name = "symbol")
    private String symbol;

    @Size(max = 255)
    @Column(name = "description")
    private String description;

    @Size(max = 255)
    @Column(name = "card_name")
    private String cardName;

    @Column(name = "card_slot")
    private Short cardSlot;

}