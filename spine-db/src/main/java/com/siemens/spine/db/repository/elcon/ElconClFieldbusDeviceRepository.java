package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconClFieldbusDevice;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cl_fieldbus_device")
@ApplicationScoped
public class ElconClFieldbusDeviceRepository extends AbstractRestCRUDRepositoryImpl<ElconClFieldbusDevice, Long> {

    @Override
    protected boolean isNew(ElconClFieldbusDevice entity) {
        return entity.getDeviceId() == null;
    }

    @Override
    public Class<ElconClFieldbusDevice> getDomainType() {
        return ElconClFieldbusDevice.class;
    }
} 