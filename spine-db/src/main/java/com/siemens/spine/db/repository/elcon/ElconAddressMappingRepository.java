package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconAddressMapping;

import javax.enterprise.context.ApplicationScoped;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 19/6/2025
 **/
@RestRepository(path = "elcon_drive")
@ApplicationScoped
public class ElconAddressMappingRepository extends AbstractRestCRUDRepositoryImpl<ElconAddressMapping, Long> {

    @Override
    protected boolean isNew(ElconAddressMapping entity) {
        return entity.getMappingId() == null;
    }

    @Override
    public Class<ElconAddressMapping> getDomainType() {
        return ElconAddressMapping.class;
    }

}
