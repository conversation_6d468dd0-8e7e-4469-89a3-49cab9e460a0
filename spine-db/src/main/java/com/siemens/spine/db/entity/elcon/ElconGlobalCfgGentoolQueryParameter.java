package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_global_cfg_gentool_query_parameter")
public class ElconGlobalCfgGentoolQueryParameter {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "parameter_id", nullable = false)
    private Long parameterId;

    @NotNull
    @Column(name = "query_id", nullable = false)
    private Long queryId;

    @Size(max = 255)
    @Column(name = "column_name")
    private String columnName;

    @Size(max = 255)
    @Column(name = "parameter_name")
    private String parameterName;

    @Size(max = 255)
    @Column(name = "description")
    private String description;

    @NotNull
    @Column(name = "param_group", nullable = false)
    private Long paramGroup;

    @NotNull
    @Column(name = "custom_sql", nullable = false)
    private Short customSql;

}