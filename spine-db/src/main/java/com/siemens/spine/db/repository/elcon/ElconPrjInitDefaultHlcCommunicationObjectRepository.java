// WARNING: Entity ElconPrjInitDefaultHlcCommunicationObject does not have @Id or @EmbeddedId. Please check entity definition!
package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPrjInitDefaultHlcCommunicationObject;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_prj_init_default_hlc_communication_objects")
@ApplicationScoped
public class ElconPrjInitDefaultHlcCommunicationObjectRepository extends AbstractRestCRUDRepositoryImpl<ElconPrjInitDefaultHlcCommunicationObject, Long> {

    // WARNING: No @Id or @EmbeddedId found. isNew() always returns false.
    @Override
    protected boolean isNew(ElconPrjInitDefaultHlcCommunicationObject entity) {
        return entity.getId() == null;
    }

    @Override
    public Class<ElconPrjInitDefaultHlcCommunicationObject> getDomainType() {
        return ElconPrjInitDefaultHlcCommunicationObject.class;
    }
} 