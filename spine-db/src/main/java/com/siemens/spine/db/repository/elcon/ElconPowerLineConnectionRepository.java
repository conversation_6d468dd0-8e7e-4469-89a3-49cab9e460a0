package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPowerLineConnection;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_power_line_connection")
@ApplicationScoped
public class ElconPowerLineConnectionRepository extends AbstractRestCRUDRepositoryImpl<ElconPowerLineConnection, Long> {

    @Override
    protected boolean isNew(ElconPowerLineConnection entity) {
        return entity.getConnectionId() == null;
    }

    @Override
    public Class<ElconPowerLineConnection> getDomainType() {
        return ElconPowerLineConnection.class;
    }
} 