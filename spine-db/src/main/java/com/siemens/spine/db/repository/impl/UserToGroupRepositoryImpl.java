package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.UserToGroupEntity;
import com.siemens.spine.db.repository.UserToGroupRepository;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.Collections;
import java.util.List;

@ApplicationScoped
public class UserToGroupRepositoryImpl extends GenericJpaRepositoryImpl<UserToGroupEntity, Long>
        implements UserToGroupRepository {

    @Override
    public Class<UserToGroupEntity> getDomainType() {
        return UserToGroupEntity.class;
    }

    @Override
    public UserToGroupEntity isUserInGroup(String username, String userGroup) {
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(userGroup)) {
            return null;
        }

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<UserToGroupEntity> cq = cb.createQuery(UserToGroupEntity.class);
        Root<UserToGroupEntity> rootEntry = cq.from(UserToGroupEntity.class);

        Predicate condition = cb.and(
                cb.equal(rootEntry.get("user"), username),
                cb.equal(rootEntry.get("userGroup"), userGroup)
        );
        cq.select(rootEntry).where(condition);

        List<UserToGroupEntity> userGroups = entityManager.createQuery(cq).getResultList();
        return (userGroups == null || userGroups.isEmpty()) ? null : userGroups.get(0);
    }

    @Override
    public List<UserToGroupEntity> findUserGroups(String username) {
        if (StringUtils.isEmpty(username)) {
            return Collections.emptyList();
        }

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<UserToGroupEntity> cq = cb.createQuery(UserToGroupEntity.class);
        Root<UserToGroupEntity> rootEntry = cq.from(UserToGroupEntity.class);

        Predicate condition = cb.equal(rootEntry.get("user"), username);
        cq.select(rootEntry).where(condition);

        return entityManager.createQuery(cq).getResultList();
    }

}

