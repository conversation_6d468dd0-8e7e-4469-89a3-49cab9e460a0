package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconImportEvent;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_import_event")
@ApplicationScoped
public class ElconImportEventRepository extends AbstractRestCRUDRepositoryImpl<ElconImportEvent, Long> {

    @Override
    protected boolean isNew(ElconImportEvent entity) {
        return entity.getId() == null;
    }

    @Override
    public Class<ElconImportEvent> getDomainType() {
        return ElconImportEvent.class;
    }
} 