package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPrjInitCommPartnerExternal;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_prj_init_comm_partner_external")
@ApplicationScoped
public class ElconPrjInitCommPartnerExternalRepository extends AbstractRestCRUDRepositoryImpl<ElconPrjInitCommPartnerExternal, Long> {

    @Override
    protected boolean isNew(ElconPrjInitCommPartnerExternal entity) {
        return entity.getCfgId() == null;
    }

    @Override
    public Class<ElconPrjInitCommPartnerExternal> getDomainType() {
        return ElconPrjInitCommPartnerExternal.class;
    }
} 