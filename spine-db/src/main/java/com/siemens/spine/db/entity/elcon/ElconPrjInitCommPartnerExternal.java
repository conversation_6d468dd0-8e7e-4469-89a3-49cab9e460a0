package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_prj_init_comm_partner_external")
public class ElconPrjInitCommPartnerExternal {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "cfg_id", nullable = false)
    private Long cfgId;

    @Size(max = 20)
    @Column(name = "connection_type", length = 20)
    private String connectionType;

    @Size(max = 20)
    @Column(name = "connection_id", length = 20)
    private String connectionId;

    @Column(name = "active_flag")
    private Short activeFlag;

    @Size(max = 40)
    @Column(name = "local_ip_address", length = 40)
    private String localIpAddress;

    @Size(max = 20)
    @Column(name = "local_tsap", length = 20)
    private String localTsap;

    @Column(name = "local_port")
    private Integer localPort;

    @Size(max = 255)
    @NotNull
    @Column(name = "domain", nullable = false)
    private String domain;

}