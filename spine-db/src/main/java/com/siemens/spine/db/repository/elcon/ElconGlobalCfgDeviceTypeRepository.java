package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconGlobalCfgDeviceType;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_global_cfg_device_type")
@ApplicationScoped
public class ElconGlobalCfgDeviceTypeRepository extends AbstractRestCRUDRepositoryImpl<ElconGlobalCfgDeviceType, String> {

    @Override
    protected boolean isNew(ElconGlobalCfgDeviceType entity) {
        return entity.getDeviceType() == null;
    }

    @Override
    public Class<ElconGlobalCfgDeviceType> getDomainType() {
        return ElconGlobalCfgDeviceType.class;
    }
} 