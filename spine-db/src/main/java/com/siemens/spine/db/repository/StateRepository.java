package com.siemens.spine.db.repository;

import com.siemens.spine.db.entity.StateEntity;
import com.siemens.spine.db.repository.filter.ComponentStateFilter;

import java.util.List;

public interface StateRepository extends GenericJpaRepository<StateEntity, Long> {

    /**
     * Get the latest state of the components
     *
     * @param componentIds
     * @return
     */
    List<StateEntity> findLatestComponentState(List<Long> componentIds);

    /**
     * @param filter
     * @return
     */
    List<StateEntity> filter(ComponentStateFilter filter);

    /**
     * Delete the states of the components
     *
     * @param componentIds
     * @return
     */
    long deleteByComponentIds(List<Long> componentIds);

}

