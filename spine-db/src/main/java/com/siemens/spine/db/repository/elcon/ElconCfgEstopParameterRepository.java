package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCfgEstopParameter;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cfg_estop_parameter")
@ApplicationScoped
public class ElconCfgEstopParameterRepository extends AbstractRestCRUDRepositoryImpl<ElconCfgEstopParameter, Long> {

    @Override
    protected boolean isNew(ElconCfgEstopParameter entity) {
        return entity.getId() == null;
    }

    @Override
    public Class<ElconCfgEstopParameter> getDomainType() {
        return ElconCfgEstopParameter.class;
    }
} 