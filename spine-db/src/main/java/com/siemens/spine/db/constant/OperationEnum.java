package com.siemens.spine.db.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 26/12/2022
 */
public enum OperationEnum {
    PROJECT_VIEW_PARAMETERS,
    TYPE_VIEW,
    DECOMPOSITION_ATTRIBUTES_VIEW,
    COMPONENT_MODIFY,
    COMPONENT_VIEW,
    TODOLIST_SAPBOM,
    ADMIN_CREATE_NEW_PROJECT,
    ADMIN_MANAGE_PERMISSIONS,
    ADMIN_UNLOCK_COMPONENT,
    BACKUP_CREATE_MANUALLY,
    BAC<PERSON>UP_RESTORE,
    BACKUP_VIEW,
    COMPONENT_DELETE,
    COMPONENT_MOVE_BACKWARD,
    COMPONENT_MOVE_FORWARD,
    COMPONENT_REMOVE,
    COMPONENT_VIEW_DECOMPOSITION,
    DECOMPOSITION_ATTRIBUTES_MODIFY,
    DECOMPOSITION_ATTRIBUTES_DELETE,
    DECOMPOSITION_ATTRIBUTES_UPLOAD,
    DRIVE_ASSIGNMENT,
    DRIVE_EXPORT,
    DRIVE_CALCULATE_PARAMETERS_PHASE_EXECUTION,
    DRIVE_CALCULATE_PHASE_OFFER,
    GROUPS_ASSIGN_DELIVERY_DATE,
    GROUPS_CREATE_BUT_DRAWING,
    GROUPS_DELETE,
    GROUPS_VIEW,
    GROUPS_RENAME,
    PROJECT_CHANGE_TO_EXECUTION_PHASE,
    PROJECT_MODIFY_DEFAULT_PARAMETERS,
    PROJECT_MODIFY_PARAMETERS,
    PROJECT_STATISTICS,
    TODOLIST_CALCULATION,
    TODOLIST_SIMULATION,
    TODOLIST_EMULATION,
    TODOLIST_IT,
    TYPE_MODIFY,
    TYPE_APPROVAL,
    TYPE_CREATION,
    TYPE_DELETE,
    EXPORT_OBJECTXML,
    ELECTRIC_SYNCHRONIZATION,
}

