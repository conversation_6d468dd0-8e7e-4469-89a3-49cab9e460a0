package com.siemens.spine.db.entity;

import com.siemens.spine.db.constant.GroupTypeEnum;
import com.siemens.spine.db.entity.converter.GroupTypeAttributeConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

@Entity
@Table(name = "`group`")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Audited
public class GroupEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "group_id_seq_gen")
    @SequenceGenerator(name = "group_id_seq_gen", sequenceName = "group_id_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Basic
    @Column(name = "grouptype")
    @Convert(converter = GroupTypeAttributeConverter.class)
    private GroupTypeEnum grouptype;

    @Basic
    @Column(name = "deliverydate")
    private Timestamp deliverydate;

    @Basic
    @Column(name = "comment")
    private String comment;

    @Basic
    @Column(name = "subtype")
    private String subtype;

    @ManyToMany(mappedBy = "groups")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private Set<ComponentEntity> components = new HashSet<>();

    @Column(name = "projectid")
    private Long projectId;

    @Override
    public int hashCode() {
        return Objects.hash(name, grouptype, projectId);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GroupEntity that = (GroupEntity) o;
        return name.equals(that.name) && grouptype == that.grouptype && projectId.equals(that.projectId);
    }

    public void removeComponent(ComponentEntity component) {
        components.remove(component);
        component.getGroups().remove(this);
    }

}
