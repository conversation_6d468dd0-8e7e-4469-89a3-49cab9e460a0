package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.repository.AuditRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.query.AuditEntity;
import org.hibernate.envers.query.AuditQuery;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 17/4/2025
 **/
@ApplicationScoped
public class AuditRepositoryImpl implements AuditRepository {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public int getCurrentRevComponent(Long componentId) {
        AuditReader auditReader = AuditReaderFactory.get(entityManager);
        AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(ComponentEntity.class, false, true)
                .add(AuditEntity.id().eq(componentId))
                .addOrder(AuditEntity.revisionNumber().asc());
        List<Object[]> resultList = query.getResultList();
        int size = resultList.size();
        if (size == 0) {
            return 0;
        }
        Object[] objects = resultList.get(size - 1);
        DefaultRevisionEntity revision = (DefaultRevisionEntity) objects[1];
        return revision.getId();
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<Long, Long> getCurrentRevsComponents(List<Long> componentIds) {
        if (CollectionUtils.isEmpty(componentIds)) {
            return new HashMap<>();
        }

        String queryStr = """
                SELECT c.id, COALESCE(MAX(ca.REV), 0) as maxRev
                FROM component c
                    LEFT JOIN component_aud ca ON c.id = ca.id
                    LEFT JOIN revinfo ri ON ca.REV = ri.REV
                WHERE c.id IN (:ids)
                GROUP BY c.id
                """;

        List<Object[]> results = entityManager.createNativeQuery(queryStr)
                .setParameter("ids", componentIds)
                .getResultList();

        Map<Long, Long> revisionMap = new HashMap<>();
        for (Object[] result : results) {
            Long heroId = ((Number) result[0]).longValue();
            Long revision = ((Number) result[1]).longValue();
            revisionMap.put(heroId, revision);
        }
        return revisionMap;
    }

}

