package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconSafetyPushbutton;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_safety_pushbutton")
@ApplicationScoped
public class ElconSafetyPushbuttonRepository extends AbstractRestCRUDRepositoryImpl<ElconSafetyPushbutton, Long> {

    @Override
    protected boolean isNew(ElconSafetyPushbutton entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconSafetyPushbutton> getDomainType() {
        return ElconSafetyPushbutton.class;
    }
} 