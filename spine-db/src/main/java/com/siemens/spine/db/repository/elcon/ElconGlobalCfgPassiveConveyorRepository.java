package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconGlobalCfgPassiveConveyor;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_global_cfg_passive_conveyors")
@ApplicationScoped
public class ElconGlobalCfgPassiveConveyorRepository extends AbstractRestCRUDRepositoryImpl<ElconGlobalCfgPassiveConveyor, Long> {

    @Override
    protected boolean isNew(ElconGlobalCfgPassiveConveyor entity) {
        return entity.getCfgId() == null;
    }

    @Override
    public Class<ElconGlobalCfgPassiveConveyor> getDomainType() {
        return ElconGlobalCfgPassiveConveyor.class;
    }
} 