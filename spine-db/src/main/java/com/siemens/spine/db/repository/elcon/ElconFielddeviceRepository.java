package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconFielddevice;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_fielddevice")
@ApplicationScoped
public class ElconFielddeviceRepository extends AbstractRestCRUDRepositoryImpl<ElconFielddevice, Long> {

    @Override
    protected boolean isNew(ElconFielddevice entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconFielddevice> getDomainType() {
        return ElconFielddevice.class;
    }
} 