package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCfgLogisticsProperty;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cfg_logistics_property")
@ApplicationScoped
public class ElconCfgLogisticsPropertyRepository extends AbstractRestCRUDRepositoryImpl<ElconCfgLogisticsProperty, Long> {

    @Override
    protected boolean isNew(ElconCfgLogisticsProperty entity) {
        return entity.getCfgId() == null;
    }

    @Override
    public Class<ElconCfgLogisticsProperty> getDomainType() {
        return ElconCfgLogisticsProperty.class;
    }
} 