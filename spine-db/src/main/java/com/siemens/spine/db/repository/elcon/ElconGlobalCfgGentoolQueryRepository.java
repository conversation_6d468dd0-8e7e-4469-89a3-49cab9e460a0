package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconGlobalCfgGentoolQuery;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_global_cfg_gentool_query")
@ApplicationScoped
public class ElconGlobalCfgGentoolQueryRepository extends AbstractRestCRUDRepositoryImpl<ElconGlobalCfgGentoolQuery, Long> {

    @Override
    protected boolean isNew(ElconGlobalCfgGentoolQuery entity) {
        return entity.getQueryId() == null;
    }

    @Override
    public Class<ElconGlobalCfgGentoolQuery> getDomainType() {
        return ElconGlobalCfgGentoolQuery.class;
    }
} 