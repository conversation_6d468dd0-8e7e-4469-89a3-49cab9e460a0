// WARNING: Entity ElconCfgActiveSlave does not have @Id or @EmbeddedId. Please check entity definition!
package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCfgActiveSlave;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cfg_active_slave")
@ApplicationScoped
public class ElconCfgActiveSlaveRepository extends AbstractRestCRUDRepositoryImpl<ElconCfgActiveSlave, Long> {

    // WARNING: No @Id or @EmbeddedId found. isNew() always returns false.
    @Override
    protected boolean isNew(ElconCfgActiveSlave entity) {
        return entity.getId() == null;
    }

    @Override
    public Class<ElconCfgActiveSlave> getDomainType() {
        return ElconCfgActiveSlave.class;
    }
} 