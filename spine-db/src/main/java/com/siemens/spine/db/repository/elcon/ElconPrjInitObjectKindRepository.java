package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPrjInitObjectKind;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_prj_init_object_kind")
@ApplicationScoped
public class ElconPrjInitObjectKindRepository extends AbstractRestCRUDRepositoryImpl<ElconPrjInitObjectKind, Long> {

    @Override
    protected boolean isNew(ElconPrjInitObjectKind entity) {
        return entity.getCfgId() == null;
    }

    @Override
    public Class<ElconPrjInitObjectKind> getDomainType() {
        return ElconPrjInitObjectKind.class;
    }
} 