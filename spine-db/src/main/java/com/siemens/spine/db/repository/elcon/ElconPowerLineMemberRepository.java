package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPowerLineMember;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_power_line_member")
@ApplicationScoped
public class ElconPowerLineMemberRepository extends AbstractRestCRUDRepositoryImpl<ElconPowerLineMember, Long> {

    @Override
    protected boolean isNew(ElconPowerLineMember entity) {
        return entity.getMemberId() == null;
    }

    @Override
    public Class<ElconPowerLineMember> getDomainType() {
        return ElconPowerLineMember.class;
    }
} 