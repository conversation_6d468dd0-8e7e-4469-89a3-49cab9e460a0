package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCoNeighbour;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_co_neighbour")
@ApplicationScoped
public class ElconCoNeighbourRepository extends AbstractRestCRUDRepositoryImpl<ElconCoNeighbour, Long> {

    @Override
    protected boolean isNew(ElconCoNeighbour entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconCoNeighbour> getDomainType() {
        return ElconCoNeighbour.class;
    }
} 