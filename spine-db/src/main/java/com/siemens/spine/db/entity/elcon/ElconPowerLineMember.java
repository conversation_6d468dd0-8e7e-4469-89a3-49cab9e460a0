package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "elcon_power_line_member")
public class ElconPowerLineMember {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "member_id", nullable = false)
    private Long memberId;

    @NotNull
    @Column(name = "device_id", nullable = false)
    private Long deviceId;

    @Size(max = 10)
    @Column(name = "power_kind", length = 10)
    private String powerKind;

    @NotNull
    @Column(name = "line_sequence", nullable = false)
    private Integer lineSequence;

    @NotNull
    @Column(name = "line_id", nullable = false)
    private Long lineId;

    @Size(max = 255)
    @Column(name = "description")
    private String description;

    @Size(max = 20)
    @Column(name = "connector", length = 20)
    private String connector;

    @Size(max = 20)
    @Column(name = "socket_type", length = 20)
    private String socketType;

    @Column(name = "segment")
    private BigDecimal segment;

    @Size(max = 20)
    @Column(name = "sub_type", length = 20)
    private String subType;

}