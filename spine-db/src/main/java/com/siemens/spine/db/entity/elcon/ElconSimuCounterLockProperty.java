package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_simu_counter_lock_property")
public class ElconSimuCounterLockProperty {

    @Id
    @Column(name = "property_id", nullable = false)
    private Long propertyId;

    @NotNull
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @Column(name = "cp_nr")
    private Short cpNr;

    @Size(max = 5)
    @Column(name = "andor", length = 5)
    private String andor;

    @Size(max = 255)
    @Column(name = "counter_a1")
    private String counterA1;

    @Size(max = 255)
    @Column(name = "counter_a2")
    private String counterA2;

    @Column(name = "limit_a")
    private Integer limitA;

    @Column(name = "delta_a")
    private Integer deltaA;

    @Size(max = 255)
    @Column(name = "counter_b1")
    private String counterB1;

    @Size(max = 255)
    @Column(name = "counter_b2")
    private String counterB2;

    @Column(name = "limit_b")
    private Integer limitB;

    @Column(name = "delta_b")
    private Integer deltaB;

    @Size(max = 255)
    @Column(name = "counter_c1")
    private String counterC1;

    @Size(max = 255)
    @Column(name = "counter_c2")
    private String counterC2;

    @Column(name = "limit_c")
    private Integer limitC;

    @Column(name = "delta_c")
    private Integer deltaC;

    @Size(max = 10)
    @Column(name = "property_origin", length = 10)
    private String propertyOrigin;

    @Column(name = "extension_id")
    private Long extensionId;

}