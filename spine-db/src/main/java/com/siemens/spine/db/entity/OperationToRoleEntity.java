package com.siemens.spine.db.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 26/12/2022
 */
@Entity
@Table(name = "operationtorole")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OperationToRoleEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "operationtorole_id_seq_gen")
    @SequenceGenerator(name = "operationtorole_id_seq_gen", sequenceName = "operationtorole_id_seq")
    private Long id;

    private String operation;

    private String roleName;

}
