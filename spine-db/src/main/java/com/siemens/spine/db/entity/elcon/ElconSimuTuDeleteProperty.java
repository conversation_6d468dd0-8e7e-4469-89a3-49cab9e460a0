package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_simu_tu_delete_property")
public class ElconSimuTuDeleteProperty {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "property_id", nullable = false)
    private Long propertyId;

    @NotNull
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @Column(name = "cp_nr")
    private Short cpNr;

    @Size(max = 10)
    @Column(name = "property_origin", length = 10)
    private String propertyOrigin;

    @Column(name = "extension_id")
    private Long extensionId;

}