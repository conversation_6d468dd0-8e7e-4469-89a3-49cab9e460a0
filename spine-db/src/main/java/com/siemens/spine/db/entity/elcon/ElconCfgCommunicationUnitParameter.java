package com.siemens.spine.db.entity.elcon;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.Hibernate;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@Entity
@Table(name = "elcon_cfg_communication_unit_parameters")
public class ElconCfgCommunicationUnitParameter {

    @EmbeddedId
    private ElconCfgCommunicationUnitParameterId id;

    @NotNull
    @Column(name = "cfg_id", nullable = false)
    private Long cfgId;

    @NotNull
    @Column(name = "project_id", nullable = false)
    private Long projectId;

    @Size(max = 20)
    @Column(name = "communication_kind", length = 20)
    private String communicationKind;

    @Size(max = 20)
    @Column(name = "connection_type", length = 20)
    private String connectionType;

    @Column(name = "tcp_port")
    private Integer tcpPort;

    @Size(max = 20)
    @Column(name = "tsap", length = 20)
    private String tsap;

    @Size(max = 50)
    @Column(name = "interface_name_pattern", length = 50)
    private String interfaceNamePattern;

    @Size(max = 50)
    @Column(name = "line_name_pattern", length = 50)
    private String lineNamePattern;

    @Data
    @Embeddable
    public static class ElconCfgCommunicationUnitParameterId implements Serializable {
        private Long cfgId;

        private Long projectId;

        private String communicationKind;

        private String connectionType;

        private Integer tcpPort;

        private String interfaceNamePattern;

        private String lineNamePattern;


    }
}