package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "elcon_cl_device_component_pos_info")
public class ElconClDeviceComponentPosInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "pos_id", nullable = false)
    private Long posId;

    @NotNull
    @Column(name = "component_id", nullable = false)
    private Long componentId;

    @Size(max = 255)
    @Column(name = "pos_type")
    private String posType;

    @Column(name = "speed")
    private BigDecimal speed;

    @Column(name = "pos_distance")
    private BigDecimal posDistance;

}