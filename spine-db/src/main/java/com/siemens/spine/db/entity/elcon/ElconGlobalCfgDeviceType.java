package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_global_cfg_device_type")
public class ElconGlobalCfgDeviceType {

    @Id
    @Size(max = 255)
    @Column(name = "device_type", nullable = false)
    private String deviceType;

    @Size(max = 255)
    @Column(name = "device_kind")
    private String deviceKind;

}