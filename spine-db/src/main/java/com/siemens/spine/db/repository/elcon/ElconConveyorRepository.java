package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconConveyor;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_conveyor")
@ApplicationScoped
public class ElconConveyorRepository extends AbstractRestCRUDRepositoryImpl<ElconConveyor, Long> {

    @Override
    protected boolean isNew(ElconConveyor entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconConveyor> getDomainType() {
        return ElconConveyor.class;
    }
} 