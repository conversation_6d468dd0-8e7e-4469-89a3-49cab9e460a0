package com.siemens.spine.db.adapter;

import javax.xml.bind.annotation.adapters.XmlAdapter;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @since 2025/04/21
 */

public class TimestampToLongAdapter extends XmlAdapter<Long, Timestamp> {

    @Override
    public Timestamp unmarshal(Long v) throws Exception {
        return v != null ? new Timestamp(v) : null;
    }

    @Override
    public Long marshal(Timestamp v) throws Exception {
        return v != null ? v.getTime() : null;
    }

}

