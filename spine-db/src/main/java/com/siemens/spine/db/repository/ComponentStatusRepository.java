package com.siemens.spine.db.repository;

import com.siemens.spine.db.entity.ComponentStatusEntity;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2/7/2023
 */
public interface ComponentStatusRepository
        extends GenericJpaRepository<com.siemens.spine.db.entity.ComponentStatusEntity, Long> {

    /**
     * @param projectName
     * @param componentIds
     * @return
     */
    List<ComponentStatusEntity> find(String projectName, List<Long> componentIds);

    int updateStateByOperation(String operation, List<Long> uniqueIds, Integer state, Date date);

    int updateMarkAsDoneBom(Long uniqueId, String sapId, Integer state, Date date);

    List<ComponentStatusEntity> findChangeComponentStatus(List<Long> componentIds);

}

