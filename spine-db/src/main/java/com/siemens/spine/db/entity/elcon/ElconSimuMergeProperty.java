package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "elcon_simu_merge_property")
public class ElconSimuMergeProperty {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "property_id", nullable = false)
    private Long propertyId;

    @NotNull
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @Column(name = "cp_nr")
    private Short cpNr;

    @Column(name = "ratio")
    private BigDecimal ratio;

    @Size(max = 10)
    @Column(name = "property_origin", length = 10)
    private String propertyOrigin;

    @Size(max = 5)
    @Column(name = "total_priority", length = 5)
    private String totalPriority;

    @Column(name = "extension_id")
    private Long extensionId;

}