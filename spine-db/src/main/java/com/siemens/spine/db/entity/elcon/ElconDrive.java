package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@Entity
@Table(name = "elcon_drive")
public class ElconDrive {

    @Id
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @NotNull
    @Column(name = "enable_ana_trc_boxes", nullable = false)
    private Short enableAnaTrcBoxes;

    @Column(name = "max_speed_set")
    private Integer maxSpeedSet;

    @Column(name = "disable_time_at_stop")
    private Long disableTimeAtStop;

    @Column(name = "pn_ok_timer")
    private Long pnOkTimer;

    @NotNull
    @Column(name = "disable_by_config", nullable = false)
    private Short disableByConfig;

}