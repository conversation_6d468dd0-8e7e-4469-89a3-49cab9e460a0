package com.siemens.spine.db.repository.metadata.impl;

import com.siemens.spine.db.repository.metadata.AttributeMetadata;
import com.siemens.spine.db.repository.metadata.EntityMetadata;
import com.siemens.spine.db.repository.metadata.IdMetadata;
import com.siemens.spine.db.utils.CompositeKeyUtils;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Default implementation of EntityMetadata.
 * Provides comprehensive cached metadata for JPA entities with focus on performance.
 *
 * @param <T> The entity type
 * <AUTHOR> <PERSON>am
 * @since 1.0
 */
@Slf4j
@Getter
@Builder
public class DefaultEntityMetadata<T> implements EntityMetadata<T> {

    private final Class<T> entityType;
    private final String tableName;
    private final IdMetadata idMetadata;
    private final List<AttributeMetadata> allAttributes;
    private final String primaryKeyFieldName;

    private final ConcurrentMap<String, AttributeMetadata> attributeCache = new ConcurrentHashMap<>();

    DefaultEntityMetadata(Class<T> entityType,
                          String tableName,
                          IdMetadata idMetadata,
                          List<AttributeMetadata> allAttributes,
                          String primaryKeyFieldName,
                          ConcurrentMap<String, AttributeMetadata> attributeCache) {
        this.entityType = entityType;
        this.tableName = tableName;
        this.idMetadata = idMetadata;
        this.allAttributes = allAttributes;
        this.primaryKeyFieldName = primaryKeyFieldName;
        if (attributeCache != null) {
            this.attributeCache.putAll(attributeCache);
        }
    }

    @Override
    public Optional<AttributeMetadata> getAttribute(String attributeName) {
        return Optional.ofNullable(attributeCache.computeIfAbsent(attributeName, this::findAttribute));
    }

    @Override
    public boolean hasCompositeKey() {
        return idMetadata.isComposite();
    }

    @Override
    public CompositeKeyUtils.CompositeKeyType getKeyType() {
        return idMetadata.getKeyType();
    }

    private AttributeMetadata findAttribute(String attributeName) {
        return allAttributes.stream()
                .filter(attr -> attr.matchesName(attributeName)).findFirst().orElse(null);
    }

    private void populateAttributeCache() {
        for (AttributeMetadata attribute : allAttributes) {
            // Cache both camelCase and snake_case names
            attributeCache.put(attribute.getName(), attribute);
            attributeCache.put(attribute.getSnakeCaseName(), attribute);
        }

        log.debug("Populated attribute cache for {} with {} entries", entityType.getSimpleName(),
                attributeCache.size());
    }

    /**
     * Gets metadata statistics for monitoring and debugging.
     */
    public EntityMetadataStats getStatistics() {
        long idAttributeCount = allAttributes.stream().mapToLong(attr -> attr.isId() ? 1 : 0).sum();

        return EntityMetadataStats.builder()
                .entityType(entityType.getSimpleName())
                .totalAttributes(allAttributes.size())
                .idAttributes((int) idAttributeCount)
                .hasCompositeKey(hasCompositeKey())
                .keyType(getKeyType())
                .cacheSize(attributeCache.size())
                .build();
    }

    /**
     * Builder class for creating DefaultEntityMetadata instances.
     */
    public static class DefaultEntityMetadataBuilder<T> {

        public DefaultEntityMetadata<T> build() {
            DefaultEntityMetadata<T> metadata = new DefaultEntityMetadata<>(entityType, tableName, idMetadata,
                    allAttributes, primaryKeyFieldName, new ConcurrentHashMap<>());

            // Pre-populate attribute cache for performance
            metadata.populateAttributeCache();

            log.debug("Created EntityMetadata for {} with {} attributes, composite key: {}", entityType.getSimpleName(),
                    allAttributes.size(), metadata.hasCompositeKey());

            return metadata;
        }

    }

    @Builder
    @Getter
    public static class EntityMetadataStats {

        private final String entityType;
        private final int totalAttributes;
        private final int idAttributes;
        private final boolean hasCompositeKey;
        private final CompositeKeyUtils.CompositeKeyType keyType;
        private final int cacheSize;

        @Override
        public String toString() {
            return String.format(
                    "EntityMetadataStats{entity='%s', attributes=%d, id=%d, composite=%s, type=%s, cache=%d}",
                    entityType, totalAttributes, idAttributes, hasCompositeKey, keyType, cacheSize);
        }

    }

}