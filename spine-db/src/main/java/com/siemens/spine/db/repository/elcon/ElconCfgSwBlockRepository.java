package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCfgSwBlock;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cfg_sw_block")
@ApplicationScoped
public class ElconCfgSwBlockRepository extends AbstractRestCRUDRepositoryImpl<ElconCfgSwBlock, Long> {

    @Override
    protected boolean isNew(ElconCfgSwBlock entity) {
        return entity.getSwBlockId() == null;
    }

    @Override
    public Class<ElconCfgSwBlock> getDomainType() {
        return ElconCfgSwBlock.class;
    }
} 