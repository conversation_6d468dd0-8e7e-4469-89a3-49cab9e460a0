package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.ProjectEntity;
import com.siemens.spine.db.repository.ProjectRepository;
import com.siemens.spine.db.repository.filter.ProjectFilter;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.NoResultException;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> <PERSON>
 * @version 1.0
 * @since 21/12/2022
 */
@ApplicationScoped
@Transactional(TxType.REQUIRED)
public class ProjectRepositoryImpl extends GenericJpaRepositoryImpl<ProjectEntity, Long> implements
        ProjectRepository {

    @Override
    public Class<ProjectEntity> getDomainType() {
        return ProjectEntity.class;
    }

    @Override
    public List<ProjectEntity> find(ProjectFilter filter) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<ProjectEntity> cq = cb.createQuery(ProjectEntity.class);
        Root<ProjectEntity> rootEntry = cq.from(ProjectEntity.class);

        List<Predicate> conditions = buildWhereClause(filter, cb, rootEntry);

        cq.select(rootEntry)
                .where(conditions.toArray(new Predicate[0]));
        List<ProjectEntity> ret = entityManager.createQuery(cq)
                .setMaxResults(1)
                .getResultList();

        return (ret == null || ret.isEmpty()) ? null : ret;
    }

    @Override
    public List<ProjectEntity> findAllProjectsByStatusState(List<String> states) {
        return entityManager.createQuery(
                        "select p from ProjectEntity p" +
                                " where p.projectName is not null and p.mainProjectState in (:states)", ProjectEntity.class)
                .setParameter("states", states)
                .getResultList();
    }

    @Override
    public boolean existByName(String name) {
        Optional<ProjectEntity> entity = findByName(name);
        return entity.isPresent();
    }

    @Override
    public Optional<ProjectEntity> findByName(String name) {
        try {
            ProjectEntity project = entityManager.createQuery(
                            """
                                    select p
                                    from ProjectEntity p
                                    where p.projectName = :name
                                    """, ProjectEntity.class)
                    .setParameter("name", name)
                    .getSingleResult();
            return Optional.of(project);
        } catch (NoResultException ex) {
            return Optional.empty();
        }
    }

    @Override
    public Long findIdByName(String name) {
        try {
            return entityManager.createQuery("select p.projectID " +
                            " from ProjectEntity p" +
                            " where p.projectName = :name", Long.class)
                    .setParameter("name", name)
                    .getSingleResult();
        } catch (NoResultException ex) {
            return null;
        }
    }

    private List<Predicate> buildWhereClause(ProjectFilter filter, CriteriaBuilder cb, Root<ProjectEntity> rootEntry) {
        if (filter == null) {
            return Collections.emptyList();
        }

        List<Predicate> ret = new ArrayList<>();

        if (filter.getProjectId() != null) {
            ret.add(
                    cb.equal(rootEntry.get("projectID"), filter.getProjectId())
            );
        }

        if (filter.getProjectName() != null) {
            Predicate name = cb.equal(rootEntry.get("projectName"), filter.getProjectName());
            Predicate sap = cb.equal(rootEntry.get("sapProjectKey"), filter.getProjectName());
            ret.add(cb.or(name, sap));
        }

        return ret;
    }

}

