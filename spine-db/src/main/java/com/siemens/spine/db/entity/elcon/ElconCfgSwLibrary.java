package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.Instant;

@Getter
@Setter
@Entity
@Table(name = "elcon_cfg_sw_library")
public class ElconCfgSwLibrary {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "sw_library_id", nullable = false)
    private Long swLibraryId;

    @NotNull
    @Column(name = "project_id", nullable = false)
    private Long projectId;

    @Size(max = 255)
    @Column(name = "library_name")
    private String libraryName;

    @Size(max = 255)
    @Column(name = "domain")
    private String domain;

    @Size(max = 255)
    @Column(name = "version")
    private String version;

    @Size(max = 255)
    @Column(name = "description")
    private String description;

    @Column(name = "last_import_date")
    private Instant lastImportDate;

}