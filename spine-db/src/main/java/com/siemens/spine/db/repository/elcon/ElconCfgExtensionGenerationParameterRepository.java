package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCfgExtensionGenerationParameter;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cfg_extension_generation_parameter")
@ApplicationScoped
public class ElconCfgExtensionGenerationParameterRepository extends AbstractRestCRUDRepositoryImpl<ElconCfgExtensionGenerationParameter, Long> {

    @Override
    protected boolean isNew(ElconCfgExtensionGenerationParameter entity) {
        return entity.getId() == null;
    }

    @Override
    public Class<ElconCfgExtensionGenerationParameter> getDomainType() {
        return ElconCfgExtensionGenerationParameter.class;
    }
} 