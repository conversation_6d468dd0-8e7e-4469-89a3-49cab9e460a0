package com.siemens.spine.db.repository.factory;

import com.siemens.spine.db.exception.JpaException;
import com.siemens.spine.db.utils.CompositeKeyUtils;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.Id;
import javax.persistence.IdClass;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * Factory for handling entities with @IdClass annotation.
 * This strategy uses a separate ID class to define the composite key structure
 * while the entity has corresponding @Id fields.
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
public class IdClassFactory extends AbstractCompositeKeyFactory {

    @Override
    public boolean canHandle(Class<?> entityClass) {
        return entityClass != null && entityClass.isAnnotationPresent(IdClass.class);
    }

    @Override
    public CompositeKeyUtils.CompositeKeyType getKeyType() {
        return CompositeKeyUtils.CompositeKeyType.ID_CLASS;
    }

    @Override
    public List<Field> extractIdFields(Class<?> entityClass) {
        List<Field> idFields = new ArrayList<>();

        if (entityClass == null || !entityClass.isAnnotationPresent(IdClass.class)) {
            return idFields;
        }

        // Get the @IdClass and analyze its fields
        IdClass idClassAnnotation = entityClass.getAnnotation(IdClass.class);
        Class<?> idClass = idClassAnnotation.value();

        for (Field idClassField : idClass.getDeclaredFields()) {
            if (!idClassField.getName().equals("serialVersionUID")) {
                // Find corresponding @Id field in entity
                try {
                    Field entityField = entityClass.getDeclaredField(idClassField.getName());
                    if (entityField.isAnnotationPresent(Id.class)) {
                        idFields.add(entityField);
                    }
                } catch (NoSuchFieldException e) {
                    log.warn("Field {} from @IdClass not found in entity {}",
                            idClassField.getName(), entityClass.getSimpleName());
                }
            }
        }

        // Sort fields by name for consistent ordering
        idFields.sort(Comparator.comparing(Field::getName));

        return idFields;
    }

    @Override
    public CompositeKeyUtils.CompositeKeyData createKeyDataFromMap(Map<String, String> keyValueMap,
                                                                   Class<?> entityClass) throws JpaException {
        List<Field> idFields = extractIdFields(entityClass);

        validateKeyComponentCount(keyValueMap, idFields, keyValueMap.toString(), entityClass);

        CompositeKeyUtils.CompositeKeyData keyData = new CompositeKeyUtils.CompositeKeyData();
        keyData.setKeyType(getKeyType());

        // Map each key-value pair to the corresponding field
        for (Field field : idFields) {
            String fieldName = field.getName();
            String stringValue = null;
            String usedKeyName = null;

            // Find the value for this field, supporting snake_case to camelCase conversion
            for (Map.Entry<String, String> entry : keyValueMap.entrySet()) {
                String keyName = entry.getKey();

                try {
                    FieldMatch fieldMatch = findMatchingField(keyName, List.of(field), entityClass);
                    if (fieldMatch.field().equals(field)) {
                        stringValue = entry.getValue();
                        usedKeyName = keyName;
                        break;
                    }
                } catch (JpaException e) {
                    // Continue searching
                }
            }

            if (stringValue == null) {
                // Try all keys against this field with proper matching
                for (String keyName : keyValueMap.keySet()) {
                    try {
                        FieldMatch fieldMatch = findMatchingField(keyName, List.of(field), entityClass);
                        if (fieldMatch.field().equals(field)) {
                            stringValue = keyValueMap.get(keyName);
                            usedKeyName = keyName;
                            break;
                        }
                    } catch (JpaException e) {
                        // Continue trying
                    }
                }
            }

            if (stringValue == null) {
                // Provide helpful error message using standardized format
                throw new JpaException(createFieldNotFoundError(fieldName, entityClass, keyValueMap, "@IdClass"));
            }

            try {
                Object convertedValue = convertToFieldType(stringValue, field.getType());
                keyData.addComponent(fieldName, field.getType(), convertedValue);
                log.debug("Successfully mapped key '{}' to @IdClass field '{}' with value '{}'",
                        usedKeyName, fieldName, stringValue);
            } catch (Exception e) {
                throw new JpaException(String.format(
                        "Failed to convert key value '%s' to type %s for @IdClass field '%s' (from key '%s')",
                        stringValue, field.getType().getSimpleName(), fieldName, usedKeyName
                ), e);
            }
        }

        return keyData;
    }

    @Override
    public void validateKeyConfiguration(Class<?> entityClass) throws JpaException {
        if (!entityClass.isAnnotationPresent(IdClass.class)) {
            throw new JpaException(String.format(
                    "Entity class %s does not have @IdClass annotation",
                    entityClass.getSimpleName()
            ));
        }

        IdClass idClassAnnotation = entityClass.getAnnotation(IdClass.class);
        Class<?> idClass = idClassAnnotation.value();

        // Validate the ID class itself
        if (idClass == null) {
            throw new JpaException(String.format(
                    "Entity class %s has null @IdClass value",
                    entityClass.getSimpleName()
            ));
        }

        // Check that all ID class fields have corresponding @Id fields in entity
        List<Field> idClassFields = new ArrayList<>();
        for (Field field : idClass.getDeclaredFields()) {
            if (!field.getName().equals("serialVersionUID")) {
                idClassFields.add(field);
            }
        }

        if (idClassFields.isEmpty()) {
            throw new JpaException(String.format(
                    "@IdClass %s has no valid fields for entity %s",
                    idClass.getSimpleName(), entityClass.getSimpleName()
            ));
        }

        List<Field> entityIdFields = new ArrayList<>();
        for (Field idClassField : idClassFields) {
            try {
                Field entityField = entityClass.getDeclaredField(idClassField.getName());
                if (!entityField.isAnnotationPresent(Id.class)) {
                    throw new JpaException(String.format(
                            "Field '%s' from @IdClass is not annotated with @Id in entity %s",
                            idClassField.getName(), entityClass.getSimpleName()
                    ));
                }

                // Validate type compatibility
                if (!idClassField.getType().equals(entityField.getType())) {
                    throw new JpaException(String.format(
                            "Type mismatch for field '%s': @IdClass has %s but entity has %s",
                            idClassField.getName(), idClassField.getType().getSimpleName(),
                            entityField.getType().getSimpleName()
                    ));
                }

                entityIdFields.add(entityField);
            } catch (NoSuchFieldException e) {
                throw new JpaException(String.format(
                        "Field '%s' from @IdClass %s not found in entity %s",
                        idClassField.getName(), idClass.getSimpleName(), entityClass.getSimpleName()
                ), e);
            }
        }

        // Validate that all field types are supported
        for (Field field : entityIdFields) {
            if (isSupportedType(field.getType())) {
                throw new JpaException(String.format(
                        "Unsupported @IdClass field type %s for field '%s' in entity %s",
                        field.getType().getSimpleName(), field.getName(), entityClass.getSimpleName()
                ));
            }
        }
    }

    @Override
    public int getPriority() {
        return 20; // Second highest - @IdClass is also very specific
    }

}