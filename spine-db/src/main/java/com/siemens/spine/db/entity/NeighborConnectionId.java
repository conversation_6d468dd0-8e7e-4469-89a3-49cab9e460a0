package com.siemens.spine.db.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

@Data
public class NeighborConnectionId implements Serializable {

    private Long owner;
    private Long neighbor;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        NeighborConnectionId that = (NeighborConnectionId) o;
        return Objects.equals(owner, that.owner) && Objects.equals(neighbor, that.neighbor);
    }

    @Override
    public int hashCode() {
        return Objects.hash(owner, neighbor);
    }

}