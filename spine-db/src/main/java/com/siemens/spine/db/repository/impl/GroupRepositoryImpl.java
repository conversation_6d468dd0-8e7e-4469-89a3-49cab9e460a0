package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.constant.GroupTypeEnum;
import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.GroupEntity;
import com.siemens.spine.db.repository.GroupRepository;
import com.siemens.spine.db.repository.ProjectRepository;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.persistence.Query;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 26/12/2022
 */
@ApplicationScoped
@Transactional(TxType.REQUIRED)
@Slf4j
public class GroupRepositoryImpl extends GenericJpaAuditRepositoryImpl<GroupEntity, Long> implements GroupRepository {

    @Inject
    private ProjectRepository projectRepository;

    @Override
    public Class<GroupEntity> getDomainType() {
        return GroupEntity.class;
    }

    @Override
    public List<GroupEntity> findGroupsByProjectNameAndGroupNameAndGroupType(String projectName,
                                                                             List<String> groupNames,
                                                                             GroupTypeEnum groupType) {
        Long projectId = projectRepository.findIdByName(projectName);
        if (projectId == null) {
            return Collections.emptyList();
        }
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<GroupEntity> cq = cb.createQuery(GroupEntity.class);
        Root<GroupEntity> rootEntry = cq.from(GroupEntity.class);

        // build where clause
        List<Predicate> conditions = new ArrayList<>();
        if (projectName != null) {
            conditions.add(cb.equal(rootEntry.get("projectId"), projectId));
        }

        if (groupType != null) {
            conditions.add(cb.equal(rootEntry.get("grouptype"), groupType));
        }

        if (groupNames != null && !groupNames.isEmpty()) {
            conditions.add(rootEntry.get("name").in(groupNames));
        }

        CriteriaQuery<GroupEntity> query = cq.select(rootEntry)
                .where(conditions.toArray(new Predicate[0]));
        List<GroupEntity> groupEntities = entityManager.createQuery(query)
                .getResultList();

        return (groupEntities == null) ? Collections.emptyList() : groupEntities;
    }

    @Override
    public List<GroupEntity> findGroupsByProjectId(Long projectId) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<GroupEntity> cq = cb.createQuery(GroupEntity.class);
        Root<GroupEntity> rootEntry = cq.from(GroupEntity.class);
        CriteriaQuery<GroupEntity> query = cq.select(rootEntry)
                .where(cb.equal(rootEntry.get("projectId"), projectId));
        List<GroupEntity> groupEntities = entityManager.createQuery(query)
                .getResultList();
        return (groupEntities == null) ? Collections.emptyList() : groupEntities;
    }

    @Override
    public List<GroupEntity> findComponentGroup(Long componentId) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<GroupEntity> cq = cb.createQuery(GroupEntity.class);
        Root<GroupEntity> rootEntry = cq.from(GroupEntity.class);
        Join<GroupEntity, ComponentEntity> componentJoin = rootEntry.join("components", JoinType.LEFT);

        Predicate condition = cb.equal(componentJoin.get("id"), componentId);
        cq.select(rootEntry).where(condition);

        return entityManager.createQuery(cq)
                .getResultList();
    }

    @Override
    public List<GroupEntity> findGroupsByProjectIdAndGroupIdIn(Long projectId, List<Long> groupIds) {
        Query query = entityManager.createQuery(
                "select g from GroupEntity g"
                        + " where g.projectId = :projectId"
                        + " and g.id in :groupIds",
                GroupEntity.class);
        query.setParameter("groupIds", groupIds);
        query.setParameter("projectId", projectId);
        return query.getResultList();
    }

    @Override
    public List<GroupEntity> findAllGroupsByTypeAndNameAndProjectId(Map<GroupTypeEnum, String> pairs, Long projectId) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<GroupEntity> cq = cb.createQuery(GroupEntity.class);
        Root<GroupEntity> rootEntry = cq.from(GroupEntity.class);
        List<Predicate> nameTypePredicates = new ArrayList<>();
        Predicate projectPredicate = cb.equal(rootEntry.get("projectId"), projectId);
        for (Map.Entry<GroupTypeEnum, String> pair : pairs.entrySet()) {
            Predicate pairPredicate = cb.and(cb.equal(rootEntry.get("grouptype"), pair.getKey()),
                    cb.equal(rootEntry.get("name"), pair.getValue()));
            nameTypePredicates.add(pairPredicate);

        }
        Predicate finalPredicate = cb.and(projectPredicate, cb.or(nameTypePredicates.toArray(new Predicate[0])));
        cq.select(rootEntry).where(finalPredicate);
        return entityManager.createQuery(cq).getResultList();
    }

    @Override
    public List<GroupEntity> findSubGroupsByProjectIdAndGroupNameAndGroupType(Long projectId,
                                                                              String groupName,
                                                                              GroupTypeEnum subGroupType) {
        return entityManager.createQuery("select g from GroupEntity g where g.name in (select p.subGroupName " +
                                " from ProjectViewGroupMappingEntity p" +
                                " where p.projectId = :projectId and p.name = :name) and g.grouptype = :subtype and g.projectId = :projectId",
                        GroupEntity.class)
                .setParameter("projectId", projectId)
                .setParameter("name", groupName)
                .setParameter("subtype", subGroupType)
                .getResultList();
    }

    @Override
    public List<GroupEntity> findGroupsNotEmpty(List<Long> ids) {
        return entityManager.createQuery("select g " +
                        " from GroupEntity g join g.components c " +
                        " where g.id in :ids and c is not empty", GroupEntity.class)
                .setParameter("ids", ids)
                .getResultList();
    }

    @Override
    public Optional<GroupEntity> getHistoryAtRevisions(Long id, Long revision) {
        if (revision == 0) {
            log.info("Revision is 0, return current component with id {}", id);
            return findById(id);
        }

        log.info("Get component at revision {} with id {}", revision, id);
        GroupEntity groupEntity = getAuditReader().find(GroupEntity.class, id, revision.intValue());
        return Optional.ofNullable(groupEntity);
    }

}

