package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_safety_zone")
public class ElconSafetyZone {

    @Id
    @Column(name = "zone_id", nullable = false)
    private Long zoneId;

    @NotNull
    @Column(name = "project_version_id", nullable = false)
    private Long projectVersionId;

    @Size(max = 255)
    @Column(name = "item_name")
    private String itemName;

    @Size(max = 255)
    @Column(name = "zone_type")
    private String zoneType;

}