package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconGlobalCfgDefaultObject;
import com.siemens.spine.db.entity.elcon.ElconGlobalCfgDefaultObjectId;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_global_cfg_default_objects")
@ApplicationScoped
public class ElconGlobalCfgDefaultObjectRepository extends AbstractRestCRUDRepositoryImpl<ElconGlobalCfgDefaultObject, ElconGlobalCfgDefaultObjectId> {

    @Override
    protected boolean isNew(ElconGlobalCfgDefaultObject entity) {
        return entity.getId() == null;
    }

    @Override
    public Class<ElconGlobalCfgDefaultObject> getDomainType() {
        return ElconGlobalCfgDefaultObject.class;
    }
} 