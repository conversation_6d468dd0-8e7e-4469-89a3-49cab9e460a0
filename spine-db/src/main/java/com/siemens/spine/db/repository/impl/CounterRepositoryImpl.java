package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.CounterEntity;
import com.siemens.spine.db.repository.CounterRepository;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.LockModeType;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;
import java.util.Collections;
import java.util.List;
import java.util.stream.LongStream;

@ApplicationScoped
@Slf4j
@Transactional(TxType.REQUIRED)
public class CounterRepositoryImpl extends GenericJpaRepositoryImpl<CounterEntity, String>
        implements CounterRepository {

    @Override
    public Class<CounterEntity> getDomainType() {
        return CounterEntity.class;
    }

    @Override
    public List<Long> getNextValuesCounter(String name, int amount) {
        CounterEntity counterEntity = entityManager.find(CounterEntity.class, name, LockModeType.PESSIMISTIC_WRITE);
        if (counterEntity == null) {
            return Collections.emptyList();
        }
        long currentValue = counterEntity.getCounterValue();
        long nextValue = currentValue + amount;
        if (nextValue > counterEntity.getMaxCounter()) {
            log.error("ID value exceeds maximum allowed value");
            return Collections.emptyList();
        }
        counterEntity.setCounterValue(nextValue);
        save(counterEntity);
        return LongStream.range(currentValue, nextValue).boxed().toList();
    }

}

