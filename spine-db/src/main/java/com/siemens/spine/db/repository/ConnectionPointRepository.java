package com.siemens.spine.db.repository;

import com.siemens.spine.db.entity.ConnectionPointEntity;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

public interface ConnectionPointRepository extends GenericJpaRepository<ConnectionPointEntity, Long> {

    /**
     * Delete all connection point of a component
     *
     * @param componentIds
     * @return
     */
    long deleteByComponents(List<Long> componentIds);

    List<ConnectionPointEntity> findByComponentId(Long componentId);

    List<ConnectionPointEntity> findByComponentIdAndName(List<Pair<String, String>> connectionPointConditions);

}

