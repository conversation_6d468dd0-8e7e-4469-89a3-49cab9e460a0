package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "elcon_simu_buffer_lock_diff_property")
public class ElconSimuBufferLockDiffProperty {

    @Id
    @Column(name = "property_id", nullable = false)
    private Long propertyId;

    @NotNull
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @Column(name = "cp_nr")
    private Short cpNr;

    @Column(name = "error")
    private Integer error;

    @Column(name = "pushbutton")
    private Integer pushbutton;

    @Size(max = 255)
    @Column(name = "counter1")
    private String counter1;

    @Size(max = 255)
    @Column(name = "counter2")
    private String counter2;

    @Column(name = "max_count")
    private Integer maxCount;

    @Column(name = "ratio")
    private BigDecimal ratio;

    @Column(name = "delay")
    private BigDecimal delay;

    @Column(name = "continue")
    private Integer continueField;

    @Size(max = 50)
    @Column(name = "dynamic_static", length = 50)
    private String dynamicStatic;

    @Size(max = 10)
    @Column(name = "property_origin", length = 10)
    private String propertyOrigin;

    @Column(name = "extension_id")
    private Long extensionId;

}