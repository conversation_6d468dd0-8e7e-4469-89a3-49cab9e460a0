package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_chute")
public class ElconChute {

    @Id
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @Column(name = "di_addr_jam")
    private Long diAddrJam;

    @Column(name = "di_addr_disable")
    private Long diAddrDisable;

    @Column(name = "di_addr_disable_req")
    private Long diAddrDisableReq;

    @Column(name = "di_addr_ready_recv")
    private Long diAddrReadyRecv;

    @Column(name = "do_addr_par_released")
    private Long doAddrParReleased;

    @Column(name = "do_addr_disabled")
    private Long doAddrDisabled;

    @Column(name = "do_addr_disabled_conf")
    private Long doAddrDisabledConf;

    @Size(max = 255)
    @Column(name = "pnpn_coupler_name")
    private String pnpnCouplerName;

}