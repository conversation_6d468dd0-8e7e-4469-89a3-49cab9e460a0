package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.ChangeGroupEntity;
import com.siemens.spine.db.repository.ChangeGroupRepository;
import com.siemens.spine.db.repository.filter.ChangeGroupFilter;
import com.siemens.spine.db.repository.views.ComponentChangeGroupView;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.NoResultException;
import javax.persistence.Query;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@ApplicationScoped
public class ChangeGroupRepositoryImpl extends GenericJpaRepositoryImpl<ChangeGroupEntity, Long>
        implements ChangeGroupRepository {

    private static final int BATCH_SIZE = 2000;

    @Override
    public Class<ChangeGroupEntity> getDomainType() {
        return ChangeGroupEntity.class;
    }

    @Override
    public List<ComponentChangeGroupView> getListChangeGroupByEachComponentIds(List<Long> componentIds) {

        if (CollectionUtils.isEmpty(componentIds)) {
            return Collections.emptyList();
        }
        List<Object[]> results = new ArrayList<>();
        int size = componentIds.size();
        for (int i = 0; i < size; i += BATCH_SIZE) {
            int end = Math.min(size, i + BATCH_SIZE);
            // TODO not sure about the last changegroup
            Query query = entityManager.createNativeQuery("SELECT  name, description , component_id\n" +
                    "FROM \n" +
                    "CHANGEGROUP CHGROUP,\n" +
                    "\n" +
                    "(SELECT DISTINCT ON (COMPONENT_ID) COMPONENT_ID,\n" +
                    "\tCHANGE_GROUP_ID\n" +
                    "\tFROM STATE\n" +
                    "\tWHERE COMPONENT_ID in (:componentIds) and CHANGE_GROUP_ID is not null\n" +
                    "\tORDER BY COMPONENT_ID,\n" +
                    "\tSTATUS_DATE DESC) COMSTATE\n" +
                    "\n" +
                    "WHERE CHGROUP.CHANGE_GROUP_ID = COMSTATE.CHANGE_GROUP_ID");
            query.setParameter("componentIds", componentIds.subList(i, end));
            List<Object[]> subResultList = query.getResultList();
            if (CollectionUtils.isEmpty(subResultList)) {
                for (Long componentId : componentIds) {
                    Object[] defaultComponent = new Object[3];
                    defaultComponent[0] = StringUtils.EMPTY;
                    defaultComponent[1] = StringUtils.EMPTY;
                    defaultComponent[2] = componentId;
                    subResultList.add(defaultComponent);
                }
            }
            results.addAll(subResultList);
        }

        return results.stream()
                .map(item -> new ComponentChangeGroupView(String.valueOf(item[0]), String.valueOf(item[1]),
                        Long.valueOf(item[2] + ""))).toList();
    }

    @Override
    public List<ChangeGroupEntity> getChangeGroups(ChangeGroupFilter filter) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<ChangeGroupEntity> cq = cb.createQuery(ChangeGroupEntity.class);
        Root<ChangeGroupEntity> rootEntry = cq.from(ChangeGroupEntity.class);

        List<Predicate> predicates = buildWhereClause(filter, cb, rootEntry);

        cq.select(rootEntry)
                .where(predicates.toArray(new Predicate[0]));

        return entityManager.createQuery(cq).getResultList();
    }

    @Override
    public Optional<ChangeGroupEntity> getChangeGroupByProjectIdAndName(Long projectId, String name) {
        try {
            return Optional.of(entityManager.createQuery("select cg " +
                                    " from ChangeGroupEntity cg " +
                                    " where cg.projectEntity.projectID = :projectId and cg.name = :name order by cg.id desc",
                            ChangeGroupEntity.class)
                    .setParameter("projectId", projectId)
                    .setParameter("name", name)
                    .setMaxResults(1)
                    .getSingleResult());
        } catch (NoResultException ex) {
            return Optional.empty();
        }
    }

    /**
     * Build the where clause of the query
     *
     * @param filter
     * @param cb
     * @param rootEntry
     * @return
     */
    private List<Predicate> buildWhereClause(ChangeGroupFilter filter,
                                             CriteriaBuilder cb,
                                             Root<ChangeGroupEntity> rootEntry) {
        if (filter == null) {
            return Collections.emptyList();
        }

        List<Predicate> predicates = new ArrayList<>();

        if (filter.getProjectId() != null) {
            predicates.add(
                    cb.equal(rootEntry.get("projectEntity").get("projectID"), filter.getProjectId())
            );
        }

        String projectName = filter.getProjectName();
        if (!StringUtils.isEmpty(projectName)) {
            predicates.add(
                    cb.equal(rootEntry.get("projectEntity").get("projectName"), projectName.trim())
            );
        }

        String text = filter.getText();
        if (text != null && !text.trim().equals("")) {
            String textPattern = "%" + text.trim().toLowerCase() + "%";
            predicates.add(
                    cb.like(cb.lower(rootEntry.get("name")), textPattern)
            );
        }

        return predicates;
    }

}

