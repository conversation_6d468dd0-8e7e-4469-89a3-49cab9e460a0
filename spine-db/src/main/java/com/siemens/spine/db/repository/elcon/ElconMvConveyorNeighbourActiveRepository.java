// WARNING: Entity ElconMvConveyorNeighbourActive does not have @Id or @EmbeddedId. Please check entity definition!
package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconMvConveyorNeighbourActive;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_mv_conveyor_neighbour_active")
@ApplicationScoped
public class ElconMvConveyorNeighbourActiveRepository extends AbstractRestCRUDRepositoryImpl<ElconMvConveyorNeighbourActive, Long> {

    // WARNING: No @Id or @EmbeddedId found. isNew() always returns false.
    @Override
    protected boolean isNew(ElconMvConveyorNeighbourActive entity) {
        return entity.getId() == null;
    }

    @Override
    public Class<ElconMvConveyorNeighbourActive> getDomainType() {
        return ElconMvConveyorNeighbourActive.class;
    }
} 