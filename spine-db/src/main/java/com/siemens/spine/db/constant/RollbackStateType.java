package com.siemens.spine.db.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum RollbackStateType {

    TYPE_0(0, "40.0 or 200.0"),
    TYPE_1(1, "40.1 or 200.1");

    private static final Map<Integer, RollbackStateType> mapping = new HashMap<>();

    static {
        for (RollbackStateType type : values()) {
            mapping.put(type.getTypeNum(), type);
        }
    }

    private final int typeNum;
    private final String description;

    public static RollbackStateType resolve(int typeNum) {
        return mapping.get(typeNum);
    }

}

