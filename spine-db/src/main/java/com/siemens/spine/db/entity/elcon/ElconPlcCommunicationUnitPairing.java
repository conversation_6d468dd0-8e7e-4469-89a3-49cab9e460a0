package com.siemens.spine.db.entity.elcon;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Getter
@Setter
@Entity
@Table(name = "elcon_plc_communication_unit_pairing")
public class ElconPlcCommunicationUnitPairing {
    @EmbeddedId
    private ElconPlcCommunicationUnitPairingId id;

    @NotNull
    @Column(name = "communication_id", nullable = false)
    private Long communicationId;

    @NotNull
    @Column(name = "communication_unit_id_1", nullable = false)
    private Long communicationUnitId1;

    @Column(name = "communication_unit_id_2")
    private Long communicationUnitId2;

    @Size(max = 50)
    @NotNull
    @Column(name = "category", nullable = false, length = 50)
    private String category;

    @Size(max = 4000)
    @Column(name = "additional_data_1", length = 4000)
    private String additionalData1;

    @Size(max = 4000)
    @Column(name = "additional_data_2", length = 4000)
    private String additionalData2;

    @NotNull
    @Column(name = "plc_communication_unit_pairing_id", nullable = false)
    private Long plcCommunicationUnitPairingId;

    @Data
    @Embeddable
    public static class ElconPlcCommunicationUnitPairingId implements Serializable {
        private Long communicationId;

        private Long communicationUnitId1;

        private Long communicationUnitId2;

        private String additionalData1;

        private String additionalData2;

        private Long plcCommunicationUnitPairingId;
    }

}