package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_discharge")
public class ElconDischarge {

    @Id
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @NotNull
    @Column(name = "dis_order_index", nullable = false)
    private Integer disOrderIndex;

    @Size(max = 127)
    @Column(name = "trig_dis_unit_name", length = 127)
    private String trigDisUnitName;

    @Size(max = 127)
    @Column(name = "gap_dis_unit_name", length = 127)
    private String gapDisUnitName;

}