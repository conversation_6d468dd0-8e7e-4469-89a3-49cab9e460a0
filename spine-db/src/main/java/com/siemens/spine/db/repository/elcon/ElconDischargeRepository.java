package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconDischarge;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_discharge")
@ApplicationScoped
public class ElconDischargeRepository extends AbstractRestCRUDRepositoryImpl<ElconDischarge, Long> {

    @Override
    protected boolean isNew(ElconDischarge entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconDischarge> getDomainType() {
        return ElconDischarge.class;
    }
} 