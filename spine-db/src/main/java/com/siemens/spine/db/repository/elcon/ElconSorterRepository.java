package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconSorter;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_sorter")
@ApplicationScoped
public class ElconSorterRepository extends AbstractRestCRUDRepositoryImpl<ElconSorter, Long> {

    @Override
    protected boolean isNew(ElconSorter entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconSorter> getDomainType() {
        return ElconSorter.class;
    }
} 