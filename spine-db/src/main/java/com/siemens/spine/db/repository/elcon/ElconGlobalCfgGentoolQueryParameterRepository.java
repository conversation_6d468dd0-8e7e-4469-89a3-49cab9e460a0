package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconGlobalCfgGentoolQueryParameter;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_global_cfg_gentool_query_parameter")
@ApplicationScoped
public class ElconGlobalCfgGentoolQueryParameterRepository extends AbstractRestCRUDRepositoryImpl<ElconGlobalCfgGentoolQueryParameter, Long> {

    @Override
    protected boolean isNew(ElconGlobalCfgGentoolQueryParameter entity) {
        return entity.getParameterId() == null;
    }

    @Override
    public Class<ElconGlobalCfgGentoolQueryParameter> getDomainType() {
        return ElconGlobalCfgGentoolQueryParameter.class;
    }
} 