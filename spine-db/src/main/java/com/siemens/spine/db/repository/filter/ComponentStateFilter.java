package com.siemens.spine.db.repository.filter;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ComponentStateFilter {

    private List<Long> componentIds;

    private String changedBy;

    private Timestamp changedAfter;

    private Timestamp changedBefore;

    private Long changeGroupId;

    private String orderBy;

}

