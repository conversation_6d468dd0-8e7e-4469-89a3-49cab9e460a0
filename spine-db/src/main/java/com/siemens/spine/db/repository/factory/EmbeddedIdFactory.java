package com.siemens.spine.db.repository.factory;

import com.siemens.spine.db.exception.JpaException;
import com.siemens.spine.db.utils.CompositeKeyUtils;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.EmbeddedId;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * Factory for handling entities with @EmbeddedId annotation.
 * This strategy uses an embedded ID class that contains all the key fields
 * as a single entity field annotated with @EmbeddedId.
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
public class EmbeddedIdFactory extends AbstractCompositeKeyFactory {

    @Override
    public boolean canHandle(Class<?> entityClass) {
        if (entityClass == null) {
            return false;
        }

        // Check for @EmbeddedId fields
        for (Field field : entityClass.getDeclaredFields()) {
            if (field.isAnnotationPresent(EmbeddedId.class)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public CompositeKeyUtils.CompositeKeyType getKeyType() {
        return CompositeKeyUtils.CompositeKeyType.EMBEDDED_ID;
    }

    @Override
    public List<Field> extractIdFields(Class<?> entityClass) {
        List<Field> idFields = new ArrayList<>();

        if (entityClass == null) {
            return idFields;
        }

        // Find the @EmbeddedId field and get its component fields
        for (Field field : entityClass.getDeclaredFields()) {
            if (field.isAnnotationPresent(EmbeddedId.class)) {
                // Get fields from the embedded ID class
                Class<?> embeddedIdClass = field.getType();
                for (Field embeddedField : embeddedIdClass.getDeclaredFields()) {
                    if (!embeddedField.getName().equals("serialVersionUID")) {
                        idFields.add(embeddedField);
                    }
                }
                break; // Should only be one @EmbeddedId field
            }
        }

        // Sort fields by name for consistent ordering
        idFields.sort(Comparator.comparing(Field::getName));

        return idFields;
    }

    @Override
    public CompositeKeyUtils.CompositeKeyData createKeyDataFromMap(Map<String, String> keyValueMap,
                                                                   Class<?> entityClass) throws JpaException {
        List<Field> idFields = extractIdFields(entityClass);

        validateKeyComponentCount(keyValueMap, idFields, keyValueMap.toString(), entityClass);

        CompositeKeyUtils.CompositeKeyData keyData = new CompositeKeyUtils.CompositeKeyData();
        keyData.setKeyType(getKeyType());

        // Map each key-value pair to the corresponding embedded field
        for (Field field : idFields) {
            String fieldName = field.getName();
            String stringValue = null;
            String usedKeyName = null;

            // Find the value for this field, supporting snake_case to camelCase conversion
            for (Map.Entry<String, String> entry : keyValueMap.entrySet()) {
                String keyName = entry.getKey();

                try {
                    FieldMatch fieldMatch = findMatchingField(keyName, List.of(field), entityClass);
                    if (fieldMatch.field().equals(field)) {
                        stringValue = entry.getValue();
                        usedKeyName = keyName;
                        break;
                    }
                } catch (JpaException e) {
                    // Continue searching
                }
            }

            if (stringValue == null) {
                for (String keyName : keyValueMap.keySet()) {
                    try {
                        stringValue = keyValueMap.get(keyName);
                        usedKeyName = keyName;
                        break;
                    } catch (JpaException e) {
                        log.warn("Could not find key {} in {}", keyName, keyValueMap);
                    }
                }
            }

            if (stringValue == null) {
                log.error("Could not find key {} in {}", usedKeyName, keyValueMap);
                throw new JpaException(createFieldNotFoundError(fieldName, entityClass, keyValueMap, "@EmbeddedId"));
            }

            try {
                Object convertedValue = convertToFieldType(stringValue, field.getType());
                keyData.addComponent(fieldName, field.getType(), convertedValue);
                log.debug("Successfully mapped key '{}' to @EmbeddedId field '{}' with value '{}'", usedKeyName,
                        fieldName, stringValue);
            } catch (Exception e) {
                throw new JpaException(String.format(
                        "Failed to convert key value '%s' to type %s for @EmbeddedId field '%s' (from key '%s')",
                        stringValue, field.getType().getSimpleName(), fieldName, usedKeyName), e);
            }
        }

        return keyData;
    }

    @Override
    public void validateKeyConfiguration(Class<?> entityClass) throws JpaException {
        Field embeddedIdField = null;
        int embeddedIdCount = 0;

        for (Field field : entityClass.getDeclaredFields()) {
            if (field.isAnnotationPresent(EmbeddedId.class)) {
                embeddedIdField = field;
                embeddedIdCount++;
            }
        }

        if (embeddedIdCount == 0) {
            throw new JpaException(
                    String.format("Entity class %s does not have @EmbeddedId field", entityClass.getSimpleName()));
        }

        if (embeddedIdCount > 1) {
            throw new JpaException(String.format("Entity class %s has %d @EmbeddedId fields, should have exactly 1",
                    entityClass.getSimpleName(), embeddedIdCount));
        }

        // Validate the embedded ID class
        Class<?> embeddedIdClass = embeddedIdField.getType();

        // Check that embedded ID class has valid fields
        List<Field> embeddedFields = new ArrayList<>();
        for (Field field : embeddedIdClass.getDeclaredFields()) {
            if (!field.getName().equals("serialVersionUID")) {
                embeddedFields.add(field);
            }
        }

        if (embeddedFields.isEmpty()) {
            throw new JpaException(String.format("@EmbeddedId class %s has no valid fields for entity %s",
                    embeddedIdClass.getSimpleName(), entityClass.getSimpleName()));
        }

        // Validate that all embedded field types are supported
        for (Field field : embeddedFields) {
            if (isSupportedType(field.getType())) {
                throw new JpaException(
                        String.format("Unsupported @EmbeddedId field type %s for field '%s' in class %s (entity %s)",
                                field.getType().getSimpleName(), field.getName(), embeddedIdClass.getSimpleName(),
                                entityClass.getSimpleName()));
            }
        }

        // Check that embedded ID class has proper equals and hashCode methods
        try {
            embeddedIdClass.getDeclaredMethod("equals", Object.class);
            embeddedIdClass.getDeclaredMethod("hashCode");
        } catch (NoSuchMethodException e) {
            log.warn("@EmbeddedId class {} should override equals() and hashCode() methods for entity {}",
                    embeddedIdClass.getSimpleName(), entityClass.getSimpleName());
        }
    }

    @Override
    public int getPriority() {
        return 10; // Highest priority - @EmbeddedId is most specific
    }

}