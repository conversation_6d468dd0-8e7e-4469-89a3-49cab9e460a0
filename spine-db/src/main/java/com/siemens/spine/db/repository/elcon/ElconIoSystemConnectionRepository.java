package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconIoSystemConnection;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_io_system_connection")
@ApplicationScoped
public class ElconIoSystemConnectionRepository extends AbstractRestCRUDRepositoryImpl<ElconIoSystemConnection, Long> {

    @Override
    protected boolean isNew(ElconIoSystemConnection entity) {
        return entity.getConnectionId() == null;
    }

    @Override
    public Class<ElconIoSystemConnection> getDomainType() {
        return ElconIoSystemConnection.class;
    }
} 