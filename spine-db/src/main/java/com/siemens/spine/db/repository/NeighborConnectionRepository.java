package com.siemens.spine.db.repository;

import com.siemens.spine.db.entity.ConnectionPointEntity;
import com.siemens.spine.db.entity.NeighborConnectionEntity;

import java.util.List;

public interface NeighborConnectionRepository extends GenericJpaRepository<NeighborConnectionEntity, Long> {

    long removeAllNeighborConnections(ConnectionPointEntity connectionPointEntity);

    List<NeighborConnectionEntity> findNeighborConnectionByComponentIds(List<Long> componentId);

}

