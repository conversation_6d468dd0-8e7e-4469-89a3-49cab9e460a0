package com.siemens.spine.db.repository;

import com.siemens.spine.db.constant.GroupTypeEnum;
import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.ComponentIdRev;
import com.siemens.spine.db.exception.JpaException;
import com.siemens.spine.db.repository.filter.ComponentFilter;
import com.siemens.spine.db.repository.views.ComponentView;

import java.util.List;
import java.util.Map;

public interface ComponentRepository extends GenericJpaAuditRepository<ComponentEntity, Long> {

    /**
     * Filter the component entities
     *
     * @param filter
     * @return
     */
    List<ComponentEntity> filter(ComponentFilter filter);

    List<Long> findIdsByProjectNameAndCondition(String projectName,
                                                Map<String, List<String>> conditions,
                                                int start,
                                                int end) throws JpaException;

    List<ComponentEntity> findByProjectNameAndComponentIds(String projectName, List<Long> componentIds);

    List<Long> findIdsByProjectNameAndGroupTypeAndGroupIds(String projectName,
                                                           GroupTypeEnum groupTypes,
                                                           List<String> groupNames);

    List<ComponentEntity> findByProjectIdAndNotInExecutionPhase(Long projectId);

    List<Long> findIdsByProject(String projectName);

    List<ComponentView> findAllByProjectId(Long projectId);

    int updateStateComponentByIds(List<Long> ids, Integer state);

    List<ComponentEntity> findByProjectIdAndVersionId(Long projectId, Long versionId);

    List<ComponentIdRev> findLatestComponentAuditByProjectId(Long projectId);

}
