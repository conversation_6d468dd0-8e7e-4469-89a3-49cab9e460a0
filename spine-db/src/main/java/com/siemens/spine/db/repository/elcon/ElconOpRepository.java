package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconOp;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_op")
@ApplicationScoped
public class ElconOpRepository extends AbstractRestCRUDRepositoryImpl<ElconOp, Long> {

    @Override
    protected boolean isNew(ElconOp entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconOp> getDomainType() {
        return ElconOp.class;
    }
} 