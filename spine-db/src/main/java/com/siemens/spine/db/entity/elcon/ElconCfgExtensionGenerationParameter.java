package com.siemens.spine.db.entity.elcon;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.Hibernate;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@Entity
@Table(name = "elcon_cfg_extension_generation_parameters")
public class ElconCfgExtensionGenerationParameter {

    @EmbeddedId
    private ElconCfgExtensionGenerationParameterId id;

    @NotNull
    @Column(name = "cfg_id", nullable = false)
    private Long cfgId;

    @NotNull
    @Column(name = "project_id", nullable = false)
    private Long projectId;

    @Size(max = 50)
    @Column(name = "generation_rule", length = 50)
    private String generationRule;

    @Size(max = 255)
    @Column(name = "name_template")
    private String nameTemplate;

    @Size(max = 255)
    @Column(name = "hw_outfit_name")
    private String hwOutfitName;

    @Data
    @Embeddable
    public static class ElconCfgExtensionGenerationParameterId implements Serializable {
        private Long cfgId;

        private Long projectId;

        private String generationRule;

        private String nameTemplate;

        private String hwOutfitName;


    }
}