package com.siemens.spine.db.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 26/12/2022
 */
@Entity
@Table(name = "usertogroup")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserToGroupEntity {

    @Id
    @GeneratedValue
    private Long id;

    @Column(name = "username")
    private String user;

    @Column(name = "groupname")
    private String userGroup;

}

