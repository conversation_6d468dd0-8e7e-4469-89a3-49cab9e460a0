package com.siemens.spine.db.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 24/2/2023
 */
public enum TypeAggregatorEnum {
    MECHANIC("mechanic"),
    ELECTRIC("electric"),
    STEELWORK("steelwork"),
    VIRTUAL("virtual"),
    ;
    private static final Map<String, TypeAggregatorEnum> TYPE_AGGREGATOR_ENUM_MAP = new HashMap<>();

    static {
        for (TypeAggregatorEnum typeAggregator : values()) {
            TYPE_AGGREGATOR_ENUM_MAP.put(typeAggregator.getValue(), typeAggregator);
        }
    }

    private final String value;

    TypeAggregatorEnum(String value) {
        this.value = value;
    }

    public static TypeAggregatorEnum resolve(String type) {
        return (type != null ? TYPE_AGGREGATOR_ENUM_MAP.get(type.toLowerCase()) : null);
    }

    public String getValue() {
        return value;
    }

    public boolean matches(String type) {
        return (this == resolve(type.toLowerCase()));
    }
}

