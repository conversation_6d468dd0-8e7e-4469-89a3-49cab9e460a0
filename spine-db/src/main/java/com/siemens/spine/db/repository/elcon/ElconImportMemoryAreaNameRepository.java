package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconImportMemoryAreaName;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_import_memory_area_name")
@ApplicationScoped
public class ElconImportMemoryAreaNameRepository extends AbstractRestCRUDRepositoryImpl<ElconImportMemoryAreaName, Long> {

    @Override
    protected boolean isNew(ElconImportMemoryAreaName entity) {
        return entity.getId() == null;
    }

    @Override
    public Class<ElconImportMemoryAreaName> getDomainType() {
        return ElconImportMemoryAreaName.class;
    }
} 