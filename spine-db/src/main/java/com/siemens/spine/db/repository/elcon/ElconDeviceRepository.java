package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconDevice;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_device")
@ApplicationScoped
public class ElconDeviceRepository extends AbstractRestCRUDRepositoryImpl<ElconDevice, Long> {

    @Override
    protected boolean isNew(ElconDevice entity) {
        return entity.getDeviceId() == null;
    }

    @Override
    public Class<ElconDevice> getDomainType() {
        return ElconDevice.class;
    }
} 