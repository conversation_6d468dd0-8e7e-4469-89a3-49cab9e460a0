package com.siemens.spine.db.repository;

import com.siemens.spine.db.entity.ProjectEntity;
import com.siemens.spine.db.repository.filter.ProjectFilter;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 21/12/2022
 */
public interface ProjectRepository extends GenericJpaRepository<ProjectEntity, Long> {

    List<ProjectEntity> find(ProjectFilter filter);

    List<ProjectEntity> findAllProjectsByStatusState(List<String> states);

    boolean existByName(String name);

    Optional<ProjectEntity> findByName(String name);

    Long findIdByName(String name);

}

