package com.siemens.spine.db.repository;

import com.siemens.spine.db.entity.DecompositionEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 22/12/2022
 */
public interface DecompositionRepository extends GenericJpaRepository<DecompositionEntity, Long> {

    List<DecompositionEntity> findByComponentId(Long componentId);

    List<DecompositionEntity> findUnfinishedMaterials(Long projectId);

    long deleteByComponentIds(List<Long> componentIds);

}

