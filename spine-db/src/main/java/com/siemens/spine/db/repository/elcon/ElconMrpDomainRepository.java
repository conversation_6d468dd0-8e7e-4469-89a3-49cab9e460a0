package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconMrpDomain;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_mrp_domain")
@ApplicationScoped
public class ElconMrpDomainRepository extends AbstractRestCRUDRepositoryImpl<ElconMrpDomain, Long> {

    @Override
    protected boolean isNew(ElconMrpDomain entity) {
        return entity.getDomainId() == null;
    }

    @Override
    public Class<ElconMrpDomain> getDomainType() {
        return ElconMrpDomain.class;
    }
} 