package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconObjectExtension;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_object_extension")
@ApplicationScoped
public class ElconObjectExtensionRepository extends AbstractRestCRUDRepositoryImpl<ElconObjectExtension, Long> {

    @Override
    protected boolean isNew(ElconObjectExtension entity) {
        return entity.getExtensionId() == null;
    }

    @Override
    public Class<ElconObjectExtension> getDomainType() {
        return ElconObjectExtension.class;
    }
} 