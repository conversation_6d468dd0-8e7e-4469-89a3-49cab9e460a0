package com.siemens.spine.db.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Entity
@Table(name = "grouptoprojectandrole")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
//@Audited
public class GroupToProjectAndRoleEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "grouptoprojectandrole_id_seq_gen")
    @SequenceGenerator(name = "grouptoprojectandrole_id_seq_gen", sequenceName = "grouptoprojectandrole_id_seq")
    private Long id;

    @Column(name = "groupname")
    private String userGroup;

    @ManyToOne
    @JoinColumn(name = "projectId")
    // some projects referenced here are NOT IN PROJECT TABLE (dataset) - where the hell are they? do we accept projects here that are absent from Project?
    //    @NotAudited
    private ProjectEntity project;

    private String roleName;

}

