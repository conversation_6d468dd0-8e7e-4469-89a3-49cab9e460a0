package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_component")
public class ElconComponent {

    @NotNull
    @Id
    @Column(name = "component_id", nullable = false)
    private Long componentId;

    @Size(max = 255)
    @NotNull
    @Column(name = "component_kind", nullable = false)
    private String componentKind;

    @NotNull
    @Column(name = "plc_area_id", nullable = false)
    private Long plcAreaId;

    @Column(name = "sw_block_id")
    private Long swBlockId;

}