package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconInduction;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_induction")
@ApplicationScoped
public class ElconInductionRepository extends AbstractRestCRUDRepositoryImpl<ElconInduction, Long> {

    @Override
    protected boolean isNew(ElconInduction entity) {
        return entity.getId() == null;
    }

    @Override
    public Class<ElconInduction> getDomainType() {
        return ElconInduction.class;
    }
} 