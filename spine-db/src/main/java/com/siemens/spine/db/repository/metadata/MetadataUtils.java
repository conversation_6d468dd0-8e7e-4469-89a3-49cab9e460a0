package com.siemens.spine.db.repository.metadata;

import com.siemens.spine.db.exception.JpaException;
import com.siemens.spine.db.utils.CompositeKeyUtils;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

/**
 * Utility methods for working with EntityMetadata and IdMetadata.
 * Provides common operations for field resolution, path building, and query construction.
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
public final class MetadataUtils {

    private MetadataUtils() {
        // Utility class
    }

    /**
     * Builds JPA predicates for composite key lookup using cached metadata.
     * This method provides optimal performance by using pre-computed JPA paths.
     *
     * @param keyData    The composite key data
     * @param idMetadata The cached ID metadata
     * @param cb         The criteria builder
     * @param root       The query root
     * @param <T>        The entity type
     * @return List of predicates for the key components
     * @throws JpaException if path building fails
     */
    public static <T> List<Predicate> buildKeyPredicates(
            CompositeKeyUtils.CompositeKeyData keyData,
            IdMetadata idMetadata,
            CriteriaBuilder cb,
            Root<T> root) throws JpaException {

        List<Predicate> predicates = new ArrayList<>();

        for (CompositeKeyUtils.CompositeKeyData.KeyComponent component : keyData.getComponents()) {
            String jpaPath = idMetadata.buildJpaPath(component.fieldName());
            Path<Object> path = buildNestedPath(root, jpaPath);

            Predicate predicate = cb.equal(path, component.value());
            predicates.add(predicate);

            log.debug("Built predicate: {} = {} (path: {})",
                    component.fieldName(), component.value(), jpaPath);
        }

        return predicates;
    }

    /**
     * Builds a nested JPA path from a dot-separated string.
     * Handles paths like "id.fieldName" for @EmbeddedId entities.
     *
     * @param root    The query root
     * @param jpaPath The dot-separated path string
     * @param <T>     The entity type
     * @return The built path
     */
    @SuppressWarnings("unchecked")
    public static <T> Path<Object> buildNestedPath(Root<T> root, String jpaPath) {
        String[] pathComponents = jpaPath.split("\\.");
        Path<Object> path = (Path<Object>) root;

        for (String component : pathComponents) {
            path = path.get(component);
        }

        return path;
    }

}