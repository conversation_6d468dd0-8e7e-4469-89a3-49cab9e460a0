package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconLinkSafetyLineDevice;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_link_safety_line_device")
@ApplicationScoped
public class ElconLinkSafetyLineDeviceRepository extends AbstractRestCRUDRepositoryImpl<ElconLinkSafetyLineDevice, Long> {

    @Override
    protected boolean isNew(ElconLinkSafetyLineDevice entity) {
        return entity.getLinkId() == null;
    }

    @Override
    public Class<ElconLinkSafetyLineDevice> getDomainType() {
        return ElconLinkSafetyLineDevice.class;
    }
} 