package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@Entity
@Table(name = "elcon_carrier")
public class ElconCarrier {

    @Id
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @NotNull
    @Column(name = "inv_motor_dir", nullable = false)
    private Short invMotorDir;

    @Column(name = "can_node_id")
    private Integer canNodeId;

    @Column(name = "network_parent_cell_id")
    private Integer networkParentCellId;

    @Column(name = "pwr_supply_unit_parent_cell_id")
    private Integer pwrSupplyUnitParentCellId;

    @Column(name = "pwr_collector_parent_cell_id")
    private Integer pwrCollectorParentCellId;

}