package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconProjectVersion;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_project_version")
@ApplicationScoped
public class ElconProjectVersionRepository extends AbstractRestCRUDRepositoryImpl<ElconProjectVersion, Long> {

    @Override
    protected boolean isNew(ElconProjectVersion entity) {
        return entity.getProjectVersionId() == null;
    }

    @Override
    public Class<ElconProjectVersion> getDomainType() {
        return ElconProjectVersion.class;
    }
} 