package com.siemens.spine.db.repository.filter;

import com.siemens.spine.db.constant.GroupTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ComponentFilter {

    private List<Long> componentIds;

    private Long projectId;

    private String specificProjectName;

    private List<Integer> states;

    private Pair<GroupTypeEnum, List<String>> groupMapping;

}

