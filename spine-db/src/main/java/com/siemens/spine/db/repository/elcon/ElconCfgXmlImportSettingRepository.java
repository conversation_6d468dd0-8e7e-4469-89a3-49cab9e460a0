package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCfgXmlImportSetting;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cfg_xml_import_setting")
@ApplicationScoped
public class ElconCfgXmlImportSettingRepository extends AbstractRestCRUDRepositoryImpl<ElconCfgXmlImportSetting, Long> {

    @Override
    protected boolean isNew(ElconCfgXmlImportSetting entity) {
        return entity.getCfgId() == null;
    }

    @Override
    public Class<ElconCfgXmlImportSetting> getDomainType() {
        return ElconCfgXmlImportSetting.class;
    }
} 