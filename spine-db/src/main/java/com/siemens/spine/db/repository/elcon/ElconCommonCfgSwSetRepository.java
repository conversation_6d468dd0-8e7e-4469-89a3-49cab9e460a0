package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCommonCfgSwSet;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_common_cfg_sw_set")
@ApplicationScoped
public class ElconCommonCfgSwSetRepository extends AbstractRestCRUDRepositoryImpl<ElconCommonCfgSwSet, Long> {

    @Override
    protected boolean isNew(ElconCommonCfgSwSet entity) {
        return entity.getSwSetId() == null;
    }

    @Override
    public Class<ElconCommonCfgSwSet> getDomainType() {
        return ElconCommonCfgSwSet.class;
    }
} 