package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MapsId;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "elcon_safety_pushbutton")
public class ElconSafetyPushbutton {

    @Id
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @MapsId
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "object_id", nullable = false)
    private ElconObject elconObject;

    @Column(name = "fdi")
    private Short fdi;

    @Size(max = 255)
    @Column(name = "symbol")
    private String symbol;

    @Column(name = "contacts")
    private Short contacts;

    @Column(name = "coordinate_x")
    private BigDecimal coordinateX;

    @Column(name = "coordinate_y")
    private BigDecimal coordinateY;

    @Column(name = "coordinate_z")
    private BigDecimal coordinateZ;

}