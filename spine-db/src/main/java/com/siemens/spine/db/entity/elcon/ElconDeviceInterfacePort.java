package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_device_interface_port")
public class ElconDeviceInterfacePort {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "port_id", nullable = false)
    private Long portId;

    @NotNull
    @Column(name = "interface_id", nullable = false)
    private Long interfaceId;

    @Size(max = 255)
    @Column(name = "port_name")
    private String portName;

    @Size(max = 20)
    @Column(name = "port_type", length = 20)
    private String portType;

    @Size(max = 255)
    @Column(name = "connector_type")
    private String connectorType;

    @Column(name = "port_position")
    private Short portPosition;

}