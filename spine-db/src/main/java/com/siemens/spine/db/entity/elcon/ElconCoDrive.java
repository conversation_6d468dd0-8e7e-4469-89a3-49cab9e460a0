package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "elcon_co_drive")
public class ElconCoDrive {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "drive_id", nullable = false)
    private Long driveId;

    @NotNull
    @Column(name = "nr", nullable = false)
    private Long nr;

    @Column(name = "accel")
    private BigDecimal accel;

    @Size(max = 255)
    @Column(name = "base_type")
    private String baseType;

    @Size(max = 255)
    @Column(name = "brake_part_no")
    private String brakePartNo;

    @Size(max = 255)
    @Column(name = "brake_supply_voltage")
    private String brakeSupplyVoltage;

    @Size(max = 255)
    @Column(name = "brake_power")
    private String brakePower;

    @Column(name = "cos_phi")
    private BigDecimal cosPhi;

    @Size(max = 255)
    @Column(name = "brake_resist")
    private String brakeResist;

    @Size(max = 255)
    @Column(name = "cdc_revision")
    private String cdcRevision;

    @Column(name = "current")
    private BigDecimal current;

    @Size(max = 255)
    @Column(name = "colour_gmot")
    private String colourGmot;

    @Column(name = "eta_motor")
    private BigDecimal etaMotor;

    @Column(name = "current_electric")
    private BigDecimal currentElectric;

    @Size(max = 255)
    @Column(name = "brake_type")
    private String brakeType;

    @Size(max = 255)
    @Column(name = "cmod_part_no")
    private String cmodPartNo;

    @Size(max = 255)
    @Column(name = "conv_code")
    private String convCode;

    @Size(max = 255)
    @Column(name = "function_id")
    private String functionId;

    @Column(name = "decel")
    private BigDecimal decel;

    @Size(max = 255)
    @Column(name = "drive_stat")
    private String driveStat;

    @Column(name = "line_capacitance")
    private BigDecimal lineCapacitance;

    @Size(max = 255)
    @Column(name = "f_line")
    private String fLine;

    @Column(name = "current_nom")
    private BigDecimal currentNom;

    @Size(max = 255)
    @Column(name = "freq_set")
    private String freqSet;

    @Size(max = 255)
    @Column(name = "key_id")
    private String keyId;

    @Size(max = 255)
    @Column(name = "motor_assigned_date")
    private String motorAssignedDate;

    @Size(max = 255)
    @Column(name = "motor_code_number")
    private String motorCodeNumber;

    @Column(name = "gearbox_ratio")
    private BigDecimal gearboxRatio;

    @Column(name = "i_const")
    private BigDecimal iConst;

    @Size(max = 255)
    @Column(name = "mlfb1")
    private String mlfb1;

    @Size(max = 255)
    @Column(name = "mlfb2")
    private String mlfb2;

    @Size(max = 255)
    @Column(name = "mlfb3")
    private String mlfb3;

    @Size(max = 255)
    @Column(name = "gearbox_type")
    private String gearboxType;

    @Column(name = "i_eff")
    private BigDecimal iEff;

    @Column(name = "motor_mom_inert_ratio")
    private BigDecimal motorMomInertRatio;

    @Size(max = 255)
    @Column(name = "motor_plug_orientation")
    private String motorPlugOrientation;

    @Size(max = 255)
    @Column(name = "motor_plug_type")
    private String motorPlugType;

    @Column(name = "motor_rpm_at_line_freq")
    private BigDecimal motorRpmAtLineFreq;

    @Size(max = 255)
    @Column(name = "motor_temperature_sensor_type")
    private String motorTemperatureSensorType;

    @Size(max = 255)
    @Column(name = "motor_type_family")
    private String motorTypeFamily;

    @Size(max = 255)
    @Column(name = "n1f_line")
    private String n1fLine;

    @Size(max = 255)
    @Column(name = "motor_orient")
    private String motorOrient;

    @Size(max = 255)
    @Column(name = "mpos")
    private String mpos;

    @Size(max = 255)
    @Column(name = "motor_type")
    private String motorType;

    @Column(name = "motor_speed")
    private BigDecimal motorSpeed;

    @Size(max = 255)
    @Column(name = "name_id")
    private String nameId;

    @Column(name = "power")
    private BigDecimal power;

    @Column(name = "order_flag_gm")
    private Short orderFlagGm;

    @Size(max = 255)
    @Column(name = "pmod_part_no")
    private String pmodPartNo;

    @Column(name = "reactive_power")
    private BigDecimal reactivePower;

    @Column(name = "order_flag_bres")
    private Short orderFlagBres;

    @Column(name = "order_flag_vfdpm")
    private Short orderFlagVfdpm;

    @Column(name = "order_flag_ta")
    private Short orderFlagTa;

    @Column(name = "order_flag_vfdcm")
    private Short orderFlagVfdcm;

    @Column(name = "power_nom")
    private BigDecimal powerNom;

    @Size(max = 255)
    @Column(name = "sap_no")
    private String sapNo;

    @Size(max = 255)
    @Column(name = "sap_no_bres")
    private String sapNoBres;

    @Size(max = 255)
    @Column(name = "start_stop_req")
    private String startStopReq;

    @Size(max = 255)
    @Column(name = "sap_no_vfdcm")
    private String sapNoVfdcm;

    @Size(max = 255)
    @Column(name = "sap_no_ta")
    private String sapNoTa;

    @Size(max = 255)
    @Column(name = "sap_no_vfdpm")
    private String sapNoVfdpm;

    @Column(name = "sort_no")
    private Long sortNo;

    @Size(max = 255)
    @Column(name = "windg_connection")
    private String windgConnection;

    @Size(max = 255)
    @Column(name = "ta_type")
    private String taType;

    @Column(name = "static_power_required", precision = 38)
    private BigDecimal staticPowerRequired;

    @Size(max = 255)
    @Column(name = "motor_manufacturer")
    private String motorManufacturer;

    @Size(max = 255)
    @Column(name = "vfd_manufacturer")
    private String vfdManufacturer;

    @Size(max = 255)
    @Column(name = "br_res_manufacturer")
    private String brResManufacturer;

    @Size(max = 255)
    @Column(name = "motor_connection")
    private String motorConnection;

    @Size(max = 255)
    @Column(name = "motor_voltage_supply")
    private String motorVoltageSupply;

    @Column(name = "motor_power_rating_plate")
    private BigDecimal motorPowerRatingPlate;

}