package com.siemens.spine.db.repository.metadata;

import com.siemens.spine.db.utils.CompositeKeyUtils;

import java.util.List;
import java.util.Optional;

/**
 * Metadata interface for entity ID information.
 * Supports all JPA composite key approaches: single @Id, multiple @Id, @IdClass, @EmbeddedId.
 * <p>
 * Pre-computes JPA paths for efficient criteria query building without reflection overhead.
 *
 * <AUTHOR>
 * @since 1.0
 */
public interface IdMetadata {

    /**
     * Checks if this entity has composite keys.
     *
     * @return true for multiple @Id, @IdClass, or @EmbeddedId
     */
    boolean isComposite();

    /**
     * Gets the composite key type for this entity.
     *
     * @return The composite key type (SINGLE_ID, MULTIPLE_ID, ID_CLASS, EMBEDDED_ID)
     */
    CompositeKeyUtils.CompositeKeyType getKeyType();

    /**
     * Gets all ID attribute metadata.
     * For single @Id: returns single attribute
     * For composite keys: returns all ID attributes
     *
     * @return List of ID attribute metadata
     */
    List<AttributeMetadata> getIdAttributes();

    /**
     * Gets single ID attribute metadata (for single @Id entities only).
     *
     * @return Single ID attribute metadata
     * @throws IllegalStateException if entity has composite keys
     */
    AttributeMetadata getSingleIdAttribute();

    /**
     * Gets the ID class for @IdClass entities.
     *
     * @return ID class or null if not @IdClass entity
     */
    Class<?> getIdClass();

    /**
     * Gets the @EmbeddedId attribute metadata.
     *
     * @return Embedded ID attribute metadata or null if not @EmbeddedId entity
     */
    AttributeMetadata getEmbeddedIdAttribute();

    /**
     * Builds pre-computed JPA path for the given attribute name.
     * <p>
     * Examples:
     * - Single @Id: buildJpaPath("id") → "id"
     * - Multiple @Id: buildJpaPath("customerId") → "customerId"
     * - @EmbeddedId: buildJpaPath("customerId") → "id.customerId"
     * - @IdClass: buildJpaPath("customerId") → "customerId"
     *
     * @param attributeName The ID attribute name
     * @return Pre-computed JPA path for criteria queries
     */
    String buildJpaPath(String attributeName);

    /**
     * Gets ID attribute by name, supporting both camelCase and snake_case.
     *
     * @param attributeName The attribute name
     * @return ID attribute metadata if found
     */
    Optional<AttributeMetadata> getIdAttribute(String attributeName);

    /**
     * Gets the embedded ID field name (for @EmbeddedId entities).
     *
     * @return Embedded ID field name or null if not @EmbeddedId entity
     */
    String getEmbeddedIdFieldName();

    /**
     * Checks if the given attribute name is part of the entity's ID.
     *
     * @param attributeName The attribute name to check
     * @return true if attribute is part of the ID
     */
    boolean isIdAttribute(String attributeName);

    /**
     * Gets the total number of ID components.
     * Single @Id: returns 1
     * Composite keys: returns number of ID fields
     *
     * @return Number of ID components
     */
    int getIdComponentCount();

    /**
     * Validates that all required ID components are present in the given key data.
     *
     * @param keyData The composite key data to validate
     * @return true if all required ID components are present
     */
    boolean isValidKeyData(CompositeKeyUtils.CompositeKeyData keyData);

}