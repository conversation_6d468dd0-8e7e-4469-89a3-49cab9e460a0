package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconHStation;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_h_station")
@ApplicationScoped
public class ElconHStationRepository extends AbstractRestCRUDRepositoryImpl<ElconHStation, Long> {

    @Override
    protected boolean isNew(ElconHStation entity) {
        return entity.getStationId() == null;
    }

    @Override
    public Class<ElconHStation> getDomainType() {
        return ElconHStation.class;
    }
} 