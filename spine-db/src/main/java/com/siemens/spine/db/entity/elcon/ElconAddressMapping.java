package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_address_mapping")
public class ElconAddressMapping {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "mapping_id", nullable = false)
    private Long mappingId;

    @NotNull
    @Column(name = "io_system_id", nullable = false)
    private Long ioSystemId;

    @NotNull
    @Column(name = "device_id", nullable = false)
    private Long deviceId;

    @Size(max = 10)
    @Column(name = "mapping_type", length = 10)
    private String mappingType;

    @Size(max = 255)
    @Column(name = "start_address")
    private String startAddress;

    @Column(name = "data_length")
    private Long dataLength;

    @Size(max = 255)
    @Column(name = "start_address_type")
    private String startAddressType;

    @NotNull
    @Column(name = "start_address_bytes", nullable = false)
    private Long startAddressBytes;

    @NotNull
    @Column(name = "start_address_bits", nullable = false)
    private Long startAddressBits;

    @Size(max = 255)
    @Column(name = "data_length_type")
    private String dataLengthType;

    @NotNull
    @Column(name = "data_length_bytes", nullable = false)
    private Long dataLengthBytes;

    @NotNull
    @Column(name = "data_length_bits", nullable = false)
    private Long dataLengthBits;

}