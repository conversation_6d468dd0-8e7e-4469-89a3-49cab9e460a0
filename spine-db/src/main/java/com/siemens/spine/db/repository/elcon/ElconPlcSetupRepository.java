package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPlcSetup;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_plc_setup")
@ApplicationScoped
public class ElconPlcSetupRepository extends AbstractRestCRUDRepositoryImpl<ElconPlcSetup, Long> {

    @Override
    protected boolean isNew(ElconPlcSetup entity) {
        return entity.getSetupId() == null;
    }

    @Override
    public Class<ElconPlcSetup> getDomainType() {
        return ElconPlcSetup.class;
    }
} 