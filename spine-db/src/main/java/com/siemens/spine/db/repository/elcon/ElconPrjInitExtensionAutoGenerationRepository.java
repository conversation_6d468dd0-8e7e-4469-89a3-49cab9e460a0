package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPrjInitExtensionAutoGeneration;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_prj_init_extension_auto_generation")
@ApplicationScoped
public class ElconPrjInitExtensionAutoGenerationRepository extends AbstractRestCRUDRepositoryImpl<ElconPrjInitExtensionAutoGeneration, Long> {

    @Override
    protected boolean isNew(ElconPrjInitExtensionAutoGeneration entity) {
        return entity.getCfgId() == null;
    }

    @Override
    public Class<ElconPrjInitExtensionAutoGeneration> getDomainType() {
        return ElconPrjInitExtensionAutoGeneration.class;
    }
} 