package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconLinkSafetyZoneButton;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_link_safety_zone_button")
@ApplicationScoped
public class ElconLinkSafetyZoneButtonRepository extends AbstractRestCRUDRepositoryImpl<ElconLinkSafetyZoneButton, Long> {

    @Override
    protected boolean isNew(ElconLinkSafetyZoneButton entity) {
        return entity.getLinkId() == null;
    }

    @Override
    public Class<ElconLinkSafetyZoneButton> getDomainType() {
        return ElconLinkSafetyZoneButton.class;
    }
} 