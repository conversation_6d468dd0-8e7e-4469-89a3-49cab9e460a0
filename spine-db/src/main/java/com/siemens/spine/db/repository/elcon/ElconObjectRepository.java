package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconObject;

import javax.enterprise.context.ApplicationScoped;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 19/6/2025
 **/
@RestRepository(path = "elcon_object")
@ApplicationScoped
public class ElconObjectRepository extends AbstractRestCRUDRepositoryImpl<ElconObject, Long> {

    @Override
    protected boolean isNew(ElconObject entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconObject> getDomainType() {
        return ElconObject.class;
    }

}
