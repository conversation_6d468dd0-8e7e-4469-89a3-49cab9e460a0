package com.siemens.spine.db.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 23/11/2022
 */
public enum GroupTypeEnum {
    BuildSection("DEB"),
    DeliveryBatch("DEB"),
    BuildingSection("BDS"),
    CalculationArea("CAL"),
    Drawing("DRA"),
    EStopGroup("EST"),
    InstallationSection("INS"),
    LineName("LIN"),
    PlcArea("PLC"),
    Screen("SCR"),
    SequenceGroup("SEQ"),
    ProjectView("PRV");

    private static final Map<String, GroupTypeEnum> GROUP_TYPE_ENUM_MAP = new HashMap<>();

    static {
        for (GroupTypeEnum groupType : values()) {
            GROUP_TYPE_ENUM_MAP.put(groupType.getAbbrValue(), groupType);
        }
    }

    private final String abbrValue;

    GroupTypeEnum(String abbrValue) {
        this.abbrValue = abbrValue;
    }

    public static GroupTypeEnum resolve(String type) {
        return (type != null ? GROUP_TYPE_ENUM_MAP.get(type) : null);
    }

    public String getAbbrValue() {
        return abbrValue;
    }

    public boolean matches(String type) {
        return (this == resolve(type));
    }

}

