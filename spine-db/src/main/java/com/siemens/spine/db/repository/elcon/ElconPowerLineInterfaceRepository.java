package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPowerLineInterface;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_power_line_interface")
@ApplicationScoped
public class ElconPowerLineInterfaceRepository extends AbstractRestCRUDRepositoryImpl<ElconPowerLineInterface, Long> {

    @Override
    protected boolean isNew(ElconPowerLineInterface entity) {
        return entity.getInterfaceId() == null;
    }

    @Override
    public Class<ElconPowerLineInterface> getDomainType() {
        return ElconPowerLineInterface.class;
    }
} 