package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_fieldbus_subnet")
public class ElconFieldbusSubnet {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "subnet_id", nullable = false)
    private Long subnetId;

    @Size(max = 255)
    @Column(name = "fieldbus_type")
    private String fieldbusType;

    @Size(max = 255)
    @Column(name = "subnet_name")
    private String subnetName;

    @Column(name = "device_id_master")
    private Long deviceIdMaster;

}