package com.siemens.spine.db.utils;

import com.siemens.spine.db.exception.JpaException;
import com.siemens.spine.db.repository.factory.CompositeKeyFactoryRegistry;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Enhanced utility class for handling composite primary keys in REST operations.
 * Supports three JPA composite key approaches:
 * 1. Multiple @Id fields
 * 2. @IdClass annotation with separate ID class
 * 3. @EmbeddedId annotation with embedded ID class
 * <p>
 * All approaches support the format {key1:val1}_{key2:val2}_{key3:val3} for composite keys in URLs.
 * This format provides explicit key-value mapping and supports flexible key ordering.
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
public final class CompositeKeyUtils {

    /**
     * Pattern for composite key format: {key1:val1}_{key2:val2}_{key3:val3}
     * Matches: {customerId:123}_{productId:PROD-001}_{version:1}
     * Enhanced for security with stricter validation and explicit key-value mapping
     */
    public static final Pattern COMPOSITE_KEY_PATTERN = Pattern.compile("\\{([^:}\\s]{1,50}):([^}\\s]{1,100})\\}");
    public static final String COMPOSITE_KEY_SEPARATOR = "_";
    public static final String KEY_WRAPPER_START = "{";
    public static final String KEY_WRAPPER_END = "}";
    public static final String KEY_VALUE_SEPARATOR = ":";

    public static final int MAX_KEY_COMPONENTS = 10;
    public static final int MAX_KEY_NAME_LENGTH = 50;
    public static final int MAX_VALUE_LENGTH = 100;
    public static final int MAX_TOTAL_KEY_LENGTH = 500;

    private static final ConcurrentMap<Class<?>, List<Field>> ID_FIELDS_CACHE = new ConcurrentHashMap<>();
    private static final ConcurrentMap<Class<?>, Boolean> COMPOSITE_KEY_CACHE = new ConcurrentHashMap<>();
    private static final ConcurrentMap<Class<?>, CompositeKeyType> KEY_TYPE_CACHE = new ConcurrentHashMap<>();

    private CompositeKeyUtils() {
    }

    /**
     * Parses a composite key string with explicit key-value mapping into individual components.
     * Enhanced with enterprise security validation and flexible key ordering.
     *
     * @param compositeKeyStr The composite key string (e.g., "{customerId:123}_{productId:PROD-001}")
     * @return Map of key names to values
     * @throws JpaException if parsing fails or security validation fails
     */
    public static Map<String, String> parseCompositeKeyWithMapping(String compositeKeyStr) throws JpaException {
        Objects.requireNonNull(compositeKeyStr, "Composite key string cannot be null");

        validateCompositeKeyInput(compositeKeyStr);

        Map<String, String> keyValueMap = new HashMap<>();
        Matcher matcher = COMPOSITE_KEY_PATTERN.matcher(compositeKeyStr);

        while (matcher.find()) {
            String keyName = matcher.group(1);
            String keyValue = matcher.group(2);

            validateKeyName(keyName);
            validateKeyValue(keyValue);

            if (keyValueMap.containsKey(keyName)) {
                throw new JpaException("Duplicate key name found: " + keyName);
            }

            keyValueMap.put(keyName, keyValue);
        }

        if (keyValueMap.isEmpty()) {
            throw new JpaException("No valid key-value pairs found in composite key: " + compositeKeyStr);
        }

        if (keyValueMap.size() > MAX_KEY_COMPONENTS) {
            throw new JpaException(
                    "Too many key components: " + keyValueMap.size() + ". Maximum allowed: " + MAX_KEY_COMPONENTS);
        }

        log.debug("Parsed composite key '{}' into {} key-value pairs: {}", compositeKeyStr, keyValueMap.size(),
                keyValueMap);
        return keyValueMap;
    }

    /**
     * Legacy method for backward compatibility - parses composite key into ordered list.
     *
     * @param compositeKeyStr The composite key string
     * @return List of key values in the order they appear
     * @throws JpaException if parsing fails
     */
    public static List<String> parseCompositeKey(String compositeKeyStr) throws JpaException {
        Map<String, String> keyValueMap = parseCompositeKeyWithMapping(compositeKeyStr);
        return new ArrayList<>(keyValueMap.values());
    }

    /**
     * Formats key-value pairs into a composite key string with explicit mapping.
     * Supports flexible key ordering.
     *
     * @param keyValueMap Map of key names to values
     * @return Formatted composite key string (e.g., "{customerId:123}_{productId:PROD-001}")
     * @throws JpaException if components are invalid
     */
    public static String formatCompositeKeyWithMapping(Map<String, Object> keyValueMap) throws JpaException {
        if (keyValueMap == null || keyValueMap.isEmpty()) {
            throw new JpaException("Key-value map cannot be null or empty");
        }

        StringBuilder compositeKey = new StringBuilder();
        boolean first = true;

        List<String> keys = keyValueMap.keySet().stream().sorted().toList();
        for (String keyName : keys) {
            Object value = keyValueMap.get(keyName);
            if (value == null) {
                throw new JpaException("Key value for '" + keyName + "' cannot be null");
            }

            if (!first) {
                compositeKey.append(COMPOSITE_KEY_SEPARATOR);
            }
            first = false;

            compositeKey.append(KEY_WRAPPER_START).append(keyName).append(KEY_VALUE_SEPARATOR).append(value)
                    .append(KEY_WRAPPER_END);
        }

        String result = compositeKey.toString();
        log.debug("Formatted {} key-value pairs into composite key: {}", keyValueMap.size(), result);
        return result;
    }

    /**
     * Determines the composite key type for an entity class.
     * Enhanced to support all three JPA composite key approaches.
     * Now uses the Factory pattern for better extensibility.
     *
     * @param entityClass The entity class to analyze
     * @return The composite key type
     */
    public static CompositeKeyType getCompositeKeyType(Class<?> entityClass) {
        if (entityClass == null) {
            return CompositeKeyType.SINGLE_ID;
        }

        return KEY_TYPE_CACHE.computeIfAbsent(entityClass, clazz -> {
            try {
                return CompositeKeyFactoryRegistry.getInstance().getCompositeKeyType(clazz);
            } catch (JpaException e) {
                log.warn("Failed to determine composite key type for entity {}: {}", clazz.getSimpleName(),
                        e.getMessage());
                return CompositeKeyType.SINGLE_ID;
            }
        });
    }

    /**
     * Checks if an entity class has composite keys.
     * Enhanced to support all three JPA composite key approaches.
     * Now uses the Factory pattern for better extensibility.
     *
     * @param entityClass The entity class to check
     * @return true if the entity has composite keys, false otherwise
     */
    public static boolean hasCompositeKey(Class<?> entityClass) {
        if (entityClass == null) {
            return false;
        }

        return COMPOSITE_KEY_CACHE.computeIfAbsent(entityClass, clazz -> {
            boolean isComposite = CompositeKeyFactoryRegistry.getInstance().hasCompositeKey(clazz);
            log.debug("Entity class {} has composite key: {}", clazz.getSimpleName(), isComposite);
            return isComposite;
        });
    }

    /**
     * Extracts all fields that represent ID components from an entity class.
     * Enhanced to support all three JPA composite key approaches.
     * Now uses the Factory pattern for better extensibility.
     *
     * @param entityClass The entity class to analyze
     * @return List of ID fields, sorted by name for consistency
     */
    public static List<Field> getIdFields(Class<?> entityClass) {
        if (entityClass == null) {
            return new ArrayList<>();
        }

        return ID_FIELDS_CACHE.computeIfAbsent(entityClass, clazz -> {
            try {
                List<Field> idFields = CompositeKeyFactoryRegistry.getInstance().extractIdFields(clazz);
                log.debug("Found {} ID fields in entity class {}: {}", idFields.size(), clazz.getSimpleName(),
                        idFields.stream().map(Field::getName).toList());
                return new ArrayList<>(idFields);
            } catch (JpaException e) {
                log.warn("Failed to extract ID fields for entity {}: {}", clazz.getSimpleName(), e.getMessage());
                return new ArrayList<>();
            }
        });
    }

    /**
     * Creates a composite key object by parsing the string with explicit key-value mapping.
     * Enhanced to support all three JPA composite key approaches with flexible key ordering.
     * Now uses the Factory pattern for better extensibility and maintainability.
     *
     * @param compositeKeyStr The composite key string with explicit mapping
     * @param entityClass     The entity class
     * @return A CompositeKeyData containing field names and their converted values
     * @throws JpaException if parsing or conversion fails
     */
    public static CompositeKeyData createCompositeKeyDataWithMapping(String compositeKeyStr, Class<?> entityClass)
            throws JpaException {
        return CompositeKeyFactoryRegistry.getInstance().createKeyData(compositeKeyStr, entityClass);
    }

    /**
     * Legacy method for backward compatibility - creates composite key data from ordered list.
     *
     * @param compositeKeyStr The composite key string
     * @param entityClass     The entity class
     * @return A CompositeKeyData containing field names and their converted values
     * @throws JpaException if parsing or conversion fails
     */
    public static CompositeKeyData createCompositeKeyData(String compositeKeyStr, Class<?> entityClass)
            throws JpaException {
        if (compositeKeyStr.contains(KEY_VALUE_SEPARATOR)) {
            return createCompositeKeyDataWithMapping(compositeKeyStr, entityClass);
        }

        List<String> keyComponents = parseCompositeKey(compositeKeyStr);
        List<Field> idFields = getIdFields(entityClass);

        if (keyComponents.size() != idFields.size()) {
            throw new JpaException(
                    String.format("Composite key '%s' has %d components but entity '%s' requires %d components",
                            compositeKeyStr, keyComponents.size(), entityClass.getSimpleName(), idFields.size()));
        }

        CompositeKeyData keyData = new CompositeKeyData();
        keyData.setKeyType(getCompositeKeyType(entityClass));

        for (int i = 0; i < idFields.size(); i++) {
            Field field = idFields.get(i);
            String componentValue = keyComponents.get(i);

            try {
                Object convertedValue = convertToFieldType(componentValue, field.getType());
                keyData.addComponent(field.getName(), field.getType(), convertedValue);
            } catch (Exception e) {
                throw new JpaException(
                        String.format("Failed to convert key component '%s' to type %s for field '%s'", componentValue,
                                field.getType().getSimpleName(), field.getName()), e);
            }
        }

        return keyData;
    }

    public static Object convertValue(String value, Class<?> targetType) {
        try {
            if (targetType == String.class) {
                return value;
            } else if (targetType == Long.class || targetType == long.class) {
                return Long.valueOf(value);
            } else if (targetType == Integer.class || targetType == int.class) {
                return Integer.valueOf(value);
            } else if (targetType == Boolean.class || targetType == boolean.class) {
                return Boolean.valueOf(value);
            } else if (targetType == Double.class || targetType == double.class) {
                return Double.valueOf(value);
            } else if (targetType == Float.class || targetType == float.class) {
                return Float.valueOf(value);
            } else if (targetType == Short.class || targetType == short.class) {
                return Short.valueOf(value);
            } else if (targetType == Byte.class || targetType == byte.class) {
                return Byte.valueOf(value);
            } else {
                throw new JpaException("Unsupported field type for key conversion: " + targetType.getSimpleName());
            }
        } catch (NumberFormatException e) {
            throw new JpaException("Failed to convert '" + value + "' to " + targetType.getSimpleName(), e);
        }
    }

    /**
     * Validates that a composite key string matches the expected format.
     *
     * @param compositeKeyStr The composite key string to validate
     * @throws JpaException if validation fails
     */
    public static void validateCompositeKeyFormat(String compositeKeyStr) throws JpaException {
        if (compositeKeyStr == null || compositeKeyStr.trim().isEmpty()) {
            throw new JpaException("Composite key cannot be null or empty");
        }

        if (!compositeKeyStr.contains(KEY_WRAPPER_START) || !compositeKeyStr.contains(KEY_WRAPPER_END)) {
            throw new JpaException(
                    "Composite key must use format {key:value}_{key:value}. Invalid: " + compositeKeyStr);
        }

        if (compositeKeyStr.contains(KEY_VALUE_SEPARATOR)) {
            parseCompositeKeyWithMapping(compositeKeyStr);
        } else {
            parseCompositeKey(compositeKeyStr);
        }
    }

    /**
     * Validates the overall composite key input for security and format compliance.
     * Enterprise security measure.
     *
     * @param compositeKeyStr The composite key string to validate
     * @throws JpaException if validation fails
     */
    private static void validateCompositeKeyInput(String compositeKeyStr) throws JpaException {
        validateValue(compositeKeyStr, MAX_TOTAL_KEY_LENGTH);
    }

    /**
     * Validates a key name for security and format compliance.
     * Enterprise security measure.
     *
     * @param keyName The key name to validate
     * @throws JpaException if validation fails
     */
    private static void validateKeyName(String keyName) throws JpaException {
        validateValue(keyName, MAX_KEY_NAME_LENGTH);
    }

    /**
     * Validates a key value for security and format compliance.
     * Enterprise security measure.
     *
     * @param keyValue The key value to validate
     * @throws JpaException if validation fails
     */
    private static void validateKeyValue(String keyValue) throws JpaException {
        validateValue(keyValue, MAX_VALUE_LENGTH);
    }

    /**
     * Validates a key value for security and format compliance.
     * Enterprise security measure.
     *
     * @param value The key value to validate
     * @throws JpaException if validation fails
     */
    private static void validateValue(String value, Integer threshold) throws JpaException {
        if (value.length() > threshold) {
            throw new JpaException(
                    "Text too long: " + value.length() + " characters. Maximum allowed: " + threshold + " characters.");
        }
    }

    /**
     * Converts a string value to the target field type.
     *
     * @param value      The string value to convert
     * @param targetType The target type
     * @return The converted value
     * @throws JpaException if conversion fails
     */
    private static Object convertToFieldType(String value, Class<?> targetType) throws JpaException {
        return convertValue(value, targetType);
    }

    /**
     * Enum to represent different types of composite key approaches.
     */
    public enum CompositeKeyType {
        SINGLE_ID,          // Single @Id field
        ID_CLASS,          // @IdClass annotation
        EMBEDDED_ID        // @EmbeddedId annotation
    }

    /**
     * Enhanced data class to hold composite key information for all JPA approaches.
     */

    public static class CompositeKeyData {

        private final List<KeyComponent> components = new ArrayList<>();
        @Setter
        @Getter
        private CompositeKeyType keyType = CompositeKeyType.EMBEDDED_ID;

        public void addComponent(String fieldName, Class<?> fieldType, Object value) {
            components.add(new KeyComponent(fieldName, fieldType, value));
        }

        public List<KeyComponent> getComponents() {
            return new ArrayList<>(components);
        }

        public int size() {
            return components.size();
        }

        public record KeyComponent(String fieldName, Class<?> fieldType, Object value) {

        }

    }

}