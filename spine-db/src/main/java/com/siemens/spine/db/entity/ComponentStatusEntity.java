package com.siemens.spine.db.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MapsId;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "componentstatus")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@NamedEntityGraph(name = "ComponentStatusEntity.withComponent", attributeNodes = @NamedAttributeNode("component"))
@Audited
public class ComponentStatusEntity implements Serializable {

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "uniqueId")
    @MapsId
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    protected ComponentEntity component;
    protected Integer emulationState;
    protected Date emulationStateDate;
    protected Integer itState;
    protected Date itStateDate;
    protected Integer simulationState;
    protected Date simulationStateDate;
    protected Integer calculationState;
    protected Date calculationStateDate;
    protected Integer bomState;
    protected Date bomStateDate;
    protected Integer rowNum;
    protected String sapID;
    protected Date simulationComponentLastModDate;
    protected Integer objectXmlExportState;
    protected Date objectXmlExportStateDate;
    protected Integer electricSynchronizationState;
    protected Date electricSynchronizationDate;
    @Id
    private Long id;

}

