package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.DecompositionAttributesEntity;
import com.siemens.spine.db.repository.DecompositionAttributesRepository;

import javax.enterprise.context.ApplicationScoped;

@ApplicationScoped
public class DecompositionAttributesRepositoryImpl
        extends GenericJpaRepositoryImpl<DecompositionAttributesEntity, String>
        implements DecompositionAttributesRepository {

    @Override
    public Class<DecompositionAttributesEntity> getDomainType() {
        return DecompositionAttributesEntity.class;
    }

}

