package com.siemens.spine.db.repository.metadata;

import com.siemens.spine.db.utils.CompositeKeyUtils;

import java.util.List;
import java.util.Optional;

/**
 * Lightweight metadata interface for JPA entities.
 * Provides cached information about entity structure, particularly focused on
 * ID metadata for both simple and composite keys.
 * <p>
 * This interface serves as a high-performance alternative to Spring Data JPA's
 * MetaModel by pre-computing and caching all metadata at application startup.
 *
 * @param <T> The entity type
 * <AUTHOR> <PERSON>am
 * @since 1.0
 */
public interface EntityMetadata<T> {

    /**
     * Gets the entity class type.
     *
     * @return The entity class
     */
    Class<T> getEntityType();

    /**
     * Gets the table name for this entity.
     *
     * @return The database table name
     */
    String getTableName();

    /**
     * Gets comprehensive ID metadata for this entity.
     * Supports all composite key types: single @Id, multiple @Id, @IdClass, @EmbeddedId.
     *
     * @return ID metadata containing field information and JPA paths
     */
    IdMetadata getIdMetadata();

    /**
     * Gets all attribute metadata for this entity (both ID and non-ID fields).
     *
     * @return List of all attribute metadata
     */
    List<AttributeMetadata> getAllAttributes();

    /**
     * Gets attribute metadata by field name.
     * Supports both camelCase and snake_case field names.
     *
     * @param attributeName The field name
     * @return Attribute metadata if found
     */
    Optional<AttributeMetadata> getAttribute(String attributeName);

    /**
     * Checks if this entity has composite keys.
     *
     * @return true if entity has composite keys (multiple @Id, @IdClass, or @EmbeddedId)
     */
    boolean hasCompositeKey();

    /**
     * Gets the composite key type for this entity.
     *
     * @return The composite key type
     */
    CompositeKeyUtils.CompositeKeyType getKeyType();

    /**
     * Gets the primary key field name.
     * For composite keys, returns the embedded ID field name or first ID field.
     *
     * @return Primary key field name
     */
    String getPrimaryKeyFieldName();

}