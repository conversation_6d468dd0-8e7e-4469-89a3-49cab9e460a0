package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconSimuDivertTuRoutingProperty;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_simu_divert_tu_routing_property")
@ApplicationScoped
public class ElconSimuDivertTuRoutingPropertyRepository extends AbstractRestCRUDRepositoryImpl<ElconSimuDivertTuRoutingProperty, Long> {

    @Override
    protected boolean isNew(ElconSimuDivertTuRoutingProperty entity) {
        return entity.getPropertyId() == null;
    }

    @Override
    public Class<ElconSimuDivertTuRoutingProperty> getDomainType() {
        return ElconSimuDivertTuRoutingProperty.class;
    }
} 