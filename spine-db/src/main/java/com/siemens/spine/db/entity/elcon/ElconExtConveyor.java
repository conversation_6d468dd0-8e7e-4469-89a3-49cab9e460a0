package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_ext_conveyor")
public class ElconExtConveyor {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "strand_id", nullable = false)
    private Long strandId;

    @Size(max = 255)
    @NotNull
    @Column(name = "unique_id", nullable = false)
    private String uniqueId;

    @Size(max = 255)
    @Column(name = "item_name")
    private String itemName;

    @Size(max = 255)
    @Column(name = "sys_type")
    private String sysType;

    @Size(max = 255)
    @Column(name = "conv_type")
    private String convType;

    @Size(max = 255)
    @Column(name = "plc_area_name")
    private String plcAreaName;

    @Column(name = "plc_area_nr")
    private Integer plcAreaNr;

    @Size(max = 255)
    @Column(name = "hw_outfit_name")
    private String hwOutfitName;

    @NotNull
    @Column(name = "project_version_id", nullable = false)
    private Long projectVersionId;

    @Size(max = 255)
    @Column(name = "xml_object_id")
    private String xmlObjectId;

}