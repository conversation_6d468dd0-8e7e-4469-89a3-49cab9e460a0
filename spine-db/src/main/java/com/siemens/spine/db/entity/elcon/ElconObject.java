package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_object")
public class ElconObject {

    @Id
    @Column(name = "object_id", nullable = false)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "elcon_object_id_seq")
    @SequenceGenerator(name = "elcon_object_id_seq", sequenceName = "elcon_object_id_seq", allocationSize = 25)
    private Long objectId;

    @Size(max = 255)
    @NotNull
    @Column(name = "unique_id", nullable = false)
    private String uniqueId;

    @Size(max = 255)
    @Column(name = "object_kind")
    private String objectKind;

    @Size(max = 255)
    @Column(name = "hw_outfit_name")
    private String hwOutfitName;

    @Size(max = 255)
    @Column(name = "sw_set_key")
    private String swSetKey;

    @Size(max = 255)
    @Column(name = "item_name")
    private String itemName;

    @Column(name = "sw_relevant")
    @Type(type = "org.hibernate.type.NumericBooleanType")
    private Boolean swRelevant;

    @Column(name = "plc_sw_unit_id")
    private Long plcSwUnitId;

    @Column(name = "plc_sw_object_nr")
    private Long plcSwObjectNr;

    @Size(max = 255)
    @Column(name = "object_origin")
    private String objectOrigin;

    @Size(max = 255)
    @Column(name = "external_reference")
    private String externalReference;

    @Size(max = 255)
    @Column(name = "io_base_address")
    private String ioBaseAddress;

    @Size(max = 255)
    @Column(name = "logistics_property")
    private String logisticsProperty;

    @Size(max = 255)
    @Column(name = "object_sub_kind")
    private String objectSubKind;

    @Size(max = 255)
    @Column(name = "hwc_functions")
    private String hwcFunctions;

}