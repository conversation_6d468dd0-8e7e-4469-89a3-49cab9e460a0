package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCoDrive;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_co_drive")
@ApplicationScoped
public class ElconCoDriveRepository extends AbstractRestCRUDRepositoryImpl<ElconCoDrive, Long> {

    @Override
    protected boolean isNew(ElconCoDrive entity) {
        return entity.getDriveId() == null;
    }

    @Override
    public Class<ElconCoDrive> getDomainType() {
        return ElconCoDrive.class;
    }
} 