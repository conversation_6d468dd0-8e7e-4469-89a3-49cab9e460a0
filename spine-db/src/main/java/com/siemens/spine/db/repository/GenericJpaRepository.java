package com.siemens.spine.db.repository;

import java.util.List;
import java.util.Optional;

/**
 * @param <T>  Entity
 * @param <ID> Data type of the entity ID
 */
public interface GenericJpaRepository<T, ID> extends CrudRepository<T, ID> {

    <S extends T> List<S> saveAll(List<S> entities);

    <S extends T> List<S> updateAll(List<S> entities);

    <S extends T> S create(S entity);

    <S extends T> S update(S entity);

    List<T> findAll();

    Optional<T> findById(ID id);

    List<T> findAllByIds(List<ID> ids);

    void delete(T entity);

    int deleteAll(List<ID> ids);

}

