package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_area")
public class ElconArea {

    @Id
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @Size(max = 255)
    @Column(name = "area_kind")
    private String areaKind;

    @Size(max = 255)
    @Column(name = "area_name")
    private String areaName;

    @Size(max = 255)
    @Column(name = "function_type")
    private String functionType;

    @Size(max = 255)
    @Column(name = "description")
    private String description;

    @Column(name = "plc_sw_id")
    private Long plcSwId;

    @Column(name = "global_area_id")
    private Long globalAreaId;

}