package com.siemens.spine.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.RevisionEntity;

import javax.persistence.AttributeOverride;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "revinfo")
@RevisionEntity()
@Getter
@Setter
@AttributeOverride(name = "id", column = @Column(name = "REV"))
@AttributeOverride(name = "timestamp", column = @Column(name = "REVTSTMP"))
public class RevisionInfoEntity extends DefaultRevisionEntity {

    @Column(name = "principal")
    private String principal;

}

