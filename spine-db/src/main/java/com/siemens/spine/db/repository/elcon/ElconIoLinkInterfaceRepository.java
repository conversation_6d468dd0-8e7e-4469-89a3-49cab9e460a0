package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconIoLinkInterface;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_io_link_interface")
@ApplicationScoped
public class ElconIoLinkInterfaceRepository extends AbstractRestCRUDRepositoryImpl<ElconIoLinkInterface, Long> {

    @Override
    protected boolean isNew(ElconIoLinkInterface entity) {
        return entity.getInterfaceId() == null;
    }

    @Override
    public Class<ElconIoLinkInterface> getDomainType() {
        return ElconIoLinkInterface.class;
    }
} 