package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconLinkSafetyZoneSafetyLine;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_link_safety_zone_safety_line")
@ApplicationScoped
public class ElconLinkSafetyZoneSafetyLineRepository extends AbstractRestCRUDRepositoryImpl<ElconLinkSafetyZoneSafetyLine, Long> {

    @Override
    protected boolean isNew(ElconLinkSafetyZoneSafetyLine entity) {
        return entity.getLinkId() == null;
    }

    @Override
    public Class<ElconLinkSafetyZoneSafetyLine> getDomainType() {
        return ElconLinkSafetyZoneSafetyLine.class;
    }
} 