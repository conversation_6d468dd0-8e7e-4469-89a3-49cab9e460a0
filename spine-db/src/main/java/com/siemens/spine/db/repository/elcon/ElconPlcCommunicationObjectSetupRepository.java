package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPlcCommunicationObjectSetup;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_plc_communication_object_setup")
@ApplicationScoped
public class ElconPlcCommunicationObjectSetupRepository extends AbstractRestCRUDRepositoryImpl<ElconPlcCommunicationObjectSetup, Long> {

    @Override
    protected boolean isNew(ElconPlcCommunicationObjectSetup entity) {
        return entity.getLineId() == null;
    }

    @Override
    public Class<ElconPlcCommunicationObjectSetup> getDomainType() {
        return ElconPlcCommunicationObjectSetup.class;
    }
} 