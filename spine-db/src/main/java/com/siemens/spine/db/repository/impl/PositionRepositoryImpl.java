package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.PositionEntity;
import com.siemens.spine.db.repository.PositionRepository;

import javax.enterprise.context.ApplicationScoped;

@ApplicationScoped
public class PositionRepositoryImpl extends GenericJpaRepositoryImpl<PositionEntity, Long>
        implements PositionRepository {

    @Override
    public Class<PositionEntity> getDomainType() {
        return PositionEntity.class;
    }

}

