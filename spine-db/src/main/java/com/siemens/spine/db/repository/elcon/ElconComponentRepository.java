// WARNING: Entity ElconComponent does not have @Id or @EmbeddedId. Please check entity definition!
package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconComponent;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_component")
@ApplicationScoped
public class ElconComponentRepository extends AbstractRestCRUDRepositoryImpl<ElconComponent, Long> {

    // WARNING: No @Id or @EmbeddedId found. isNew() always returns false.
    @Override
    protected boolean isNew(ElconComponent entity) {
        return entity.getComponentId() == null;
    }

    @Override
    public Class<ElconComponent> getDomainType() {
        return ElconComponent.class;
    }
} 