package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPowerLinePort;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_power_line_port")
@ApplicationScoped
public class ElconPowerLinePortRepository extends AbstractRestCRUDRepositoryImpl<ElconPowerLinePort, Long> {

    @Override
    protected boolean isNew(ElconPowerLinePort entity) {
        return entity.getPortId() == null;
    }

    @Override
    public Class<ElconPowerLinePort> getDomainType() {
        return ElconPowerLinePort.class;
    }
} 