package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.elcon.QueryTemplateEntity;
import com.siemens.spine.db.repository.QueryTemplateRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.util.PSQLException;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.persistence.NoResultException;
import javax.sql.DataSource;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * Repository implementation provides safe query execution capabilities with parameter
 * substitution, pagination support, and comprehensive error handling.
 *
 * <AUTHOR>
 * @version 2.0
 * @since 1.0
 */
@ApplicationScoped
@Transactional(TxType.REQUIRED)
@Slf4j
public class QueryTemplateRepositoryImpl extends
        GenericJpaAuditRepositoryImpl<QueryTemplateEntity, Long> implements QueryTemplateRepository {

    private static final String PARAMETER_PATTERN = "\\[:(\\w+)\\]";
    private static final String PLACEHOLDER_PATTERN = "\\[:\\w+\\]";
    private static final String COUNT_QUERY_PREFIX = "SELECT COUNT(*) FROM (";
    private static final String COUNT_QUERY_SUFFIX = ") AS count_subquery";
    private static final String PAGINATION_CLAUSE = " LIMIT ? OFFSET ?";
    private static final int ERROR_CONTEXT_RADIUS = 25;

    private final DataSource dataSource;
    private final Pattern parameterPattern;
    private final Pattern placeholderPattern;

    @Inject
    public QueryTemplateRepositoryImpl(DataSource dataSource) {
        this.dataSource = Objects.requireNonNull(dataSource, "DataSource cannot be null");
        this.parameterPattern = Pattern.compile(PARAMETER_PATTERN);
        this.placeholderPattern = Pattern.compile(PLACEHOLDER_PATTERN);
    }

    @Override
    public Class<QueryTemplateEntity> getDomainType() {
        return QueryTemplateEntity.class;
    }

    /**
     * Finds a query template by its name.
     *
     * @param templateName the template name to search for
     * @return an Optional containing the found template, or empty if not found
     * @throws IllegalArgumentException if templateName is null or blank
     */
    @Override
    public Optional<QueryTemplateEntity> findByTemplateName(String templateName) {
        if (StringUtils.isBlank(templateName)) {
            throw new IllegalArgumentException("Template name cannot be null or empty");
        }

        try {
            var template = entityManager.createQuery(
                            """
                                    SELECT q FROM QueryTemplateEntity q
                                    WHERE q.templateName = :name
                                    """, QueryTemplateEntity.class)
                    .setParameter("name", templateName)
                    .getSingleResult();

            log.debug("Found template: {}", templateName);
            return Optional.of(template);
        } catch (NoResultException ex) {
            log.debug("Template not found: {}", templateName);
            return Optional.empty();
        }
    }

    /**
     * Executes a query template safely with parameter substitution and optional pagination.
     *
     * @param template   the query template to execute
     * @param parameters the parameters for the query
     * @param page       the page number (1-based) for pagination
     * @param size       the page size for pagination
     * @return a map containing the execution results with pagination metadata
     * @throws SQLException             if a database error occurs
     * @throws IllegalArgumentException if required parameters are invalid
     */
    @Override
    public Map<String, Object> executeQuerySafe(final QueryTemplateEntity template,
                                                Map<String, Object> parameters,
                                                Integer page,
                                                Integer size) throws SQLException {

        validateExecutionParameters(template, parameters, page, size);

        var queryExecution = buildQueryExecution(template, parameters, page, size);

        log.info("Executing query template: {} with {} parameters",
                template.getTemplateName(), parameters.size());

        return executeQuery(queryExecution);
    }

    /**
     * Gets the total count for a query template execution.
     *
     * @param template   the query template
     * @param parameters the query parameters
     * @return the total count of results
     * @throws SQLException if a database error occurs
     */
    @Override
    public int getTotalCount(QueryTemplateEntity template, Map<String, Object> parameters) throws SQLException {
        var countQuery = buildCountQuery(template.getSqlTemplate());
        var queryParams = extractParameterValues(template.getSqlTemplate(), parameters);

        try (var conn = dataSource.getConnection();
                var stmt = conn.prepareStatement(countQuery)) {

            setQueryParameters(stmt, queryParams, false, 0, 0);

            try (var rs = stmt.executeQuery()) {
                return rs.next() ? rs.getInt(1) : 0;
            }
        } catch (PSQLException ex) {
            handleDatabaseError(ex, countQuery);
            throw ex; // Never reached, but required for compilation
        } catch (SQLException ex) {
            log.error("Error executing count query for template: {}", template.getTemplateName(), ex);
            throw ex;
        }
    }

    // Private helper methods following data-oriented programming principles

    /**
     * Validates execution parameters for business rule compliance.
     */
    private void validateExecutionParameters(QueryTemplateEntity template,
                                             Map<String, Object> parameters,
                                             Integer page, Integer size) {
        Objects.requireNonNull(template, "Template cannot be null");
        Objects.requireNonNull(parameters, "Parameters cannot be null");

        if (template.isRequiresPagination()) {
            if (page == null || page < 1) {
                throw new IllegalArgumentException("Page must be greater than 0 when pagination is required");
            }
            if (size == null || size < 1) {
                throw new IllegalArgumentException("Size must be greater than 0 when pagination is required");
            }
        }
    }

    /**
     * Builds a query execution context containing all necessary information.
     */
    private QueryExecution buildQueryExecution(QueryTemplateEntity template,
                                               Map<String, Object> parameters,
                                               Integer page, Integer size) throws SQLException {

        var finalQuery = buildFinalQuery(template);
        var queryParams = extractParameterValues(template.getSqlTemplate(), parameters);

        Integer totalCount = null;
        if (template.isRequiresPagination()) {
            totalCount = getTotalCount(template, parameters);
        }

        return new QueryExecution(
                finalQuery,
                queryParams,
                template.isRequiresPagination(),
                page,
                size,
                totalCount
        );
    }

    /**
     * Executes the query and returns formatted results.
     */
    private Map<String, Object> executeQuery(QueryExecution execution) throws SQLException {
        try (var conn = dataSource.getConnection()) {
            conn.setAutoCommit(false);

            try (var stmt = conn.prepareStatement(execution.query())) {
                var offset = execution.requiresPagination() ?
                        (execution.page() - 1) * execution.size() : 0;

                setQueryParameters(stmt, execution.parameters(),
                        execution.requiresPagination(), execution.size(), offset);

                try (var rs = stmt.executeQuery()) {
                    var results = processResultSet(rs);
                    conn.commit();

                    return buildResponse(execution, results);
                }
            } catch (Exception ex) {
                conn.rollback();
                throw ex;
            }
        } catch (PSQLException ex) {
            handleDatabaseError(ex, execution.query());
            throw ex; // Never reached
        }
    }

    /**
     * Processes the ResultSet into a list of maps.
     */
    private List<Map<String, Object>> processResultSet(ResultSet rs) throws SQLException {
        var results = new ArrayList<Map<String, Object>>();
        var metaData = rs.getMetaData();
        var columnCount = metaData.getColumnCount();

        while (rs.next()) {
            var row = new LinkedHashMap<String, Object>();
            for (int i = 1; i <= columnCount; i++) {
                var columnName = metaData.getColumnName(i);
                var value = rs.getObject(i);
                row.put(columnName, value);
            }
            results.add(row);
        }

        return results;
    }

    /**
     * Builds the final response map with pagination metadata.
     */
    private Map<String, Object> buildResponse(QueryExecution execution,
                                              List<Map<String, Object>> results) {
        var response = new LinkedHashMap<String, Object>();

        if (execution.requiresPagination()) {
            response.put("page", execution.page());
            response.put("size", execution.size());
            response.put("totalCount", execution.totalCount());
        } else {
            response.put("page", null);
            response.put("size", null);
            response.put("totalCount", results.size());
        }

        response.put("data", results);
        return response;
    }

    /**
     * Extracts parameter values from the query in order of appearance.
     */
    private List<Object> extractParameterValues(String query, Map<String, Object> parameters) {
        var values = new ArrayList<>();
        var matcher = parameterPattern.matcher(query);

        while (matcher.find()) {
            var paramName = matcher.group(1);
            if (parameters.containsKey(paramName)) {
                values.add(parameters.get(paramName));
            } else {
                log.warn("Parameter {} not found in provided parameters", paramName);
            }
        }

        return values;
    }

    /**
     * Builds the final query with pagination if required.
     */
    private String buildFinalQuery(QueryTemplateEntity template) {
        var query = replaceParameterPlaceholders(template.getSqlTemplate());

        if (template.isRequiresPagination()) {
            query += PAGINATION_CLAUSE;
        }

        return query;
    }

    /**
     * Builds a count query from the original query.
     */
    private String buildCountQuery(String originalQuery) {
        var query = replaceParameterPlaceholders(originalQuery);

        if (query.toLowerCase().contains("select count(")) {
            return query; // Already a count query
        }

        return COUNT_QUERY_PREFIX + query + COUNT_QUERY_SUFFIX;
    }

    /**
     * Replaces all parameter placeholders with ? for PreparedStatement.
     */
    private String replaceParameterPlaceholders(String query) {
        return placeholderPattern.matcher(query).replaceAll("?");
    }

    /**
     * Sets parameters on the PreparedStatement with type-safe handling.
     */
    private void setQueryParameters(PreparedStatement stmt, List<Object> params,
                                    boolean addPagination, int size, int offset) throws SQLException {
        int index = 1;

        // Set dynamic parameters with type safety
        for (var param : params) {
            setParameterValue(stmt, index++, param);
        }

        // Set pagination parameters
        if (addPagination) {
            stmt.setInt(index++, size);   // LIMIT
            stmt.setInt(index, offset);   // OFFSET
        }
    }

    /**
     * Sets a single parameter value with proper type handling.
     */
    private void setParameterValue(PreparedStatement stmt, int index, Object param) throws SQLException {
        if (param == null) {
            stmt.setNull(index, java.sql.Types.NULL);
        } else if (param instanceof String s) {
            stmt.setString(index, s);
        } else if (param instanceof Integer i) {
            stmt.setInt(index, i);
        } else if (param instanceof Long l) {
            stmt.setLong(index, l);
        } else if (param instanceof Date d) {
            stmt.setDate(index, d);
        } else {
            stmt.setObject(index, param);
        }
    }

    /**
     * Handles database errors with enhanced context information.
     */
    private void handleDatabaseError(PSQLException ex, String query) {
        var serverError = ex.getServerErrorMessage();

        if (serverError != null) {
            var errorPosition = serverError.getPosition();
            var errorContext = extractErrorContext(query, errorPosition);
            var enhancedMessage = String.format(
                    "SQL Error at position %d: %s. Context: %s",
                    errorPosition, ex.getMessage(), errorContext
            );

            log.error(enhancedMessage);
            throw new RuntimeException("Database query execution failed: " + enhancedMessage, ex);
        } else {
            log.error("SQL Error: {}", ex.getMessage());
            throw new RuntimeException("Database error occurred while executing query", ex);
        }
    }

    /**
     * Extracts error context around the error position for debugging.
     */
    private static String extractErrorContext(String query, int errorPosition) {
        if (query == null || errorPosition < 0 || errorPosition >= query.length()) {
            return "N/A";
        }

        var start = Math.max(0, errorPosition - ERROR_CONTEXT_RADIUS);
        var end = Math.min(query.length(), errorPosition + ERROR_CONTEXT_RADIUS);

        return "'" + query.substring(start, end) + "'";
    }

    /**
     * Immutable data structure representing a query execution context.
     * This follows data-oriented programming principles by separating data from behavior.
     */
    private record QueryExecution(
            String query,
            List<Object> parameters,
            boolean requiresPagination,
            Integer page,
            Integer size,
            Integer totalCount
    ) {

    }

}
