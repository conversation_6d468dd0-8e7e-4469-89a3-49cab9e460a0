package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.constant.OperationEnum;
import com.siemens.spine.db.constant.RoleEnum;
import com.siemens.spine.db.entity.SpineUserEntity;
import com.siemens.spine.db.repository.SpineUserRepository;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.Query;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 26/12/2022
 */
@ApplicationScoped
@Transactional(TxType.REQUIRED)
public class SpineUserRepositoryImpl extends
        GenericJpaRepositoryImpl<SpineUserEntity, Long> implements
        SpineUserRepository {

    @Override
    public Class<SpineUserEntity> getDomainType() {
        return SpineUserEntity.class;
    }

    @Override
    public boolean isOperationPermittedForSpecificProject(Long solutionId, String userName,
                                                          Long projectId, OperationEnum operation, String userType) {
        Query query = entityManager.createQuery(
                "select count(gr) from GroupToProjectAndRoleEntity gr "
                        + "                   join UserToGroupEntity us on us.groupName = gr.groupName,"
                        + " OperationToRoleEntity op "
                        + " where us.user.type = :userType "
                        + "   and us.user.userName = :userName "
                        + "   and (gr.roleName = :admin OR "
                        + "             (gr.roleName = op.roleName and op.operation = :operation "
                        + "   and (gr.project.projectID = :projectId OR gr.project.projectID = :zero)))");

        query.setParameter("userType", userType);
        query.setParameter("userName", userName);
        query.setParameter("admin", RoleEnum.ADMIN);
        query.setParameter("projectId", projectId);
        query.setParameter("operation", operation);
        query.setParameter("zero", 0L);
        boolean isEmpty = query.getResultList().isEmpty();
        return !isEmpty;
    }

    @Override
    public boolean isOperationPermittedForAnyProject(Long solutionId, String userName,
                                                     OperationEnum operation, String userType) {
        Query query = entityManager.createQuery(
                "select count(gr) from GroupToProjectAndRoleEntity gr "
                        + "join UserToGroupEntity us on us.groupName = gr.groupName, OperationToRoleEntity op "
                        + " where us.user.userName = :userName "
                        + "     and (gr.roleName = :admin OR "
                        + "          (gr.roleName = op.roleName and op.operation = :operation and gr.project.projectID is not null))");

        query.setParameter("userName", userName);
        query.setParameter("admin", RoleEnum.ADMIN);
        query.setParameter("operation", operation);

        boolean isEmpty = query.getResultList().isEmpty();
        return !isEmpty;
    }

}

