package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconScanner;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_scanner")
@ApplicationScoped
public class ElconScannerRepository extends AbstractRestCRUDRepositoryImpl<ElconScanner, Long> {

    @Override
    protected boolean isNew(ElconScanner entity) {
        return entity.getId() == null;
    }

    @Override
    public Class<ElconScanner> getDomainType() {
        return ElconScanner.class;
    }
} 