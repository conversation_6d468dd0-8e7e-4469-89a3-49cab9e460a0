package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconSimuTuDeleteProperty;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_simu_tu_delete_property")
@ApplicationScoped
public class ElconSimuTuDeletePropertyRepository extends AbstractRestCRUDRepositoryImpl<ElconSimuTuDeleteProperty, Long> {

    @Override
    protected boolean isNew(ElconSimuTuDeleteProperty entity) {
        return entity.getPropertyId() == null;
    }

    @Override
    public Class<ElconSimuTuDeleteProperty> getDomainType() {
        return ElconSimuTuDeleteProperty.class;
    }
} 