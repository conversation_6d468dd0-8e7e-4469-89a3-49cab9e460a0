package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.exception.JpaException;
import com.siemens.spine.db.repository.GenericJpaRepository;
import com.siemens.spine.db.repository.elcon.AbstractRestCRUDRepositoryImpl;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.proxy.HibernateProxy;
import org.hibernate.proxy.LazyInitializer;

import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Transactional(TxType.REQUIRED)
@Slf4j
@SuppressWarnings("all")
public abstract class GenericJpaRepositoryImpl<T, ID> extends AbstractRestCRUDRepositoryImpl<T, ID>
        implements GenericJpaRepository<T, ID> {

    protected GenericJpaRepositoryImpl() {
    }

    @Override
    protected boolean isNew(T entity) {
        try {
            return getId(entity) == null;
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new JpaException("Failed to recognize primary key", e);
        }
    }

    @Override
    public <S extends T> List<S> saveAll(List<S> entities) {
        List<S> ret = new ArrayList<>();
        for (S s : entities) {
            S saved = save(s);
            ret.add(saved);
        }

        return ret;
    }

    @Override
    public <S extends T> List<S> updateAll(List<S> entities) {
        List<S> ret = new ArrayList<>();
        for (S s : entities) {
            S saved = update(s);
            ret.add(saved);
        }

        return ret;
    }

    @Override
    public <S extends T> S create(S entity) {
        entityManager.persist(entity);
        entityManager.flush();
        return entity;
    }

    @Override
    public <S extends T> S update(S entity) {
        var updated = entityManager.merge(entity);
        entityManager.flush();
        return updated;
    }

    @Override
    public List<T> findAll() {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<T> cq = cb.createQuery(getDomainType());
        Root<T> rootEntry = cq.from(getDomainType());
        CriteriaQuery<T> all = cq.select(rootEntry);
        TypedQuery<T> allQuery = entityManager.createQuery(all);

        return allQuery.getResultList();
    }

    @Override
    public List<T> findAllByIds(List<ID> ids) {
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyList();
        }

        List<T> results = new ArrayList<>();

        for (ID id : ids) {
            findById(id).ifPresent(results::add);
        }
        return results;
    }

    @Override
    public void delete(T entity) {
        if (entity == null) {
            throw new JpaException("Entity must not be null");
        }

        entityManager.remove(entity);
        entityManager.flush();
    }

    @Override
    public int deleteAll(List<ID> ids) {
        List<T> allAvailablesEntities = findAll();
        for (T element : allAvailablesEntities) {
            delete(element);
        }
        entityManager.flush();
        return allAvailablesEntities.size();
    }

    private <S extends T> ID getId(S entity) throws NoSuchFieldException, IllegalAccessException {
        Class entityClass;
        ID id;
        if (entity instanceof HibernateProxy) {
            LazyInitializer lazyInitializer =
                    ((HibernateProxy) entity).getHibernateLazyInitializer();
            id = (ID) lazyInitializer.getIdentifier();
        } else {
            entityClass = entity.getClass();
            Field primaryKeyField = entityClass.getDeclaredField(getIdMetadata().getSingleIdAttribute().getName());
            primaryKeyField.setAccessible(true);
            id = (ID) primaryKeyField.get(entity);
        }
        return id;
    }

}

