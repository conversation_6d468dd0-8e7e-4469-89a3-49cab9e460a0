package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconSimuCounterLockProperty;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_simu_counter_lock_property")
@ApplicationScoped
public class ElconSimuCounterLockPropertyRepository extends AbstractRestCRUDRepositoryImpl<ElconSimuCounterLockProperty, Long> {

    @Override
    protected boolean isNew(ElconSimuCounterLockProperty entity) {
        return entity.getPropertyId() == null;
    }

    @Override
    public Class<ElconSimuCounterLockProperty> getDomainType() {
        return ElconSimuCounterLockProperty.class;
    }
} 