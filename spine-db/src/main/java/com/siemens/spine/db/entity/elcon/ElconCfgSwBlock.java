package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_cfg_sw_block")
public class ElconCfgSwBlock {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "sw_block_id", nullable = false)
    private Long swBlockId;

    @Size(max = 255)
    @NotNull
    @Column(name = "sw_block", nullable = false)
    private String swBlock;

    @Size(max = 255)
    @NotNull
    @Column(name = "sw_class", nullable = false)
    private String swClass;

    @Size(max = 255)
    @NotNull
    @Column(name = "sw_subclass", nullable = false)
    private String swSubclass;

    @Column(name = "valid")
    private Short valid;

    @Column(name = "sw_library_id")
    private Long swLibraryId;

    @Size(max = 255)
    @Column(name = "description")
    private String description;

    @Size(max = 30)
    @Column(name = "naming_convention", length = 30)
    private String namingConvention;

    @Size(max = 30)
    @Column(name = "function_type", length = 30)
    private String functionType;

}