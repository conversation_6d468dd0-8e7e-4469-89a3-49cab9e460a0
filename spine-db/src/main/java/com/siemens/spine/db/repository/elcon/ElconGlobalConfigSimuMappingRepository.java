package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconGlobalConfigSimuMapping;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_global_config_simu_mapping")
@ApplicationScoped
public class ElconGlobalConfigSimuMappingRepository extends AbstractRestCRUDRepositoryImpl<ElconGlobalConfigSimuMapping, Long> {

    @Override
    protected boolean isNew(ElconGlobalConfigSimuMapping entity) {
        return entity.getCfgId() == null;
    }

    @Override
    public Class<ElconGlobalConfigSimuMapping> getDomainType() {
        return ElconGlobalConfigSimuMapping.class;
    }
} 