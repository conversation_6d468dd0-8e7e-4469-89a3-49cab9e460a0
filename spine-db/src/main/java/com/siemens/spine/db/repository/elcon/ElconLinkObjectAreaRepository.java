package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconLinkObjectArea;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_link_object_area")
@ApplicationScoped
public class ElconLinkObjectAreaRepository extends AbstractRestCRUDRepositoryImpl<ElconLinkObjectArea, Long> {

    @Override
    protected boolean isNew(ElconLinkObjectArea entity) {
        return entity.getLinkId() == null;
    }

    @Override
    public Class<ElconLinkObjectArea> getDomainType() {
        return ElconLinkObjectArea.class;
    }
} 