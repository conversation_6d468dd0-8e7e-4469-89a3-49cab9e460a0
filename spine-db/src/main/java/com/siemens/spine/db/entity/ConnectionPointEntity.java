package com.siemens.spine.db.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name = "connectionpoint")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Audited
public class ConnectionPointEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "connection_point_id_seq_gen")
    @SequenceGenerator(name = "connection_point_id_seq_gen", sequenceName = "connection_point_id_seq")
    private Long id;
    private String name;
    private String type;
    private Integer x;
    private Integer y;
    private Integer z;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "component_id", referencedColumnName = "id")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private ComponentEntity component;

    @OneToMany(mappedBy = "owner", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private List<NeighborConnectionEntity> ownedConnections = new ArrayList<>();

    @OneToMany(mappedBy = "neighbor", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private List<NeighborConnectionEntity> neighborConnections = new ArrayList<>();

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ConnectionPointEntity that = (ConnectionPointEntity) o;
        return Objects.equals(id, that.id);
    }

    public void addNeighborConnection(ConnectionPointEntity neighbor) {
        NeighborConnectionEntity connection = new NeighborConnectionEntity();
        connection.setOwner(this);
        connection.setNeighbor(neighbor);

        this.ownedConnections.add(connection);
        neighbor.getNeighborConnections().add(connection);
    }

    public void removeNeighborConnection(ConnectionPointEntity neighbor) {
        NeighborConnectionEntity connection = ownedConnections.stream()
                .filter(conn -> conn.getNeighbor().equals(neighbor))
                .findFirst()
                .orElse(null);

        if (connection != null) {
            this.ownedConnections.remove(connection);
            neighbor.getNeighborConnections().remove(connection);
        }
    }

}