package com.siemens.spine.db.repository.paging;

import java.util.Collections;
import java.util.Iterator;
import java.util.List;

public record PageImpl<T>(List<T> content, Pageable pageable, long totalElements) implements Page<T> {

    public PageImpl(List<T> content, Pageable pageable, long totalElements) {
        this.content = content != null ? content : Collections.emptyList();
        this.pageable = pageable;
        this.totalElements = totalElements;
    }

    @Override
    public int getNumber() {
        return pageable.getPageNumber();
    }

    @Override
    public int getSize() {
        return pageable.getPageSize();
    }

    @Override
    public int getNumberOfElements() {
        return content.size();
    }

    @Override
    public int getTotalPages() {
        return getSize() == 0 ? 1 : (int) Math.ceil((double) totalElements / getSize());
    }

    @Override
    public boolean isFirst() {
        return !hasPrevious();
    }

    @Override
    public boolean isLast() {
        return !hasNext();
    }

    @Override
    public boolean hasNext() {
        return getNumber() + 1 < getTotalPages();
    }

    @Override
    public boolean hasPrevious() {
        return getNumber() > 0;
    }

    @Override
    public Iterator<T> iterator() {
        return content.iterator();
    }

}