// WARNING: Entity ElconTimeSyncSetup does not have @Id or @EmbeddedId. Please check entity definition!
package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconTimeSyncSetup;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_time_sync_setup")
@ApplicationScoped
public class ElconTimeSyncSetupRepository extends AbstractRestCRUDRepositoryImpl<ElconTimeSyncSetup, Long> {

    // WARNING: No @Id or @EmbeddedId found. isNew() always returns false.
    @Override
    protected boolean isNew(ElconTimeSyncSetup entity) {
        return entity.getId() == null;
    }

    @Override
    public Class<ElconTimeSyncSetup> getDomainType() {
        return ElconTimeSyncSetup.class;
    }
} 