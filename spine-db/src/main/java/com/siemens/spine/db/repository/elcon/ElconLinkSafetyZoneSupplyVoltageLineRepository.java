package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconLinkSafetyZoneSupplyVoltageLine;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_link_safety_zone_supply_voltage_line")
@ApplicationScoped
public class ElconLinkSafetyZoneSupplyVoltageLineRepository extends AbstractRestCRUDRepositoryImpl<ElconLinkSafetyZoneSupplyVoltageLine, Long> {

    @Override
    protected boolean isNew(ElconLinkSafetyZoneSupplyVoltageLine entity) {
        return entity.getLinkId() == null;
    }

    @Override
    public Class<ElconLinkSafetyZoneSupplyVoltageLine> getDomainType() {
        return ElconLinkSafetyZoneSupplyVoltageLine.class;
    }
} 