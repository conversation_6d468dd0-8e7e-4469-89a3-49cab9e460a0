package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_mrp_domain")
public class ElconMrpDomain {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "domain_id", nullable = false)
    private Long domainId;

    @Size(max = 255)
    @Column(name = "domain_name")
    private String domainName;

    @NotNull
    @Column(name = "subnet_id", nullable = false)
    private Long subnetId;

}