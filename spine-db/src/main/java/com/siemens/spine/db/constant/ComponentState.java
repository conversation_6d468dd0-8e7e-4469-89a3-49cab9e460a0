package com.siemens.spine.db.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ComponentState {

    DELETED("Deleted", 0),

    // Proposal Phase
    PROPOSAL_PHASE_40_0("40.0 Proposal design work started", 20),
    PROPOSAL_PHASE_40_1("40.1 Proposal design layout frozen", 22),
    PROPOSAL_PHASE_40_2("40.2 Conveying objects material flow successfully defined (Layouter)", 25),
    PROPOSAL_PHASE_40_3("40.3 Drives preliminary dimensioned", 30),
    PROPOSAL_PHASE_40_4("40.4 Electrical PLC areas and components configuration preliminary defined", 35),
    PROPOSAL_PHASE_40_F("40.F Proposal layout configuration finished", 40),

    // Execution Phase
    EXECUTION_PHASE_200_0("200.0 Execution layout work started", 100),
    EXECUTION_PHASE_200_1("200.1 Basic Design finished (Layout Frozen)", 130),
    EXECUTION_PHASE_200_2("200.2 Mechanical Configuration finished", 140),
    EXECUTION_PHASE_200_3("200.3 Drives dimensioned", 160),
    EXECUTION_PHASE_200_4("200.4 Electrical PLC areas defined / AKZ defined / Electrical HW configuration defined",
            180),
    EXECUTION_PHASE_200_F("200.F Materials released for SCM", 200),
    EXECUTION_PHASE_200_6(
            "200.6 Engineering integration test finished (incl. PLC, HLC, SCADA and Hardware Configuration)", 300),
    EXECUTION_PHASE_400_F("400.F Material for Objects delivered", 400),
    EXECUTION_PHASE_550_1("550.1 Objects mechanically installed", 450),
    EXECUTION_PHASE_550_2("550.2 Objects electrically installed (cold) IT installation done", 500),
    EXECUTION_PHASE_550_F("550.F Objects electricallyinstalled (hot)", 550),
    EXECUTION_PHASE_600_1("600.1 Conveyor mechanically commissioned", 580),
    EXECUTION_PHASE_600_2("600.2 Objects commissioned", 620),
    EXECUTION_PHASE_650_F("650.F Final Acceptance", 650);

    private static final Map<Integer, ComponentState> mapping = new HashMap<>();
    private static final List<ComponentState> componentStateOrder = new ArrayList<>();

    static {
        for (ComponentState componentState : values()) {
            mapping.put(componentState.getV1Status(), componentState);

            componentStateOrder.add(componentState);
        }

        componentStateOrder.remove(DELETED);
        componentStateOrder.sort((o1, o2) -> o1.getV1Status() - o2.v1Status);
    }

    private final String name;
    private final int v1Status;

    public static ComponentState resolve(int v1Status) {
        return mapping.get(v1Status);
    }

    /**
     * Check if the input state is the first state of the flow?
     *
     * @param checkingState
     * @return
     */
    public static boolean isFirstState(int checkingState) {
        return checkingState == PROPOSAL_PHASE_40_0.getV1Status();
    }

    /**
     * Check if the input state is the final state of the flow?
     *
     * @param checkingState
     * @return
     */
    public static boolean isFinalState(int checkingState) {
        return checkingState == EXECUTION_PHASE_650_F.getV1Status();
    }

    /**
     * Get the next state of current state
     *
     * @param currentState
     * @return
     */
    public static ComponentState getNextState(int currentState) {
        ComponentState current = resolve(currentState);
        if (current != null && current != DELETED && !isFinalState(currentState)) {
            return componentStateOrder.get(componentStateOrder.indexOf(current) + 1);
        }

        return null;
    }
}

