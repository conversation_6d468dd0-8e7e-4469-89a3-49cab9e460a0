package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_fieldbus_member")
public class ElconFieldbusMember {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "member_id", nullable = false)
    private Long memberId;

    @NotNull
    @Column(name = "device_id", nullable = false)
    private Long deviceId;

    @NotNull
    @Column(name = "subnet_id", nullable = false)
    private Long subnetId;

    @Size(max = 255)
    @NotNull
    @Column(name = "member_kind", nullable = false)
    private String memberKind;

    @Size(max = 255)
    @Column(name = "member_name")
    private String memberName;

    @Size(max = 20)
    @Column(name = "fieldbus_role", length = 20)
    private String fieldbusRole;

}