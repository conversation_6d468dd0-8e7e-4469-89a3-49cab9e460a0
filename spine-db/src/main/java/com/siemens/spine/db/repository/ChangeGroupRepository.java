package com.siemens.spine.db.repository;

import com.siemens.spine.db.entity.ChangeGroupEntity;
import com.siemens.spine.db.repository.filter.ChangeGroupFilter;
import com.siemens.spine.db.repository.views.ComponentChangeGroupView;

import java.util.List;
import java.util.Optional;

public interface ChangeGroupRepository extends GenericJpaRepository<ChangeGroupEntity, Long> {

    List<ComponentChangeGroupView> getListChangeGroupByEachComponentIds(List<Long> ids);

    /**
     * Filter the change group
     *
     * @param filter
     * @return
     */
    List<ChangeGroupEntity> getChangeGroups(ChangeGroupFilter filter);

    Optional<ChangeGroupEntity> getChangeGroupByProjectIdAndName(Long projectId, String name);

}

