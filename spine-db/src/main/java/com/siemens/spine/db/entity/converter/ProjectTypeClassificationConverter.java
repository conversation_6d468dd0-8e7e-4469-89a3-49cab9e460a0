package com.siemens.spine.db.entity.converter;

import com.siemens.spine.db.constant.ProjectTypeClassificationEnum;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class ProjectTypeClassificationConverter implements AttributeConverter<ProjectTypeClassificationEnum, String> {

    @Override
    public String convertToDatabaseColumn(ProjectTypeClassificationEnum projTypeEnum) {
        if (projTypeEnum == null) {
            return null;
        }
        return projTypeEnum.getValue();
    }

    @Override
    public ProjectTypeClassificationEnum convertToEntityAttribute(String value) {
        return ProjectTypeClassificationEnum.resolve(value);
    }

}

