package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.RevisionInfoEntity;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.query.AuditEntity;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;

/**
 * <AUTHOR>
 * @since 2025/05/12
 */

@ApplicationScoped
@Transactional(TxType.REQUIRED)
public class RevisionJpaAuditRepositoryImpl {

    @PersistenceContext
    private EntityManager entityManager;

    public RevisionInfoEntity getLatestRevisionInfo() {

        return entityManager.createQuery(
                        "SELECT r FROM RevisionInfoEntity r ORDER BY r.id DESC",
                        RevisionInfoEntity.class
                )
                .setMaxResults(1)
                .getSingleResult();
    }

    public RevisionInfoEntity getRevisionInfoById(Long revisionId) {
        return entityManager.find(RevisionInfoEntity.class, revisionId);
    }

    public <T> RevisionInfoEntity getLatestRevisionInfo(Class<T> entityClass) {
        AuditReader auditReader = AuditReaderFactory.get(entityManager);

        // Get the latest revision number for the specified entity class
        Number latestRevNumber = (Number) auditReader.createQuery()
                .forRevisionsOfEntity(entityClass, false, false)
                .addOrder(AuditEntity.revisionNumber().desc())
                .setMaxResults(1)
                .getSingleResult();

        return entityManager.find(RevisionInfoEntity.class, latestRevNumber);
    }

    public <T> T getObjectByRevisionId(Class<T> entityClass, Long entityId, Number revisionId) {
        AuditReader auditReader = AuditReaderFactory.get(entityManager);
        return auditReader.find(entityClass, entityId, revisionId);
    }

}

