package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_prj_init_object_kind")
public class ElconPrjInitObjectKind {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "cfg_id", nullable = false)
    private Long cfgId;

    @Size(max = 255)
    @Column(name = "object_kind")
    private String objectKind;

    @Size(max = 255)
    @Column(name = "object_sub_kind")
    private String objectSubKind;

    @Column(name = "number_min")
    private Long numberMin;

    @Column(name = "number_max")
    private Long numberMax;

    @Size(max = 255)
    @Column(name = "sw_class")
    private String swClass;

    @NotNull
    @Column(name = "manual_creation_allowed", nullable = false)
    private Short manualCreationAllowed;

    @NotNull
    @Column(name = "manual_plc_sw_id_change_flag", nullable = false)
    private Short manualPlcSwIdChangeFlag;

}