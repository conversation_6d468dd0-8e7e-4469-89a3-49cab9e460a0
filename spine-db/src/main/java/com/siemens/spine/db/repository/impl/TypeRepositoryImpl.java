package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.TypeEntity;
import com.siemens.spine.db.repository.TypeRepository;
import com.siemens.spine.db.repository.filter.TypeFilter;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.NoResultException;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> <PERSON>am
 * @version 1.0
 * @since 26/12/2022
 */
@ApplicationScoped
@Transactional(TxType.REQUIRED)
public class TypeRepositoryImpl extends GenericJpaRepositoryImpl<TypeEntity, String> implements TypeRepository {

    public static final String PROJECT = "project";

    private static final long PROJECT_ID_OF_GENERIC_TYPE = 860889;

    @Override
    public Class<TypeEntity> getDomainType() {
        return TypeEntity.class;
    }

    @Override
    public int deleteByIds(List<String> ids) {
        return entityManager.createQuery(
                        "delete from TypeEntity p " +
                                " where p.typeId in :ids")
                .setParameter("ids", ids)
                .executeUpdate();
    }

    @Override
    public List<TypeEntity> filter(TypeFilter filter) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<TypeEntity> cq = cb.createQuery(TypeEntity.class);
        Root<TypeEntity> rootEntry = cq.from(TypeEntity.class);

        List<Predicate> predicates = buildWhereClause(filter, cb, rootEntry);

        CriteriaQuery<TypeEntity> query = cq.select(rootEntry)
                .where(predicates.toArray(new Predicate[0]));

        return entityManager.createQuery(query).getResultList();
    }

    @Override
    public List<TypeEntity> findGenericType() {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<TypeEntity> cq = cb.createQuery(TypeEntity.class);
        Root<TypeEntity> rootEntry = cq.from(TypeEntity.class);

        Predicate condition = cb.or(
                cb.isNull(rootEntry.get("project")),
                cb.isNull(rootEntry.get("projectSpecific")),
                cb.isFalse(rootEntry.get("projectSpecific"))
        );

        cq.select(rootEntry)
                .where(condition);

        return entityManager.createQuery(cq).getResultList();
    }

    @Override
    public TypeEntity findGenericTypeById(String typeId) {
        try {
            return entityManager.createQuery("select t" +
                            " from TypeEntity t" +
                            " where t.typeId = :typeId" +
                            "    and t.projectSpecific is false and t.outdated is false", TypeEntity.class)
                    .setParameter("typeId", typeId)
                    .setMaxResults(1)
                    .getSingleResult();
        } catch (NoResultException ex) {
            return null;
        }
    }

    @Override
    public List<String> findAllTypeIdsByProjectId(Long projectId) {
        return entityManager.createQuery("select t.typeId " +
                                " from TypeEntity t" +
                                " where (t.project.projectID = :projectId and t.outdated = false) or t.projectSpecific = false",
                        String.class)
                .setParameter("projectId", projectId)
                .getResultList();
    }

    /**
     * Build the where clause
     *
     * @param filter
     * @param cb
     * @param rootEntry
     * @return
     */
    private List<Predicate> buildWhereClause(TypeFilter filter, CriteriaBuilder cb, Root<TypeEntity> rootEntry) {
        if (filter == null) {
            return Collections.emptyList();
        }

        List<Predicate> predicates = new ArrayList<>();

        // find by project ids
        List<Long> projectIds = filter.getProjectIds();
        if (projectIds != null && !projectIds.isEmpty()) {
            predicates.add(
                    rootEntry.get(PROJECT).get("projectID").in(projectIds)
            );
        }

        // find by specific project name
        if (!StringUtils.isEmpty(filter.getSpecificProjectName())) {
            predicates.add(
                    cb.equal(rootEntry.get(PROJECT).get("projectName"), filter.getSpecificProjectName().trim())
            );
        }

        // filter the outdated type
        if (filter.getOutdated() != null) {
            predicates.add(cb.equal(rootEntry.get("outdated"), filter.getOutdated()));
        }

        return predicates;
    }

}

