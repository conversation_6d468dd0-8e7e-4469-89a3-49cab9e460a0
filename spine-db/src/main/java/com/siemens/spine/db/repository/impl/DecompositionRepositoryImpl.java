package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.DecompositionEntity;
import com.siemens.spine.db.repository.DecompositionRepository;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.Query;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaDelete;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;
import java.util.List;

/**
 * <AUTHOR> Pham
 * @version 1.0
 * @since 22/12/2022
 */
@ApplicationScoped
@Transactional(TxType.REQUIRED)
public class DecompositionRepositoryImpl extends
        GenericJpaRepositoryImpl<DecompositionEntity, Long> implements DecompositionRepository {

    @Override
    public Class<DecompositionEntity> getDomainType() {
        return DecompositionEntity.class;
    }

    @Override
    public List<DecompositionEntity> findByComponentId(Long componentId) {
        Query query = entityManager.createQuery(
                "from DecompositionEntity d where d.component.id = :componentId",
                DecompositionEntity.class);
        query.setParameter("componentId", componentId);
        return query.getResultList();
    }

    @Override
    public List<DecompositionEntity> findUnfinishedMaterials(Long projectId) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<DecompositionEntity> cq = cb.createQuery(DecompositionEntity.class);
        Root<DecompositionEntity> rootEntry = cq.from(DecompositionEntity.class);

        // A list of all placeholder numbers where no information is available in SAP
        String placeholderNumberPattern1 = "77.%";

        // A list of all placeholder numbers that are like a known component in SAP but need modifications
        String placeholderNumberPattern2 = "78.%";

        CriteriaQuery<DecompositionEntity> query = cq.select(rootEntry)
                .where(
                        cb.and(
                                cb.isNotNull(rootEntry.get("component")),
                                cb.equal(rootEntry.get("component").get("projectId"), projectId),
                                cb.or(
                                        cb.like(rootEntry.get("sapMaterialNumber"), placeholderNumberPattern1),
                                        cb.like(rootEntry.get("sapMaterialNumber"), placeholderNumberPattern2)
                                )
                        )
                );

        return entityManager.createQuery(query).getResultList();
    }

    @Override
    public long deleteByComponentIds(List<Long> componentIds) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaDelete<DecompositionEntity> cq = cb.createCriteriaDelete(DecompositionEntity.class);
        Root<DecompositionEntity> rootEntry = cq.from(DecompositionEntity.class);

        CriteriaDelete<DecompositionEntity> query = cq.where(
                cb.and(rootEntry.get("component").in(componentIds))
        );

        return entityManager.createQuery(query).executeUpdate();
    }

}

