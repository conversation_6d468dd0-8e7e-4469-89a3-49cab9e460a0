package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconClDeviceIo;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cl_device_io")
@ApplicationScoped
public class ElconClDeviceIoRepository extends AbstractRestCRUDRepositoryImpl<ElconClDeviceIo, Long> {

    @Override
    protected boolean isNew(ElconClDeviceIo entity) {
        return entity.getIoId() == null;
    }

    @Override
    public Class<ElconClDeviceIo> getDomainType() {
        return ElconClDeviceIo.class;
    }
} 