package com.siemens.spine.db.entity.elcon;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "elcon_mv_conveyor_neighbour_active")
public class ElconMvConveyorNeighbourActive {

    @EmbeddedId
    private ElconMvConveyorNeighbourActiveId id;

    @Column(name = "gap")
    private Long gap;

    @Size(max = 255)
    @Column(name = "cp_type")
    private String cpType;

    @Column(name = "object_id")
    private Long objectId;

    @Column(name = "object_id_neighbour")
    private Long objectIdNeighbour;

    @Column(name = "nr")
    private Long nr;

    @Column(name = "nr_neighbour")
    private Long nrNeighbour;

    @Column(name = "passive_flag")
    private BigDecimal passiveFlag;

    @Column(name = "passive_flag_neighbour")
    private BigDecimal passiveFlagNeighbour;

    @Column(name = "active_object_id")
    private Long activeObjectId;

    @Column(name = "active_object_id_neighbour")
    private Long activeObjectIdNeighbour;

    @Data
    @Embeddable
    public static class ElconMvConveyorNeighbourActiveId implements Serializable {

        private String cpType;

        private Long objectId;

        private Long objectIdNeighbour;

        private Long nrNeighbour;

        private BigDecimal passiveFlag;

        private BigDecimal passiveFlagNeighbour;

        private Long activeObjectId;

        private Long activeObjectIdNeighbour;

    }
}