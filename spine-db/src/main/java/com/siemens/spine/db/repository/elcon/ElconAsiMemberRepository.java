package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconAsiMember;

import javax.enterprise.context.ApplicationScoped;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 19/6/2025
 **/
@RestRepository(path = "elcon_drive")
@ApplicationScoped
public class ElconAsiMemberRepository extends AbstractRestCRUDRepositoryImpl<ElconAsiMember, Long> {

    @Override
    protected boolean isNew(ElconAsiMember entity) {
        return entity.getMemberId() == null;
    }

    @Override
    public Class<ElconAsiMember> getDomainType() {
        return ElconAsiMember.class;
    }

}
