package com.siemens.spine.db.repository;

import com.siemens.spine.db.entity.ProjectComponentVersionEntity;

import java.util.List;
import java.util.Optional;

public interface ProjectComponentVersionRepository extends GenericJpaRepository<ProjectComponentVersionEntity, Long> {

    List<ProjectComponentVersionEntity> findByProjectId(Long projectId);

    Optional<ProjectComponentVersionEntity> findTopByProjectIdOrderByRevDesc(Long projectId);

    Optional<ProjectComponentVersionEntity> findByProjectIdAndRev(Long projectId, Integer rev);

}

