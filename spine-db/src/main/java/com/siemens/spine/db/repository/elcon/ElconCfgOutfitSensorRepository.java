package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCfgOutfitSensor;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cfg_outfit_sensor")
@ApplicationScoped
public class ElconCfgOutfitSensorRepository extends AbstractRestCRUDRepositoryImpl<ElconCfgOutfitSensor, Long> {

    @Override
    protected boolean isNew(ElconCfgOutfitSensor entity) {
        return entity.getCfgId() == null;
    }

    @Override
    public Class<ElconCfgOutfitSensor> getDomainType() {
        return ElconCfgOutfitSensor.class;
    }
} 