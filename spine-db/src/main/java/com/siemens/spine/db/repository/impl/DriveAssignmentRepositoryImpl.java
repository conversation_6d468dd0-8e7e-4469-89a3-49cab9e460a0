package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.DriveAssignmentDataEntity;
import com.siemens.spine.db.repository.DriveAssignmentRepository;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.NoResultException;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;
import java.util.List;

@ApplicationScoped
@Transactional(TxType.REQUIRED)
public class DriveAssignmentRepositoryImpl extends GenericJpaRepositoryImpl<DriveAssignmentDataEntity, Long>
        implements DriveAssignmentRepository {

    @Override
    public Class<DriveAssignmentDataEntity> getDomainType() {
        return DriveAssignmentDataEntity.class;
    }

    @Override
    public List<DriveAssignmentDataEntity> findByComponentId(List<Long> componentIds) {
        if (componentIds == null || componentIds.isEmpty()) {
            return List.of();
        }

        return entityManager.createQuery(
                        "SELECT d FROM DriveAssignmentDataEntity d " +
                                "WHERE d.componentId IN :componentIds " +
                                "ORDER BY d.componentId, d.driverNumber", DriveAssignmentDataEntity.class)
                .setParameter("componentIds", componentIds)
                .getResultList();
    }

    @Override
    public DriveAssignmentDataEntity findByComponentIdAndDriverNumber(Long componentId, Integer driverNumber) {
        try {
            return entityManager.createQuery("select d " +
                                    " from DriveAssignmentDataEntity d " +
                                    " where d.componentId = :componentId and d.driverNumber = :driverNumber",
                            DriveAssignmentDataEntity.class)
                    .setParameter("componentId", componentId)
                    .setParameter("driverNumber", driverNumber)
                    .setMaxResults(1)
                    .getSingleResult();
        } catch (NoResultException ex) {
            return null;
        }
    }

}

