package com.siemens.spine.db.repository;

import com.siemens.spine.db.entity.UserToGroupEntity;

import java.util.List;

/**
 * Identity and access management
 */
public interface UserToGroupRepository extends GenericJpaRepository<UserToGroupEntity, Long> {

    /**
     * Check if a user is already in a group
     *
     * @param username
     * @param userGroup
     * @return entity that represent for user in group. null means group not found or user is not belong to group
     */
    UserToGroupEntity isUserInGroup(String username, String userGroup);

    /**
     * Get all of groups that user belong to
     *
     * @param username
     * @return
     */
    List<UserToGroupEntity> findUserGroups(String username);

}

