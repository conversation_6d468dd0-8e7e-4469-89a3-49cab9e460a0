package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_network_com_channel")
public class ElconNetworkComChannel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "channel_id", nullable = false)
    private Long channelId;

    @NotNull
    @Column(name = "interface_id", nullable = false)
    private Long interfaceId;

    @Size(max = 20)
    @Column(name = "communication_protocol", length = 20)
    private String communicationProtocol;

    @Column(name = "tcp_port")
    private Integer tcpPort;

    @Size(max = 20)
    @Column(name = "tsap", length = 20)
    private String tsap;

    @Size(max = 255)
    @Column(name = "description")
    private String description;

}