package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_power_line")
public class ElconPowerLine {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "line_id", nullable = false)
    private Long lineId;

    @NotNull
    @Column(name = "master_device_id", nullable = false)
    private Long masterDeviceId;

    @Size(max = 10)
    @Column(name = "power_kind", length = 10)
    private String powerKind;

    @Size(max = 255)
    @Column(name = "line_name")
    private String lineName;

    @Column(name = "line_sequence")
    private Integer lineSequence;

    @Size(max = 20)
    @Column(name = "breaker_current", length = 20)
    private String breakerCurrent;

}