package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MapsId;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_sorter")
public class ElconSorter {

    @Id
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @MapsId
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "object_id", nullable = false)
    private ElconObject elconObject;

    @Size(max = 15)
    @Column(name = "pid_type", length = 15)
    private String pidType;

    @NotNull
    @Column(name = "max_destinations", nullable = false)
    private Integer maxDestinations;

    @NotNull
    @Column(name = "max_recirculation", nullable = false)
    private Integer maxRecirculation;

    @NotNull
    @Column(name = "max_distance_counter_abs", nullable = false)
    private Long maxDistanceCounterAbs;

}