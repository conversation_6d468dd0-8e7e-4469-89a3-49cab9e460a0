package com.siemens.spine.db.utils;

import com.siemens.spine.db.exception.JpaException;
import lombok.extern.slf4j.Slf4j;

import java.util.regex.Pattern;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 17/7/2025
 **/
@Slf4j
public final class StringUtils {

    public static final Pattern SNAKE_CASE_PATTERN = Pattern.compile("^[a-z][a-z0-9]*(_[a-z0-9]+)*$");

    private StringUtils() {
    }

    /**
     * Converts snake_case string to camelCase.
     *
     * @param snakeCase The snake_case string to convert
     * @return The converted camelCase string
     * @throws JpaException if input is null or invalid
     */
    public static String snakeToCamelCase(String snakeCase) throws JpaException {
        if (snakeCase == null || snakeCase.trim().isEmpty()) {
            throw new JpaException("Snake case string cannot be null or empty");
        }

        String trimmed = snakeCase.trim().toLowerCase();

        // If it's not snake_case, return as-is (might already be camelCase)
        if (!SNAKE_CASE_PATTERN.matcher(trimmed).matches()) {
            log.debug("String '{}' is not in snake_case format, returning as-is", snakeCase);
            return snakeCase;
        }

        StringBuilder camelCase = new StringBuilder();
        String[] parts = trimmed.split("_");

        // First part remains lowercase
        camelCase.append(parts[0]);

        // Subsequent parts are capitalized
        for (int i = 1; i < parts.length; i++) {
            String part = parts[i];
            if (!part.isEmpty()) {
                camelCase.append(Character.toUpperCase(part.charAt(0)));
                if (part.length() > 1) {
                    camelCase.append(part.substring(1));
                }
            }
        }

        String result = camelCase.toString();
        log.debug("Converted snake_case '{}' to camelCase '{}'", snakeCase, result);
        return result;
    }

    /**
     * Converts camelCase string to snake_case.
     *
     * @param camelCase The camelCase string to convert
     * @return The converted snake_case string
     * @throws JpaException if input is null or invalid
     */
    public static String camelToSnakeCase(String camelCase) throws JpaException {
        if (camelCase == null || camelCase.trim().isEmpty()) {
            throw new JpaException("Camel case string cannot be null or empty");
        }

        String trimmed = camelCase.trim();

        StringBuilder snakeCase = new StringBuilder();
        for (int i = 0; i < trimmed.length(); i++) {
            char c = trimmed.charAt(i);

            // Insert underscore before uppercase letters (except first character)
            if (i > 0 && Character.isUpperCase(c)) {
                snakeCase.append('_');
            }

            snakeCase.append(Character.toLowerCase(c));
        }

        String result = snakeCase.toString();
        log.debug("Converted camelCase '{}' to snake_case '{}'", camelCase, result);
        return result;
    }

}
