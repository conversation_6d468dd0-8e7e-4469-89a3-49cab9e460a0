package com.siemens.spine.db.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "decompositionattributes")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DecompositionAttributesEntity {

    @Id
    private String SAPMaterialNumber;

    private String DriveType;

    private String DriveTVolt;

    private String DriveWiring;

    private String DriveProtection;

    private String DriveStarter;

    private String DriveReversible;

    private String DriveVFD;

    private String DriveBreak;

    private String DriveVoltage;

    private double DriveFrequency;

    private double StartStopCycles;

    private double DrivePower;

    private double DriveCurrent;

    private double DriveCosPhi;

}

