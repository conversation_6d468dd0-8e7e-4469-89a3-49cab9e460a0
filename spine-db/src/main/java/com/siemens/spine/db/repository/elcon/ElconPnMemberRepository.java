package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPnMember;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_pn_member")
@ApplicationScoped
public class ElconPnMemberRepository extends AbstractRestCRUDRepositoryImpl<ElconPnMember, Long> {

    @Override
    protected boolean isNew(ElconPnMember entity) {
        return entity.getMemberId() == null;
    }

    @Override
    public Class<ElconPnMember> getDomainType() {
        return ElconPnMember.class;
    }
} 