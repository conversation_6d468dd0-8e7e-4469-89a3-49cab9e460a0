package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_communication_unit")
public class ElconCommunicationUnit {

    @Id
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @Size(max = 20)
    @Column(name = "communication_kind", length = 20)
    private String communicationKind;

    @Size(max = 20)
    @Column(name = "connection_type", length = 20)
    private String connectionType;

    @Size(max = 20)
    @Column(name = "connection_id", length = 20)
    private String connectionId;

    @Column(name = "active_flag")
    private Short activeFlag;

    @Size(max = 20)
    @Column(name = "redundancy_channel", length = 20)
    private String redundancyChannel;

    @Column(name = "local_channel_id")
    private Long localChannelId;

}