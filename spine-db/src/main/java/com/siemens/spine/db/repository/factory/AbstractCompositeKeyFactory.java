package com.siemens.spine.db.repository.factory;

import com.siemens.spine.db.exception.JpaException;
import com.siemens.spine.db.utils.CompositeKeyUtils;
import com.siemens.spine.db.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;

/**
 * Abstract base factory providing common functionality for all composite key factories.
 * Implements shared operations like type conversion, validation, and snake_case handling.
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
public abstract class AbstractCompositeKeyFactory implements CompositeKeyFactory {

    @Override
    public CompositeKeyUtils.CompositeKeyData createKeyData(String compositeKeyStr, Class<?> entityClass)
            throws JpaException {
        Map<String, String> keyValueMap = CompositeKeyUtils.parseCompositeKeyWithMapping(compositeKeyStr);
        return createKeyDataFromMap(keyValueMap, entityClass);
    }

    /**
     * Converts a string value to the target field type.
     * Shared functionality across all factory implementations.
     *
     * @param value      The string value to convert
     * @param targetType The target type
     * @return The converted value
     * @throws JpaException if conversion fails
     */
    protected Object convertToFieldType(String value, Class<?> targetType) throws JpaException {
        return CompositeKeyUtils.convertValue(value, targetType);
    }

    /**
     * Checks if the given type is supported for ID fields.
     * Shared functionality across all factory implementations.
     *
     * @param type The type to check
     * @return true if the type is supported
     */
    protected boolean isSupportedType(Class<?> type) {
        return type != String.class &&
                type != Long.class && type != long.class &&
                type != Integer.class && type != int.class &&
                type != Boolean.class && type != boolean.class &&
                type != Double.class && type != double.class &&
                type != Float.class && type != float.class &&
                type != Short.class && type != short.class &&
                type != Byte.class && type != byte.class;
    }

    /**
     * Creates a standardized error message for missing field keys.
     * Ensures consistent error formatting across all factories.
     *
     * @param fieldName      The field name that wasn't found
     * @param entityClass    The entity class
     * @param keyValueMap    Available keys
     * @param keyTypeContext Context about the key type (e.g., "@IdClass", "@EmbeddedId")
     * @return Formatted error message
     */
    protected String createFieldNotFoundError(String fieldName, Class<?> entityClass,
                                              Map<String, String> keyValueMap, String keyTypeContext) {
        String snakeCaseFieldName = StringUtils.camelToSnakeCase(fieldName);
        return String.format(
                "Key for %s field '%s' not found in composite key for entity '%s'. " +
                        "Available keys: [%s]. Try using '%s' if providing snake_case names.",
                keyTypeContext.isEmpty() ? "" : keyTypeContext + " ",
                fieldName,
                entityClass.getSimpleName(),
                String.join(", ", keyValueMap.keySet()),
                snakeCaseFieldName
        );
    }

    /**
     * Finds a matching field for the given key name, supporting snake_case to camelCase conversion.
     * Shared functionality across all factory implementations.
     *
     * @param keyName     The key name from user input
     * @param idFields    List of available ID fields
     * @param entityClass The entity class (for error messages)
     * @return The matching field and the actual key name used
     * @throws JpaException if no matching field is found
     */
    protected FieldMatch findMatchingField(String keyName, List<Field> idFields, Class<?> entityClass)
            throws JpaException {
        for (Field field : idFields) {
            if (field.getName().equals(keyName)) {
                log.debug("Found exact field match for key '{}': {}", keyName, field.getName());
                return new FieldMatch(field, keyName);
            }
        }

        if (StringUtils.SNAKE_CASE_PATTERN.matcher(keyName.toLowerCase()).matches()) {
            String camelCaseKey = StringUtils.snakeToCamelCase(keyName);
            for (Field field : idFields) {
                if (field.getName().equals(camelCaseKey)) {
                    log.debug("Found snake_case converted field match for key '{}' -> '{}': {}",
                            keyName, camelCaseKey, field.getName());
                    return new FieldMatch(field, keyName);
                }
            }
        }

        for (Field field : idFields) {
            String snakeCaseFieldName = StringUtils.camelToSnakeCase(field.getName());
            if (snakeCaseFieldName.equals(keyName.toLowerCase())) {
                log.debug("Found camelCase to snake_case field match for key '{}': {} -> {}",
                        keyName, field.getName(), snakeCaseFieldName);
                return new FieldMatch(field, keyName);
            }
        }

        StringBuilder errorMessage = new StringBuilder();
        errorMessage.append(String.format("Key '%s' not found for entity '%s'. Available keys: [",
                keyName, entityClass.getSimpleName()));

        for (int i = 0; i < idFields.size(); i++) {
            Field field = idFields.get(i);
            if (i > 0) {
                errorMessage.append(", ");
            }
            errorMessage.append(field.getName()).append(" (or ")
                    .append(StringUtils.camelToSnakeCase(field.getName())).append(")");
        }
        errorMessage.append("]");

        throw new JpaException(errorMessage.toString());
    }

    /**
     * Validates the number of key components against the expected number of fields.
     *
     * @param keyValueMap     The parsed key-value map
     * @param idFields        The expected ID fields
     * @param compositeKeyStr The original composite key string (for error messages)
     * @param entityClass     The entity class (for error messages)
     * @throws JpaException if counts don't match
     */
    protected void validateKeyComponentCount(Map<String, String> keyValueMap, List<Field> idFields,
                                             String compositeKeyStr, Class<?> entityClass) throws JpaException {
        if (keyValueMap.size() != idFields.size()) {
            throw new JpaException(String.format(
                    "Composite key '%s' has %d key-value pairs but entity '%s' requires %d components",
                    compositeKeyStr, keyValueMap.size(), entityClass.getSimpleName(), idFields.size()
            ));
        }
    }

    /**
     * Helper record to hold field match results.
     */
    protected record FieldMatch(Field field, String usedKeyName) {

    }

}