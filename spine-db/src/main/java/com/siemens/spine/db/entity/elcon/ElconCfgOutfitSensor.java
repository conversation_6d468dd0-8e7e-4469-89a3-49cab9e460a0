package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "elcon_cfg_outfit_sensor")
public class ElconCfgOutfitSensor {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "cfg_id", nullable = false)
    private Long cfgId;

    @NotNull
    @Column(name = "project_id", nullable = false)
    private Long projectId;

    @Size(max = 255)
    @Column(name = "outfit_name")
    private String outfitName;

    @Size(max = 255)
    @Column(name = "sensor_name")
    private String sensorName;

    @Column(name = "speed")
    private BigDecimal speed;

    @Column(name = "distance")
    private Long distance;

    @Column(name = "min_length")
    private Long minLength;

    @Column(name = "max_length")
    private Long maxLength;

}