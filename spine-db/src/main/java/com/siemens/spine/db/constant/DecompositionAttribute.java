package com.siemens.spine.db.constant;

import com.siemens.spine.db.entity.DecompositionAttributesEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Getter
@AllArgsConstructor
public enum DecompositionAttribute {

    DRIVE_BREAK("DriveBreak", Constants.AttributeType.CHAR, DecompositionAttributesEntity::getDriveBreak),

    DRIVE_COS_PHI("DriveCosPhi", Constants.AttributeType.DOUBLE, DecompositionAttributesEntity::getDriveCosPhi),

    DRIVE_CURRENT("DriveCurrent", Constants.AttributeType.DOUBLE, DecompositionAttributesEntity::getDriveCurrent),

    DRIVE_FREQUENCY("DriveFrequency", Constants.AttributeType.INTEGER,
            DecompositionAttributesEntity::getDriveFrequency),

    DRIVE_POWER("DrivePower", Constants.AttributeType.DOUBLE, DecompositionAttributesEntity::getDrivePower),

    DRIVE_PROTECTION("DriveProtection", Constants.AttributeType.CHAR,
            DecompositionAttributesEntity::getDriveProtection),

    DRIVE_REVERSIBLE("DriveReversible", Constants.AttributeType.CHAR,
            DecompositionAttributesEntity::getDriveReversible),

    DRIVE_STARTER("DriveStarter", Constants.AttributeType.CHAR, DecompositionAttributesEntity::getDriveStarter),

    DRIVE_STOP_CYCLES("StartStopCycles", Constants.AttributeType.INTEGER,
            DecompositionAttributesEntity::getStartStopCycles),

    DRIVE_T_VOLT("DriveTVolt", Constants.AttributeType.CHAR, DecompositionAttributesEntity::getDriveTVolt),

    DRIVE_TYPE("DriveType", Constants.AttributeType.CHAR, DecompositionAttributesEntity::getDriveType),

    DRIVE_VFD("DriveVFD", Constants.AttributeType.CHAR, DecompositionAttributesEntity::getDriveVFD),

    DRIVE_VOLTAGE("DriveVoltage", Constants.AttributeType.CHAR, DecompositionAttributesEntity::getDriveVoltage),

    DRIVE_WIRING("DriveWiring", Constants.AttributeType.CHAR, DecompositionAttributesEntity::getDriveWiring),

    SAP_MATERIAL_NUMBER("SAPMaterialNumber", Constants.AttributeType.CHAR,
            DecompositionAttributesEntity::getSAPMaterialNumber);

    private static final Map<String, DecompositionAttribute> mapping = new HashMap<>();

    static {
        for (DecompositionAttribute attribute : values()) {
            mapping.put(attribute.getAttrName(), attribute);
        }
    }

    private final String attrName;
    private final String attrType;
    private Function<DecompositionAttributesEntity, Object> getter;

    public static DecompositionAttribute resolve(String attrName) {
        if (attrName == null) {
            return null;
        }

        return mapping.get(attrName);
    }
}

