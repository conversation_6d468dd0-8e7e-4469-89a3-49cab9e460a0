package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconExtCoNeighbour;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_ext_co_neighbour")
@ApplicationScoped
public class ElconExtCoNeighbourRepository extends AbstractRestCRUDRepositoryImpl<ElconExtCoNeighbour, Long> {

    @Override
    protected boolean isNew(ElconExtCoNeighbour entity) {
        return entity.getNeighbourId() == null;
    }

    @Override
    public Class<ElconExtCoNeighbour> getDomainType() {
        return ElconExtCoNeighbour.class;
    }
} 