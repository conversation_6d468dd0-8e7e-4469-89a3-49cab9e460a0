package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_cfg_xml_import_setting")
public class ElconCfgXmlImportSetting {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "cfg_id", nullable = false)
    private Long cfgId;

    @Size(max = 2000)
    @Column(name = "type", length = 2000)
    private String type;

    @NotNull
    @Column(name = "exclude_as_sys_type", nullable = false)
    private Short excludeAsSysType;

    @NotNull
    @Column(name = "exclude_as_conv_type", nullable = false)
    private Short excludeAsConvType;

}