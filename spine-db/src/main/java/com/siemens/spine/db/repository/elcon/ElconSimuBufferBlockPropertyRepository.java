package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconSimuBufferBlockProperty;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_simu_buffer_block_property")
@ApplicationScoped
public class ElconSimuBufferBlockPropertyRepository extends AbstractRestCRUDRepositoryImpl<ElconSimuBufferBlockProperty, Long> {

    @Override
    protected boolean isNew(ElconSimuBufferBlockProperty entity) {
        return entity.getPropertyId() == null;
    }

    @Override
    public Class<ElconSimuBufferBlockProperty> getDomainType() {
        return ElconSimuBufferBlockProperty.class;
    }
} 