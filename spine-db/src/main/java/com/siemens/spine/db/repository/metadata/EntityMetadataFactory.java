package com.siemens.spine.db.repository.metadata;

import com.siemens.spine.db.exception.JpaException;
import com.siemens.spine.db.repository.factory.CompositeKeyFactoryRegistry;
import com.siemens.spine.db.repository.metadata.impl.DefaultEntityMetadata;
import com.siemens.spine.db.repository.metadata.impl.DefaultIdMetadata;
import com.siemens.spine.db.utils.CompositeKeyUtils;
import com.siemens.spine.db.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.Table;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * Factory for creating EntityMetadata instances.
 * Leverages the existing CompositeKeyFactoryRegistry for composite key analysis.
 * <p>
 * Pre-computes all metadata at startup to avoid reflection overhead during runtime.
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
public class EntityMetadataFactory {

    private final CompositeKeyFactoryRegistry factoryRegistry;

    public EntityMetadataFactory() {
        this.factoryRegistry = CompositeKeyFactoryRegistry.getInstance();
    }

    /**
     * Creates EntityMetadata for the given entity class.
     * Uses existing composite key infrastructure for ID analysis.
     *
     * @param entityClass The entity class
     * @param <T>         The entity type
     * @return EntityMetadata instance with pre-computed information
     * @throws JpaException if metadata creation fails
     */
    public <T> EntityMetadata<T> createEntityMetadata(Class<T> entityClass) throws JpaException {
        log.debug("Creating EntityMetadata for {}", entityClass.getSimpleName());

        long startTime = System.currentTimeMillis();

        String tableName = extractTableName(entityClass);
        IdMetadata idMetadata = createIdMetadata(entityClass);
        List<AttributeMetadata> allAttributes = extractAllAttributes(entityClass, idMetadata);
        String primaryKeyFieldName = determinePrimaryKeyFieldName(idMetadata);
        EntityMetadata<T> metadata = DefaultEntityMetadata.<T>builder()
                .entityType(entityClass)
                .tableName(tableName)
                .idMetadata(idMetadata)
                .allAttributes(allAttributes)
                .primaryKeyFieldName(primaryKeyFieldName)
                .build();

        long duration = System.currentTimeMillis() - startTime;
        log.debug("Created EntityMetadata for {} in {}ms with {} attributes", entityClass.getSimpleName(), duration,
                allAttributes.size());

        return metadata;
    }

    /**
     * Creates IdMetadata using the existing CompositeKeyFactoryRegistry.
     */
    private IdMetadata createIdMetadata(Class<?> entityClass) throws JpaException {
        CompositeKeyUtils.CompositeKeyType keyType = factoryRegistry.getCompositeKeyType(entityClass);
        List<Field> idFields = factoryRegistry.extractIdFields(entityClass);

        List<AttributeMetadata> idAttributes = new ArrayList<>();
        Class<?> idClass = null;
        AttributeMetadata embeddedIdAttribute = null;
        String embeddedIdFieldName = null;

        switch (keyType) {
            case SINGLE_ID:
                for (Field field : idFields) {
                    AttributeMetadata attr = createAttributeMetadata(field, true, false);
                    idAttributes.add(attr);
                }
                break;

            case ID_CLASS:
                idClass = extractIdClass(entityClass);
                for (Field field : idFields) {
                    AttributeMetadata attr = createAttributeMetadata(field, true, false);
                    idAttributes.add(attr);
                }
                break;

            case EMBEDDED_ID:
                Field embeddedIdField = findEmbeddedIdField(entityClass);
                embeddedIdFieldName = embeddedIdField.getName();
                embeddedIdAttribute = createAttributeMetadata(embeddedIdField, true, true);

                Class<?> embeddedIdClass = embeddedIdField.getType();
                for (Field field : embeddedIdClass.getDeclaredFields()) {
                    if (!field.getName().equals("serialVersionUID")) {
                        AttributeMetadata attr = createAttributeMetadata(field, true, false);
                        idAttributes.add(attr);
                    }
                }
                break;
        }

        return DefaultIdMetadata.builder()
                .keyType(keyType)
                .idAttributes(idAttributes)
                .idClass(idClass)
                .embeddedIdAttribute(embeddedIdAttribute)
                .embeddedIdFieldName(embeddedIdFieldName)
                .build();
    }

    /**
     * Extracts all attribute metadata from the entity class.
     */
    private List<AttributeMetadata> extractAllAttributes(Class<?> entityClass, IdMetadata idMetadata) {
        List<AttributeMetadata> attributes = new ArrayList<>(idMetadata.getIdAttributes());

        if (idMetadata.getEmbeddedIdAttribute() != null) {
            attributes.add(idMetadata.getEmbeddedIdAttribute());
        }

        Class<?> currentClass = entityClass;
        while (currentClass != null && currentClass != Object.class) {
            for (Field field : currentClass.getDeclaredFields()) {
                if (!field.isAnnotationPresent(Id.class) && !field.isAnnotationPresent(
                        EmbeddedId.class) && !field.getName().equals("serialVersionUID")) {

                    AttributeMetadata attr = createAttributeMetadata(field, false, false);
                    attributes.add(attr);
                }
            }
            currentClass = currentClass.getSuperclass();
        }

        return attributes;
    }

    /**
     * Creates AttributeMetadata for a single field.
     */
    private AttributeMetadata createAttributeMetadata(Field field, boolean isId, boolean isEmbeddedId) {
        String columnName = extractColumnName(field);
        String jpaPath = field.getName();
        boolean nullable = isNullable(field);
        boolean hasDefaultValue = hasDefaultValue(field);
        String generationStrategy = extractGenerationStrategy(field);

        return AttributeMetadata.builder()
                .name(field.getName())
                .type(field.getType())
                .field(field)
                .isId(isId)
                .isEmbeddedId(isEmbeddedId)
                .jpaPath(jpaPath)
                .columnName(columnName)
                .snakeCaseName(StringUtils.camelToSnakeCase(field.getName()))
                .nullable(nullable)
                .hasDefaultValue(hasDefaultValue)
                .generationStrategy(generationStrategy)
                .build();
    }

    private String extractTableName(Class<?> entityClass) {
        Table tableAnnotation = entityClass.getAnnotation(Table.class);
        if (tableAnnotation != null && !tableAnnotation.name().isEmpty()) {
            return tableAnnotation.name();
        }

        Entity entityAnnotation = entityClass.getAnnotation(Entity.class);
        if (entityAnnotation != null && !entityAnnotation.name().isEmpty()) {
            return entityAnnotation.name();
        }

        return entityClass.getSimpleName().toLowerCase();
    }

    private Class<?> extractIdClass(Class<?> entityClass) {
        IdClass idClassAnnotation = entityClass.getAnnotation(IdClass.class);
        return idClassAnnotation != null ? idClassAnnotation.value() : null;
    }

    private Field findEmbeddedIdField(Class<?> entityClass) throws JpaException {
        for (Field field : entityClass.getDeclaredFields()) {
            if (field.isAnnotationPresent(EmbeddedId.class)) {
                return field;
            }
        }
        throw new JpaException("No @EmbeddedId field found in " + entityClass.getSimpleName());
    }

    private String extractColumnName(Field field) {
        Column columnAnnotation = field.getAnnotation(Column.class);
        if (columnAnnotation != null && !columnAnnotation.name().isEmpty()) {
            return columnAnnotation.name();
        }

        return field.getName();
    }

    private boolean isNullable(Field field) {
        Column columnAnnotation = field.getAnnotation(Column.class);
        return columnAnnotation == null || columnAnnotation.nullable();
    }

    private boolean hasDefaultValue(Field field) {
        Column columnAnnotation = field.getAnnotation(Column.class);
        return columnAnnotation != null &&
                !columnAnnotation.columnDefinition().isEmpty() &&
                columnAnnotation.columnDefinition().toLowerCase().contains("default");
    }

    private String extractGenerationStrategy(Field field) {
        GeneratedValue generatedValue = field.getAnnotation(GeneratedValue.class);
        return generatedValue != null ? generatedValue.strategy().name() : null;
    }

    private String determinePrimaryKeyFieldName(IdMetadata idMetadata) {
        if (idMetadata.getKeyType() == CompositeKeyUtils.CompositeKeyType.EMBEDDED_ID) {
            return idMetadata.getEmbeddedIdFieldName();
        } else {
            return idMetadata.getIdAttributes().get(0).getName();
        }
    }

}