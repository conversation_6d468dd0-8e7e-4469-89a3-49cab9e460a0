package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconIoLinkMember;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_io_link_member")
@ApplicationScoped
public class ElconIoLinkMemberRepository extends AbstractRestCRUDRepositoryImpl<ElconIoLinkMember, Long> {

    @Override
    protected boolean isNew(ElconIoLinkMember entity) {
        return entity.getMemberId() == null;
    }

    @Override
    public Class<ElconIoLinkMember> getDomainType() {
        return ElconIoLinkMember.class;
    }
} 