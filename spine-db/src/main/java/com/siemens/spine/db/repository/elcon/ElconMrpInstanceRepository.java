package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconMrpInstance;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_mrp_instance")
@ApplicationScoped
public class ElconMrpInstanceRepository extends AbstractRestCRUDRepositoryImpl<ElconMrpInstance, Long> {

    @Override
    protected boolean isNew(ElconMrpInstance entity) {
        return entity.getInstanceId() == null;
    }

    @Override
    public Class<ElconMrpInstance> getDomainType() {
        return ElconMrpInstance.class;
    }
} 