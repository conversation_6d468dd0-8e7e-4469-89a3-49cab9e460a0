package com.siemens.spine.db.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "neighbor_connection")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Audited
@IdClass(NeighborConnectionId.class)
public class NeighborConnectionEntity implements Serializable {

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "own_cp_id", referencedColumnName = "id")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private ConnectionPointEntity owner;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "neighbor_cp_id", referencedColumnName = "id")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private ConnectionPointEntity neighbor;

    @Column(name = "neighbor_assignment")
    private String assignment = "DEFAULT";

    @Column(name = "is_manual")
    private Boolean manual = false;

    private Double gap = 0.0;

}

