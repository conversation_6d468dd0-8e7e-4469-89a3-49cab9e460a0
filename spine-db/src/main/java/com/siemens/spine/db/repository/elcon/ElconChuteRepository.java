package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconChute;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_chute")
@ApplicationScoped
public class ElconChuteRepository extends AbstractRestCRUDRepositoryImpl<ElconChute, Long> {

    @Override
    protected boolean isNew(ElconChute entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconChute> getDomainType() {
        return ElconChute.class;
    }
} 