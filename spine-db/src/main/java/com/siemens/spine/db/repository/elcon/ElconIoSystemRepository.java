package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconIoSystem;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_io_system")
@ApplicationScoped
public class ElconIoSystemRepository extends AbstractRestCRUDRepositoryImpl<ElconIoSystem, Long> {

    @Override
    protected boolean isNew(ElconIoSystem entity) {
        return entity.getSystemId() == null;
    }

    @Override
    public Class<ElconIoSystem> getDomainType() {
        return ElconIoSystem.class;
    }
} 