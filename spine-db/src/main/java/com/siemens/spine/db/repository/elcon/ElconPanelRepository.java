package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPanel;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_panel")
@ApplicationScoped
public class ElconPanelRepository extends AbstractRestCRUDRepositoryImpl<ElconPanel, Long> {

    @Override
    protected boolean isNew(ElconPanel entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconPanel> getDomainType() {
        return ElconPanel.class;
    }
} 