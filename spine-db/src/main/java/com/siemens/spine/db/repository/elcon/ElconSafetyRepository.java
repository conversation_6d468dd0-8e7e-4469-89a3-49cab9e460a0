package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconSafety;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_safety")
@ApplicationScoped
public class ElconSafetyRepository extends AbstractRestCRUDRepositoryImpl<ElconSafety, Long> {

    @Override
    protected boolean isNew(ElconSafety entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconSafety> getDomainType() {
        return ElconSafety.class;
    }
} 