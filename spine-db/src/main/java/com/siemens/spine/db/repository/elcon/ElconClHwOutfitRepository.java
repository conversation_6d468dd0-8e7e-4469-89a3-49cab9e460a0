package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconClHwOutfit;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cl_hw_outfit")
@ApplicationScoped
public class ElconClHwOutfitRepository extends AbstractRestCRUDRepositoryImpl<ElconClHwOutfit, Long> {

    @Override
    protected boolean isNew(ElconClHwOutfit entity) {
        return entity.getOutfitId() == null;
    }

    @Override
    public Class<ElconClHwOutfit> getDomainType() {
        return ElconClHwOutfit.class;
    }
} 