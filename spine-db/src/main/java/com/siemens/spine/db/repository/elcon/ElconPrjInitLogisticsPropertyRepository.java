package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPrjInitLogisticsProperty;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_prj_init_logistics_property")
@ApplicationScoped
public class ElconPrjInitLogisticsPropertyRepository extends AbstractRestCRUDRepositoryImpl<ElconPrjInitLogisticsProperty, Long> {

    @Override
    protected boolean isNew(ElconPrjInitLogisticsProperty entity) {
        return entity.getCfgId() == null;
    }

    @Override
    public Class<ElconPrjInitLogisticsProperty> getDomainType() {
        return ElconPrjInitLogisticsProperty.class;
    }
} 