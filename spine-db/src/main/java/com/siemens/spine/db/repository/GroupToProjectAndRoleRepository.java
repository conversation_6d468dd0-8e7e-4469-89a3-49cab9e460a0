package com.siemens.spine.db.repository;

import com.siemens.spine.db.entity.GroupToProjectAndRoleEntity;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 26/12/2022
 */
public interface GroupToProjectAndRoleRepository extends GenericJpaRepository<GroupToProjectAndRoleEntity, Long> {

    List<GroupToProjectAndRoleEntity> findProjectRoleOfUser(Long projectId, String user);

    List<GroupToProjectAndRoleEntity> findAllProjectsByProjectsId(Long projectId);

    Optional<GroupToProjectAndRoleEntity> findByProjectIdAndUserGroup(Long projectId, String userGroup);

    List<String> findAllUserGroupsByRole(String role);

    List<String> findRoleByProjectAndUserGroup(String projectName, List<String> userGroups);

    List<String> findRoleByProjectIdAndUserGroup(Long projectId, List<String> userGroups);

}

