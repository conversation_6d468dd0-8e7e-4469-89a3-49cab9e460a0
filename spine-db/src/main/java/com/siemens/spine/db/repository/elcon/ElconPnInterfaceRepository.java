package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPnInterface;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_pn_interface")
@ApplicationScoped
public class ElconPnInterfaceRepository extends AbstractRestCRUDRepositoryImpl<ElconPnInterface, Long> {

    @Override
    protected boolean isNew(ElconPnInterface entity) {
        return entity.getInterfaceId() == null;
    }

    @Override
    public Class<ElconPnInterface> getDomainType() {
        return ElconPnInterface.class;
    }
} 