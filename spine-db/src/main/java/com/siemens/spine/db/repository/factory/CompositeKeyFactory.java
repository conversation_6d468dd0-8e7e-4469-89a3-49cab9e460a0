package com.siemens.spine.db.repository.factory;

import com.siemens.spine.db.exception.JpaException;
import com.siemens.spine.db.utils.CompositeKeyUtils;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;

/**
 * Factory interface for creating and managing composite key operations.
 * Implements the Factory pattern to handle different composite key strategies
 * based on JPA annotation types (Single @Id, Multiple @Id, @IdClass, @EmbeddedId).
 *
 * <AUTHOR> <PERSON>am
 * @since 1.0
 */
public interface CompositeKeyFactory {

    /**
     * Determines if this factory can handle the given entity class.
     *
     * @param entityClass The entity class to check
     * @return true if this factory can handle the entity class
     */
    boolean canHandle(Class<?> entityClass);

    /**
     * Gets the composite key type that this factory handles.
     *
     * @return The composite key type
     */
    CompositeKeyUtils.CompositeKeyType getKeyType();

    /**
     * Extracts ID fields from the entity class using the strategy
     * appropriate for this factory's key type.
     *
     * @param entityClass The entity class
     * @return List of ID fields
     */
    List<Field> extractIdFields(Class<?> entityClass);

    /**
     * Creates composite key data from a key-value mapping string.
     *
     * @param compositeKeyStr The composite key string with explicit mapping
     * @param entityClass     The entity class
     * @return CompositeKeyData containing field names and converted values
     * @throws JpaException if creation fails
     */
    CompositeKeyUtils.CompositeKeyData createKeyData(String compositeKeyStr, Class<?> entityClass) throws JpaException;

    /**
     * Creates composite key data from a key-value map.
     *
     * @param keyValueMap The map of key names to values
     * @param entityClass The entity class
     * @return CompositeKeyData containing field names and converted values
     * @throws JpaException if creation fails
     */
    CompositeKeyUtils.CompositeKeyData createKeyDataFromMap(Map<String, String> keyValueMap, Class<?> entityClass)
            throws JpaException;

    /**
     * Validates that the composite key configuration is correct for this factory's strategy.
     *
     * @param entityClass The entity class to validate
     * @throws JpaException if validation fails
     */
    void validateKeyConfiguration(Class<?> entityClass) throws JpaException;

    /**
     * Determines the priority of this factory. Lower numbers have higher priority.
     * Used when multiple factories might be able to handle the same entity class.
     *
     * @return The priority value (lower = higher priority)
     */
    default int getPriority() {
        return 100;
    }

}