package com.siemens.spine.db.repository;

import com.siemens.spine.db.constant.GroupTypeEnum;
import com.siemens.spine.db.entity.GroupEntity;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 26/12/2022
 */
public interface GroupRepository
        extends GenericJpaRepository<GroupEntity, Long>, GenericJpaAuditRepository<GroupEntity, Long> {

    List<GroupEntity> findGroupsByProjectNameAndGroupNameAndGroupType(String projectName,
                                                                      List<String> groupName,
                                                                      GroupTypeEnum groupType);

    List<GroupEntity> findGroupsByProjectId(Long projectId);

    List<GroupEntity> findComponentGroup(Long componentId);

    List<GroupEntity> findGroupsByProjectIdAndGroupIdIn(Long projectId, List<Long> groupIds);

    List<GroupEntity> findAllGroupsByTypeAndNameAndProjectId(Map<GroupTypeEnum, String> pairs, Long projectId);

    List<GroupEntity> findSubGroupsByProjectIdAndGroupNameAndGroupType(Long projectId,
                                                                       String groupName,
                                                                       GroupTypeEnum subGroupType);

    List<GroupEntity> findGroupsNotEmpty(List<Long> ids);

    Optional<GroupEntity> getHistoryAtRevisions(Long groupId, Long revisionId);

}

