package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCfgExtensionAutoGeneration;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cfg_extension_auto_generation")
@ApplicationScoped
public class ElconCfgExtensionAutoGenerationRepository extends AbstractRestCRUDRepositoryImpl<ElconCfgExtensionAutoGeneration, Long> {

    @Override
    protected boolean isNew(ElconCfgExtensionAutoGeneration entity) {
        return entity.getCfgId() == null;
    }

    @Override
    public Class<ElconCfgExtensionAutoGeneration> getDomainType() {
        return ElconCfgExtensionAutoGeneration.class;
    }
} 