package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconSafetyZone;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_safety_zone")
@ApplicationScoped
public class ElconSafetyZoneRepository extends AbstractRestCRUDRepositoryImpl<ElconSafetyZone, Long> {

    @Override
    protected boolean isNew(ElconSafetyZone entity) {
        return entity.getZoneId() == null;
    }

    @Override
    public Class<ElconSafetyZone> getDomainType() {
        return ElconSafetyZone.class;
    }
} 