package com.siemens.spine.db.repository.views;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter

public class ComponentChangeGroupView {

    private String changeGroupName;
    private String changeGroupDescription;
    private Long componentId;

    public ComponentChangeGroupView(String changeGroupName, String changeGroupDescription, Long componentId) {
        this.changeGroupName = changeGroupName;
        this.changeGroupDescription = changeGroupDescription;
        this.componentId = componentId;
    }

    @Override
    public String toString() {
        return componentId + "_" + changeGroupName + " " + changeGroupDescription;
    }

}

