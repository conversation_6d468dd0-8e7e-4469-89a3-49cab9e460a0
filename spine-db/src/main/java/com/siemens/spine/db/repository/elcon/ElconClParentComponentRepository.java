package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconClParentComponent;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cl_parent_component")
@ApplicationScoped
public class ElconClParentComponentRepository extends AbstractRestCRUDRepositoryImpl<ElconClParentComponent, Long> {

    @Override
    protected boolean isNew(ElconClParentComponent entity) {
        return entity.getComponentId() == null;
    }

    @Override
    public Class<ElconClParentComponent> getDomainType() {
        return ElconClParentComponent.class;
    }
} 