package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.OperationToRoleEntity;
import com.siemens.spine.db.repository.OperationToRoleRepository;

import javax.enterprise.context.ApplicationScoped;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 7/3/2023
 */
@ApplicationScoped
@Transactional(TxType.REQUIRED)
public class OperationToRoleRepositoryImpl extends
        GenericJpaRepositoryImpl<OperationToRoleEntity, Long> implements
        OperationToRoleRepository {

    @Override
    public Class<OperationToRoleEntity> getDomainType() {
        return OperationToRoleEntity.class;
    }

}

