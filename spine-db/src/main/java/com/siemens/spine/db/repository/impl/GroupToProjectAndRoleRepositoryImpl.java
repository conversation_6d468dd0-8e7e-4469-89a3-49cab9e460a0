package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.GroupToProjectAndRoleEntity;
import com.siemens.spine.db.repository.GroupToProjectAndRoleRepository;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.NoResultException;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> <PERSON>am
 * @version 1.0
 * @since 26/12/2022
 */
@ApplicationScoped
@Transactional(TxType.REQUIRED)
public class GroupToProjectAndRoleRepositoryImpl extends
        GenericJpaRepositoryImpl<GroupToProjectAndRoleEntity, Long> implements
        GroupToProjectAndRoleRepository {

    @Override
    public Class<GroupToProjectAndRoleEntity> getDomainType() {
        return GroupToProjectAndRoleEntity.class;
    }

    @Override
    public List<GroupToProjectAndRoleEntity> findProjectRoleOfUser(Long projectId, String user) {
        String query = "select r from GroupToProjectAndRoleEntity r " +
                "join UserToGroupEntity g on g.userGroup = r.userGroup " +
                "where g.user = :user and r.project.projectID = :projectId";

        return entityManager.createQuery(query, GroupToProjectAndRoleEntity.class)
                .setParameter("user", user)
                .setParameter("projectId", projectId)
                .getResultList();
    }

    @Override
    public List<GroupToProjectAndRoleEntity> findAllProjectsByProjectsId(Long projectId) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<GroupToProjectAndRoleEntity> cq = cb.createQuery(GroupToProjectAndRoleEntity.class);
        Root<GroupToProjectAndRoleEntity> rootEntry = cq.from(GroupToProjectAndRoleEntity.class);
        CriteriaQuery<GroupToProjectAndRoleEntity> query = cq.select(rootEntry)
                .where(cb.equal(rootEntry.get("project").get("projectID"), projectId));
        List<GroupToProjectAndRoleEntity> groupEntities = entityManager.createQuery(query)
                .getResultList();
        return (groupEntities == null) ? Collections.emptyList() : groupEntities;
    }

    @Override
    public Optional<GroupToProjectAndRoleEntity> findByProjectIdAndUserGroup(Long projectId, String userGroup) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<GroupToProjectAndRoleEntity> cq = cb.createQuery(GroupToProjectAndRoleEntity.class);
        Root<GroupToProjectAndRoleEntity> rootEntry = cq.from(GroupToProjectAndRoleEntity.class);
        CriteriaQuery<GroupToProjectAndRoleEntity> query = cq.select(rootEntry)
                .where(cb.equal(rootEntry.get("project").get("projectID"), projectId),
                        cb.equal(rootEntry.get("userGroup"), userGroup));
        try {
            return Optional.ofNullable(entityManager.createQuery(query).getSingleResult());
        } catch (NoResultException ex) {
            return Optional.empty();
        }
    }

    @Override
    public List<String> findAllUserGroupsByRole(String role) {
        return entityManager.createQuery("select distinct g.userGroup" +
                        " from GroupToProjectAndRoleEntity g" +
                        " where g.roleName = :role", String.class)
                .setParameter("role", role)
                .getResultList();
    }

    @Override
    public List<String> findRoleByProjectAndUserGroup(String projectName, List<String> userGroups) {
        return entityManager.createQuery("select distinct g.roleName" +
                        " from GroupToProjectAndRoleEntity g" +
                        " where g.project.projectName = :projectName and g.userGroup in (:userGroups)", String.class)
                .setParameter("projectName", projectName)
                .setParameter("userGroups", userGroups)
                .getResultList();
    }

    @Override
    public List<String> findRoleByProjectIdAndUserGroup(Long projectId, List<String> userGroups) {
        return entityManager.createQuery("select distinct g.roleName" +
                        " from GroupToProjectAndRoleEntity g" +
                        " where g.project.projectID = :projectId and g.userGroup in (:userGroups)", String.class)
                .setParameter("projectId", projectId)
                .setParameter("userGroups", userGroups)
                .getResultList();
    }

}

