package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.ComponentStatusEntity;
import com.siemens.spine.db.repository.ComponentStatusRepository;
import com.siemens.spine.db.repository.ProjectRepository;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2/7/2023
 */

@ApplicationScoped
@Transactional(TxType.REQUIRED)
public class ComponentStatusRepositoryImpl extends
        GenericJpaRepositoryImpl<ComponentStatusEntity, Long> implements ComponentStatusRepository {

    @Inject
    private ProjectRepository projectRepository;

    @Override
    public Class<ComponentStatusEntity> getDomainType() {
        return ComponentStatusEntity.class;
    }

    @Override
    public List<ComponentStatusEntity> find(String projectName, List<Long> componentIds) {
        Long projectId;
        if (StringUtils.isNotEmpty(projectName)) {
            projectId = projectRepository.findIdByName(projectName);
        } else {
            return Collections.EMPTY_LIST;
        }
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<ComponentStatusEntity> query = criteriaBuilder.createQuery(ComponentStatusEntity.class);
        Root<ComponentStatusEntity> rootComponent = query.from(ComponentStatusEntity.class);
        rootComponent.fetch("component", JoinType.LEFT);

        List<Predicate> conditions = new ArrayList<>();
        if (!StringUtils.isEmpty(projectName)) {
            conditions.add(
                    criteriaBuilder.equal(rootComponent.get("component").get("projectId"), projectId)
            );
        }

        if (componentIds != null && !componentIds.isEmpty()) {
            conditions.add(
                    rootComponent.get("component").get("id").in(componentIds)
            );
        }

        query.select(rootComponent).where(conditions.toArray(new Predicate[0]));
        return entityManager.createQuery(query).getResultList();
    }

    @Override
    public int updateStateByOperation(String operation, List<Long> uniqueIds, Integer state, Date date) {
        String qlString = "update ComponentStatusEntity cs ";
        String condition = " where cs.id in (:uniqueIds) ";
        String update = null;
        switch (operation) {
            case "BOM":
                update = " set cs.bomState = :state," +
                        "     cs.bomStateDate = :date ";
                break;
            case "EMULATION":
                update = " set cs.emulationState = :state," +
                        "     cs.emulationStateDate = :date ";
                break;
            case "IT":
                update = " set cs.itState = :state," +
                        "     cs.itStateDate = :date ";
                break;
            case "SIMULATION":
                update = " set cs.simulationState = :state," +
                        "     cs.simulationStateDate = :date," +
                        "     cs.simulationComponentLastModDate = :date ";
                break;
            case "CALCULATION":
                update = " set cs.calculationState = :state," +
                        "     cs.calculationStateDate = :date ";
                break;
            case "OBJECTXML":
                update = " set cs.objectXmlExportState = :state," +
                        "     cs.objectXmlExportDate = :date ";
                break;
            case "ELECTRIC":
                update = " set cs.electricSynchronizationState = :state," +
                        "     cs.electricSynchronizationDate = :date ";
                break;
            default:
                break;
        }
        int cnt = entityManager.createQuery(qlString + update + condition)
                .setParameter("uniqueIds", uniqueIds)
                .setParameter("state", state)
                .setParameter("date", date)
                .executeUpdate();
        entityManager.flush();
        return cnt;
    }

    @Override
    public int updateMarkAsDoneBom(Long uniqueId, String sapId, Integer state, Date date) {
        int cnt = entityManager.createQuery("update ComponentStatusEntity cs " +
                        " set cs.bomState = :state," +
                        "      cs.bomStateDate = :date," +
                        "   cs.sapID = :sapId" +
                        " where cs.id = :id")
                .setParameter("id", uniqueId)
                .setParameter("state", state)
                .setParameter("date", date)
                .setParameter("sapId", sapId)
                .executeUpdate();
        entityManager.flush();
        return cnt;
    }

    @Override
    public List<ComponentStatusEntity> findChangeComponentStatus(List<Long> componentIds) {
        return entityManager.createQuery("select cs " +
                                " from ComponentStatusEntity cs left join fetch cs.component c" +
                                " where cs.component.id in (:componentIds) and (cs.bomState is null or cs.bomState = 20 and cs.bomStateDate < c.sysModDate)",
                        ComponentStatusEntity.class)
                .setParameter("componentIds", componentIds)
                .getResultList();
    }

    @Override
    public Optional<ComponentStatusEntity> findById(Long id) {

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<ComponentStatusEntity> cq = cb.createQuery(ComponentStatusEntity.class);
        Root<ComponentStatusEntity> root = cq.from(ComponentStatusEntity.class);
        CriteriaQuery<ComponentStatusEntity> query = cq.select(root)
                .where(cb.equal(root.get("component").get("id"), id));
        List<ComponentStatusEntity> groupEntities = entityManager.createQuery(query)
                .getResultList();
        return (groupEntities == null) ? null : Optional.ofNullable(groupEntities.get(0));

    }

}
