package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_plc_setup")
public class ElconPlcSetup {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "setup_id", nullable = false)
    private Long setupId;

    @NotNull
    @Column(name = "device_id", nullable = false)
    private Long deviceId;

    @Column(name = "communication_load")
    private Short communicationLoad;

    @Size(max = 255)
    @Column(name = "system_memory_address")
    private String systemMemoryAddress;

    @Size(max = 255)
    @Column(name = "clock_memory_address")
    private String clockMemoryAddress;

    @Column(name = "timezone")
    private Short timezone;

    @Column(name = "daylight_saving_flag")
    private Short daylightSavingFlag;

}