package com.siemens.spine.db.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 3/4/2023
 */
@Entity
@Table(name = "project_view_group_mapping")
public class ProjectViewGroupMappingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "project_views_group_mapping_id_seq_gen")
    @SequenceGenerator(name = "project_views_group_mapping_id_seq_gen",
            sequenceName = "project_views_group_mapping_id_seq")
    private Long id;
    private String name;
    @Column(name = "sub_group_name")
    private String subGroupName;
    @Column(name = "project_id")
    private Long projectId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSubGroupName() {
        return subGroupName;
    }

    public void setSubGroupName(String subGroupName) {
        this.subGroupName = subGroupName;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

}

