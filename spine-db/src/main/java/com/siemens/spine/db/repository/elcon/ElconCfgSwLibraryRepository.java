package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCfgSwLibrary;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cfg_sw_library")
@ApplicationScoped
public class ElconCfgSwLibraryRepository extends AbstractRestCRUDRepositoryImpl<ElconCfgSwLibrary, Long> {

    @Override
    protected boolean isNew(ElconCfgSwLibrary entity) {
        return entity.getSwLibraryId() == null;
    }

    @Override
    public Class<ElconCfgSwLibrary> getDomainType() {
        return ElconCfgSwLibrary.class;
    }
} 