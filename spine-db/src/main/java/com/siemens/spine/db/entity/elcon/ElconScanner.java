package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MapsId;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_scanner")
public class ElconScanner {

    @Id
    @Column(name = "object_id", nullable = false)
    private Long id;

    @MapsId
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "object_id", nullable = false)
    private ElconAddressMapping elconAddressMapping;

    @Size(max = 15)
    @NotNull
    @Column(name = "scanner_type", nullable = false, length = 15)
    private String scannerType;

    @Size(max = 15)
    @NotNull
    @Column(name = "dest_req_point_unit_name", nullable = false, length = 15)
    private String destReqPointUnitName;

    @Column(name = "adr_do_trigger")
    private Long adrDoTrigger;

    @Column(name = "adr_do_sort_pulse")
    private Long adrDoSortPulse;

    @Column(name = "adr_di_controller_ok")
    private Long adrDiControllerOk;

    @Column(name = "adr_di_sensor")
    private Long adrDiSensor;

    @NotNull
    @Column(name = "connection_type", nullable = false)
    private Integer connectionType;

    @NotNull
    @Column(name = "active_est", nullable = false)
    private Short activeEst;

    @NotNull
    @Column(name = "local_port", nullable = false)
    private Integer localPort;

    @NotNull
    @Column(name = "remote_port", nullable = false)
    private Integer remotePort;

}