package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_mrp_instance")
public class ElconMrpInstance {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "instance_id", nullable = false)
    private Long instanceId;

    @NotNull
    @Column(name = "interface_id", nullable = false)
    private Long interfaceId;

    @NotNull
    @Column(name = "domain_id", nullable = false)
    private Long domainId;

    @NotNull
    @Column(name = "ring_port_src", nullable = false)
    private Long ringPortSrc;

    @Column(name = "ring_port_dest")
    private Long ringPortDest;

    @Size(max = 255)
    @Column(name = "mrp_role")
    private String mrpRole;

}