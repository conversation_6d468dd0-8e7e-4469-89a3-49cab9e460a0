package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_io_system_connection")
public class ElconIoSystemConnection {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "connection_id", nullable = false)
    private Long connectionId;

    @NotNull
    @Column(name = "interface_id", nullable = false)
    private Long interfaceId;

    @NotNull
    @Column(name = "io_system_id", nullable = false)
    private Long ioSystemId;

    @Size(max = 10)
    @NotNull
    @Column(name = "io_system_role", nullable = false, length = 10)
    private String ioSystemRole;

}