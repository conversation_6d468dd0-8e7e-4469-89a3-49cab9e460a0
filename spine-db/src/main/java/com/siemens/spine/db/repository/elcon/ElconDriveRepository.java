package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconDrive;

import javax.enterprise.context.ApplicationScoped;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 19/6/2025
 **/
@RestRepository(path = "elcon_drive")
@ApplicationScoped
public class ElconDriveRepository extends AbstractRestCRUDRepositoryImpl<ElconDrive, Long> {

    @Override
    protected boolean isNew(ElconDrive entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconDrive> getDomainType() {
        return ElconDrive.class;
    }
}
