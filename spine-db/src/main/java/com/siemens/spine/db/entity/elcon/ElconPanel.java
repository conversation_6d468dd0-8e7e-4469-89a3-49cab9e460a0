package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MapsId;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_panel")
public class ElconPanel {

    @Id
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @MapsId
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "object_id", nullable = false)
    private ElconObject elconObject;

    @Size(max = 255)
    @Column(name = "panel_type")
    private String panelType;

    @Column(name = "asi_panel_id")
    private Short asiPanelId;

    @Column(name = "pn_panel_id")
    private Short pnPanelId;

    @Column(name = "estop_panel_id")
    private Short estopPanelId;

    @Column(name = "power_panel_id")
    private Short powerPanelId;

}