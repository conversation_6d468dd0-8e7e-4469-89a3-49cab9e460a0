package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.ConnectionPointEntity;
import com.siemens.spine.db.entity.StateEntity;
import com.siemens.spine.db.repository.ConnectionPointRepository;
import org.apache.commons.lang3.tuple.Pair;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaDelete;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@ApplicationScoped
public class ConnectionPointRepositoryImpl extends GenericJpaRepositoryImpl<ConnectionPointEntity, Long>
        implements ConnectionPointRepository {

    @Override
    public Class<ConnectionPointEntity> getDomainType() {
        return ConnectionPointEntity.class;
    }

    @Override
    public long deleteByComponents(List<Long> componentIds) {
        if (componentIds == null || componentIds.isEmpty()) {
            return 0L;
        }

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaDelete<StateEntity> cq = cb.createCriteriaDelete(StateEntity.class);
        Root<StateEntity> rootEntry = cq.from(StateEntity.class);

        CriteriaDelete<StateEntity> query = cq.where(
                rootEntry.get("component").get("id").in(componentIds)
        );

        return entityManager.createQuery(query).executeUpdate();
    }

    @Override
    public List<ConnectionPointEntity> findByComponentId(Long componentId) {
        return entityManager.createQuery(
                        "select d from ConnectionPointEntity d where d.component.id = :componentId",
                        ConnectionPointEntity.class)
                .setParameter("componentId", componentId)
                .getResultList();
    }

    @Override
    public List<ConnectionPointEntity> findByComponentIdAndName(List<Pair<String, String>> connectionPointConditions) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<ConnectionPointEntity> cq = cb.createQuery(ConnectionPointEntity.class);
        Root<ConnectionPointEntity> rootEntry = cq.from(ConnectionPointEntity.class);
        List<Predicate> finalPredicates = new ArrayList<>();
        for (Pair<String, String> connectionPointCondition : connectionPointConditions) {
            Predicate connectionPoint = cb.and(
                    cb.equal(rootEntry.get("component").get("id"), connectionPointCondition.getLeft()),
                    cb.equal(rootEntry.get("name"), connectionPointCondition.getRight()));
            finalPredicates.add(connectionPoint);
        }
        cq.where(cb.or(finalPredicates.toArray(new Predicate[0])));
        return entityManager.createQuery(cq).getResultList();
    }

}

