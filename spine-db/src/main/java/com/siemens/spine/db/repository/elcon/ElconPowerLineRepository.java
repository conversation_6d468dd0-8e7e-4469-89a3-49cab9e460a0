package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPowerLine;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_power_line")
@ApplicationScoped
public class ElconPowerLineRepository extends AbstractRestCRUDRepositoryImpl<ElconPowerLine, Long> {

    @Override
    protected boolean isNew(ElconPowerLine entity) {
        return entity.getLineId() == null;
    }

    @Override
    public Class<ElconPowerLine> getDomainType() {
        return ElconPowerLine.class;
    }
} 