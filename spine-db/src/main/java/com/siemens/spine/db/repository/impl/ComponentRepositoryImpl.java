package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.constant.GroupTypeEnum;
import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.ComponentIdRev;
import com.siemens.spine.db.entity.ComponentStatusEntity;
import com.siemens.spine.db.entity.ConnectionPointEntity;
import com.siemens.spine.db.entity.DecompositionEntity;
import com.siemens.spine.db.entity.GroupEntity;
import com.siemens.spine.db.entity.PositionEntity;
import com.siemens.spine.db.exception.JpaException;
import com.siemens.spine.db.repository.ComponentRepository;
import com.siemens.spine.db.repository.ProjectRepository;
import com.siemens.spine.db.repository.filter.ComponentFilter;
import com.siemens.spine.db.repository.views.ComponentView;
import com.siemens.spine.db.repository.views.GroupView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.hibernate.annotations.QueryHints;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.query.AuditEntity;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.From;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@ApplicationScoped
@Transactional(TxType.REQUIRED)
@Slf4j
public class ComponentRepositoryImpl extends
        GenericJpaAuditRepositoryImpl<ComponentEntity, Long> implements ComponentRepository {

    private static final String LIKE_SPEC = "like";

    private static final String PERCENT_SIGN = "%";

    private final ProjectRepository projectRepository;

    @Inject
    public ComponentRepositoryImpl(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    @Override
    public Class<ComponentEntity> getDomainType() {
        return ComponentEntity.class;
    }

    @Override
    public Optional<ComponentEntity> getHistoryAtRevision(Long id, int revision) {
        if (revision == 0) {
            log.info("Revision is 0, return current component with id {}", id);
            return findById(id);
        }

        log.info("Get component at revision {} with id {}", revision, id);
        ComponentEntity component = getAuditReader().find(ComponentEntity.class, id, revision);

        if (component != null) {
            PositionEntity positionAtRevision = getPositionAtRevision(id, revision);
            ComponentStatusEntity statusAtRevision = getStatusAtRevision(id, revision);

            component.setStatus(statusAtRevision);
            component.setPosition(positionAtRevision);
        }
        return Optional.ofNullable(component);
    }

    @Override
    public List<ComponentEntity> filter(ComponentFilter filter) {
        if (StringUtils.isNotEmpty(filter.getSpecificProjectName())) {
            filter.setProjectId(projectRepository.findIdByName(filter.getSpecificProjectName()));
        }
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<ComponentEntity> componentQuery = criteriaBuilder.createQuery(
                ComponentEntity.class);
        Root<ComponentEntity> rootComponent = componentQuery.from(ComponentEntity.class);

        // load the status by default to avoid invoke database multiple time when converting to DTO
        rootComponent.fetch("status", JoinType.LEFT);
        rootComponent.fetch("position", JoinType.LEFT);

        List<Predicate> predicates = buildWhereClause(filter, criteriaBuilder, rootComponent);
        componentQuery.select(rootComponent)
                .where(predicates.toArray(new Predicate[0]));

        return entityManager.createQuery(componentQuery).getResultList();
    }

    @Override
    public List<Long> findIdsByProjectNameAndCondition(String projectName,
                                                       Map<String, List<String>> conditions,
                                                       int start, int end) throws JpaException {
        Long projectId = projectRepository.findIdByName(projectName);
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> componentQuery = criteriaBuilder.createQuery(Long.class);
        Root<ComponentEntity> component = componentQuery.from(ComponentEntity.class);

        List<Predicate> conditionsList = new ArrayList<>();

        // projectId
        Predicate condition = criteriaBuilder.equal(component.get("projectId"), projectId);
        conditionsList.add(condition);

        if (MapUtils.isNotEmpty(conditions)) {
            for (Map.Entry<String, List<String>> conditionEntry : conditions.entrySet()) {
                String path = conditionEntry.getKey().toLowerCase();
                if (path.contains(LIKE_SPEC)) {
                    likeSpecificationBuilder(criteriaBuilder, component, conditionsList, conditionEntry,
                            path);
                }
            }
        }

        // query builder
        componentQuery.select(component.get("id"))
                .distinct(true)
                .where(conditionsList.toArray(new Predicate[] {}))
                .orderBy(criteriaBuilder.asc(component.get("id")));

        return entityManager.createQuery(componentQuery)
                .setFirstResult(start - 1)
                .setMaxResults(end - start)
                .getResultList();
    }

    @Override
    public List<ComponentEntity> findByProjectNameAndComponentIds(String projectName,
                                                                  List<Long> componentIds) {
        Long projectId = projectRepository.findIdByName(projectName);
        if (projectId == null) {
            return Collections.emptyList();
        }
        List<ComponentEntity> components = entityManager.createQuery(
                        "select distinct c from ComponentEntity c " +
                                " left join fetch c.status s " +
                                " left join fetch c.position p " +
                                " left join fetch c.groups g " +
                                " where c.projectId = :projectId and c.id in :componentIds order by c.id asc",
                        ComponentEntity.class)
                .setParameter("projectId", projectId)
                .setParameter("componentIds", componentIds)
                .setHint(QueryHints.PASS_DISTINCT_THROUGH, false)
                .getResultList();

        components = entityManager.createQuery(
                        "select distinct c from ComponentEntity c " +
                                " left join fetch c.connectionPoints d " +
                                " where c in :components order by c.id asc ",
                        ComponentEntity.class)
                .setParameter("components", components)
                .setHint(QueryHints.PASS_DISTINCT_THROUGH, true)
                .getResultList();

        components = entityManager.createQuery(
                        "select distinct c from ComponentEntity c " +
                                " left join fetch c.decompositions d " +
                                " where c in :components order by c.id asc ",
                        ComponentEntity.class)
                .setParameter("components", components)
                .setHint(QueryHints.PASS_DISTINCT_THROUGH, true)
                .getResultList();
        return components;
    }

    @Override
    public List<Long> findIdsByProjectNameAndGroupTypeAndGroupIds(String projectName,
                                                                  GroupTypeEnum groupType,
                                                                  List<String> groupNames) {
        Long projectId = projectRepository.findIdByName(projectName);
        if (projectId == null) {
            return Collections.emptyList();
        }
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> componentQuery = criteriaBuilder.createQuery(Long.class);
        Root<ComponentEntity> componentRoot = componentQuery.from(ComponentEntity.class);

        ComponentFilter filter = ComponentFilter.builder()
                .projectId(projectId)
                .groupMapping(Pair.of(groupType, groupNames))
                .build();
        List<Predicate> conditionsList = buildWhereClause(filter, criteriaBuilder, componentRoot);
        componentQuery.select(componentRoot.get("id"))
                .where(conditionsList.toArray(new Predicate[] {}));

        return entityManager.createQuery(componentQuery)
                .getResultList();
    }

    @Override
    public List<ComponentEntity> findByProjectIdAndNotInExecutionPhase(Long projectId) {
        if (projectId == null) {
            return Collections.emptyList();
        }
        TypedQuery<ComponentEntity> query = entityManager.createQuery(
                "select c from ComponentEntity c where c.projectId = :projectId and c.currentState < 100",
                ComponentEntity.class);
        query.setParameter("projectId", projectId);
        return query.getResultList();
    }

    @Override
    public List<Long> findIdsByProject(String projectName) {
        Long projectId = projectRepository.findIdByName(projectName);
        if (projectId == null) {
            return Collections.emptyList();
        }
        return entityManager.createQuery("select c.id" +
                        " from ComponentEntity c" +
                        " where c.projectId = :projectId", Long.class)
                .setParameter("projectId", projectId)
                .getResultList();
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<ComponentView> findAllByProjectId(Long projectId) {
        String query =
                """
                        WITH component_data AS (
                             SELECT
                                 c.id as id,
                                 c.currentState,
                                 c.comment as comment,
                                 c.typeid,
                                 c.posno,
                                 c.colorgroup,
                                 c.lengthtotal,
                                 c.width,
                                 c.speed,
                                 c.load,
                                 c.throughput,
                                 c.motorcontroller,
                                 c.unitreversible,
                                 c.plantdomain,
                                 c.amountbufferelec,
                                 c.amountbuffermech,
                                 c.buffersize,
                                 c.driveposition,
                                 c.motordirection,
                                 c.curveangle,
                                 c.curvedirection,
                                 c.curveradius,
                                 c.breaktype,
                                 c.section1angle,
                                 c.section1length,
                                 c.section2angle,
                                 c.section2length,
                                 c.section3angle,
                                 c.section3length,
                                 c.section4angle,
                                 c.section4length,
                                 c.reference,
                                 c.rotation,
                                 c.rotation3d,
                                 c.sysmoddate,
                                 c.syscreatedate,
                                 cs.bomstate
                             FROM
                                 component c
                             LEFT JOIN componentstatus cs ON
                                 c.id = cs.uniqueid
                             WHERE
                                 c.projectid = :projectId
                        ),
                         ranked_groups AS (
                             SELECT
                                 cg.componentid,
                                 g.grouptype,
                                 g.name AS group_name,
                                 ROW_NUMBER() OVER (
                                     PARTITION BY cg.componentid,
                                     g.grouptype
                                 ORDER BY
                                     g.id DESC
                                 ) AS rn
                             FROM
                                 groupcomponent cg
                             JOIN "group" g ON
                                 cg.groupid = g.id
                             JOIN component c ON
                                 c.id = cg.componentid
                             WHERE
                                 c.projectid = :projectId
                         )
                         SELECT
                             cd.*,
                             MAX(CASE WHEN rg.grouptype = 'BDS' AND rg.rn = 1 THEN rg.group_name END) AS bds,
                             MAX(CASE WHEN rg.grouptype = 'CAL' AND rg.rn = 1 THEN rg.group_name END) AS cal,
                             MAX(CASE WHEN rg.grouptype = 'DEB' AND rg.rn = 1 THEN rg.group_name END) AS deb,
                             MAX(CASE WHEN rg.grouptype = 'DRA' AND rg.rn = 1 THEN rg.group_name END) AS dra,
                             MAX(CASE WHEN rg.grouptype = 'EST' AND rg.rn = 1 THEN rg.group_name END) AS est,
                             MAX(CASE WHEN rg.grouptype = 'INS' AND rg.rn = 1 THEN rg.group_name END) AS ins,
                             MAX(CASE WHEN rg.grouptype = 'LIN' AND rg.rn = 1 THEN rg.group_name END) AS lin,
                             MAX(CASE WHEN rg.grouptype = 'PLC' AND rg.rn = 1 THEN rg.group_name END) AS plc,
                             MAX(CASE WHEN rg.grouptype = 'SCR' AND rg.rn = 1 THEN rg.group_name END) AS scr,
                             MAX(CASE WHEN rg.grouptype = 'SEQ' AND rg.rn = 1 THEN rg.group_name END) AS seq
                         FROM
                             component_data cd
                         LEFT JOIN ranked_groups rg ON
                             cd.id = rg.componentid
                         GROUP BY
                             cd.id,
                             cd.currentState,
                             cd.comment,
                             cd.typeid,
                             cd.posno,
                             cd.colorgroup,
                             cd.lengthtotal,
                             cd.width,
                             cd.speed,
                             cd.load,
                             cd.throughput,
                             cd.motorcontroller,
                             cd.unitreversible,
                             cd.plantdomain,
                             cd.amountbufferelec,
                             cd.amountbuffermech,
                             cd.buffersize,
                             cd.driveposition,
                             cd.motordirection,
                             cd.curveangle,
                             cd.curvedirection,
                             cd.curveradius,
                             cd.breaktype,
                             cd.section1angle,
                             cd.section1length,
                             cd.section2angle,
                             cd.section2length,
                             cd.section3angle,
                             cd.section3length,
                             cd.section4angle,
                             cd.section4length,
                             cd.reference,
                             cd.rotation,
                             cd.rotation3d,
                             cd.sysmoddate,
                             cd.syscreatedate,
                             cd.bomstate
                          ORDER BY
                             cd.id
                        """;
        List<Tuple> componentViews = entityManager.createNativeQuery(query, Tuple.class)
                .setParameter("projectId", projectId)
                .setHint(QueryHints.PASS_DISTINCT_THROUGH, false)
                .getResultList();
        return componentViews.stream().map(this::toComponentView).toList();
    }

    @Override
    public int updateStateComponentByIds(List<Long> ids, Integer state) {
        int cnt = entityManager.createQuery("update ComponentEntity c " +
                        "   set c.currentState = :state" +
                        "   where c.id in (:ids)")
                .setParameter("ids", ids)
                .setParameter("state", state)
                .executeUpdate();
        entityManager.flush();
        return cnt;
    }

    @Override
    @Transactional
    public List<ComponentEntity> findByProjectIdAndVersionId(Long projectId, Long rev) {
        if (projectId == null || rev == null) {
            return Collections.emptyList();
        }

        // Khởi tạo AuditReader
        AuditReader auditReader = AuditReaderFactory.get(entityManager);

        return auditReader.createQuery()
                .forEntitiesAtRevision(ComponentEntity.class,
                        rev.intValue())  // Lấy entity tại revisionNumber
                .add(AuditEntity.property("projectId").eq(projectId))           // filter by projectId
                .getResultList();
    }

    @Override
    public List<ComponentIdRev> findLatestComponentAuditByProjectId(Long projectId) {
        String sql = "SELECT DISTINCT ON (id) id, rev " +
                "FROM component_aud " +
                "WHERE projectid = :projectId AND revtype <> 2 " +
                "ORDER BY id, rev DESC";

        @SuppressWarnings("unchecked")
        List<Object[]> results = entityManager.createNativeQuery(sql)
                .setParameter("projectId", projectId)
                .getResultList();

        List<ComponentIdRev> componentIdRevs = new ArrayList<>();
        for (Object[] row : results) {
            Long id = ((Number) row[0]).longValue();
            Integer rev = ((Number) row[1]).intValue();
            componentIdRevs.add(new ComponentIdRev(id, rev));
        }

        return componentIdRevs;
    }

    private ComponentStatusEntity getStatusAtRevision(Long id, Integer revision) {
        return getAuditReader().find(ComponentStatusEntity.class, id, revision);
    }

    private PositionEntity getPositionAtRevision(Long id, Integer revision) {
        return getAuditReader().find(PositionEntity.class, id, revision);
    }

    private void likeSpecificationBuilder(CriteriaBuilder criteriaBuilder,
                                          Root<ComponentEntity> componentRoot,
                                          List<Predicate> conditionsList,
                                          Map.Entry<String, List<String>> conditionEntry,
                                          String path) {
        String property = getProperty(StringUtils.remove(path, LIKE_SPEC));
        List<String> patterns = conditionEntry.getValue();
        // TODO need verify the client send the percent sign -> exclude this pattern
        patterns.removeAll(List.of(PERCENT_SIGN));
        if (CollectionUtils.isNotEmpty(patterns)) {
            Predicate predicate = toPredicateLike(componentRoot, criteriaBuilder, property, patterns);
            conditionsList.add(predicate);
        }
    }

    private Predicate toPredicateLike(Root<ComponentEntity> componentRoot,
                                      CriteriaBuilder criteriaBuilder,
                                      String property,
                                      List<String> patterns) {
        From<?, ?> from = getRoot(property, componentRoot);
        String field = getProperty(property);
        if (patterns.size() == 1) {
            return criteriaBuilder.like(from.get(field), patterns.get(0));
        }
        List<Predicate> predicates = new ArrayList<>();
        for (String pattern : patterns) {
            predicates.add(criteriaBuilder.like(from.get(field), pattern));
        }
        return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
    }

    private String getProperty(String property) {
        if (property.contains(".")) {
            return StringUtils.split(property, ".")[1];
        }
        return property;
    }

    private From<?, ?> getRoot(String property, Root<ComponentEntity> root) {
        if (property == null || root == null) {
            throw new IllegalArgumentException("Property and root must not be null");
        }
        int dotIndex = property.indexOf('.');
        if (dotIndex > 0) {
            String joinProperty = property.substring(0, dotIndex);
            return root.join(joinProperty, JoinType.LEFT);
        }
        return root;
    }

    /**
     * Build the where clause of the query
     */
    private List<Predicate> buildWhereClause(ComponentFilter filter,
                                             CriteriaBuilder cb,
                                             Root<ComponentEntity> component) {
        if (filter == null) {
            return Collections.emptyList();
        }

        component.alias("c");

        List<Predicate> predicates = new ArrayList<>();

        if (filter.getProjectId() != null) {
            predicates.add(
                    cb.equal(component.get("projectId"), filter.getProjectId())
            );
        }

        List<Long> componentIds = filter.getComponentIds();
        if (componentIds != null && !componentIds.isEmpty()) {
            predicates.add(
                    cb.and(component.get("id").in(filter.getComponentIds()))
            );
        }

        List<Integer> searchingStates = filter.getStates();
        if (searchingStates != null && !searchingStates.isEmpty()) {
            predicates.add(
                    cb.and(component.get("currentState").in(searchingStates))
            );
        }

        Pair<GroupTypeEnum, List<String>> groupMapping = filter.getGroupMapping();
        if (groupMapping != null) {
            GroupTypeEnum groupType = groupMapping.getLeft();
            List<String> groupNames = groupMapping.getRight();
            Join<ComponentEntity, GroupEntity> componentGroupJoin = component.join("groups",
                    JoinType.LEFT);

            if (groupType != null) {
                predicates.add(cb.and(
                        cb.isNotNull(componentGroupJoin.get("grouptype")),
                        cb.equal(componentGroupJoin.get("grouptype"), groupType)
                ));
            }

            if (groupNames != null && !groupNames.isEmpty()) {
                predicates.add(
                        cb.and(componentGroupJoin.get("name").in(groupNames))
                );
            }
        }

        return predicates;
    }

    private ComponentView toComponentView(Tuple tuple) {
        return ComponentView.builder()
                .id(tuple.get("id", BigInteger.class).longValue())
                .currentState(tuple.get("currentState", Integer.class))
                .comment(tuple.get("comment", String.class))
                .typeId(tuple.get("typeId", String.class))
                .groupView(GroupView.builder()
                        .deliveryBatch(tuple.get("deb", String.class))
                        .calculationArea(tuple.get("cal", String.class))
                        .eStopGroup(tuple.get("est", String.class))
                        .plcArea(tuple.get("plc", String.class))
                        .screen(tuple.get("scr", String.class))
                        .sequenceGroup(tuple.get("seq", String.class))
                        .lineName(tuple.get("lin", String.class))
                        .buildingSection(tuple.get("bds", String.class))
                        .installationSection(tuple.get("ins", String.class))
                        .drawing(tuple.get("dra", String.class))
                        .build())
                .posNo(tuple.get("posno", String.class))
                .colorGroup(tuple.get("colorgroup", String.class))
                .lengthTotal(tuple.get("lengthtotal", Integer.class))
                .width(tuple.get("width", Integer.class))
                .speed(tuple.get("speed", String.class))
                .load(tuple.get("load", Double.class))
                .throughput(tuple.get("throughput", Integer.class))
                .motorController(tuple.get("motorcontroller", String.class))
                .unitReversible(tuple.get("unitreversible", Boolean.class))
                .plantDomain(tuple.get("plantdomain", String.class))
                .amountBufferElec(tuple.get("amountbufferelec", Integer.class))
                .amountBufferMech(tuple.get("amountbuffermech", Integer.class))
                .bufferSize(tuple.get("buffersize", Integer.class))
                .drivePosition(tuple.get("driveposition", String.class))
                .motorDirection(tuple.get("motordirection", String.class))
                .curveAngle(tuple.get("curveangle", Integer.class))
                .curveDirection(tuple.get("curvedirection", String.class))
                .curveRadius(tuple.get("curveradius", Integer.class))
                .breakType(tuple.get("breaktype", String.class))
                .section1Angle(tuple.get("section1angle", Double.class))
                .section1Length(tuple.get("section1length", Double.class))
                .section2Angle(tuple.get("section2angle", Double.class))
                .section2Length(tuple.get("section2length", Double.class))
                .section3Angle(tuple.get("section3angle", Double.class))
                .section3Length(tuple.get("section3length", Double.class))
                .section4Angle(tuple.get("section4angle", Double.class))
                .section4Length(tuple.get("section4length", Double.class))
                .reference(tuple.get("reference", String.class))
                .rotation(tuple.get("rotation", Integer.class))
                .rotation3D(tuple.get("rotation3d", Double.class))
                .sysCreateDate(tuple.get("syscreatedate", Timestamp.class))
                .sysModDate(tuple.get("sysmoddate", Timestamp.class))
                .bomState(tuple.get("bomstate", Integer.class))
                .build();
    }

}
