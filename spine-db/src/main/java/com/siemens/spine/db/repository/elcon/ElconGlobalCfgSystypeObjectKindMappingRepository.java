package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconGlobalCfgSystypeObjectKindMapping;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_global_cfg_systype_object_kind_mapping")
@ApplicationScoped
public class ElconGlobalCfgSystypeObjectKindMappingRepository extends AbstractRestCRUDRepositoryImpl<ElconGlobalCfgSystypeObjectKindMapping, Long> {

    @Override
    protected boolean isNew(ElconGlobalCfgSystypeObjectKindMapping entity) {
        return entity.getCfgId() == null;
    }

    @Override
    public Class<ElconGlobalCfgSystypeObjectKindMapping> getDomainType() {
        return ElconGlobalCfgSystypeObjectKindMapping.class;
    }
} 