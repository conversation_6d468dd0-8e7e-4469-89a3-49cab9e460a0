package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPlcArea;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_plc_area")
@ApplicationScoped
public class ElconPlcAreaRepository extends AbstractRestCRUDRepositoryImpl<ElconPlcArea, Long> {

    @Override
    protected boolean isNew(ElconPlcArea entity) {
        return entity.getAreaId() == null;
    }

    @Override
    public Class<ElconPlcArea> getDomainType() {
        return ElconPlcArea.class;
    }
} 