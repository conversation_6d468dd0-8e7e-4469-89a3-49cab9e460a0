package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_prj_init_extension_auto_generation")
public class ElconPrjInitExtensionAutoGeneration {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "cfg_id", nullable = false)
    private Long cfgId;

    @Size(max = 255)
    @Column(name = "category")
    private String category;

    @Size(max = 255)
    @Column(name = "generation_rule")
    private String generationRule;

    @Size(max = 50)
    @Column(name = "name_prefix", length = 50)
    private String namePrefix;

    @Size(max = 255)
    @Column(name = "outfit_name")
    private String outfitName;

    @Size(max = 255)
    @Column(name = "object_kind")
    private String objectKind;

}