package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_global_cfg_default_objects")
public class ElconGlobalCfgDefaultObject {

    @EmbeddedId
    private ElconGlobalCfgDefaultObjectId id;

    @Size(max = 255)
    @Column(name = "description")
    private String description;

    @Size(max = 255)
    @Column(name = "object_kind")
    private String objectKind;

    @Size(max = 255)
    @Column(name = "sw_block")
    private String swBlock;

}