package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_group_header")
public class ElconGroupHeader {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "group_header_id", nullable = false)
    private Long groupHeaderId;

    @NotNull
    @Column(name = "project_version_id", nullable = false)
    private Long projectVersionId;

    @Size(max = 255)
    @Column(name = "unique_id")
    private String uniqueId;

    @Size(max = 255)
    @Column(name = "name")
    private String name;

    @Size(max = 255)
    @Column(name = "hw_outfit_name")
    private String hwOutfitName;

    @NotNull
    @Column(name = "plc_area_nr", nullable = false)
    private Short plcAreaNr;

    @NotNull
    @Column(name = "group_nr", nullable = false)
    private Short groupNr;

}