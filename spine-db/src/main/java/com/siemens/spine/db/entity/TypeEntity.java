package com.siemens.spine.db.entity;

import com.siemens.spine.db.constant.ProjectTypeClassificationEnum;
import com.siemens.spine.db.constraint.TypeAttribute;
import com.siemens.spine.db.constraint.TypeAttributeMapper;
import com.siemens.spine.db.entity.converter.ProjectTypeClassificationConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.sql.Timestamp;

@Entity
@Table(name = "type")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TypeEntity {

    @Id
    @TypeAttributeMapper(TypeAttribute.TYPE_ID)
    private String typeId; // cust_reference8

    @TypeAttributeMapper(TypeAttribute.MAIN_TYPE)
    private String mainType; // CUST_REFERENCE1

    @TypeAttributeMapper(TypeAttribute.SUB_TYPE)
    private String subType; // CUST_REFERENCE2

    @TypeAttributeMapper(TypeAttribute.SPECIAL_TYPE)
    private String specialType; // CUST_REFERENCE3

    @TypeAttributeMapper(TypeAttribute.TYPE_VERSION)
    private String typeVersion; // CUST_REFERENCE4

    @TypeAttributeMapper(TypeAttribute.DESCRIPTION)
    private String description; // cep_service_options

    @TypeAttributeMapper(TypeAttribute.MECH_MATERIAL_COST_KPI)
    private Double mechMaterialCostKpi; // worth

    @TypeAttributeMapper(TypeAttribute.ENGINEERING_ME_H_KPI)
    private Double engineeringMeHKpi; // realnumber01

    @TypeAttributeMapper(TypeAttribute.ENGINEERING_HW_H_KPI)
    private Double engineeringHwHPki; // realnumber02

    @TypeAttributeMapper(TypeAttribute.ENGINEERING_PLC_H_KPI)
    private Double engineeringPlcHKpi; // realnumber03

    @TypeAttributeMapper(TypeAttribute.ENGINEERING_IT_H_KPI)
    private Double engineeringItHKpi; // realnumber04

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project", foreignKey = @ForeignKey(name = "FK_TYPE__PROJECT"))
    private ProjectEntity project; // vcompId

    @TypeAttributeMapper(TypeAttribute.URL1)
    private String url1; // lrefe01

    @TypeAttributeMapper(TypeAttribute.URL2)
    private String url2; // lrefe02

    @TypeAttributeMapper(TypeAttribute.URL3)
    private String url3; // lrefe03

    @TypeAttributeMapper(TypeAttribute.URL4)
    private String url4; // lrefe04

    @TypeAttributeMapper(TypeAttribute.URL5)
    private String url5; // lrefe05

    @TypeAttributeMapper(TypeAttribute.TYPE_VARIANT)
    private String typeVariant; // srefe01

    @TypeAttributeMapper(TypeAttribute.DEFAULT_SLOPE)
    private String defaultSlope; // srefe02

    @TypeAttributeMapper(TypeAttribute.DEFAULT_DRIVE_COUNT)
    private Integer defaultDriveCount; // intnumber01

    @TypeAttributeMapper(TypeAttribute.VERSION_NUMBER)
    private String versionNumber; // srefe03

    @TypeAttributeMapper(TypeAttribute.SUPPLIER)
    private String supplier; // cust_reference10

    @TypeAttributeMapper(TypeAttribute.ENGINEERING_SCADA_H_KPI)
    private Double engineeringScadaHKpi; // realnumber05

    @TypeAttributeMapper(TypeAttribute.SAP_MATERIAL_NUMBER)
    private String sapMaterialNumber; // srefe04

    @TypeAttributeMapper(TypeAttribute.MPS)
    private String mps; // cust_reference5

    @TypeAttributeMapper(TypeAttribute.INSTALLATION_H_KPI)
    private Double installationHKpi; // zollwert

    @TypeAttributeMapper(TypeAttribute.PLC_COMMISSION_H_KPI)
    private Double plcCommissioningHKpi; // zollwarenwert

    @TypeAttributeMapper(TypeAttribute.BELT_MASS)
    private Double beltMass; // realnumber06

    @TypeAttributeMapper(TypeAttribute.REQUIRED_ACCELERATION)
    private Double requiredAcceleration; // realnumber07

    @TypeAttributeMapper(TypeAttribute.DRIVE_PULLEY_DIAMETER)
    private Double drivePulleyDiameter; // realnumber08

    @TypeAttributeMapper(TypeAttribute.FRICTION_FACTOR)
    private Double frictionFactor; // realnumber09

    @TypeAttributeMapper(TypeAttribute.DRIVE_SHAFT_DIAMETER)
    private Double driveShaftDiameter; // realnumber10

    @TypeAttributeMapper(TypeAttribute.EFFICIENCY_GEARBOX)
    private Double efficiencyGearbox; // insurance

    @TypeAttributeMapper(TypeAttribute.TYPE_AGGREGATOR)
    private String typeAggregator; // reference3

    @TypeAttributeMapper(TypeAttribute.ORIGINAL_TYPE)
    private String originalType; // srefe05

    @Convert(converter = ProjectTypeClassificationConverter.class)
    @TypeAttributeMapper(TypeAttribute.PROJECT_TYPE_CLASSIFICATION)
    private ProjectTypeClassificationEnum projectTypeClassification; // MAPPED SOMEHOW! NOT STRAIGHT FROM DB - dic_free_text3

    @TypeAttributeMapper(TypeAttribute.ENGINEERING_ME_ONE_OFF_COSTS)
    private Double engineeringMeOneOffCosts; // declared_pos_wght

    @TypeAttributeMapper(TypeAttribute.ENGINEERING_HW_ONE_OFF_COSTS)
    private Double engineeringHwOneOffCosts; // declared_pos_vol

    @TypeAttributeMapper(TypeAttribute.ENGINEERING_PLC_ONE_OFF_COSTS)
    private Double engineeringPlcOneOffCosts; // warenwert_value

    @TypeAttributeMapper(TypeAttribute.ENGINEERING_IT_ONE_OFF_COSTS)
    private Double engineeringItOneOffCosts; // vereinbarte_fracht

    @TypeAttributeMapper(TypeAttribute.ENGINEERING_SCADA_ONE_OFF_COSTS)
    private Integer engineeringScadaOneOffCosts; // intnumber02  - in slang, read as getDouble while in db it is int

    @TypeAttributeMapper(TypeAttribute.HIERARCHY_TYPE)
    private String hierarchyType; // reference4

    @TypeAttributeMapper(TypeAttribute.OUTDATED)
    private Boolean outdated; // checkbox06

    private Integer solutionId; // unclear if future db will be instance-per-solution

    private Boolean projectSpecific;

    private Timestamp modificationDate;

    private Timestamp creationDate;

}

