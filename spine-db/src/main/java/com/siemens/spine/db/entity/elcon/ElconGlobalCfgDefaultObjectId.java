package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.Hibernate;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@Embeddable
public class ElconGlobalCfgDefaultObjectId implements Serializable {

    @Size(max = 255)
    @NotNull
    @Column(name = "unique_id", nullable = false)
    private String uniqueId;

    @Size(max = 255)
    @NotNull
    @Column(name = "domain", nullable = false)
    private String domain;

    @Override
    public int hashCode() {
        return Objects.hash(domain, uniqueId);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) {
            return false;
        }
        ElconGlobalCfgDefaultObjectId entity = (ElconGlobalCfgDefaultObjectId) o;
        return Objects.equals(this.domain, entity.domain) &&
                Objects.equals(this.uniqueId, entity.uniqueId);
    }

}