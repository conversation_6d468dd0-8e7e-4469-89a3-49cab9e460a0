package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconSimuAvailCheckProperty;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_simu_avail_check_property")
@ApplicationScoped
public class ElconSimuAvailCheckPropertyRepository extends AbstractRestCRUDRepositoryImpl<ElconSimuAvailCheckProperty, Long> {

    @Override
    protected boolean isNew(ElconSimuAvailCheckProperty entity) {
        return entity.getPropertyId() == null;
    }

    @Override
    public Class<ElconSimuAvailCheckProperty> getDomainType() {
        return ElconSimuAvailCheckProperty.class;
    }
} 