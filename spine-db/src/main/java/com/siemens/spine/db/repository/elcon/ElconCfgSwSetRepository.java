package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCfgSwSet;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cfg_sw_set")
@ApplicationScoped
public class ElconCfgSwSetRepository extends AbstractRestCRUDRepositoryImpl<ElconCfgSwSet, Long> {

    @Override
    protected boolean isNew(ElconCfgSwSet entity) {
        return entity.getSwSetId() == null;
    }

    @Override
    public Class<ElconCfgSwSet> getDomainType() {
        return ElconCfgSwSet.class;
    }
} 