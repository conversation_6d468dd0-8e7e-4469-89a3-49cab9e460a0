package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPlcCommunication;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_plc_communication")
@ApplicationScoped
public class ElconPlcCommunicationRepository extends AbstractRestCRUDRepositoryImpl<ElconPlcCommunication, Long> {

    @Override
    protected boolean isNew(ElconPlcCommunication entity) {
        return entity.getCommunicationId() == null;
    }

    @Override
    public Class<ElconPlcCommunication> getDomainType() {
        return ElconPlcCommunication.class;
    }
} 