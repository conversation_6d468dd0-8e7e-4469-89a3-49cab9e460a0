package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPrjInitOutfitSensor;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_prj_init_outfit_sensors")
@ApplicationScoped
public class ElconPrjInitOutfitSensorRepository extends AbstractRestCRUDRepositoryImpl<ElconPrjInitOutfitSensor, Long> {

    @Override
    protected boolean isNew(ElconPrjInitOutfitSensor entity) {
        return entity.getCfgId() == null;
    }

    @Override
    public Class<ElconPrjInitOutfitSensor> getDomainType() {
        return ElconPrjInitOutfitSensor.class;
    }
} 