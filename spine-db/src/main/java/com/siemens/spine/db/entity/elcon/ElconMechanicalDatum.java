package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "elcon_mechanical_data")
public class ElconMechanicalDatum {

    @Id
    @Column(name = "component_id", nullable = false)
    private Long componentId;

    @Column(name = "inf_angle")
    private BigDecimal infAngle;

    @Column(name = "inf_dist")
    private BigDecimal infDist;

    @Column(name = "inf_insertion")
    private BigDecimal infInsertion;

    @Column(name = "inf_length")
    private BigDecimal infLength;

    @Column(name = "inf_zoom")
    private BigDecimal infZoom;

    @Column(name = "climb_angle")
    private BigDecimal climbAngle;

    @Column(name = "curve_radius")
    private BigDecimal curveRadius;

    @Column(name = "curve_angle")
    private BigDecimal curveAngle;

    @Column(name = "angle_corr")
    private BigDecimal angleCorr;

    @Column(name = "rotation_angle")
    private BigDecimal rotationAngle;

}