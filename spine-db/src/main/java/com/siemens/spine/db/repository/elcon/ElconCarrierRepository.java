package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCarrier;

import javax.enterprise.context.ApplicationScoped;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 19/6/2025
 **/
@RestRepository(path = "elcon_drive")
@ApplicationScoped
public class ElconCarrierRepository extends AbstractRestCRUDRepositoryImpl<ElconCarrier, Long> {

    @Override
    protected boolean isNew(ElconCarrier entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconCarrier> getDomainType() {
        return ElconCarrier.class;
    }

}
