package com.siemens.spine.db.repository.factory;

import com.siemens.spine.db.exception.JpaException;
import com.siemens.spine.db.utils.CompositeKeyUtils;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Registry for managing composite key factories.
 * Implements the Factory pattern with automatic factory selection based on entity class.
 * Uses caching for performance and provides fallback mechanisms.
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
public class CompositeKeyFactoryRegistry {

    private static final CompositeKeyFactoryRegistry INSTANCE = new CompositeKeyFactoryRegistry();

    private final List<CompositeKeyFactory> factories = new ArrayList<>();

    private final ConcurrentMap<Class<?>, CompositeKeyFactory> factoryCache = new ConcurrentHashMap<>();

    private CompositeKeyFactoryRegistry() {
        registerFactory(new EmbeddedIdFactory());
        registerFactory(new IdClassFactory());
        registerFactory(new SingleIdFactory());

        log.info("Initialized CompositeKeyFactoryRegistry with {} factories", factories.size());
    }

    /**
     * Gets the singleton instance of the factory registry.
     *
     * @return The factory registry instance
     */
    public static CompositeKeyFactoryRegistry getInstance() {
        return INSTANCE;
    }

    /**
     * Registers a new factory with the registry.
     * Factories are automatically sorted by priority.
     *
     * @param factory The factory to register
     */
    public void registerFactory(CompositeKeyFactory factory) {
        if (factory != null) {
            factories.add(factory);
            factories.sort(Comparator.comparingInt(CompositeKeyFactory::getPriority));
            log.debug("Registered factory {} with priority {}",
                    factory.getClass().getSimpleName(), factory.getPriority());
        }
    }

    /**
     * Gets the appropriate factory for the given entity class.
     * Uses caching for performance.
     *
     * @param entityClass The entity class
     * @return The appropriate factory
     * @throws JpaException if no suitable factory is found
     */
    public CompositeKeyFactory getFactory(Class<?> entityClass) throws JpaException {
        if (entityClass == null) {
            throw new JpaException("Entity class cannot be null");
        }

        CompositeKeyFactory cachedFactory = factoryCache.get(entityClass);
        if (cachedFactory != null) {
            log.debug("Found cached factory {} for entity {}",
                    cachedFactory.getClass().getSimpleName(), entityClass.getSimpleName());
            return cachedFactory;
        }

        for (CompositeKeyFactory factory : factories) {
            if (factory.canHandle(entityClass)) {
                log.debug("Selected factory {} for entity {} (priority: {})",
                        factory.getClass().getSimpleName(), entityClass.getSimpleName(), factory.getPriority());

                try {
                    factory.validateKeyConfiguration(entityClass);
                } catch (JpaException e) {
                    log.warn("Factory {} failed validation for entity {}: {}",
                            factory.getClass().getSimpleName(), entityClass.getSimpleName(), e.getMessage());
                    continue;
                }

                factoryCache.put(entityClass, factory);
                return factory;
            }
        }

        throw new JpaException(String.format(
                "No suitable composite key factory found for entity class %s. " +
                        "Available factories: %s",
                entityClass.getSimpleName(),
                factories.stream()
                        .map(f -> f.getClass().getSimpleName() + "(" + f.getKeyType() + ")")
                        .toList()
        ));
    }

    /**
     * Creates composite key data using the appropriate factory.
     *
     * @param compositeKeyStr The composite key string
     * @param entityClass     The entity class
     * @return CompositeKeyData created by the appropriate factory
     * @throws JpaException if creation fails
     */
    public CompositeKeyUtils.CompositeKeyData createKeyData(String compositeKeyStr, Class<?> entityClass)
            throws JpaException {
        CompositeKeyFactory factory = getFactory(entityClass);
        return factory.createKeyData(compositeKeyStr, entityClass);
    }

    /**
     * Extracts ID fields using the appropriate factory.
     *
     * @param entityClass The entity class
     * @return List of ID fields extracted by the appropriate factory
     * @throws JpaException if extraction fails
     */
    public List<Field> extractIdFields(Class<?> entityClass) throws JpaException {
        CompositeKeyFactory factory = getFactory(entityClass);
        return factory.extractIdFields(entityClass);
    }

    /**
     * Determines the composite key type using the appropriate factory.
     *
     * @param entityClass The entity class
     * @return The composite key type
     * @throws JpaException if determination fails
     */
    public CompositeKeyUtils.CompositeKeyType getCompositeKeyType(Class<?> entityClass) throws JpaException {
        CompositeKeyFactory factory = getFactory(entityClass);
        return factory.getKeyType();
    }

    /**
     * Checks if an entity has composite keys using the appropriate factory.
     *
     * @param entityClass The entity class
     * @return true if the entity has composite keys
     */
    public boolean hasCompositeKey(Class<?> entityClass) {
        try {
            CompositeKeyFactory factory = getFactory(entityClass);
            return factory.getKeyType() != CompositeKeyUtils.CompositeKeyType.SINGLE_ID;
        } catch (JpaException e) {
            log.debug("Failed to determine composite key status for entity {}: {}",
                    entityClass.getSimpleName(), e.getMessage());
            return false;
        }
    }

}