package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconDevicePlugin;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_device_plugin")
@ApplicationScoped
public class ElconDevicePluginRepository extends AbstractRestCRUDRepositoryImpl<ElconDevicePlugin, Long> {

    @Override
    protected boolean isNew(ElconDevicePlugin entity) {
        return entity.getDeviceId() == null;
    }

    @Override
    public Class<ElconDevicePlugin> getDomainType() {
        return ElconDevicePlugin.class;
    }
} 