package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_device")
public class ElconDevice {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "device_id", nullable = false)
    private Long deviceId;

    @NotNull
    @Column(name = "component_id", nullable = false)
    private Long componentId;

    @Size(max = 255)
    @Column(name = "device_kind")
    private String deviceKind;

    @Size(max = 255)
    @Column(name = "device_name")
    private String deviceName;

    @Size(max = 255)
    @Column(name = "device_type")
    private String deviceType;

    @Column(name = "device_nr")
    private Long deviceNr;

    @Column(name = "number_ports")
    private Short numberPorts;

    @Size(max = 255)
    @Column(name = "manufacturer")
    private String manufacturer;

    @Size(max = 255)
    @Column(name = "order_nr")
    private String orderNr;

    @Size(max = 255)
    @Column(name = "firmware_version")
    private String firmwareVersion;

    @Size(max = 255)
    @Column(name = "gsdml_tag")
    private String gsdmlTag;

    @Size(max = 255)
    @Column(name = "full_device_name")
    private String fullDeviceName;

}