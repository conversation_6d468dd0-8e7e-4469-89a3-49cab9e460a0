package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_cl_device_component")
public class ElconClDeviceComponent {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "component_id", nullable = false)
    private Long componentId;

    @Size(max = 255)
    @Column(name = "component_name")
    private String componentName;

    @NotNull
    @Column(name = "io_id", nullable = false)
    private Long ioId;

    @Size(max = 255)
    @Column(name = "component_type")
    private String componentType;

    @Size(max = 255)
    @Column(name = "component_symbol")
    private String componentSymbol;

    @Size(max = 255)
    @Column(name = "description")
    private String description;

}