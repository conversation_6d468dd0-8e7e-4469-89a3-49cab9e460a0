package com.siemens.spine.db.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.util.Objects;

@Entity
@Table(name = "decomposition")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Audited
public class DecompositionEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "decomposition_id_seq_gen")
    @SequenceGenerator(name = "decomposition_id_seq_gen", sequenceName = "decomposition_id_seq")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "component_id", nullable = false)
    private ComponentEntity component;
    private String changeDescription;
    private String derivedFrom;
    private String description;
    private String mpsPrefix;
    private String materialType;
    private String sapMaterialNumber;
    private Integer quantity;
    private String positionNumber;
    private String sourceInternal;
    private Boolean standardMaterial;
    private String unitInSAP;
    private String zOptions;
    private String source;

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DecompositionEntity that = (DecompositionEntity) o;
        return Objects.equals(id, that.id);
    }

}

