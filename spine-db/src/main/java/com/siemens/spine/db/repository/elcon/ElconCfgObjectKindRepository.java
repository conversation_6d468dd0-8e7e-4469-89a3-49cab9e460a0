package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCfgObjectKind;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cfg_object_kind")
@ApplicationScoped
public class ElconCfgObjectKindRepository extends AbstractRestCRUDRepositoryImpl<ElconCfgObjectKind, Long> {

    @Override
    protected boolean isNew(ElconCfgObjectKind entity) {
        return entity.getCfgId() == null;
    }

    @Override
    public Class<ElconCfgObjectKind> getDomainType() {
        return ElconCfgObjectKind.class;
    }
} 