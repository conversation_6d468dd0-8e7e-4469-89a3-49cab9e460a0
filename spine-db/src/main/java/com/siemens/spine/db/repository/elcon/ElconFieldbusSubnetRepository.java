package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconFieldbusSubnet;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_fieldbus_subnet")
@ApplicationScoped
public class ElconFieldbusSubnetRepository extends AbstractRestCRUDRepositoryImpl<ElconFieldbusSubnet, Long> {

    @Override
    protected boolean isNew(ElconFieldbusSubnet entity) {
        return entity.getSubnetId() == null;
    }

    @Override
    public Class<ElconFieldbusSubnet> getDomainType() {
        return ElconFieldbusSubnet.class;
    }
} 