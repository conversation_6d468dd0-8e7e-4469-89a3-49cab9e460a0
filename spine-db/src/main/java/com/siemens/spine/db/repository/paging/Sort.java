package com.siemens.spine.db.repository.paging;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

public class Sort implements Iterable<Sort.Order> {

    private final List<Order> orders;

    private Sort(List<Order> orders) {
        this.orders = orders;
    }

    public static Sort by(Direction direction, String... properties) {
        List<Order> orders = new ArrayList<>();
        for (String property : properties) {
            orders.add(new Order(direction, property));
        }
        return new Sort(orders);
    }

    public static Sort by(String... properties) {
        return by(Direction.ASC, properties);
    }

    public static Sort unsorted() {
        return new Sort(Collections.emptyList());
    }

    public boolean isUnsorted() {
        return orders.isEmpty();
    }

    @Override
    public Iterator<Order> iterator() {
        return orders.iterator();
    }

    public enum Direction {
        ASC,
        DESC;

        public boolean isAscending() {
            return this == ASC;
        }

        public boolean isDescending() {
            return this == DESC;
        }
    }

    public record Order(Direction direction, String property) {

        public static Order asc(String property) {
            return new Order(Direction.ASC, property);
        }

        public static Order desc(String property) {
            return new Order(Direction.DESC, property);
        }

        public boolean isAscending() {
            return direction.isAscending();
        }

        public boolean isDescending() {
            return direction.isDescending();
        }

    }

}