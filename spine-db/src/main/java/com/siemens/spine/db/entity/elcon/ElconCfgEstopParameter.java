package com.siemens.spine.db.entity.elcon;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.Hibernate;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@Entity
@Table(name = "elcon_cfg_estop_parameters")
public class ElconCfgEstopParameter {

    @EmbeddedId
    private ElconCfgEstopParameterId id;

    @NotNull
    @Column(name = "cfg_id", nullable = false)
    private Long cfgId;

    @NotNull
    @Column(name = "project_id", nullable = false)
    private Long projectId;

    @Size(max = 50)
    @NotNull
    @Column(name = "hw_outfit_name", nullable = false, length = 50)
    private String hwOutfitName;

    @Size(max = 20)
    @NotNull
    @Column(name = "transformation", nullable = false, length = 20)
    private String transformation;

    @Size(max = 50)
    @Column(name = "item_name_pattern", length = 50)
    private String itemNamePattern;

    @Data
    @Embeddable
    public static class ElconCfgEstopParameterId implements Serializable {
        private Long cfgId;

        private Long projectId;

        private String hwOutfitName;

        private String itemNamePattern;


    }
}