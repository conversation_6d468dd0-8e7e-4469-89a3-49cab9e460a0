package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_global_cfg_gentool_query")
public class ElconGlobalCfgGentoolQuery {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "query_id", nullable = false)
    private Long queryId;

    @Size(max = 255)
    @NotNull
    @Column(name = "query_name", nullable = false)
    private String queryName;

    @Size(max = 255)
    @NotNull
    @Column(name = "view_name", nullable = false)
    private String viewName;

    @Size(max = 255)
    @NotNull
    @Column(name = "description", nullable = false)
    private String description;

    @NotNull
    @Column(name = "single_result", nullable = false)
    private Short singleResult;

    @NotNull
    @Column(name = "plc_area_specific", nullable = false)
    private Short plcAreaSpecific;

    @Size(max = 20)
    @NotNull
    @Column(name = "query_type", nullable = false, length = 20)
    private String queryType;

}