package com.siemens.spine.db.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 26/12/2022
 */
public class Constants {

    public static long SOLUTION_ID = ConstantsDemo.SOLUTION_ID_DEMO;
    public static long PROJECTS_GROUP_ID = ConstantsDemo.PROJECTS_GROUP_ID_DEMO;
    public static String TYPES_COMPANY_ID = ConstantsDemo.TYPES_COMPANY_ID_DEMO;

    public static class ConstantsDemo {

        public static long SOLUTION_ID_DEMO = 27331;
        public static long PROJECTS_GROUP_ID_DEMO = 1638261;
        public static String TYPES_COMPANY_ID_DEMO = "1638451";

    }

    public static class ConstantsFinal {

        public static long SOLUTION_ID_FINAL = 27331;
        public static long PROJECTS_GROUP_ID_FINAL = 852249;
        public static String TYPES_COMPANY_ID_FINAL = "860889";

    }

    public static class AttributeType {

        public static final String CHAR = "CHAR";

        public static final String INTEGER = "INTEGER";

        public static final String DOUBLE = "DOUBLE";

    }

    public class Permission {

        public static final String ADMIN_CREATE_NEW_PROJECT = "ADMIN_CREATE_NEW_PROJECT";
        public static final String ADMIN_MANAGE_PERMISSIONS = "ADMIN_MANAGE_PERMISSIONS";
        public static final String ADMIN_UNLOCK_COMPONENT = "ADMIN_UNLOCK_COMPONENT";
        public static final String BACKUP_CREATE_MANUALLY = "BACKUP_CREATE_MANUALLY";
        public static final String BACKUP_RESTORE = "BACKUP_RESTORE";
        public static final String BACKUP_VIEW = "BACKUP_VIEW";
        public static final String COMPONENT_DELETE = "COMPONENT_DELETE";
        public static final String COMPONENT_MODIFY = "COMPONENT_MODIFY";
        public static final String COMPONENT_MOVE_BACKWARD = "COMPONENT_MOVE_BACKWARD";
        public static final String COMPONENT_MOVE_FORWARD = "COMPONENT_MOVE_FORWARD";
        public static final String COMPONENT_REMOVE = "COMPONENT_REMOVE";
        public static final String COMPONENT_VIEW = "COMPONENT_VIEW";
        public static final String COMPONENT_VIEW_DECOMPOSITION = "COMPONENT_VIEW_DECOMPOSITION";
        public static final String DECOMPOSITION_ATTRIBUTES_DELETE = "DECOMPOSITION_ATTRIBUTES_DELETE";
        public static final String DECOMPOSITION_ATTRIBUTES_MODIFY = "DECOMPOSITION_ATTRIBUTES_MODIFY";
        public static final String DECOMPOSITION_ATTRIBUTES_UPLOAD = "DECOMPOSITION_ATTRIBUTES_UPLOAD";
        public static final String DECOMPOSITION_ATTRIBUTES_VIEW = "DECOMPOSITION_ATTRIBUTES_VIEW";
        public static final String DRIVE_ASSIGNMENT = "DRIVE_ASSIGNMENT";
        public static final String DRIVE_CALCULATE_PARAMETERS_PHASE_EXECUTION = "DRIVE_CALCULATE_PARAMETERS_PHASE_EXECUTION";
        public static final String DRIVE_CALCULATE_PHASE_OFFER = "DRIVE_CALCULATE_PHASE_OFFER";
        public static final String DRIVE_EXPORT = "DRIVE_EXPORT";
        public static final String ELECTRIC_SYNCHRONIZATION = "ELECTRIC_SYNCHRONIZATION";
        public static final String EXPORT_OBJECTXML = "EXPORT_OBJECTXML";
        public static final String GROUPS_ASSIGN_DELIVERY_DATE = "GROUPS_ASSIGN_DELIVERY_DATE";
        public static final String GROUPS_CREATE_BUT_DRAWING = "GROUPS_CREATE_BUT_DRAWING";
        public static final String GROUPS_DELETE = "GROUPS_DELETE";
        public static final String GROUPS_RENAME = "GROUPS_RENAME";
        public static final String GROUPS_VIEW = "GROUPS_VIEW";
        public static final String PROJECT_CHANGE_TO_EXECUTION_PHASE = "PROJECT_CHANGE_TO_EXECUTION_PHASE";
        public static final String PROJECT_MODIFY_DEFAULT_PARAMETERS = "PROJECT_MODIFY_DEFAULT_PARAMETERS";
        public static final String PROJECT_MODIFY_PARAMETERS = "PROJECT_MODIFY_PARAMETERS";
        public static final String PROJECT_STATISTICS = "PROJECT_STATISTICS";
        public static final String PROJECT_VIEW_PARAMETERS = "PROJECT_VIEW_PARAMETERS";
        public static final String SAP_EXPORT = "SAP_EXPORT";
        public static final String TODOLIST_CALCULATION = "TODOLIST_CALCULATION";
        public static final String TODOLIST_EMULATION = "TODOLIST_EMULATION";
        public static final String TODOLIST_IT = "TODOLIST_IT";
        public static final String TODOLIST_SAPBOM = "TODOLIST_SAPBOM";
        public static final String TODOLIST_SIMULATION = "TODOLIST_SIMULATION";
        public static final String TYPE_APPROVAL = "TYPE_APPROVAL";
        public static final String TYPE_CREATION = "TYPE_CREATION";
        public static final String TYPE_DELETE = "TYPE_DELETE";
        public static final String TYPE_MODIFY = "TYPE_MODIFY";
        public static final String TYPE_VIEW = "TYPE_VIEW";

        public static final String PROJECT_CREATE_NEW_VERSION = "PROJECT_CREATE_NEW_VERSION";

    }

}

