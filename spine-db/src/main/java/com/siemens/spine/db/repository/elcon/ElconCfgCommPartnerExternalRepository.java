package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCfgCommPartnerExternal;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cfg_comm_partner_external")
@ApplicationScoped
public class ElconCfgCommPartnerExternalRepository extends AbstractRestCRUDRepositoryImpl<ElconCfgCommPartnerExternal, Long> {

    @Override
    protected boolean isNew(ElconCfgCommPartnerExternal entity) {
        return entity.getCfgId() == null;
    }

    @Override
    public Class<ElconCfgCommPartnerExternal> getDomainType() {
        return ElconCfgCommPartnerExternal.class;
    }
} 