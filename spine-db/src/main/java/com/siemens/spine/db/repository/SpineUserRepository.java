package com.siemens.spine.db.repository;

import com.siemens.spine.db.constant.OperationEnum;
import com.siemens.spine.db.entity.SpineUserEntity;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 26/12/2022
 */
public interface SpineUserRepository extends GenericJpaRepository<SpineUserEntity, Long> {

    boolean isOperationPermittedForSpecificProject(Long solutionId, String userName, Long projectId,
                                                   OperationEnum operation, String userType);

    boolean isOperationPermittedForAnyProject(Long solutionId, String userName,
                                              OperationEnum operation, String userType);

}

