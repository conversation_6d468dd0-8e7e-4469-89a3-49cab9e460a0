package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@Entity
@Table(name = "elcon_panel_neighbour")
public class ElconPanelNeighbour {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "neighbour_id", nullable = false)
    private Long neighbourId;

    @NotNull
    @Column(name = "src_object_id", nullable = false)
    private Long srcObjectId;

    @NotNull
    @Column(name = "dest_object_id", nullable = false)
    private Long destObjectId;

}