package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconNetworkInterface;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_network_interface")
@ApplicationScoped
public class ElconNetworkInterfaceRepository extends AbstractRestCRUDRepositoryImpl<ElconNetworkInterface, Long> {

    @Override
    protected boolean isNew(ElconNetworkInterface entity) {
        return entity.getInterfaceId() == null;
    }

    @Override
    public Class<ElconNetworkInterface> getDomainType() {
        return ElconNetworkInterface.class;
    }
} 