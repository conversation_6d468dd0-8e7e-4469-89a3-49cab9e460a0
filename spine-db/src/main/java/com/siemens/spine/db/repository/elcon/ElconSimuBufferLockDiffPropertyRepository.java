package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconSimuBufferLockDiffProperty;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_simu_buffer_lock_diff_property")
@ApplicationScoped
public class ElconSimuBufferLockDiffPropertyRepository extends AbstractRestCRUDRepositoryImpl<ElconSimuBufferLockDiffProperty, Long> {

    @Override
    protected boolean isNew(ElconSimuBufferLockDiffProperty entity) {
        return entity.getPropertyId() == null;
    }

    @Override
    public Class<ElconSimuBufferLockDiffProperty> getDomainType() {
        return ElconSimuBufferLockDiffProperty.class;
    }
} 