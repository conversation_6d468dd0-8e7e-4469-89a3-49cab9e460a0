package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MapsId;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_cfg_sw_set")
public class ElconCfgSwSet {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "sw_set_id", nullable = false)
    private Long swSetId;

    @MapsId
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sw_set_id", nullable = false)
    private ElconCfgSwSet elconCfgSwSet;

    @Size(max = 255)
    @NotNull
    @Column(name = "sw_set_key", nullable = false)
    private String swSetKey;

    @NotNull
    @Column(name = "project_id", nullable = false)
    private Long projectId;

    @Size(max = 255)
    @Column(name = "sw_set_name")
    private String swSetName;

    @Column(name = "common_sw_set_id")
    private Long commonSwSetId;

}