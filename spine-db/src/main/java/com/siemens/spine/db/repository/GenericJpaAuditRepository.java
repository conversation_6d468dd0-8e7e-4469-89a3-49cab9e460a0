package com.siemens.spine.db.repository;

import com.siemens.spine.db.entity.RevisionInfoEntity;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 18/4/2025
 **/
public interface GenericJpaAuditRepository<T, ID> extends GenericJpaRepository<T, ID> {

    Optional<T> getHistoryAtRevision(ID id, int revision);

    List<Number> getRevisions(ID id);

    List<RevisionInfoEntity> getAllRevisionInfo(ID id);

}

