package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.exception.JpaException;
import com.siemens.spine.db.repository.CrudRepository;
import com.siemens.spine.db.repository.metadata.EntityMetadataCache;
import com.siemens.spine.db.repository.metadata.IdMetadata;
import com.siemens.spine.db.repository.metadata.MetadataUtils;
import com.siemens.spine.db.repository.paging.Page;
import com.siemens.spine.db.repository.paging.PageImpl;
import com.siemens.spine.db.repository.paging.Pageable;
import com.siemens.spine.db.repository.paging.Sort;
import com.siemens.spine.db.utils.CompositeKeyUtils;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Order;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Abstract base implementation for REST CRUD repositories.
 *
 * @param <T>  Entity type
 * @param <ID> ID type (simple types or CompositeKeyData)
 */
@Transactional
@Slf4j
public abstract class AbstractRestCRUDRepositoryImpl<T, ID> implements CrudRepository<T, ID>, RestCrudRepositoryMarker {

    @PersistenceContext
    protected EntityManager entityManager;

    private EntityMetadataCache metadataCache;

    private static <T> T validateNotNull(T obj, String message) {
        if (obj == null) {
            throw new JpaException(message);
        }
        return obj;
    }

    @Inject
    protected void setMetadataCache(EntityMetadataCache metadataCache) {
        if (this.metadataCache == null) {
            this.metadataCache = validateNotNull(metadataCache, "EntityMetadataCache cannot be null");
            log.info("Injected EntityMetadataCache via setter");
        }
    }

    @Override
    public <S extends T> S save(S entity) {
        validateNotNull(entity, "Entity cannot be null");

        try {
            if (isNew(entity)) {
                entityManager.persist(entity);
                log.debug("Persisted new entity of type: {}", entity.getClass().getSimpleName());
                return entity;
            } else {
                S mergedEntity = entityManager.merge(entity);
                log.debug("Merged existing entity of type: {}", entity.getClass().getSimpleName());
                return mergedEntity;
            }
        } catch (Exception e) {
            log.error("Failed to save entity of type {}: {}", entity.getClass().getSimpleName(), e.getMessage(), e);
            throw new JpaException("Failed to save entity", e);
        }
    }

    @Override
    public Optional<T> findById(ID id) {
        if (id == null) {
            log.debug("Attempted to find entity with null ID");
            return Optional.empty();
        }

        try {
            if (id instanceof CompositeKeyUtils.CompositeKeyData compositeKeyData) {
                return findByCompositeKey(compositeKeyData);
            }

            if (id instanceof String stringId && isCompositeKeyString(stringId)) {
                return findByCompositeKeyString(stringId);
            }

            T entity = entityManager.find(getDomainType(), id);
            Optional<T> result = Optional.ofNullable(entity);

            log.debug("Simple ID lookup for entity type {} with ID {}: {}", getDomainType().getSimpleName(), id,
                    result.isPresent() ? "found" : "not found");

            return result;

        } catch (JpaException e) {
            log.warn("JPA exception during findById for entity type {} with ID {}: {}", getDomainType().getSimpleName(),
                    id, e.getMessage());
            return Optional.empty();
        } catch (Exception e) {
            log.error("Unexpected error during findById for entity type {} with ID {}: {}",
                    getDomainType().getSimpleName(), id, e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public Page<T> findAll(Pageable pageable) {
        validateNotNull(pageable, "Pageable cannot be null");

        try {
            long totalElements = count();

            CriteriaBuilder cb = entityManager.getCriteriaBuilder();
            CriteriaQuery<T> query = cb.createQuery(getDomainType());
            Root<T> root = query.from(getDomainType());

            if (pageable.getSort() != null && !pageable.getSort().isUnsorted()) {
                List<Order> orders = buildSortOrders(cb, root, pageable.getSort());
                query.orderBy(orders);
            }

            TypedQuery<T> typedQuery = entityManager.createQuery(query.select(root));
            typedQuery.setFirstResult((int) pageable.getOffset());
            typedQuery.setMaxResults(pageable.getPageSize());

            List<T> content = typedQuery.getResultList();

            log.debug("Retrieved {} of {} total entities for type {} (page {}, size {})", content.size(), totalElements,
                    getDomainType().getSimpleName(), pageable.getPageNumber(), pageable.getPageSize());

            return new PageImpl<>(content, pageable, totalElements);

        } catch (Exception e) {
            log.error("Failed to retrieve paginated results for entity type {}: {}", getDomainType().getSimpleName(),
                    e.getMessage(), e);
            throw new JpaException("Failed to retrieve entities", e);
        }
    }

    @Override
    public void deleteById(ID id) {
        if (id == null) {
            log.debug("Attempted to delete entity with null ID");
            return;
        }

        try {
            Optional<T> entity = findById(id);
            if (entity.isPresent()) {
                entityManager.remove(entity.get());
                log.debug("Deleted entity of type {} with ID: {}", getDomainType().getSimpleName(), id);
            } else {
                log.debug("Entity not found for deletion - type: {}, ID: {}", getDomainType().getSimpleName(), id);
            }
        } catch (Exception e) {
            log.error("Failed to delete entity of type {} with ID {}: {}", getDomainType().getSimpleName(), id,
                    e.getMessage(), e);
            throw new JpaException("Failed to delete entity", e);
        }
    }

    @Override
    public boolean existsById(ID id) {
        if (id == null) {
            return false;
        }

        try {
            return findById(id).isPresent();
        } catch (Exception e) {
            log.error("Error checking existence for ID {}: {}", id, e.getMessage());
            return false;
        }
    }

    @Override
    public long count(Predicate... predicates) {
        try {
            CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
            CriteriaQuery<Long> criteriaQuery = criteriaBuilder.createQuery(Long.class);
            Root<T> root = criteriaQuery.from(getDomainType());
            criteriaQuery.select(criteriaBuilder.count(root));

            if (predicates != null && predicates.length > 0) {
                criteriaQuery.where(predicates);
            }

            TypedQuery<Long> query = entityManager.createQuery(criteriaQuery);
            Long countResponse = query.getSingleResult();

            long result = countResponse == null ? 0 : countResponse;
            log.debug("Count query for entity type {}: {}", getDomainType().getSimpleName(), result);

            return result;
        } catch (Exception e) {
            log.error("Failed to count entities of type {}: {}", getDomainType().getSimpleName(), e.getMessage(), e);
            throw new JpaException("Failed to count entities", e);
        }
    }

    /**
     * Template method for subclasses to implement entity persistence logic.
     */
    protected abstract boolean isNew(T entity);

    private boolean isCompositeKeyString(String stringId) {
        return stringId.contains(CompositeKeyUtils.KEY_WRAPPER_START) &&
                stringId.contains(CompositeKeyUtils.KEY_VALUE_SEPARATOR) &&
                CompositeKeyUtils.hasCompositeKey(getDomainType());
    }

    private Optional<T> findByCompositeKeyString(String compositeKeyStr) {
        try {
            CompositeKeyUtils.validateCompositeKeyFormat(compositeKeyStr);
            CompositeKeyUtils.CompositeKeyData keyData = CompositeKeyUtils.createCompositeKeyDataWithMapping(
                    compositeKeyStr, getDomainType());
            return findByCompositeKey(keyData);
        } catch (JpaException e) {
            log.error("Failed to parse composite key string '{}': {}", compositeKeyStr, e.getMessage());
            throw e;
        }
    }

    private Optional<T> findByCompositeKey(CompositeKeyUtils.CompositeKeyData keyData) {
        validateNotNull(keyData, "CompositeKeyData cannot be null");

        IdMetadata idMetadata = getIdMetadata();

        if (!idMetadata.isValidKeyData(keyData)) {
            log.debug("Invalid key data: missing or mismatched fields");
            return Optional.empty();
        }

        return executeCompositeKeyQuery(keyData, idMetadata);
    }

    private Optional<T> executeCompositeKeyQuery(CompositeKeyUtils.CompositeKeyData keyData, IdMetadata idMetadata) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<T> query = cb.createQuery(getDomainType());
        Root<T> root = query.from(getDomainType());

        List<Predicate> predicates = MetadataUtils.buildKeyPredicates(keyData, idMetadata, cb, root);

        if (predicates.isEmpty()) {
            log.warn("No predicates built for composite key lookup");
            return Optional.empty();
        }

        query.where(predicates.toArray(new Predicate[0]));
        List<T> results = entityManager.createQuery(query).getResultList();

        return handleCompositeKeyResults(results);
    }

    private Optional<T> handleCompositeKeyResults(List<T> results) {
        if (results.size() > 1) {
            log.warn("Multiple entities ({}) found for composite key, returning first one", results.size());
        }

        Optional<T> result = results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
        log.debug("Composite key lookup found {} results", results.size());

        return result;
    }

    private List<Order> buildSortOrders(CriteriaBuilder cb, Root<T> root, Sort sort) {
        List<Order> orders = new ArrayList<>();

        for (Sort.Order order : sort) {
            try {
                Order criteriaOrder = order.isAscending()
                        ? cb.asc(root.get(order.property()))
                        : cb.desc(root.get(order.property()));
                orders.add(criteriaOrder);
            } catch (IllegalArgumentException e) {
                log.error("Invalid sort property '{}': {}", order.property(), e.getMessage());
            }
        }

        return orders;
    }

    protected IdMetadata getIdMetadata() {
        if (metadataCache == null) {
            throw new JpaException("EntityMetadataCache not initialized for " + getEntityName());
        }

        try {
            return metadataCache.getIdMetadata(getDomainType());
        } catch (JpaException e) {
            log.error("Failed to get IdMetadata: {}", e.getMessage());
            throw new JpaException("IdMetadata not available for " + getEntityName(), e);
        }
    }

    private String getEntityName() {
        return getDomainType().getSimpleName();
    }

}