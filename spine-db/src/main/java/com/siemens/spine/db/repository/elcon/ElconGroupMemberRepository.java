package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconGroupMember;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_group_member")
@ApplicationScoped
public class ElconGroupMemberRepository extends AbstractRestCRUDRepositoryImpl<ElconGroupMember, Long> {

    @Override
    protected boolean isNew(ElconGroupMember entity) {
        return entity.getGroupMemberId() == null;
    }

    @Override
    public Class<ElconGroupMember> getDomainType() {
        return ElconGroupMember.class;
    }
} 