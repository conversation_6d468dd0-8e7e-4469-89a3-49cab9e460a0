package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MapsId;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_io_link_interface")
public class ElconIoLinkInterface {

    @Id
    @Column(name = "interface_id", nullable = false)
    private Long interfaceId;

    @MapsId
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "interface_id", nullable = false)
    private ElconDeviceInterface elconDeviceInterface;

    @NotNull
    @Column(name = "member_id", nullable = false)
    private Long memberId;

    @Size(max = 255)
    @Column(name = "interface_type")
    private String interfaceType;

}