package com.siemens.spine.db.entity.elcon;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Getter
@Setter
@Entity
@Table(name = "elcon_cfg_active_slave")
public class ElconCfgActiveSlave {
    @EmbeddedId
    private ElconCfgActiveSlaveId id;

    @NotNull
    @Column(name = "cfg_id", nullable = false)
    private Long cfgId;

    @NotNull
    @Column(name = "project_id", nullable = false)
    private Long projectId;

    @Size(max = 255)
    @NotNull
    @Column(name = "hw_outfit_pattern", nullable = false)
    private String hwOutfitPattern;

    @Data
    @Embeddable
    public static class ElconCfgActiveSlaveId implements Serializable {
        private Long cfgId;
        private Long projectId;
        private String hwOutfitPattern;
    }

}