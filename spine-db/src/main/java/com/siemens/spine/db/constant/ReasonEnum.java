package com.siemens.spine.db.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 22/3/2023
 */
public enum ReasonEnum {
    INCORRECT(2, "fehlerhaft"),
    MORE_WORK_NEEDED(10, "More work needed");
    private static final Map<Integer, ReasonEnum> REASON_ENUM_MAP = new HashMap<>();

    static {
        for (ReasonEnum reasonEnum : values()) {
            REASON_ENUM_MAP.put(reasonEnum.getCode(), reasonEnum);
        }
    }

    private int code;
    private String value;

    ReasonEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public static ReasonEnum resolve(Integer code) {
        return (code != null ? REASON_ENUM_MAP.get(code) : null);
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public boolean matches(Integer code) {
        return (this == resolve(code));
    }
}

