package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconGlobalArea;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_global_area")
@ApplicationScoped
public class ElconGlobalAreaRepository extends AbstractRestCRUDRepositoryImpl<ElconGlobalArea, Long> {

    @Override
    protected boolean isNew(ElconGlobalArea entity) {
        return entity.getGlobalAreaId() == null;
    }

    @Override
    public Class<ElconGlobalArea> getDomainType() {
        return ElconGlobalArea.class;
    }
} 