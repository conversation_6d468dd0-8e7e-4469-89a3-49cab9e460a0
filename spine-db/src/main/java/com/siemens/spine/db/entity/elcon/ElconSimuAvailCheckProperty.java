package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_simu_avail_check_property")
public class ElconSimuAvailCheckProperty {

    @Id
    @Column(name = "property_id", nullable = false)
    private Long propertyId;

    @NotNull
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @Column(name = "cp_nr")
    private Short cpNr;

    @Column(name = "rtr_begin")
    private Integer rtrBegin;

    @Column(name = "rtr_end")
    private Integer rtrEnd;

    @Column(name = "empty_begin")
    private Integer emptyBegin;

    @Column(name = "empty_end")
    private Integer emptyEnd;

    @Column(name = "empty_limit")
    private Integer emptyLimit;

    @Column(name = "strictly_empty")
    private Integer strictlyEmpty;

    @Column(name = "single_mode")
    private Integer singleMode;

    @Size(max = 50)
    @Column(name = "special", length = 50)
    private String special;

    @Size(max = 50)
    @Column(name = "dynamic_static", length = 50)
    private String dynamicStatic;

    @Size(max = 10)
    @Column(name = "property_origin", length = 10)
    private String propertyOrigin;

    @Column(name = "extension_id")
    private Long extensionId;

}