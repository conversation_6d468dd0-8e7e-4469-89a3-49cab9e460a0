package com.siemens.spine.db.entity.elcon;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Getter
@Setter
@Entity
@Table(name = "elcon_time_sync_setup")
public class ElconTimeSyncSetup {

    @EmbeddedId
    private ElconTimeSyncSetupId id;

    @NotNull
    @Column(name = "interface_id", nullable = false)
    private Long interfaceId;

    @Size(max = 50)
    @Column(name = "ip_address_server_1", length = 50)
    private String ipAddressServer1;

    @Size(max = 50)
    @Column(name = "ip_address_server_2", length = 50)
    private String ipAddressServer2;


    @Data
    @Embeddable
    public static class ElconTimeSyncSetupId implements Serializable {
        private Long interfaceId;

        private String ipAddressServer1;

        private String ipAddressServer2;
    }

}