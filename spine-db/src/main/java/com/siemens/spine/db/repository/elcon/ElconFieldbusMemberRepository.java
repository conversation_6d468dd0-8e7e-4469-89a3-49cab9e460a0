package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconFieldbusMember;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_fieldbus_member")
@ApplicationScoped
public class ElconFieldbusMemberRepository extends AbstractRestCRUDRepositoryImpl<ElconFieldbusMember, Long> {

    @Override
    protected boolean isNew(ElconFieldbusMember entity) {
        return entity.getMemberId() == null;
    }

    @Override
    public Class<ElconFieldbusMember> getDomainType() {
        return ElconFieldbusMember.class;
    }
} 