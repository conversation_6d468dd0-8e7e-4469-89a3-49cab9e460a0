package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "elcon_simu_divert_tu_routing_property")
public class ElconSimuDivertTuRoutingProperty {

    @Id
    @Column(name = "property_id", nullable = false)
    private Long propertyId;

    @NotNull
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @Column(name = "cp_nr")
    private Short cpNr;

    @Column(name = "error")
    private Integer error;

    @Column(name = "pushbutton")
    private Integer pushbutton;

    @Column(name = "delay")
    private BigDecimal delay;

    @Column(name = "continue")
    private Integer continueField;

    @Size(max = 10)
    @Column(name = "property_origin", length = 10)
    private String propertyOrigin;

    @Column(name = "extension_id")
    private Long extensionId;

}