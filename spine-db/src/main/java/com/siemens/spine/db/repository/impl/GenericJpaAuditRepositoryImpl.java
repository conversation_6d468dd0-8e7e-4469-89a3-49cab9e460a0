package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.RevisionInfoEntity;
import com.siemens.spine.db.exception.JpaException;
import com.siemens.spine.db.repository.GenericJpaAuditRepository;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.exception.AuditException;
import org.hibernate.envers.exception.NotAuditedException;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static javax.transaction.Transactional.TxType.REQUIRED;

@Transactional(REQUIRED)
@Slf4j
public abstract class GenericJpaAuditRepositoryImpl<T, ID> extends GenericJpaRepositoryImpl<T, ID>
        implements GenericJpaAuditRepository<T, ID> {

    @Override
    public Optional<T> getHistoryAtRevision(ID id, int revision) {
        if (revision == 0) {
            log.info("Revision is 0, return current entity with id {}", id);
            return findById(id);
        }
        try {
            return Optional.of(getAuditReader().find(getDomainType(), id, revision));
        } catch (NotAuditedException e) {
            log.error("Entity type {} is not audited or revision {} does not exist for id {}",
                    getDomainType().getSimpleName(), revision, id, e);
            throw new JpaException(
                    "Failed to retrieve history at revision " + revision + " for entity " + getDomainType().getSimpleName() + " with id " + id,
                    e);
        }
    }

    @Override
    public List<Number> getRevisions(ID id) {
        try {
            return getAuditReader().getRevisions(getDomainType(), id);
        } catch (NotAuditedException e) {
            log.error("Failed to get revisions for entity {} with id {}: entity is not audited",
                    getDomainType().getSimpleName(), id, e);
            throw new JpaException(
                    "Failed to get revisions for entity " + getDomainType().getSimpleName() + " with id " + id + ": entity is not audited",
                    e);
        }
    }

    @Override
    public List<RevisionInfoEntity> getAllRevisionInfo(ID id) {
        List<Number> revisions = this.getRevisions(id);

        try {
            // You can optionally also retrieve the revision info
            Map<Number, RevisionInfoEntity> revisionInfoEntities = getAuditReader().findRevisions(
                    RevisionInfoEntity.class, new HashSet<>(revisions));

            return new ArrayList<>(revisionInfoEntities.values());
        } catch (Exception e) {
            log.error("Failed to retrieve revision info for entity {} with id {}", getDomainType().getSimpleName(), id,
                    e);
            throw new JpaException(
                    "Failed to retrieve revision info for entity " + getDomainType().getSimpleName() + " with id " + id,
                    e);
        }
    }

    protected AuditReader getAuditReader() {
        try {
            return AuditReaderFactory.get(entityManager);
        } catch (AuditException e) {
            log.error("Failed to create AuditReader: {}", e.getMessage(), e);
            throw new JpaException("Failed to create AuditReader for audit operations", e);
        }
    }

}

