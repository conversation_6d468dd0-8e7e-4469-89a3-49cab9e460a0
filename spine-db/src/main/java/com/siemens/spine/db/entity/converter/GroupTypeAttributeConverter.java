package com.siemens.spine.db.entity.converter;

import com.siemens.spine.db.constant.GroupTypeEnum;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 23/11/2022
 */
@Converter
public class GroupTypeAttributeConverter implements AttributeConverter<GroupTypeEnum, String> {

    @Override
    public String convertToDatabaseColumn(GroupTypeEnum groupTypeEnum) {
        if (groupTypeEnum == null) {
            return null;
        }
        return groupTypeEnum.getAbbrValue();
    }

    @Override
    public GroupTypeEnum convertToEntityAttribute(String type) {
        return GroupTypeEnum.resolve(type);
    }

}

