package com.siemens.spine.db.repository.metadata;

import com.siemens.spine.db.exception.JpaException;
import lombok.Builder;
import lombok.Getter;

import java.lang.reflect.Field;
import java.util.Objects;

/**
 * Metadata for entity attributes (fields).
 * Contains pre-computed information to avoid reflection overhead during runtime.
 * <p>
 * Supports both ID and non-ID attributes with cached JPA paths for criteria queries.
 *
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@Builder
public class AttributeMetadata {

    /**
     * The field name in the entity class.
     */
    private final String name;

    /**
     * The Java type of the field.
     */
    private final Class<?> type;

    /**
     * The reflection Field object (cached for performance).
     */
    private final Field field;

    /**
     * Whether this attribute is part of the entity's ID.
     */
    private final boolean isId;

    /**
     * Whether this attribute is an embedded ID (@EmbeddedId).
     */
    private final boolean isEmbeddedId;

    /**
     * Pre-computed JPA path for criteria queries.
     * Examples:
     * - Simple field: "fieldName"
     * - Embedded ID field: "id.fieldName"
     * - Association field: "association.fieldName"
     */
    private final String jpaPath;

    /**
     * The database column name (if applicable).
     */
    private final String columnName;

    /**
     * Snake case version of the field name for flexible matching.
     */
    private final String snakeCaseName;

    /**
     * Whether this field is nullable in the database.
     */
    private final boolean nullable;

    /**
     * Whether this field has a default value.
     */
    private final boolean hasDefaultValue;

    /**
     * The field's generation strategy (for ID fields).
     */
    private final String generationStrategy;

    /**
     * Checks if this attribute matches the given name (supports camelCase and snake_case).
     *
     * @param attributeName The name to match against
     * @return true if names match
     */
    public boolean matchesName(String attributeName) {
        if (attributeName == null) {
            return false;
        }
        return Objects.equals(name, attributeName) ||
                Objects.equals(snakeCaseName, attributeName);
    }

    /**
     * Gets the field value from the given entity instance.
     *
     * @param entity The entity instance
     * @return The field value
     * @throws RuntimeException if field access fails
     */
    public Object getValue(Object entity) {
        try {
            field.setAccessible(true);
            return field.get(entity);
        } catch (IllegalAccessException e) {
            throw new JpaException("Failed to get field value for " + name, e);
        }
    }

    /**
     * Sets the field value on the given entity instance.
     *
     * @param entity The entity instance
     * @param value  The value to set
     * @throws RuntimeException if field access fails
     */
    @SuppressWarnings("all")
    public void setValue(Object entity, Object value) {
        try {
            field.setAccessible(true);
            field.set(entity, value);
        } catch (IllegalAccessException e) {
            throw new JpaException("Failed to set field value for " + name, e);
        }
    }

    /**
     * Checks if this attribute is a String type.
     *
     * @return true if field type is String
     */
    public boolean isString() {
        return type == String.class;
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, type);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AttributeMetadata that = (AttributeMetadata) o;
        return Objects.equals(name, that.name) && Objects.equals(type, that.type);
    }

    @Override
    public String toString() {
        return "AttributeMetadata{" +
                "name='" + name + '\'' +
                ", type=" + type.getSimpleName() +
                ", isId=" + isId +
                ", jpaPath='" + jpaPath + '\'' +
                '}';
    }

}