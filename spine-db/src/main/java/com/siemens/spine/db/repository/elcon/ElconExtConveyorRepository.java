package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconExtConveyor;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_ext_conveyor")
@ApplicationScoped
public class ElconExtConveyorRepository extends AbstractRestCRUDRepositoryImpl<ElconExtConveyor, Long> {

    @Override
    protected boolean isNew(ElconExtConveyor entity) {
        return entity.getStrandId() == null;
    }

    @Override
    public Class<ElconExtConveyor> getDomainType() {
        return ElconExtConveyor.class;
    }
} 