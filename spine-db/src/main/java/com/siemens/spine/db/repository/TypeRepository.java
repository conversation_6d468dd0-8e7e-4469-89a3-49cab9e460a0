package com.siemens.spine.db.repository;

import com.siemens.spine.db.entity.TypeEntity;
import com.siemens.spine.db.repository.filter.TypeFilter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 26/12/2022
 */
public interface TypeRepository extends GenericJpaRepository<TypeEntity, String> {

    int deleteByIds(List<String> ids);

    List<TypeEntity> filter(TypeFilter filter);

    List<TypeEntity> findGenericType();

    TypeEntity findGenericTypeById(String typeId);

    List<String> findAllTypeIdsByProjectId(Long projectId);

}

