// WARNING: Entity ElconPlcCommunicationUnitPairing does not have @Id or @EmbeddedId. Please check entity definition!
package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconPlcCommunicationUnitPairing;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_plc_communication_unit_pairing")
@ApplicationScoped
public class ElconPlcCommunicationUnitPairingRepository extends AbstractRestCRUDRepositoryImpl<ElconPlcCommunicationUnitPairing, Long> {

    // WARNING: No @Id or @EmbeddedId found. isNew() always returns false.
    @Override
    protected boolean isNew(ElconPlcCommunicationUnitPairing entity) {
        return entity.getId() == null;
    }

    @Override
    public Class<ElconPlcCommunicationUnitPairing> getDomainType() {
        return ElconPlcCommunicationUnitPairing.class;
    }
} 