package com.siemens.spine.db.entity;

import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Parameter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;
import java.sql.Timestamp;

@TypeDef(
        name = "jsonb",
        typeClass = JsonBinaryType.class,
        parameters = @Parameter(name = "class", value = "com.siemens.spine.db.entity.ComponentReferenceSet")
)
@Entity
@Table(name = "project_component_version", indexes = {
        @Index(name = "idx_project_rev", columnList = "project_id, rev")  // Index on project_id and rev
})
@Getter
@Setter
public class ProjectComponentVersionEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "project_id", nullable = false)
    private Long projectId;

    @Column(name = "rev", nullable = false)
    private Integer rev;

    @Column
    @CreationTimestamp
    private Timestamp sysCreateDate;

    @Column
    @UpdateTimestamp
    private Timestamp sysModDate;

    private String createdBy;

    private String message;

    @Column(name = "version_name", nullable = false, unique = true)
    private String versionName;

    @Column(name = "version_id", nullable = false, unique = true)
    private String versionId; //May be we need for define identifier for the version with special business rules

    //That field is used to store the reference of the component and revision
    @Column(name = "component_refs", columnDefinition = "jsonb")
    //    @Convert(converter = ComponentReferenceSetConverter.class)
    @Type(type = "jsonb")
    private ComponentReferenceSet componentRefs;

}

