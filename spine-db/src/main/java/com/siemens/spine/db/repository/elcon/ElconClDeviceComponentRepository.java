package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconClDeviceComponent;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cl_device_component")
@ApplicationScoped
public class ElconClDeviceComponentRepository extends AbstractRestCRUDRepositoryImpl<ElconClDeviceComponent, Long> {

    @Override
    protected boolean isNew(ElconClDeviceComponent entity) {
        return entity.getComponentId() == null;
    }

    @Override
    public Class<ElconClDeviceComponent> getDomainType() {
        return ElconClDeviceComponent.class;
    }
} 