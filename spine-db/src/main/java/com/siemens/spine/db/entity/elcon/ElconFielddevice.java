package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_fielddevice")
public class ElconFielddevice {

    @Id
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @Size(max = 31)
    @NotNull
    @Column(name = "fd_type", nullable = false, length = 31)
    private String fdType;

    @Column(name = "di_addr_power_supply")
    private Long diAddrPowerSupply;

    @Column(name = "di_addr_fan")
    private Long diAddrFan;

    @Column(name = "di_addr_sitop_select")
    private Long diAddrSitopSelect;

    @Column(name = "di_addr_scalance")
    private Long diAddrScalance;

}