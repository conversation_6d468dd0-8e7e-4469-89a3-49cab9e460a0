package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconDeviceInterface;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_device_interface")
@ApplicationScoped
public class ElconDeviceInterfaceRepository extends AbstractRestCRUDRepositoryImpl<ElconDeviceInterface, Long> {

    @Override
    protected boolean isNew(ElconDeviceInterface entity) {
        return entity.getInterfaceId() == null;
    }

    @Override
    public Class<ElconDeviceInterface> getDomainType() {
        return ElconDeviceInterface.class;
    }
} 