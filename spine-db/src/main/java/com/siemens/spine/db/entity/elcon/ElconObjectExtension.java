package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_object_extension")
public class ElconObjectExtension {

    @Id
    @Column(name = "extension_id", nullable = false)
    private Long extensionId;

    @NotNull
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @Size(max = 255)
    @NotNull
    @Column(name = "extension_name", nullable = false)
    private String extensionName;

    @Size(max = 255)
    @Column(name = "extension_kind")
    private String extensionKind;

    @Size(max = 255)
    @Column(name = "item_name")
    private String itemName;

    @Size(max = 255)
    @Column(name = "layer_name")
    private String layerName;

    @Size(max = 255)
    @Column(name = "hw_outfit_name")
    private String hwOutfitName;

    @Size(max = 255)
    @Column(name = "annotation")
    private String annotation;

    @Size(max = 255)
    @Column(name = "extension_origin")
    private String extensionOrigin;

    @Column(name = "cp_nr")
    private Short cpNr;

    @Size(max = 255)
    @Column(name = "io_base_address")
    private String ioBaseAddress;

    @Size(max = 255)
    @Column(name = "extension_subkind")
    private String extensionSubkind;

    @Column(name = "logical_object_id")
    private Long logicalObjectId;

    @Column(name = "sw_relevant")
    private Short swRelevant;

    @Column(name = "extension_order")
    private Long extensionOrder;

    @Column(name = "es_relevant")
    private Short esRelevant;

}