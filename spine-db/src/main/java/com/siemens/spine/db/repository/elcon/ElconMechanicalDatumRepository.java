package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconMechanicalDatum;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_mechanical_data")
@ApplicationScoped
public class ElconMechanicalDatumRepository extends AbstractRestCRUDRepositoryImpl<ElconMechanicalDatum, Long> {

    @Override
    protected boolean isNew(ElconMechanicalDatum entity) {
        return entity.getComponentId() == null;
    }

    @Override
    public Class<ElconMechanicalDatum> getDomainType() {
        return ElconMechanicalDatum.class;
    }
} 