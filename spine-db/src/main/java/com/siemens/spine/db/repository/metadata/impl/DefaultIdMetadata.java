package com.siemens.spine.db.repository.metadata.impl;

import com.siemens.spine.db.exception.JpaException;
import com.siemens.spine.db.repository.metadata.AttributeMetadata;
import com.siemens.spine.db.repository.metadata.IdMetadata;
import com.siemens.spine.db.utils.CompositeKeyUtils;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Default implementation of IdMetadata.
 * Provides cached metadata for all composite key types with pre-computed JPA paths.
 *
 * <AUTHOR> <PERSON>am
 * @since 1.0
 */
@Slf4j
@Builder
@Getter
public class DefaultIdMetadata implements IdMetadata {

    private final CompositeKeyUtils.CompositeKeyType keyType;
    private final List<AttributeMetadata> idAttributes;
    private final Class<?> idClass;
    private final AttributeMetadata embeddedIdAttribute;
    private final String embeddedIdFieldName;

    // Pre-computed JPA path cache for performance
    private final ConcurrentMap<String, String> jpaPathCache = new ConcurrentHashMap<>();

    // Attribute name lookup cache (supports camelCase and snake_case)
    private final ConcurrentMap<String, AttributeMetadata> attributeCache = new ConcurrentHashMap<>();

    // Constructor needed for Builder
    DefaultIdMetadata(CompositeKeyUtils.CompositeKeyType keyType,
                      List<AttributeMetadata> idAttributes,
                      Class<?> idClass,
                      AttributeMetadata embeddedIdAttribute,
                      String embeddedIdFieldName,
                      ConcurrentMap<String, String> jpaPathCache,
                      ConcurrentMap<String, AttributeMetadata> attributeCache) {
        this.keyType = keyType;
        this.idAttributes = idAttributes;
        this.idClass = idClass;
        this.embeddedIdAttribute = embeddedIdAttribute;
        this.embeddedIdFieldName = embeddedIdFieldName;
        this.jpaPathCache.putAll(jpaPathCache);
        this.attributeCache.putAll(attributeCache);
    }

    @Override
    public boolean isComposite() {
        return keyType != CompositeKeyUtils.CompositeKeyType.SINGLE_ID;
    }

    @Override
    public AttributeMetadata getSingleIdAttribute() {
        if (isComposite()) {
            throw new IllegalStateException("Entity has composite keys, use getIdAttributes() instead");
        }
        return idAttributes.get(0);
    }

    @Override
    public String buildJpaPath(String attributeName) {
        return jpaPathCache.computeIfAbsent(attributeName, this::computeJpaPath);
    }

    @Override
    public Optional<AttributeMetadata> getIdAttribute(String attributeName) {
        return Optional.ofNullable(attributeCache.computeIfAbsent(attributeName, this::findAttribute));
    }

    @Override
    public boolean isIdAttribute(String attributeName) {
        return getIdAttribute(attributeName).isPresent();
    }

    @Override
    public int getIdComponentCount() {
        return idAttributes.size();
    }

    @Override
    public boolean isValidKeyData(CompositeKeyUtils.CompositeKeyData keyData) {
        if (keyData == null || keyData.getComponents().isEmpty()) {
            return false;
        }

        // Check if key data type matches entity type
        if (keyData.getKeyType() != this.keyType) {
            return false;
        }

        // Check if all required ID attributes are present
        if (keyData.size() != getIdComponentCount()) {
            return false;
        }

        // Validate that all key data components correspond to valid ID attributes
        for (CompositeKeyUtils.CompositeKeyData.KeyComponent component : keyData.getComponents()) {
            if (!isIdAttribute(component.fieldName())) {
                return false;
            }
        }

        return true;
    }

    private String computeJpaPath(String attributeName) {
        AttributeMetadata attribute = getIdAttribute(attributeName)
                .orElseThrow(() -> new JpaException("ID attribute not found: " + attributeName));

        switch (keyType) {
            case SINGLE_ID, ID_CLASS:
                return attribute.getName();

            case EMBEDDED_ID:
                // Embedded path: root.get("embeddedIdField").get("fieldName")
                return embeddedIdFieldName + "." + attribute.getName();

            default:
                throw new JpaException("Unsupported key type: " + keyType);
        }
    }

    private AttributeMetadata findAttribute(String attributeName) {
        return idAttributes.stream()
                .filter(attr -> attr.matchesName(attributeName))
                .findFirst()
                .orElse(null);
    }

    private void populateAttributeCache() {
        for (AttributeMetadata attribute : idAttributes) {
            // Cache both camelCase and snake_case names
            attributeCache.put(attribute.getName(), attribute);
            attributeCache.put(attribute.getSnakeCaseName(), attribute);
        }
    }

    /**
     * Builder method to create DefaultIdMetadata instances.
     */
    public static class DefaultIdMetadataBuilder {

        public DefaultIdMetadata build() {
            DefaultIdMetadata metadata = new DefaultIdMetadata(keyType, idAttributes, idClass,
                    embeddedIdAttribute, embeddedIdFieldName, new ConcurrentHashMap<>(),
                    new ConcurrentHashMap<>());

            // Pre-populate attribute cache for performance
            metadata.populateAttributeCache();

            log.debug("Created IdMetadata for key type {} with {} attributes",
                    keyType, idAttributes.size());

            return metadata;
        }

    }

}