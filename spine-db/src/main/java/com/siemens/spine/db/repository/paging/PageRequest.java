package com.siemens.spine.db.repository.paging;

public class PageRequest implements Pageable {

    private final int page;

    private final int size;

    private final Sort sort;

    private PageRequest(int page, int size, Sort sort) {
        if (page < 0) {
            throw new IllegalArgumentException("Page index must not be less than zero!");
        }
        if (size <= 0) {
            throw new IllegalArgumentException("Page size must be greater than zero!");
        }

        this.page = page;
        this.size = size;
        this.sort = sort != null ? sort : Sort.unsorted();
    }

    public static PageRequest of(int page, int size) {
        return new PageRequest(page, size, Sort.unsorted());
    }

    public static PageRequest of(int page, int size, Sort sort) {
        return new PageRequest(page, size, sort);
    }

    public static PageRequest of(int page, int size, Sort.Direction direction, String... properties) {
        return new PageRequest(page, size, Sort.by(direction, properties));
    }

    @Override
    public int getPageNumber() {
        return page;
    }

    @Override
    public int getPageSize() {
        return size;
    }

    @Override
    public long getOffset() {
        return (long) page * size;
    }

    @Override
    public Sort getSort() {
        return sort;
    }

    @Override
    public Pageable next() {
        return new PageRequest(page + 1, size, sort);
    }

    @Override
    public Pageable previousOrFirst() {
        return page == 0 ? this : new PageRequest(page - 1, size, sort);
    }

    @Override
    public Pageable first() {
        return new PageRequest(0, size, sort);
    }

    @Override
    public boolean hasPrevious() {
        return page > 0;
    }

}