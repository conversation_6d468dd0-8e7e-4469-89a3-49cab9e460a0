package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MapsId;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@Entity
@Table(name = "elcon_device_plugin")
public class ElconDevicePlugin {

    @Id
    @Column(name = "device_id", nullable = false)
    private Long deviceId;

    @MapsId
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "device_id", nullable = false)
    private ElconDevice elconDevice;

    @NotNull
    @Column(name = "device_id_chassis", nullable = false)
    private Long deviceIdChassis;

    @Column(name = "slot_nr")
    private Long slotNr;

    @Column(name = "new_potential_group_flag")
    private Short newPotentialGroupFlag;

}