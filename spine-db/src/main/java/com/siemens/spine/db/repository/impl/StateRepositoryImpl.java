package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.StateEntity;
import com.siemens.spine.db.repository.StateRepository;
import com.siemens.spine.db.repository.filter.ComponentStateFilter;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.Query;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaDelete;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@ApplicationScoped
@Transactional(TxType.REQUIRED)
public class StateRepositoryImpl extends GenericJpaRepositoryImpl<StateEntity, Long> implements StateRepository {

    private static final String ASC = "ASC";

    @Override
    public Class<StateEntity> getDomainType() {
        return StateEntity.class;
    }

    @Override
    public List<StateEntity> findLatestComponentState(List<Long> componentIds) {
        assert componentIds != null;
        Query query = entityManager.createNativeQuery(
                "SELECT DISTINCT ON (COMPONENT_ID) ID \n" +
                        "\t FROM STATE \n" +
                        "\t WHERE COMPONENT_ID in (:componentIds) \n" +
                        "\t ORDER BY COMPONENT_ID, STATUS_DATE DESC \n");
        query.setParameter("componentIds", componentIds);

        List<Integer> stateIdObjs = query.getResultList();
        if (stateIdObjs == null || stateIdObjs.isEmpty()) {
            return Collections.emptyList();
        }

        List<Long> stateIds = stateIdObjs.stream().map(Long::valueOf).toList();
        Query stateQuery = entityManager.createQuery(
                "select state from StateEntity state where state.id in (:stateIds)",
                StateEntity.class);
        stateQuery.setParameter("stateIds", stateIds);
        return stateQuery.getResultList();
    }

    @Override
    public List<StateEntity> filter(ComponentStateFilter filter) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<StateEntity> cq = cb.createQuery(StateEntity.class);
        Root<StateEntity> rootEntry = cq.from(StateEntity.class);

        List<Predicate> predicates = buildWhereClause(filter, cb, rootEntry);

        cq.select(rootEntry)
                .where(predicates.toArray(new Predicate[0])); // sort by changed date_time

        if (filter != null && ASC.equals(filter.getOrderBy())) {
            cq.orderBy(cb.asc(rootEntry.get("statusDate")));
        } else {
            cq.orderBy(cb.desc(rootEntry.get("statusDate")));
        }

        return entityManager.createQuery(cq).getResultList();
    }

    @Override
    public long deleteByComponentIds(List<Long> componentIds) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaDelete<StateEntity> cq = cb.createCriteriaDelete(StateEntity.class);
        Root<StateEntity> rootEntry = cq.from(StateEntity.class);

        CriteriaDelete<StateEntity> query = cq.where(
                cb.and(rootEntry.get("component").get("id").in(componentIds))
        );

        return entityManager.createQuery(query).executeUpdate();
    }

    /**
     * Build the where clause of the query
     *
     * @param filter
     * @param cb
     * @param rootEntry
     * @return
     */
    private List<Predicate> buildWhereClause(ComponentStateFilter filter,
                                             CriteriaBuilder cb,
                                             Root<StateEntity> rootEntry) {
        if (filter == null) {
            return Collections.emptyList();
        }

        List<Predicate> predicates = new ArrayList<>();

        // adding component to filtering
        List<Long> componentIds = filter.getComponentIds();
        if (componentIds != null && !componentIds.isEmpty()) {
            predicates.add(
                    cb.and(rootEntry.get("component").get("id").in(componentIds))
            );
        }

        // adding modifier to filtering
        String changedBy = filter.getChangedBy();
        if (changedBy != null && !changedBy.trim().equals("")) {
            String textPattern = "%" + changedBy.trim().toLowerCase() + "%";
            predicates.add(
                    cb.like(cb.lower(rootEntry.get("username")), textPattern)
            );
        }

        // adding changed_date to filtering
        if (filter.getChangedAfter() != null) {
            predicates.add(
                    cb.and(cb.greaterThanOrEqualTo(rootEntry.get("statusDate"), filter.getChangedAfter()))
            );
        }

        if (filter.getChangedBefore() != null) {
            predicates.add(
                    cb.and(cb.lessThanOrEqualTo(rootEntry.get("statusDate"), filter.getChangedBefore()))
            );
        }

        // adding change group to filtering
        if (filter.getChangeGroupId() != null) {
            predicates.add(
                    cb.equal(rootEntry.get("changeGroup").get("id"), filter.getChangeGroupId())
            );
        }

        return predicates;
    }

}

