package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "elcon_conveyor")
public class ElconConveyor {

    @Id
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @Size(max = 255)
    @Column(name = "sys_type")
    private String sysType;

    @Size(max = 255)
    @Column(name = "conv_type")
    private String convType;

    @Size(max = 255)
    @Column(name = "mechanical_name")
    private String mechanicalName;

    @Column(name = "length")
    private BigDecimal length;

    @Column(name = "speed")
    private Double speed;

    @Column(name = "throughput")
    private BigDecimal throughput;

    @Column(name = "jam_area_flag")
    private Short jamAreaFlag;

    @Size(max = 5)
    @Column(name = "overdrive", length = 5)
    private String overdrive;

    @Column(name = "sw_master_upstream")
    private Long swMasterUpstream;

    @Column(name = "sw_master_downstream")
    private Long swMasterDownstream;

    @Column(name = "tracking")
    private Long tracking;

    @Column(name = "send_unit_filling")
    private Short sendUnitFilling;

}