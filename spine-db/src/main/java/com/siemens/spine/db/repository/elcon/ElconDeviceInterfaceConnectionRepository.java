package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconDeviceInterfaceConnection;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_device_interface_connection")
@ApplicationScoped
public class ElconDeviceInterfaceConnectionRepository extends AbstractRestCRUDRepositoryImpl<ElconDeviceInterfaceConnection, Long> {

    @Override
    protected boolean isNew(ElconDeviceInterfaceConnection entity) {
        return entity.getConnectionId() == null;
    }

    @Override
    public Class<ElconDeviceInterfaceConnection> getDomainType() {
        return ElconDeviceInterfaceConnection.class;
    }
} 