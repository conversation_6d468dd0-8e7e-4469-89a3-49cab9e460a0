package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@Entity
@Table(name = "elcon_link_estop_box_safety_button")
public class ElconLinkEstopBoxSafetyButton {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "link_id", nullable = false)
    private Long linkId;

    @NotNull
    @Column(name = "panel_object_id", nullable = false)
    private Long panelObjectId;

    @NotNull
    @Column(name = "pushbutton_object_id", nullable = false)
    private Long pushbuttonObjectId;

    @Column(name = "\"position\"")
    private Short position;

}