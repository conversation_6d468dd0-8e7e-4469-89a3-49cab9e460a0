package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_common_cfg_sw_set")
public class ElconCommonCfgSwSet {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "sw_set_id", nullable = false)
    private Long swSetId;

    @Size(max = 255)
    @Column(name = "sw_set_key")
    private String swSetKey;

    @Size(max = 255)
    @Column(name = "sw_set_name")
    private String swSetName;

    @Size(max = 255)
    @Column(name = "domain")
    private String domain;

}