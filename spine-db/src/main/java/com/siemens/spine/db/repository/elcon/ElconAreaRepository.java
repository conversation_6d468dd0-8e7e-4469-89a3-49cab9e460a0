package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconArea;

import javax.enterprise.context.ApplicationScoped;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 19/6/2025
 **/
@RestRepository(path = "elcon_area")
@ApplicationScoped
public class ElconAreaRepository extends AbstractRestCRUDRepositoryImpl<ElconArea, Long> {

    @Override
    protected boolean isNew(ElconArea entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconArea> getDomainType() {
        return ElconArea.class;
    }

}
