package com.siemens.spine.db.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Entity
@Table(name = "component")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Audited
public class ComponentEntity implements Serializable {

    //has to be all lower case otherwise hibernate throws errors
    @Id
    private Long id;

    private Integer amountBufferElec;
    private Integer amountBufferMech;
    private String breakType;
    private Integer bufferSize;
    private String comment;
    private Integer curveAngle;
    private String curveDirection;
    private Integer curveRadius;
    private String drivePosition;
    private String driveStation;
    private Integer height;
    private Integer lengthTotal;
    private Double load;
    private String motorController;
    private String motorDirection;
    private String motorPosition;
    private String parent;
    private String plantDomain;
    private String posNo;
    private String reference;
    private Integer rotation;
    private Double rotation3D;
    private Double section1Angle;
    private Double section1Length;
    private Double section2Angle;
    private Double section2Length;
    private Double section3Angle;
    private Double section3Length;
    private Double section4Angle;
    private Double section4Length;
    private String slaveDrive;
    private Double slope;
    private String speed;
    private Integer startStopCycles;
    private String supplier;
    private Integer throughput;
    private String typeId;
    private Boolean unitReversible;
    private String usage;
    private Integer version;
    private String virtual;
    private Integer width;
    private Integer installationHours;
    private Integer engineeringHours;
    private String colorGroup;
    private String akz;
    private String customerAKZ;
    private String autoCadLayer;
    private String user1;
    private String user2;
    private String user3;
    private String user4;
    private String user5;
    private String driveSide;
    private String angleCorr;
    private String levelStart;
    private String levelEnd;
    private String controlNr;
    private String vaultInstance;
    private String drawingVersion;
    private String drawingRevision;
    private String outfit3D;
    private String parcelTypeID;
    private String orderPlant;
    private String designPlant;
    private Boolean storageConveyor;
    private Double drivePulleyDiameter;
    private Double driveShaftDiameter;
    private Double acceleration;
    private Integer currentState;
    private Long projectId;

    @Column
    @CreationTimestamp
    private Timestamp sysCreateDate;

    @Column
    @UpdateTimestamp
    private Timestamp sysModDate;

    @ManyToMany(cascade = { CascadeType.PERSIST, CascadeType.MERGE })
    @JoinTable(name = "groupcomponent",
            joinColumns = @JoinColumn(name = "componentid", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "groupid", referencedColumnName = "id")
    )
    @Audited
    private Set<GroupEntity> groups = new HashSet<>();

    @OneToMany(mappedBy = "component", cascade = CascadeType.ALL, orphanRemoval = true)
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private List<ConnectionPointEntity> connectionPoints = new ArrayList<>();

    @OneToMany(mappedBy = "component", cascade = CascadeType.ALL, orphanRemoval = true)
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private List<DecompositionEntity> decompositions = new ArrayList<>();

    @OneToOne(mappedBy = "component", cascade = CascadeType.ALL)
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private PositionEntity position;

    @OneToOne(mappedBy = "component", cascade = CascadeType.ALL)
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private ComponentStatusEntity status;

    @OneToMany(mappedBy = "component", cascade = CascadeType.ALL, orphanRemoval = true)
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private List<StateEntity> states = new ArrayList<>();

    @OneToMany(mappedBy = "component", cascade = CascadeType.ALL, orphanRemoval = true)
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private List<DriveAssignmentDataEntity> driveAssignmentDatas = new ArrayList<>();

    public void updateConnectionPoints(List<ConnectionPointEntity> connectionPoints) {
        Iterator<ConnectionPointEntity> iterator = this.connectionPoints.iterator();
        while (iterator.hasNext()) {
            ConnectionPointEntity connectionPoint = iterator.next();
            connectionPoint.setComponent(null);
            iterator.remove();
        }
        for (ConnectionPointEntity connectionPoint : connectionPoints) {
            this.connectionPoints.add(connectionPoint);
            connectionPoint.setComponent(this);
        }
    }

    public void updateDecompositions(List<DecompositionEntity> decompositions) {
        for (DecompositionEntity decomposition : decompositions) {
            decomposition.setComponent(this);
            this.decompositions.add(decomposition);
        }
    }

    public void addGroup(GroupEntity group) {
        this.groups.add(group);
        group.getComponents().add(this);
    }

    public void removeGroup(GroupEntity group) {
        this.groups.remove(group);
        group.getComponents().remove(this);
    }

    public void removeConnectionPoints() {
        Iterator<ConnectionPointEntity> iterator = this.connectionPoints.iterator();
        while (iterator.hasNext()) {
            ConnectionPointEntity connectionPoint = iterator.next();
            connectionPoint.setComponent(null);
            iterator.remove();
        }
    }

    public void removeDecompositions() {
        Iterator<DecompositionEntity> iterator = this.decompositions.iterator();
        while (iterator.hasNext()) {
            DecompositionEntity decomposition = iterator.next();
            decomposition.setComponent(null);
            iterator.remove();
        }
    }

    public void removeDecompositionsBySource(String source) {
        if (StringUtils.isEmpty(source)) {
            return;
        }
        Iterator<DecompositionEntity> iterator = this.decompositions.iterator();
        while (iterator.hasNext()) {
            DecompositionEntity decomposition = iterator.next();
            if (source.equalsIgnoreCase(decomposition.getSource())) {
                decomposition.setComponent(null);
                iterator.remove();
            }
        }
    }

    public void removeDriveAssignmentData() {
        Iterator<DriveAssignmentDataEntity> iterator = this.driveAssignmentDatas.iterator();
        while (iterator.hasNext()) {
            DriveAssignmentDataEntity driveAssignmentData = iterator.next();
            driveAssignmentData.setComponent(null);
            iterator.remove();
        }
    }

    public void addDriveAssignmentData(DriveAssignmentDataEntity driveAssignmentData) {
        this.driveAssignmentDatas.add(driveAssignmentData);
        driveAssignmentData.setComponent(this);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ComponentEntity that = (ComponentEntity) o;
        return Objects.equals(id, that.id);
    }

}

