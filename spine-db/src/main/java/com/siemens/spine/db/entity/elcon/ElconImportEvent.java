package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_import_event")
public class ElconImportEvent {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Column(name = "project_version_id", nullable = false)
    private Long projectVersionId;

    @Size(max = 10)
    @Column(name = "event_type", length = 10)
    private String eventType;

    @Size(max = 2000)
    @Column(name = "event_text", length = 2000)
    private String eventText;

    @Size(max = 255)
    @Column(name = "item_name")
    private String itemName;

    @Size(max = 255)
    @Column(name = "unique_id")
    private String uniqueId;

    @Size(max = 255)
    @Column(name = "filename")
    private String filename;

    @Size(max = 255)
    @Column(name = "item_plc")
    private String itemPlc;

    @Size(max = 255)
    @Column(name = "event_category")
    private String eventCategory;

}