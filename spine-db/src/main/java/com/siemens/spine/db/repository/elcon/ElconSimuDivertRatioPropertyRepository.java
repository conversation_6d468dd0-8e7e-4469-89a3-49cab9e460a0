package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconSimuDivertRatioProperty;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_simu_divert_ratio_property")
@ApplicationScoped
public class ElconSimuDivertRatioPropertyRepository extends AbstractRestCRUDRepositoryImpl<ElconSimuDivertRatioProperty, Long> {

    @Override
    protected boolean isNew(ElconSimuDivertRatioProperty entity) {
        return entity.getPropertyId() == null;
    }

    @Override
    public Class<ElconSimuDivertRatioProperty> getDomainType() {
        return ElconSimuDivertRatioProperty.class;
    }
} 