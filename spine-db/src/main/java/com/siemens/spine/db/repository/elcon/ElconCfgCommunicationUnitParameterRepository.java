package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCfgCommunicationUnitParameter;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_cfg_communication_unit_parameter")
@ApplicationScoped
public class ElconCfgCommunicationUnitParameterRepository extends AbstractRestCRUDRepositoryImpl<ElconCfgCommunicationUnitParameter, Long> {

    @Override
    protected boolean isNew(ElconCfgCommunicationUnitParameter entity) {
        return entity.getId() == null;
    }

    @Override
    public Class<ElconCfgCommunicationUnitParameter> getDomainType() {
        return ElconCfgCommunicationUnitParameter.class;
    }
} 