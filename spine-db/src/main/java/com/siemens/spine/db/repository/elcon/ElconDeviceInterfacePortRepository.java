package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconDeviceInterfacePort;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_device_interface_port")
@ApplicationScoped
public class ElconDeviceInterfacePortRepository extends AbstractRestCRUDRepositoryImpl<ElconDeviceInterfacePort, Long> {

    @Override
    protected boolean isNew(ElconDeviceInterfacePort entity) {
        return entity.getPortId() == null;
    }

    @Override
    public Class<ElconDeviceInterfacePort> getDomainType() {
        return ElconDeviceInterfacePort.class;
    }
} 