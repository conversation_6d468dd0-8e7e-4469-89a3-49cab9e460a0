package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_safety_line")
public class ElconSafetyLine {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "line_id", nullable = false)
    private Long lineId;

    @Size(max = 255)
    @Column(name = "item_name")
    private String itemName;

    @Column(name = "line_nr")
    private Short lineNr;

    @NotNull
    @Column(name = "panel_object_id", nullable = false)
    private Long panelObjectId;

    @Size(max = 255)
    @Column(name = "line_type")
    private String lineType;

}