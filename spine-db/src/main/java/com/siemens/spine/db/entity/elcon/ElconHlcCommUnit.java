package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_hlc_comm_unit")
public class ElconHlcCommUnit {

    @Id
    @NotNull
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @Size(max = 255)
    @Column(name = "operating_mode")
    private String operatingMode;

    @Column(name = "iecp_nr")
    private Long iecpNr;

    @Size(max = 255)
    @Column(name = "partner_name")
    private String partnerName;

    @Size(max = 20)
    @Column(name = "partner_system_type", length = 20)
    private String partnerSystemType;

    @Size(max = 15)
    @Column(name = "partner_ip_address", length = 15)
    private String partnerIpAddress;

    @Size(max = 15)
    @Column(name = "partner_subnet_mask", length = 15)
    private String partnerSubnetMask;

    @Column(name = "partner_port")
    private Integer partnerPort;

    @Size(max = 20)
    @Column(name = "tim_dest_nodeid", length = 20)
    private String timDestNodeid;

    @Column(name = "tim_id")
    private Long timId;

    @Size(max = 15)
    @Column(name = "partner_gateway_ip", length = 15)
    private String partnerGatewayIp;

}