package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_cl_fieldbus_device")
public class ElconClFieldbusDevice {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "device_id", nullable = false)
    private Long deviceId;

    @NotNull
    @Column(name = "outfit_id", nullable = false)
    private Long outfitId;

    @Size(max = 20)
    @Column(name = "device_kind", length = 20)
    private String deviceKind;

    @Column(name = "device_nr")
    private Integer deviceNr;

    @Size(max = 255)
    @Column(name = "device_name")
    private String deviceName;

    @Size(max = 255)
    @Column(name = "sub_type")
    private String subType;

    @Size(max = 255)
    @Column(name = "order_number")
    private String orderNumber;

}