package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconCommunicationUnit;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_communication_unit")
@ApplicationScoped
public class ElconCommunicationUnitRepository extends AbstractRestCRUDRepositoryImpl<ElconCommunicationUnit, Long> {

    @Override
    protected boolean isNew(ElconCommunicationUnit entity) {
        return entity.getObjectId() == null;
    }

    @Override
    public Class<ElconCommunicationUnit> getDomainType() {
        return ElconCommunicationUnit.class;
    }
} 