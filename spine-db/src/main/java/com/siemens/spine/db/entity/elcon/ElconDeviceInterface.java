package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_device_interface")
public class ElconDeviceInterface {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "interface_id", nullable = false)
    private Long interfaceId;

    @NotNull
    @Column(name = "device_id", nullable = false)
    private Long deviceId;

    @Size(max = 255)
    @Column(name = "interface_kind")
    private String interfaceKind;

    @Size(max = 255)
    @Column(name = "interface_name")
    private String interfaceName;

    @Size(max = 255)
    @Column(name = "full_interface_name")
    private String fullInterfaceName;

    @Column(name = "\"position\"")
    private Short position;

}