package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_prj_init_logistics_property")
public class ElconPrjInitLogisticsProperty {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "cfg_id", nullable = false)
    private Long cfgId;

    @Size(max = 255)
    @Column(name = "logistics_property")
    private String logisticsProperty;

    @Column(name = "load")
    private Integer load;

    @Size(max = 255)
    @Column(name = "description")
    private String description;

    @Column(name = "max_tu_length")
    private Integer maxTuLength;

    @Column(name = "avg_tu_length")
    private Integer avgTuLength;

    @Column(name = "min_tu_length")
    private Integer minTuLength;

}