package com.siemens.spine.db.entity.elcon;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Getter
@Setter
@Entity
@Table(name = "elcon_prj_init_default_hlc_communication_objects")
public class ElconPrjInitDefaultHlcCommunicationObject {
    @EmbeddedId
    private ElconPrjInitDefaultHlcCommunicationObjectId id;

    @NotNull
    @Column(name = "cfg_id", nullable = false)
    private Long cfgId;

    @Size(max = 200)
    @Column(name = "channel", length = 200)
    private String channel;

    @NotNull
    @Column(name = "unit_nr", nullable = false)
    private Long unitNr;

    @Size(max = 200)
    @NotNull
    @Column(name = "unit_name", nullable = false, length = 200)
    private String unitName;

    @Size(max = 200)
    @NotNull
    @Column(name = "unit_type", nullable = false, length = 200)
    private String unitType;

    @NotNull
    @Column(name = "tim_id", nullable = false)
    private Long timId;

    @Size(max = 200)
    @Column(name = "tim_destination_node", length = 200)
    private String timDestinationNode;

    @Size(max = 200)
    @Column(name = "tsap_host", length = 200)
    private String tsapHost;

    @Size(max = 200)
    @Column(name = "tsap_plc", length = 200)
    private String tsapPlc;

    @Size(max = 200)
    @Column(name = "unit_operating_node", length = 200)
    private String unitOperatingNode;

    @NotNull
    @Column(name = "iecp_nr", nullable = false)
    private Long iecpNr;

    @NotNull
    @Column(name = "assigned_highav_unit_nr", nullable = false)
    private Long assignedHighavUnitNr;

    @Size(max = 200)
    @Column(name = "object_comment", length = 200)
    private String objectComment;

    @Size(max = 200)
    @Column(name = "domain", length = 200)
    private String domain;

    @Size(max = 200)
    @Column(name = "source_com_channel_device_name", length = 200)
    private String sourceComChannelDeviceName;

    @Size(max = 200)
    @Column(name = "source_com_channel_interface_name", length = 200)
    private String sourceComChannelInterfaceName;

    @Size(max = 255)
    @Column(name = "partner_name")
    private String partnerName;

    @Size(max = 20)
    @Column(name = "partner_system_type", length = 20)
    private String partnerSystemType;

    @Size(max = 15)
    @Column(name = "partner_ip_address", length = 15)
    private String partnerIpAddress;

    @Size(max = 15)
    @Column(name = "partner_subnet_mask", length = 15)
    private String partnerSubnetMask;

    @Size(max = 15)
    @Column(name = "partner_gateway_ip", length = 15)
    private String partnerGatewayIp;

    @Column(name = "partner_port")
    private Integer partnerPort;

    @Size(max = 200)
    @Column(name = "unit_operating_mode", length = 200)
    private String unitOperatingMode;

    @Data
    @Embeddable
    public static class ElconPrjInitDefaultHlcCommunicationObjectId implements Serializable {
        private Long cfgId;

        private Long unitNr;

        private String unitName;

        private String unitType;

        private Long timId;

        private String timDestinationNode;

        private String tsapHost;

        private String tsapPlc;

        private String unitOperatingNode;

        private Long iecpNr;

        private Long assignedHighavUnitNr;

        private String objectComment;

        private String sourceComChannelDeviceName;

        private String sourceComChannelInterfaceName;

        private String partnerName;

        private String partnerSystemType;

        private String partnerIpAddress;

        private String partnerSubnetMask;

        private String partnerGatewayIp;

        private Integer partnerPort;

        private String unitOperatingMode;
    }
}