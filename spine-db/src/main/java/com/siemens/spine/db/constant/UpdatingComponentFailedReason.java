package com.siemens.spine.db.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UpdatingComponentFailedReason {

    /**
     * The state of
     */
    INVALID_STATUS,

    /**
     * The component was marked as deleted before
     */
    MARKED_AS_DELETED,

    /**
     * Other reasons (because an exception occurred or The component is unknown by id, etc.)
     */
    OTHER
}

