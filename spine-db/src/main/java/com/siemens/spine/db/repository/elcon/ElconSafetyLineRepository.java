package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconSafetyLine;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_safety_line")
@ApplicationScoped
public class ElconSafetyLineRepository extends AbstractRestCRUDRepositoryImpl<ElconSafetyLine, Long> {

    @Override
    protected boolean isNew(ElconSafetyLine entity) {
        return entity.getLineId() == null;
    }

    @Override
    public Class<ElconSafetyLine> getDomainType() {
        return ElconSafetyLine.class;
    }
} 