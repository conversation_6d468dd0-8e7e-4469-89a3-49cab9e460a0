package com.siemens.spine.db.constant;

import java.util.HashMap;
import java.util.Map;

public enum ProjectTypeClassificationEnum {

    GENERIC("generic"),
    NONSTAND("nonstandard"),
    DERIVED("derived"),
    USER("user"),
    PREDEF("predefined"),
    UNKNOWN("unknown");

    private static final Map<String, ProjectTypeClassificationEnum> PROJ_TYPE_CLASS_ENUM_MAP = new HashMap<>(16);

    static {
        for (ProjectTypeClassificationEnum projectType : values()) {
            PROJ_TYPE_CLASS_ENUM_MAP.put(projectType.fullName, projectType);
        }
    }

    public final String fullName;

    ProjectTypeClassificationEnum(String fullName) {
        this.fullName = fullName;
    }

    public static ProjectTypeClassificationEnum resolve(String value) {
        if (value == null) {
            return null;
        }
        return PROJ_TYPE_CLASS_ENUM_MAP.get(value);
    }

    public String getValue() {
        return fullName;
    }

}

