package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconLinkEstopBoxSafetyButton;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_link_estop_box_safety_button")
@ApplicationScoped
public class ElconLinkEstopBoxSafetyButtonRepository extends AbstractRestCRUDRepositoryImpl<ElconLinkEstopBoxSafetyButton, Long> {

    @Override
    protected boolean isNew(ElconLinkEstopBoxSafetyButton entity) {
        return entity.getLinkId() == null;
    }

    @Override
    public Class<ElconLinkEstopBoxSafetyButton> getDomainType() {
        return ElconLinkEstopBoxSafetyButton.class;
    }
} 