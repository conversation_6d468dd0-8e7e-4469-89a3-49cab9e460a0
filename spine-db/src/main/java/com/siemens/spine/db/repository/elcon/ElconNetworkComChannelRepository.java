package com.siemens.spine.db.repository.elcon;

import com.siemens.spine.db.entity.elcon.ElconNetworkComChannel;

import javax.enterprise.context.ApplicationScoped;

@RestRepository(path = "elcon_network_com_channel")
@ApplicationScoped
public class ElconNetworkComChannelRepository extends AbstractRestCRUDRepositoryImpl<ElconNetworkComChannel, Long> {

    @Override
    protected boolean isNew(ElconNetworkComChannel entity) {
        return entity.getChannelId() == null;
    }

    @Override
    public Class<ElconNetworkComChannel> getDomainType() {
        return ElconNetworkComChannel.class;
    }
} 