package com.siemens.spine.db.repository.views;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 20/3/2023
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ComponentView {

    private Long id;
    private Integer currentState;
    private String comment;
    private String typeId;
    private GroupView groupView;
    private String posNo;
    private String colorGroup;
    private Integer lengthTotal;
    private Integer width;
    private String speed;
    private Double load;
    private Integer throughput;
    private String motorController;
    private Boolean unitReversible;
    private String plantDomain;
    private Integer amountBufferElec;
    private Integer amountBufferMech;
    private Integer bufferSize;
    private String drivePosition;
    private String motorDirection;
    private Integer curveAngle;
    private String curveDirection;
    private Integer curveRadius;
    private String breakType;
    private Double section1Angle;
    private Double section1Length;
    private Double section2Angle;
    private Double section2Length;
    private Double section3Angle;
    private Double section3Length;
    private Double section4Angle;
    private Double section4Length;
    private String reference;
    private Integer rotation;
    private Double rotation3D;
    private Timestamp sysCreateDate;
    private Timestamp sysModDate;
    private Integer bomState;

}

