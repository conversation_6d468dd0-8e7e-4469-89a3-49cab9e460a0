package com.siemens.spine.db.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 24/2/2023
 */
public enum HierarchyTypeEnum {
    SORTER("Sorter"),
    CAROUSEL("Carousel"),
    INDUCTION_GROUP("InductionGroup"),
    OUTLET_GROUP("OutletGroup"),
    FUNCTION_GROUP("FunctionGroup"),
    CONVEYOR("Conveyor"),
    ADD_ON("AddOn"),
    SIMPLE("Simple"),
    ;
    private static final Map<String, HierarchyTypeEnum> HIERARCHY_TYPE_ENUM_MAP = new HashMap<>();

    static {
        for (HierarchyTypeEnum hierarchyType : values()) {
            HIERARCHY_TYPE_ENUM_MAP.put(hierarchyType.getValue().toLowerCase(), hierarchyType);
        }
    }

    private final String value;

    HierarchyTypeEnum(String value) {
        this.value = value;
    }

    public static HierarchyTypeEnum resolve(String type) {
        return (type != null ? HIERARCHY_TYPE_ENUM_MAP.get(type.toLowerCase()) : null);
    }

    public String getValue() {
        return value;
    }

    public boolean matches(String type) {
        return (this == resolve(type));
    }
}

