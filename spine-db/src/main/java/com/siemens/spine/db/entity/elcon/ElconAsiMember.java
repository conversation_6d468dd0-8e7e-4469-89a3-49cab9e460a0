package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_asi_member")
public class ElconAsiMember {

    @Id
    @Column(name = "member_id", nullable = false)
    private Long memberId;

    @Column(name = "asi_address")
    private Integer asiAddress;

    @Column(name = "asi_seq")
    private Long asiSeq;

    @Column(name = "asi_nibbles")
    private Long asiNibbles;

    @Size(max = 5)
    @Column(name = "asi_address_ab", length = 5)
    private String asiAddressAb;

    @Column(name = "asi_segment")
    private Short asiSegment;

    @Size(max = 10)
    @Column(name = "asi_slave_type", length = 10)
    private String asiSlaveType;

}