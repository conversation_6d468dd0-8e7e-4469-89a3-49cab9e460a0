package com.siemens.spine.db.repository.views;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 20/3/2023
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GroupView {

    private String drawing;

    private String deliveryBatch;

    private String buildingSection;

    private String calculationArea;

    private String eStopGroup;

    private String plcArea;

    private String sequenceGroup;

    private String lineName;

    private String screen;

    private String installationSection;

    private String projectView;

}

