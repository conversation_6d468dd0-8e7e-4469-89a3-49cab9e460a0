package com.siemens.spine.db.repository.impl;

import com.siemens.spine.db.entity.ConnectionPointEntity;
import com.siemens.spine.db.entity.NeighborConnectionEntity;
import com.siemens.spine.db.repository.NeighborConnectionRepository;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaDelete;
import javax.persistence.criteria.Root;
import java.util.List;

@ApplicationScoped
public class NeighborConnectionRepositoryImpl extends GenericJpaRepositoryImpl<NeighborConnectionEntity, Long>
        implements NeighborConnectionRepository {

    @Override
    public Class<NeighborConnectionEntity> getDomainType() {
        return NeighborConnectionEntity.class;
    }

    @Override
    public long removeAllNeighborConnections(ConnectionPointEntity connectionPointEntity) {
        if (connectionPointEntity == null) {
            return 0L;
        }

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaDelete<NeighborConnectionEntity> cq = cb.createCriteriaDelete(NeighborConnectionEntity.class);
        Root<NeighborConnectionEntity> rootEntry = cq.from(NeighborConnectionEntity.class);
        CriteriaDelete<NeighborConnectionEntity> query = cq.where(
                cb.or(
                        cb.equal(rootEntry.get("owner"), connectionPointEntity.getId()),
                        cb.equal(rootEntry.get("neighbor"), connectionPointEntity.getId())
                )
        );

        return entityManager.createQuery(query).executeUpdate();
    }

    @Override
    public List<NeighborConnectionEntity> findNeighborConnectionByComponentIds(List<Long> componentIds) {
        return entityManager.createQuery("select n from NeighborConnectionEntity n" +
                        " inner join ConnectionPointEntity c on n.owner = c.id" +
                        " where c.component.id in (:componentIds)", NeighborConnectionEntity.class)
                .setParameter("componentIds", componentIds)
                .getResultList();
    }

}

