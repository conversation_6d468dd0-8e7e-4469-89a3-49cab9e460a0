package com.siemens.spine.db.repository.elcon;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation for marking repositories for REST exposure.
 * Any repository implementing {@link RestCrudRepositoryMarker} and annotated with this
 * will be considered for automatic REST API exposure.
 *
 * <AUTHOR>
 * @since 1.0
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface RestRepository {

    /**
     * The REST endpoint path. If not provided, a default path will be generated based on the entity class name.
     * The default path is the entity class name in lowercase, pluralized (e.g., "User" becomes "users").
     *
     * @return the REST endpoint path
     */
    String path();

}
