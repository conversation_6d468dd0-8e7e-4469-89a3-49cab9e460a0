package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MapsId;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "elcon_safety")
public class ElconSafety {

    @Id
    @Column(name = "object_id", nullable = false)
    private Long objectId;

    @MapsId
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "object_id", nullable = false)
    private ElconObject elconObject;

    @Size(max = 31)
    @NotNull
    @Column(name = "safety_type", nullable = false, length = 31)
    private String safetyType;

    @NotNull
    @Column(name = "message_no", nullable = false)
    private Integer messageNo;

    @NotNull
    @Column(name = "invert_estop_pb", nullable = false)
    private Short invertEstopPb;

    @NotNull
    @Column(name = "unit_station_number", nullable = false)
    private Integer unitStationNumber;

    @Column(name = "di_addr_estop_pb_pressed")
    private Long diAddrEstopPbPressed;

    @Column(name = "do_addr_estop_sig_light")
    private Long doAddrEstopSigLight;

    @Column(name = "do_addr_conf_estop_pb")
    private Long doAddrConfEstopPb;

}