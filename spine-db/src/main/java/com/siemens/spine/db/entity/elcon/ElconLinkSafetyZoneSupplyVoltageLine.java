package com.siemens.spine.db.entity.elcon;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@Entity
@Table(name = "elcon_link_safety_zone_supply_voltage_line")
public class ElconLinkSafetyZoneSupplyVoltageLine {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "link_id", nullable = false)
    private Long linkId;

    @NotNull
    @Column(name = "safety_zone_id", nullable = false)
    private Long safetyZoneId;

    @NotNull
    @Column(name = "line_id", nullable = false)
    private Long lineId;

}