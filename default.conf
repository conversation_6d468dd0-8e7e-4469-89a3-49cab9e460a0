server {
    listen 8082;
    
    # Add proper MIME type configuration
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Add specific MIME type for JavaScript modules
    types {
        application/javascript js mjs;
    }
    
    location /spine-core-frontend/ {
        alias /usr/share/nginx/html/spine-ui/;
        try_files $uri $uri/ /spine-core-frontend/index.html;
        
        # Ensure JavaScript files are served with correct MIME type
        location ~* \.js$ {
            add_header Content-Type application/javascript;
        }
    }
    
    # Redirect root to the application
    location = / {
        return 301 /spine-core-frontend/;
    }
    
    # Add proper error handling
    error_page 404 /spine-core-frontend/index.html;
}
