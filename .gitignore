# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*

# Maven
target/
.m2/
dependency-reduced-pom.xml
pom.xml.versionsBackup

# MacOS
.DS_Store
profile

# IntelliJ Idea
.idea/
*.iws
*.ipr
*.iml
*.releaseBackup
atlassian-ide-plugin.xml

# Netbeans
nbactions.xml
nb-configuration.xml

# Eclipse 
.settings
.settings/
.project
.classpath

# Asciidoctor
.asciidoctor/

# Vim
*.swp

# Gradle
.gradle/
build/

# Node.js
node_modules/
node/

# Helidon CLI
.helidon

# Other
*~
user.txt
ObjectStore/
PutObjectStoreDirHere/
package-lock.json
RUNTIMEDB/


# vscode
.vscode

testdb.properties
testdb.script
spine-core.tar
/docker/postgresql/
/docker/schema-db/
