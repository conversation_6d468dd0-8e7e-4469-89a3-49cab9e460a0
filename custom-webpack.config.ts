const webpack = require('webpack');

module.exports = {
  plugins: [
    new webpack.DefinePlugin({
      $ENV: {
        API_URL: JSON.stringify(process.env["API_URL"]),
        KEYCLOAK_URL: JSON.stringify(process.env["KEYCLOAK_URL"]),
        KEYCLOAK_REALM: JSON.stringify(process.env["KEYCLOAK_REALM"]),
        KEYCLOAK_CLIENT_ID: JSON.stringify(process.env["KEYCLOAK_CLIENT_ID"])
      }
    })
  ]
};