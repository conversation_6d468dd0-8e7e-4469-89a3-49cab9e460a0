
# Docker Compose Guide for Spine Service Application

## Overview
This guide explains how to set up and run a spine service application using Docker Compose with the following services:
- PostgreSQL (with initial data load)
- Keycloak (authentication)
- Backend service

## Prerequisites
- Docker Engine 20.10.0+
- Docker Compose 2.0.0+
- 4GB+ RAM recommended (for data loading)

## Service Startup Order
Services must start in this order due to dependencies:
1. PostgreSQL (with data initialization)
2. Keycloak (depends on PostgreSQL)
3. Backend (depends on PostgreSQL and Keycloak)

## Preparation
1. Unzip the data archive to the current directory:
    - realm-config.7z and schema-db.7z in the docker directory.

## Startup Commands
1. Postgres startup (with data initialization):

```bash
docker-compose up -d -f postgresql.yml
# Wait for data initialization to complete (monitor logs)
docker-compose logs -f postgresql
```

2. Keycloak startup:

```bash
docker-compose up -d -f keycloak.yml
```
3. Backend service startup:

```bash
docker-compose up -d -f app.yml
```

## Verification
- **PostgreSQL**: Check if the database is running and initialized.
  - Verify the database connection using a PostgreSQL client.
  ```bash
  url: postgres://postgres:pO5t$zum@localhost:5432/spine
  ```
  - Check if the initial data is loaded correctly in the `prod` schema.
- **Keycloak**: Verify the admin user can log in.
  - Access Keycloak admin console at `http://127.0.0.1:9080/admin/master/console/` with `admin`/`admin` credentials.
  - Check the access token generation.
 
  ```bash
    curl --location 'http://localhost:9080/realms/spine/protocol/openid-connect/token' \
    --header 'Access-Control-Allow-Origin: *' \
    --header 'Content-Type: application/x-www-form-urlencoded' \
    --data-urlencode 'username=spine_admin' \
    --data-urlencode 'password=admin' \
    --data-urlencode 'client_id=spine-core' \
    --data-urlencode 'grant_type=password'
  ```
- **Backend**: Test API endpoints for expected responses. Import the postman collection `spine-core.postman_collection.json` to Postman for testing

  ```bash
    curl --location 'http://localhost:8085/api/v1/data-rest/elcon_object/1792753' \
    --header 'Authorization: Bearer <access_token>'
  ```
