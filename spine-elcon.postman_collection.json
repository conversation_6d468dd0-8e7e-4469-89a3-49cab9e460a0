{"info": {"_postman_id": "e81e1d61-22fd-41b9-adf3-a03191e69a2e", "name": "spine-elcon", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "2624401"}, "item": [{"name": "elcon-repository-rest", "item": [{"name": "Create object", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "<access_token>", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"unique_id\": \"asdasdas\",\r\n    \"object_kind\": \"CONVEYOR\",\r\n    \"hw_outfit_name\": \"TSTR002\",\r\n    \"sw_set_key\": \"TSTR001\",\r\n    \"item_name\": \"LP260.BHT51_HS115\",\r\n    \"sw_relevant\": false,\r\n    \"plc_sw_unit_id\": 47,\r\n    \"plc_sw_object_nr\": 47,\r\n    \"object_origin\": \"XML\",\r\n    \"io_base_address\": \"12023.0\",\r\n    \"logistics_property\": \"T.X.X\",\r\n    \"object_sub_kind\": \"CONVEYOR\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8085/api/v1/data-rest/elcon_object", "protocol": "http", "host": ["localhost"], "port": "8085", "path": ["api", "v1", "data-rest", "elcon_object"]}}, "response": []}, {"name": "Get object by id", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "<access_token>", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:8085/api/v1/data-rest/elcon_object/1792753", "protocol": "http", "host": ["localhost"], "port": "8085", "path": ["api", "v1", "data-rest", "elcon_object", "1792753"]}}, "response": []}, {"name": "Update object", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "<access_token>", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"object_id\": 1792728,\r\n    \"unique_id\": \"ccccc\",\r\n    \"object_kind\": \"CONVEYOR\",\r\n    \"hw_outfit_name\": \"TSTR002\",\r\n    \"sw_set_key\": \"TSTR001\",\r\n    \"item_name\": \"LP260.BHT51_HS115\",\r\n    \"sw_relevant\": false,\r\n    \"plc_sw_unit_id\": 47,\r\n    \"plc_sw_object_nr\": 47,\r\n    \"object_origin\": \"XML\",\r\n    \"io_base_address\": \"12023.0\",\r\n    \"logistics_property\": \"T.X.X\",\r\n    \"object_sub_kind\": \"CONVEYOR\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8085/api/v1/data-rest/elcon_object/1792753", "protocol": "http", "host": ["localhost"], "port": "8085", "path": ["api", "v1", "data-rest", "elcon_object", "1792753"]}}, "response": []}, {"name": "Delete object by id", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "<access_token>", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:8085/api/v1/data-rest/elcon_object/1792753", "protocol": "http", "host": ["localhost"], "port": "8085", "path": ["api", "v1", "data-rest", "elcon_object", "1792753"]}}, "response": []}, {"name": "List objects with filter and sort", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "<access_token>", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:8085/api/v1/data-rest/elcon_object?size=10&sort=objectId,desc", "protocol": "http", "host": ["localhost"], "port": "8085", "path": ["api", "v1", "data-rest", "elcon_object"], "query": [{"key": "size", "value": "10"}, {"key": "sort", "value": "objectId,desc"}]}}, "response": []}]}]}