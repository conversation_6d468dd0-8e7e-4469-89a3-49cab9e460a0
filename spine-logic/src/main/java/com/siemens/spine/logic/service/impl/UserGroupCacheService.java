package com.siemens.spine.logic.service.impl;

import com.siemens.spine.logic.util.SecurityUtils;
import io.helidon.common.http.FormParams;
import io.helidon.common.http.Http;
import io.helidon.common.http.MediaType;
import io.helidon.common.reactive.Single;
import io.helidon.media.jsonp.JsonpSupport;
import io.helidon.microprofile.scheduling.FixedRate;
import io.helidon.webclient.WebClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.json.JsonArray;
import javax.json.JsonObject;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.StampedLock;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2/3/2023
 */
@ApplicationScoped
@Slf4j
public class UserGroupCacheService {

    private final StampedLock lock = new StampedLock();
    private Set<String> currentUserGroups = new TreeSet<>();
    @Inject
    @ConfigProperty(name = "security.providers.0.oidc.token-endpoint-uri")
    private String tokenEndpoint;

    @Inject
    @ConfigProperty(name = "security.providers.0.oidc.client-id")
    private String clientId;

    @Inject
    @ConfigProperty(name = "security.providers.0.oidc.client-secret")
    private String clientSecret;

    @Inject
    @ConfigProperty(name = "user-groups.uri")
    private String userGroupsEndpoint;

    public void setCurrentUserGroups(Set<String> currentUserGroups) {
        this.currentUserGroups = currentUserGroups;
    }

    @FixedRate(initialDelay = 5, value = 10, timeUnit = TimeUnit.MINUTES)
    public void synchronizeUserGroup() {
        log.info("Start retrieve user groups from keycloak.");
        WebClient webClient = WebClient.builder()
                .addMediaSupport(JsonpSupport.create())
                .build();
        Single<JsonObject> token = retrieveToken(webClient);
        try {
            JsonObject resultToken = token.await();
            String accessToken = resultToken.getString("access_token");
            if (StringUtils.isNotEmpty(accessToken)) {
                Single<JsonArray> userGroupsJsonArrays = retrieveGroups(accessToken, webClient);
                JsonArray resultUserGroups = userGroupsJsonArrays.await();
                List<JsonObject> userGroupsJson = resultUserGroups.getValuesAs(JsonObject.class);
                Set<String> userGroups = new TreeSet<>();
                for (JsonObject jsonObject : userGroupsJson) {
                    String group = jsonObject.getString("name");
                    if (group.startsWith(SecurityUtils.USER_GROUP_PREFIX) &&
                            !SecurityUtils.PAR_ADMIN_GROUP.equals(group)) {
                        userGroups.add(group);
                    }
                }
                long stamp = lock.writeLock();
                try {
                    currentUserGroups = new TreeSet<>(userGroups);
                } finally {
                    lock.unlockWrite(stamp);
                }
            }
        } catch (Exception exception) {
            log.error("Error while retrieve user groups from keycloak: {}", exception.getMessage());
        }
        log.info("Finish retrieve user groups from keycloak.");
    }

    public Set<String> getUserGroups() {
        long stamp = lock.readLock();
        try {
            return currentUserGroups;
        } finally {
            lock.unlockRead(stamp);
        }
    }

    private Single<JsonObject> retrieveToken(WebClient webClient) {
        FormParams.Builder form = FormParams.builder()
                .add("grant_type", "client_credentials")
                .add("client_id", clientId)
                .add("client_secret", clientSecret);

        return webClient
                .post()
                .uri(tokenEndpoint)
                .accept(MediaType.APPLICATION_JSON)
                .submit(form.build())
                .flatMapSingle(response -> {
                    if (response.status().family() == Http.ResponseStatus.Family.SUCCESSFUL) {
                        return response.content()
                                .as(JsonObject.class);
                    } else {
                        log.error("Error while retrieve token: error_code {}, {}", response.status().code(),
                                response.status().reasonPhrase());
                        return Single.error(new RuntimeException(response.status().reasonPhrase()));
                    }
                });
    }

    private Single<JsonArray> retrieveGroups(String token, WebClient webClient) {
        return webClient
                .get()
                .uri(userGroupsEndpoint)
                .headers(it -> {
                    it.add(Http.Header.AUTHORIZATION, String.format("Bearer %s", token));
                    return it;
                }).accept(MediaType.APPLICATION_JSON)
                .request()
                .flatMapSingle(response -> {
                    if (response.status().family() == Http.ResponseStatus.Family.SUCCESSFUL) {
                        return response.content()
                                .as(JsonArray.class);
                    } else {
                        log.error("Error while retrieve user groups: error_code {}, {}", response.status().code(),
                                response.status().reasonPhrase());
                        return Single.error(new RuntimeException(response.status().reasonPhrase()));
                    }
                });
    }

}
