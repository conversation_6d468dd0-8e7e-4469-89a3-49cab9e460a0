package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.entity.elcon.QueryTemplateEntity;
import com.siemens.spine.db.repository.QueryTemplateRepository;
import com.siemens.spine.logic.dto.QueryTemplateDTO;
import com.siemens.spine.logic.dto.request.CreateQueryTemplateDTO;
import com.siemens.spine.logic.dto.request.DynamicQueryByNameDTO;
import com.siemens.spine.logic.dto.request.DynamicQueryDTO;
import com.siemens.spine.logic.mapper.DynamicQueryMapper;
import com.siemens.spine.logic.service.DynamicQueryService;
import com.siemens.spine.logic.util.SecurityUtils;
import com.siemens.spine.logic.validator.query.QueryValidationContext;
import com.siemens.spine.logic.validator.query.QueryValidatorFactory;
import com.siemens.spine.logic.validator.query.ValidationResult;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.sql.SQLException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@ApplicationScoped
@Slf4j
@Transactional
public class DynamicQueryServiceImpl implements DynamicQueryService {

    private final QueryTemplateRepository queryTemplateRepository;
    private final DynamicQueryMapper dynamicQueryMapper;
    private final QueryValidatorFactory queryValidatorFactory;

    @Inject
    public DynamicQueryServiceImpl(QueryTemplateRepository queryTemplateRepository,
                                   DynamicQueryMapper dynamicQueryMapper,
                                   QueryValidatorFactory queryValidatorFactory) {
        this.queryTemplateRepository = queryTemplateRepository;
        this.dynamicQueryMapper = dynamicQueryMapper;
        this.queryValidatorFactory = queryValidatorFactory;
    }

    public QueryTemplateDTO getQueryTemplate(Long templateId) {
        if (templateId == null) {
            throw new IllegalArgumentException("Template ID cannot be null");
        }

        QueryTemplateEntity template = getQueryTemplateEntity(templateId);
        return dynamicQueryMapper.toQueryTemplateDTO(template);
    }

    @Override
    public QueryTemplateDTO getQueryTemplate(String templateName) {
        if (templateName == null || templateName.isEmpty()) {
            throw new IllegalArgumentException("Template name cannot be null or empty");
        }

        QueryTemplateEntity template = getQueryTemplateByName(templateName);
        return dynamicQueryMapper.toQueryTemplateDTO(template);
    }

    public int getTotalCount(String templateName, Map<String, Object> parameters) throws SQLException {
        QueryTemplateEntity template = this.getQueryTemplateByName(templateName);
        return queryTemplateRepository.getTotalCount(template, parameters);
    }

    @Override
    public Map<String, Object> executeQuerySafe(DynamicQueryDTO dto) throws SQLException {

        final Long templateId = dto.getTemplateId();
        final Map<String, Object> parameters = dto.getParameters();
        Integer page = Math.max(1, dto.getPage());
        Integer size = Math.max(1, dto.getSize());

        QueryTemplateEntity template = getQueryTemplateEntity(templateId);

        return queryTemplateRepository.executeQuerySafe(template, parameters, page, size);

    }

    @Override
    public Map<String, Object> executeQuerySafe(DynamicQueryByNameDTO dto) throws SQLException {
        Optional<QueryTemplateEntity> existingTemplate = queryTemplateRepository.findByTemplateName(
                dto.getTemplateName());

        if (existingTemplate.isEmpty()) {
            throw new IllegalArgumentException("Template with name '" + dto.getTemplateName() + "' does not exist");
        }

        QueryTemplateEntity template = existingTemplate.get();
        final Map<String, Object> parameters = dto.getParameters();
        Integer page = Math.max(1, dto.getPage());
        Integer size = Math.max(1, dto.getSize());

        return queryTemplateRepository.executeQuerySafe(template, parameters, page, size);
    }

    @Override
    public Collection<QueryTemplateDTO> getAllTemplates() {

        List<QueryTemplateEntity> entities = queryTemplateRepository.findAll();
        if (entities == null || entities.isEmpty()) {
            return List.of();
        }

        return entities.stream().map(queryTemplateEntity -> {
            QueryTemplateDTO dto = dynamicQueryMapper.toQueryTemplateDTO(queryTemplateEntity);
            if (dto == null) {
                log.warn("Failed to map QueryTemplateEntity to QueryTemplateDTO for entity: {}", queryTemplateEntity);
                return null;
            }
            return dto;
        }).filter(Objects::nonNull).toList();
    }

    @Override
    public ValidationResult validateQueryTemplate(String template) {
        QueryValidationContext context = QueryValidationContext.builder()
                .queryString(template)
                .build();
        return queryValidatorFactory.createValidationChain().validate(context);
    }

    @Override
    public QueryTemplateDTO createQueryTemplate(CreateQueryTemplateDTO template) {
        if (template == null || template.getSqlTemplate() == null || template.getSqlTemplate()
                .isEmpty() || template.getTemplateName() == null || template.getTemplateName().isEmpty()) {
            throw new IllegalArgumentException("Template or SQL template cannot be null");
        }

        Optional<QueryTemplateEntity> existingTemplate = queryTemplateRepository.findByTemplateName(
                template.getTemplateName());

        if (existingTemplate.isPresent()) {
            throw new IllegalArgumentException(
                    "Template with name '" + template.getTemplateName() + "' already exists");
        }

        ValidationResult validationResult = validateQueryTemplate(template.getSqlTemplate());
        if (!validationResult.valid()) {
            throw new IllegalArgumentException("Invalid SQL template: " + validationResult.errorMessage());
        }

        QueryTemplateEntity entity = dynamicQueryMapper.toQueryTemplateEntity(template);
        entity.setCreatedBy(SecurityUtils.getUsernameFromToken());
        entity.setModifiedBy(SecurityUtils.getUsernameFromToken());
        entity = queryTemplateRepository.save(entity);

        return dynamicQueryMapper.toQueryTemplateDTO(entity);
    }

    @Override
    public QueryTemplateDTO updateQueryTemplate(QueryTemplateDTO template) {
        if (template == null || template.getId() == null || template.getSqlTemplate() == null || template.getSqlTemplate()
                .isEmpty() || template.getTemplateName() == null || template.getTemplateName().isEmpty()) {
            throw new IllegalArgumentException("Template or SQL template cannot be null");
        }
        Optional<QueryTemplateEntity> existingTemplate = queryTemplateRepository.findById(template.getId());

        if (existingTemplate.isEmpty()) {
            throw new IllegalArgumentException("Template with id '" + template.getId() + "' does not exist");
        }

        QueryTemplateEntity entity = existingTemplate.get();

        if (!template.getTemplateName().equals(entity.getTemplateName())) {
            Optional<QueryTemplateEntity> existingTemplateTmp = queryTemplateRepository.findByTemplateName(
                    template.getTemplateName());

            if (existingTemplateTmp.isPresent()) {
                throw new IllegalArgumentException(
                        "Template with name '" + template.getTemplateName() + "' already exists");
            }
        }

        ValidationResult validationResult = validateQueryTemplate(template.getSqlTemplate());
        if (!validationResult.valid()) {
            throw new IllegalArgumentException("Invalid SQL template: " + validationResult.errorMessage());
        }
        entity.setModifiedBy(SecurityUtils.getUsernameFromToken());
        entity.setDescription(template.getDescription());
        entity.setRequiresPagination(template.isRequiresPagination());
        entity.setTemplateName(template.getTemplateName());
        entity.setSqlTemplate(template.getSqlTemplate());

        entity = queryTemplateRepository.save(entity);

        return dynamicQueryMapper.toQueryTemplateDTO(entity);
    }

    @Override
    public void deleteQueryTemplate(Long templateId) {
        queryTemplateRepository.deleteById(templateId);
    }

    @Override
    public QueryTemplateEntity getQueryTemplateEntity(Long templateId) {
        if (templateId == null) {
            throw new IllegalArgumentException("Template ID cannot be null");
        }

        Optional<QueryTemplateEntity> entity = queryTemplateRepository.findById(templateId);
        if (entity.isEmpty()) {
            throw new IllegalArgumentException("Template ID " + templateId + " not found");
        }

        return entity.get();
    }

    @Override
    public QueryTemplateEntity getQueryTemplateByName(String templateName) {
        if (templateName == null || templateName.isEmpty()) {
            throw new IllegalArgumentException("Template name cannot be null or empty");
        }
        Optional<QueryTemplateEntity> entity = queryTemplateRepository.findByTemplateName(templateName);
        if (entity.isEmpty()) {
            throw new IllegalArgumentException("Template with name '" + templateName + "' not found");
        }
        return entity.get();
    }

}
