package com.siemens.spine.logic.model;

import java.util.ArrayList;
import java.util.List;

public class DecompositionList {

    protected List<Decomposition> decompositions;
    protected String mode;
    protected String source;

    /**
     * Gets the value of the decompositions property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the decompositions property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDecompositions().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Decomposition }
     */
    public List<Decomposition> getDecompositions() {
        if (decompositions == null) {
            decompositions = new ArrayList<>();
        }
        return this.decompositions;
    }

    /**
     * Gets the value of the mode property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getMode() {
        return mode;
    }

    /**
     * Sets the value of the mode property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setMode(String value) {
        this.mode = value;
    }

    /**
     * Gets the value of the source property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getSource() {
        return source;
    }

    /**
     * Sets the value of the source property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSource(String value) {
        this.source = value;
    }

}
