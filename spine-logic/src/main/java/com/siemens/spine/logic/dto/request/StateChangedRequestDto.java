package com.siemens.spine.logic.dto.request;

import com.siemens.spine.logic.dto.ChangeGroupDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StateChangedRequestDto {

    private Long projectId;

    private List<Long> componentIds;

    private int newState;

    private String description;

    private ChangeGroupDto changeGroup;

}
