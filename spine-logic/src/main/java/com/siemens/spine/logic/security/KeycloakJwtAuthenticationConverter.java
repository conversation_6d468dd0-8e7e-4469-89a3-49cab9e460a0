package com.siemens.spine.logic.security;

import com.siemens.spine.logic.util.SecurityUtils;
import io.helidon.security.jwt.Jwt;

import javax.enterprise.context.ApplicationScoped;
import javax.json.JsonArray;
import javax.json.JsonValue;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@ApplicationScoped
public class KeycloakJwtAuthenticationConverter {

    private static final String ROLES = "roles";

    private static final String REALM_ACCESS = "realm_access";

    /**
     * Parsing the authentication info from token
     *
     * @param jwt
     * @return
     */
    public Authentication parseJwt(Jwt jwt) {
        if (jwt == null) {
            return null;
        }

        return Authentication.builder()
                .firstName(getFirstName(jwt))
                .lastName(getLastName(jwt))
                .id(getSubject(jwt))
                .username(getUserName(jwt))
                .email(getEmail(jwt))
                .groups(getGroup(jwt))
                .roles(getRoles(jwt))
                .isAdmin(isAdmin(jwt))
                .build();
    }

    /**
     * get username from token claim
     *
     * @param jwt
     * @return
     */
    private String getUserName(Jwt jwt) {
        return jwt.preferredUsername().isPresent() ? jwt.preferredUsername().get() : null;
    }

    /**
     * get email from token claim
     *
     * @param jwt
     * @return
     */
    private String getEmail(Jwt jwt) {
        return jwt.email().isPresent() ? jwt.email().get() : null;
    }

    /**
     * @param jwt
     * @return
     */
    private Set<String> getGroup(Jwt jwt) {
        Set<String> groups = new HashSet<>();
        Optional<JsonValue> groupsClaim = jwt.payloadClaim("groups");
        if (groupsClaim.isPresent()) {
            JsonArray array = groupsClaim.get().asJsonArray();
            for (int i = 0; i < array.size(); i++) {
                groups.add(array.getString(i));
            }
        }

        return groups;
    }

    /**
     * get roles from token
     *
     * @param jwt
     * @return
     */
    private Set<String> getRoles(Jwt jwt) {
        Set<String> ret = new HashSet<>();
        Optional<JsonValue> keycloakClientAuthorities = jwt.payloadClaim(REALM_ACCESS);
        if (keycloakClientAuthorities.isPresent() && keycloakClientAuthorities.get().asJsonObject()
                .get(ROLES) != null) {
            JsonArray clientRolesInStandard = keycloakClientAuthorities.get().asJsonObject().getJsonArray(ROLES);

            for (int i = 0; i < clientRolesInStandard.size(); i++) {
                ret.add(clientRolesInStandard.getString(i));
            }
        }

        return ret;
    }

    /**
     * get first name from token claim
     *
     * @param jwt
     * @return
     */
    private String getFirstName(Jwt jwt) {
        return jwt.givenName().orElse(null);
    }

    /**
     * get last name from token claim
     *
     * @param jwt
     * @return
     */
    private String getLastName(Jwt jwt) {
        return jwt.familyName().orElse(null);
    }

    private String getSubject(Jwt jwt) {
        return jwt.subject().orElse(null);
    }

    private boolean isAdmin(Jwt jwt) {
        List<String> groups = jwt.userGroups().orElse(Collections.emptyList());
        return groups.stream().anyMatch(g -> g.startsWith(SecurityUtils.USER_GROUP_PREFIX) &&
                SecurityUtils.PAR_ADMIN_GROUP.equals(g));
    }

}
