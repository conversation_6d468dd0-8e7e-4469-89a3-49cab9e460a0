package com.siemens.spine.logic.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 11/9/2023
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class DecompositionListDto {

    private List<DecompositionDto> decompositions;

    private String mode;

    private String source;

}
