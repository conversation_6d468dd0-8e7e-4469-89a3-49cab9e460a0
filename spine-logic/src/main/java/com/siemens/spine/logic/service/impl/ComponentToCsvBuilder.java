package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.constant.GroupTypeEnum;
import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.ConnectionPointEntity;
import com.siemens.spine.db.entity.DecompositionEntity;
import com.siemens.spine.db.entity.PositionEntity;
import com.siemens.spine.db.entity.StateEntity;
import com.siemens.spine.logic.util.CSVUtils;
import com.siemens.spine.logic.util.ComponentUtils;
import com.siemens.spine.logic.util.DateTimeUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <PERSON>am
 * @version 1.0
 * @since 4/1/2023
 */
public class ComponentToCsvBuilder {

    /**
     * extract component to csv builder
     *
     * @param component
     * @return
     */
    public static StringBuilder extractComponentCSV(ComponentEntity component, StateEntity state) {
        Map<GroupTypeEnum, String> groupMapping = ComponentUtils.getComponentGroupMapping(component);

        String componentMark = "C";
        String connectionPointMark = "P";
        String decompositionMark = "D";
        StringBuilder compCsv = new StringBuilder();
        compCsv.append(CSVUtils.addParam(componentMark));

        // add state
        compCsv.append(CSVUtils.addParam(component.getId())); //1
        if (state != null) {
            compCsv.append(CSVUtils.addParam(state.getUsername())); //2
        } else {
            compCsv.append(CSVUtils.addParam(CSVUtils.EMPTY)); //2
        }
        compCsv.append(CSVUtils.addParam(component.getCurrentState())); //3
        if (state != null) {
            compCsv.append(CSVUtils.addParam(state.getReason())); //4
        } else {
            compCsv.append(CSVUtils.addParam(CSVUtils.EMPTY)); //4
        }

        // add position
        PositionEntity position = component.getPosition();
        if (position == null) {
            compCsv.append(CSVUtils.addParam(CSVUtils.EMPTY)); //5
            compCsv.append(CSVUtils.addParam(CSVUtils.EMPTY)); //6
            compCsv.append(CSVUtils.addParam(CSVUtils.EMPTY)); //7
        } else {
            compCsv.append(CSVUtils.addParam(position.getX())); //5
            compCsv.append(CSVUtils.addParam(position.getY())); //6
            compCsv.append(CSVUtils.addParam(position.getZ())); //7
        }
        compCsv.append(CSVUtils.addParam(DateTimeUtils.formatDateISO(component.getSysModDate()))); //8
        compCsv.append(CSVUtils.addParam(DateTimeUtils.formatDateISO(component.getSysCreateDate()))); //9
        compCsv.append(CSVUtils.addParam(component.getAmountBufferElec())); //10
        compCsv.append(CSVUtils.addParam(component.getAmountBufferMech())); //11
        compCsv.append(CSVUtils.addParam(component.getBreakType())); //12
        compCsv.append(CSVUtils.addParam(component.getBufferSize())); //13
        compCsv.append(CSVUtils.addParam(groupMapping.get(GroupTypeEnum.DeliveryBatch))); //14
        compCsv.append(CSVUtils.addParam(groupMapping.get(GroupTypeEnum.CalculationArea))); //15
        compCsv.append(CSVUtils.addParam(component.getComment())); //16
        compCsv.append(CSVUtils.addParam(component.getCurveAngle())); //17
        compCsv.append(CSVUtils.addParam(component.getCurveDirection())); //18
        compCsv.append(CSVUtils.addParam(component.getCurveRadius())); //19
        compCsv.append(CSVUtils.addParam(groupMapping.get(GroupTypeEnum.Drawing))); //20
        compCsv.append(CSVUtils.addParam(component.getDrivePosition())); //21
        compCsv.append(CSVUtils.addParam(component.getDriveStation())); //22
        compCsv.append(CSVUtils.addParam(groupMapping.get(GroupTypeEnum.EStopGroup))); //23
        compCsv.append(CSVUtils.addParam(component.getHeight())); //24
        compCsv.append(CSVUtils.addParam(component.getLengthTotal())); //25
        compCsv.append(CSVUtils.addParam(component.getLoad())); //26
        compCsv.append(CSVUtils.addParam(component.getMotorController())); //27
        compCsv.append(CSVUtils.addParam(component.getMotorDirection())); //28
        compCsv.append(CSVUtils.addParam(component.getMotorPosition())); //29
        compCsv.append(CSVUtils.addParam(groupMapping.get(GroupTypeEnum.PlcArea))); //30
        compCsv.append(CSVUtils.addParam(component.getParent())); //31
        compCsv.append(CSVUtils.addParam(component.getPlantDomain())); //32
        compCsv.append(CSVUtils.addParam(component.getPosNo())); //33
        compCsv.append(CSVUtils.addParam(component.getReference())); //34
        compCsv.append(CSVUtils.addParam(component.getRotation())); //35
        compCsv.append(CSVUtils.addParam(component.getRotation3D())); //36
        compCsv.append(CSVUtils.addParam(groupMapping.get(GroupTypeEnum.Screen))); //37
        compCsv.append(CSVUtils.addParam(component.getSection1Angle())); //38
        compCsv.append(CSVUtils.addParam(component.getSection1Length())); //39
        compCsv.append(CSVUtils.addParam(component.getSection2Angle())); //40
        compCsv.append(CSVUtils.addParam(component.getSection2Length())); //41
        compCsv.append(CSVUtils.addParam(component.getSection3Angle())); //42
        compCsv.append(CSVUtils.addParam(component.getSection3Length())); //43
        compCsv.append(CSVUtils.addParam(component.getSection4Angle())); //44
        compCsv.append(CSVUtils.addParam(component.getSection4Length())); //45
        compCsv.append(CSVUtils.addParam(groupMapping.get(GroupTypeEnum.SequenceGroup))); //46
        compCsv.append(CSVUtils.addParam(component.getSlaveDrive())); //47
        compCsv.append(CSVUtils.addParam(component.getSlope())); //48
        compCsv.append(CSVUtils.addParam(component.getSpeed())); //49
        compCsv.append(CSVUtils.addParam(component.getStartStopCycles())); //50
        compCsv.append(CSVUtils.addParam(component.getSupplier())); //51
        compCsv.append(CSVUtils.addParam(component.getThroughput())); //52
        compCsv.append(CSVUtils.addParam(component.getTypeId())); //53
        compCsv.append(CSVUtils.addParam(component.getId())); //54
        compCsv.append(CSVUtils.addParam(
                component.getUnitReversible() != null ? component.getUnitReversible().toString() : null)); //55
        compCsv.append(CSVUtils.addParam(component.getUsage())); //56
        compCsv.append(CSVUtils.addParam(component.getVersion())); //57
        compCsv.append(CSVUtils.addParam(component.getVirtual())); //58
        compCsv.append(CSVUtils.addParam(component.getWidth())); //59
        compCsv.append(CSVUtils.addParam(component.getInstallationHours())); //60
        compCsv.append(CSVUtils.addParam(component.getEngineeringHours())); //61
        compCsv.append(CSVUtils.addParam(component.getColorGroup())); //62
        compCsv.append(CSVUtils.addParam(component.getAkz())); //63
        compCsv.append(CSVUtils.addParam(component.getCustomerAKZ())); //64
        compCsv.append(CSVUtils.addParam(component.getAutoCadLayer())); //65
        compCsv.append(CSVUtils.addParam(component.getUser1())); //66
        compCsv.append(CSVUtils.addParam(component.getUser2())); //67
        compCsv.append(CSVUtils.addParam(component.getUser3())); //68
        compCsv.append(CSVUtils.addParam(component.getUser4())); //69
        compCsv.append(CSVUtils.addParam(component.getUser5())); //70
        compCsv.append(CSVUtils.addParam(groupMapping.get(GroupTypeEnum.LineName))); //71
        compCsv.append(CSVUtils.addParam(groupMapping.get(GroupTypeEnum.BuildingSection))); //72
        compCsv.append(CSVUtils.addParam(groupMapping.get(GroupTypeEnum.InstallationSection))); //73
        compCsv.append(CSVUtils.addParam(component.getDriveSide())); //74
        compCsv.append(CSVUtils.addParam(component.getAngleCorr())); //75
        compCsv.append(CSVUtils.addParam(component.getLevelStart())); //76
        compCsv.append(CSVUtils.addParam(component.getLevelEnd())); //77
        compCsv.append(CSVUtils.addParam(component.getControlNr())); //78
        compCsv.append(CSVUtils.addParam(component.getVaultInstance())); //79
        compCsv.append(CSVUtils.addParam(component.getDrawingVersion())); //80
        compCsv.append(CSVUtils.addParam(component.getDrawingRevision())); //81
        compCsv.append(CSVUtils.addParam(component.getOutfit3D())); //82
        compCsv.append(CSVUtils.addParam(component.getParcelTypeID())); //83
        compCsv.append(CSVUtils.addParam(component.getOrderPlant())); //84
        compCsv.append(CSVUtils.addParam(component.getDesignPlant())); //85
        compCsv.append(CSVUtils.addParam(
                component.getStorageConveyor() != null ? component.getStorageConveyor().toString() : null));//86
        compCsv.append(CSVUtils.addParam(component.getDrivePulleyDiameter())); //87
        compCsv.append(CSVUtils.addParam(component.getDriveShaftDiameter())); //88
        compCsv.append(CSVUtils.addParam(component.getAcceleration())); //89

        // add connection points part
        List<ConnectionPointEntity> connectionPoints = component.getConnectionPoints();
        StringBuilder cpBuilder = addConnectionPoints(connectionPointMark, connectionPoints);
        if (cpBuilder != null) {
            compCsv.append(cpBuilder);
        }

        // add decompositions part
        List<DecompositionEntity> decompositions = component.getDecompositions();
        StringBuilder dcBuilder = addDecompositions(decompositionMark, decompositions);
        if (dcBuilder != null) {
            compCsv.append(dcBuilder);
        }

        compCsv.append(CSVUtils.NEW_LINE);
        return compCsv;
    }

    private static StringBuilder addConnectionPoints(String connectionPointPart,
                                                     List<ConnectionPointEntity> connectionPoints) {
        StringBuilder connectionPointsBuilder = null;
        if (CollectionUtils.isNotEmpty(connectionPoints)) {
            connectionPointsBuilder = new StringBuilder();
            for (ConnectionPointEntity connectionPoint : connectionPoints) {
                String connectionPointBuilder = CSVUtils.NEW_LINE +
                        CSVUtils.addParam(connectionPointPart) +//0
                        CSVUtils.addParam(connectionPoint.getName()) +//1
                        CSVUtils.addParam(connectionPoint.getType()) +//2
                        CSVUtils.addParam(connectionPoint.getX()) +//3
                        CSVUtils.addParam(connectionPoint.getY()) +//4
                        CSVUtils.addParam(connectionPoint.getZ());//5
                connectionPointsBuilder.append(connectionPointBuilder);
            }
        }
        return connectionPointsBuilder;
    }

    private static StringBuilder addDecompositions(String decompositionMark,
                                                   List<DecompositionEntity> decompositions) {
        StringBuilder decompositionsBuilder = null;
        if (CollectionUtils.isNotEmpty(decompositions)) {
            decompositionsBuilder = new StringBuilder();
            for (DecompositionEntity decomposition : decompositions) {
                if (decomposition.getSource() == null) {
                    continue;
                }
                String decompositionBuilder = CSVUtils.NEW_LINE +
                        CSVUtils.addParam(decompositionMark) + //0
                        CSVUtils.addParam(decomposition.getSource()) +//1
                        CSVUtils.addParam(decomposition.getSapMaterialNumber()) + //2
                        CSVUtils.addParam(decomposition.getQuantity()) +//3
                        CSVUtils.addParam(decomposition.getZOptions()) +//4
                        CSVUtils.addParam(decomposition.getUnitInSAP()) +//5
                        CSVUtils.addParam(decomposition.getDescription()) +//6
                        CSVUtils.addParam(decomposition.getDerivedFrom()) +//7
                        CSVUtils.addParam(decomposition.getStandardMaterial() != null ?
                                decomposition.getStandardMaterial().toString() :
                                null) +//8
                        CSVUtils.addParam(decomposition.getChangeDescription()) +//9
                        CSVUtils.addParam(decomposition.getMpsPrefix()) +//10
                        CSVUtils.addParam(decomposition.getMaterialType()) +//11
                        CSVUtils.addParam(decomposition.getPositionNumber()) +//12
                        CSVUtils.addParam(decomposition.getSourceInternal());//13
                decompositionsBuilder.append(decompositionBuilder);
            }
        }
        return decompositionsBuilder;
    }

}