package com.siemens.spine.logic.mapper;

import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.ConnectionPointEntity;
import com.siemens.spine.db.entity.DecompositionEntity;
import com.siemens.spine.db.entity.GroupEntity;
import com.siemens.spine.logic.model.Component;
import com.siemens.spine.logic.model.ConnectionPoint;
import com.siemens.spine.logic.model.Decomposition;
import com.siemens.spine.logic.model.Group;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValueMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/04/22
 */

@Mapper(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
public interface GroupVersionMapper {

    GroupVersionMapper INSTANCE = Mappers.getMapper(GroupVersionMapper.class);

    @Named("toGroupModel")
    @Mapping(source = "components", target = "components", qualifiedByName = "toComponents")
    Group toGroup(GroupEntity groupEntity);

    @Named("toComponents")
    @IterableMapping(qualifiedByName = "toComponent")
    Set<Component> toComponents(Set<ComponentEntity> entities);

    @Mapping(source = "decompositions", target = "decompositions", qualifiedByName = "toDecompositions")
    @Mapping(source = "connectionPoints", target = "connectionPoints", qualifiedByName = "toConnectionPoints")
    @Named("toComponent")
    Component toComponent(ComponentEntity entity);

    @Named("toDecompositions")
    @IterableMapping(qualifiedByName = "toDecomposition")
    List<Decomposition> toDecompositions(List<DecompositionEntity> entities);

    @Named("toDecomposition")
    Decomposition toDecomposition(DecompositionEntity entity);

    @Named("toConnectionPoints")
    @IterableMapping(qualifiedByName = "toConnectionPoint")
    List<ConnectionPoint> toConnectionPoints(List<ConnectionPointEntity> entities);

    @Named("toConnectionPoint")
    ConnectionPoint toConnectionPoint(ConnectionPointEntity entity);

}
