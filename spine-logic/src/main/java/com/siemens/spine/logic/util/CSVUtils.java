package com.siemens.spine.logic.util;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 26/12/2022
 */
public class CSVUtils {

    public static final String DELIMITER = ";";
    public static final String QUOTE = "\"";
    public static final String NEW_LINE = System.lineSeparator();

    public static final String EMPTY = "";

    public static String addParam(String param) {
        if (param == null || param.equals(EMPTY)) {
            return DELIMITER;
        }
        return QUOTE + String.join(QUOTE + QUOTE, param.split(QUOTE)) + QUOTE
                + DELIMITER;
    }

    public static String addParam(Number param) {
        if (param == null) {
            return addParam(EMPTY);
        }
        return addParam(String.valueOf(param));
    }

}
