package com.siemens.spine.logic.security;

import com.siemens.spine.logic.exception.UnauthorizedException;

public interface AuthService {

    /**
     * validate the access token
     *
     * @param token
     */
    void validateToken(String token) throws UnauthorizedException;

    /**
     * Check the roles allowed
     *
     * @param rolesAllowed
     */
    boolean checkRolesAllowed(String[] rolesAllowed);

}
