package com.siemens.spine.logic.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.siemens.spine.logic.exception.SpineException;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.Objects;

/**
 * Service for handling JSON serialization and deserialization operations.
 * Provides centralized JSON processing functionality for REST operations
 * with proper error handling and logging.
 *
 * <AUTHOR>
 * @since 1.0
 */
@ApplicationScoped
@Slf4j
public class JsonSerializationService {

    private final ObjectMapper objectMapper;

    /**
     * Constructor for dependency injection.
     */
    @Inject
    public JsonSerializationService(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * Deserializes JSON string to an entity object of the specified class.
     *
     * @param jsonBody    The JSON string to deserialize
     * @param entityClass The target entity class
     * @return The deserialized entity object
     * @throws SpineException if deserialization fails
     */
    public Object deserializeEntity(String jsonBody, Class<?> entityClass) throws SpineException {
        Objects.requireNonNull(jsonBody, "JSON body cannot be null");
        Objects.requireNonNull(entityClass, "Entity class cannot be null");

        if (jsonBody.trim().isEmpty()) {
            throw new SpineException("JSON body cannot be empty");
        }

        try {
            log.debug("Deserializing JSON to entity class: {}", entityClass.getSimpleName());
            return objectMapper.readValue(jsonBody, entityClass);
        } catch (Exception e) {
            log.error("Failed to deserialize JSON to entity class {}: ", entityClass.getSimpleName(), e);
            throw new SpineException(e.getMessage(), e);
        }
    }

}
