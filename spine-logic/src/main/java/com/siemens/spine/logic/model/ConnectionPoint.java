package com.siemens.spine.logic.model;

public class ConnectionPoint {

    protected String name;
    protected String type;
    protected Integer x;
    protected Integer y;
    protected Integer z;

    /**
     * Gets the value of the name property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Gets the value of the type property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * Gets the value of the x property.
     *
     * @return possible object is
     * {@link Integer }
     */
    public Integer getX() {
        return x;
    }

    /**
     * Sets the value of the x property.
     *
     * @param value allowed object is
     *              {@link Integer }
     */
    public void setX(Integer value) {
        this.x = value;
    }

    /**
     * Gets the value of the y property.
     *
     * @return possible object is
     * {@link Integer }
     */
    public Integer getY() {
        return y;
    }

    /**
     * Sets the value of the y property.
     *
     * @param value allowed object is
     *              {@link Integer }
     */
    public void setY(Integer value) {
        this.y = value;
    }

    /**
     * Gets the value of the z property.
     *
     * @return possible object is
     * {@link Integer }
     */
    public Integer getZ() {
        return z;
    }

    /**
     * Sets the value of the z property.
     *
     * @param value allowed object is
     *              {@link Integer }
     */
    public void setZ(Integer value) {
        this.z = value;
    }

}
