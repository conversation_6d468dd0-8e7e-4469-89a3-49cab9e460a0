package com.siemens.spine.logic.dto.request;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.siemens.spine.logic.anotation.Trim;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CreateQueryTemplateDTO {

    @JsonAlias({ "template_name", "templateName" })
    @Trim
    private String templateName;

    @JsonAlias({ "sqlTemplate", "sql_template" })
    @Trim
    private String sqlTemplate;

    @Trim
    private String description;

    //  @JsonIgnore
    @JsonAlias({ "requiresPagination", "requires_pagination" })
    private boolean requiresPagination = true;

}