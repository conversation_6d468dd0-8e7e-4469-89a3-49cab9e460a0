package com.siemens.spine.logic.service.impl;

import com.siemens.spine.logic.dto.ComponentStateChangedResponseDto;
import com.siemens.spine.logic.dto.request.StateChangedRequestDto;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.service.BatchJobService;
import com.siemens.spine.logic.service.functioninterface.ComponentStateChangedProcessor;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;

@ApplicationScoped
@Slf4j
public class BatchJobServiceImpl implements BatchJobService {

    @Override
    public <T, R> List<R> executeJobs(Function<List<T>, R> function, List<T> input) throws SpineException {
        return executeJobs(function, input, DEFAULT_BATCH_SIZE);
    }

    @Override
    public <T, R> List<R> executeJobs(Function<List<T>, R> function, List<T> input, int batchSize)
            throws SpineException {
        return executeJobs(function, input, batchSize, null);
    }

    @Override
    public <T, R> List<R> executeJobs(Function<List<T>, R> function,
                                      List<T> input,
                                      int batchSize,
                                      BatchJobExceptionHandler exceptionHandler) throws SpineException {
        if (batchSize <= 0) {
            batchSize = DEFAULT_BATCH_SIZE;
        }

        // Execute in current thread when the number of data is not big enough
        if (input.size() <= batchSize) {
            return List.of(function.apply(input));
        }

        batchSize = optimizeBatchSize(input.size(), batchSize);

        // Split to multiple tasks
        List<CompletableFuture<R>> futureTasks = splitTasks(function, input, batchSize);
        // wait until all tasks are completed
        CompletableFuture.allOf(futureTasks.toArray(new CompletableFuture[0])).join();

        return completeResponse(futureTasks, exceptionHandler);
    }

    @Override
    @Transactional
    public List<ComponentStateChangedResponseDto> executeComponentStateChanged(ComponentStateChangedProcessor processor,
                                                                               StateChangedRequestDto input,
                                                                               int batchSize) throws SpineException {
        List<Long> componentIds = input.getComponentIds();
        if (componentIds == null || componentIds.isEmpty()) {
            log.error("The input components should not be empty");
            return Collections.emptyList();
        }

        if (batchSize <= 0) {
            batchSize = DEFAULT_BATCH_SIZE;
        }

        // Execute in current thread when the number of data is not big enough
        if (componentIds.size() <= batchSize) {
            return List.of(processor.process(input));
        }

        batchSize = optimizeBatchSize(componentIds.size(), batchSize);

        List<CompletableFuture<ComponentStateChangedResponseDto>> futureTasks = new ArrayList<>();
        // split to multiple tasks
        for (int i = 0; i < componentIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, componentIds.size());
            List<Long> subComponents = componentIds.subList(i, endIndex);
            StateChangedRequestDto subData = StateChangedRequestDto.builder()
                    .projectId(input.getProjectId())
                    .componentIds(subComponents)
                    .newState(input.getNewState())
                    .description(input.getDescription())
                    .changeGroup(input.getChangeGroup())
                    .build();

            // execute job in async
            CompletableFuture<ComponentStateChangedResponseDto> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return processor.process(subData);
                } catch (SpineException e) {
                    throw new RuntimeException(e);
                }
            });
            futureTasks.add(future);
        }

        // wait until all tasks are completed
        CompletableFuture.allOf(futureTasks.toArray(new CompletableFuture[0])).join();

        return completeResponse(futureTasks, null);
    }

    private <T, R> List<CompletableFuture<R>> splitTasks(Function<List<T>, R> function, List<T> input, int batchSize) {
        List<CompletableFuture<R>> futureTasks = new ArrayList<>();

        // split to multiple tasks
        for (int i = 0; i < input.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, input.size());
            List<T> subData = input.subList(i, endIndex);

            CompletableFuture<R> future = CompletableFuture.supplyAsync(() -> function.apply(subData));
            futureTasks.add(future);
        }

        log.debug(String.format("Execute %d job(s) to process %d data", futureTasks.size(), input.size()));
        return futureTasks;
    }

    private <R> List<R> completeResponse(List<CompletableFuture<R>> futureTasks,
                                         BatchJobExceptionHandler exceptionHandler) {
        List<R> ret = new ArrayList<>();
        for (CompletableFuture<R> task : futureTasks) {
            try {
                if (task.get() != null) {
                    ret.add(task.get());
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                if (exceptionHandler != null) {
                    exceptionHandler.handle(e);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                if (exceptionHandler != null) {
                    exceptionHandler.handle(e);
                }
            }
        }

        return ret;
    }

    /**
     * Balance the amount of processing items for each executor
     * Eg. totalItems = 1001, batchSize = 1000 -> optimize the batchSize to 501 instead of 1000
     *
     * @param totalItems
     * @param currentBatchSize
     * @return
     */
    private int optimizeBatchSize(int totalItems, int currentBatchSize) {
        int mod = totalItems % currentBatchSize;
        int numOfJobs = mod == 0 ? (totalItems / currentBatchSize) : (totalItems / currentBatchSize + 1);

        return mod == 0 ? currentBatchSize : (totalItems / numOfJobs + 1);
    }

}
