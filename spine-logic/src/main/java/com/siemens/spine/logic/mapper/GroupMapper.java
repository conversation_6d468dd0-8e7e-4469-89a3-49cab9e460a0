package com.siemens.spine.logic.mapper;

import com.siemens.spine.db.entity.GroupEntity;
import com.siemens.spine.logic.dto.GroupDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 10/01/2023
 */
@Mapper
public interface GroupMapper {

    GroupMapper INSTANCE = Mappers.getMapper(GroupMapper.class);

    @Mapping(target = "deliverydate", source = "deliverydate")
    GroupDto toGroup(GroupEntity groupEntity);

    List<GroupDto> toGroups(List<GroupEntity> groupEntities);

}
