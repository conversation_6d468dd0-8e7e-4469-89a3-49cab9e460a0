package com.siemens.spine.logic.service;

import com.siemens.spine.db.entity.elcon.QueryTemplateEntity;
import com.siemens.spine.logic.dto.QueryTemplateDTO;
import com.siemens.spine.logic.dto.request.CreateQueryTemplateDTO;
import com.siemens.spine.logic.dto.request.DynamicQueryByNameDTO;
import com.siemens.spine.logic.dto.request.DynamicQueryDTO;
import com.siemens.spine.logic.validator.query.ValidationResult;

import java.sql.SQLException;
import java.util.Collection;
import java.util.Map;

public interface DynamicQueryService {

    QueryTemplateDTO getQueryTemplate(Long templateId);

    QueryTemplateDTO getQueryTemplate(String templateName);

    int getTotalCount(String templateName, Map<String, Object> parameters) throws SQLException;

    Map<String, Object> executeQuerySafe(DynamicQueryDTO dto) throws SQLException;

    Map<String, Object> executeQuerySafe(DynamicQueryByNameDTO dto) throws SQLException;

    Collection<QueryTemplateDTO> getAllTemplates();

    ValidationResult validateQueryTemplate(String template) throws SQLException;

    QueryTemplateDTO createQueryTemplate(CreateQueryTemplateDTO template) throws SQLException;

    QueryTemplateDTO updateQueryTemplate(QueryTemplateDTO template) throws SQLException;

    void deleteQueryTemplate(Long templateId) throws SQLException;

    QueryTemplateEntity getQueryTemplateEntity(Long templateId);

    QueryTemplateEntity getQueryTemplateByName(String templateName);

}
