package com.siemens.spine.logic.service.impl;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.siemens.spine.db.constant.ComponentState;
import com.siemens.spine.db.constant.ProjectState;
import com.siemens.spine.db.constant.RollbackStateType;
import com.siemens.spine.db.constant.UpdatingComponentFailedReason;
import com.siemens.spine.db.entity.ChangeGroupEntity;
import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.ProjectEntity;
import com.siemens.spine.db.entity.StateEntity;
import com.siemens.spine.db.repository.ChangeGroupRepository;
import com.siemens.spine.db.repository.ComponentRepository;
import com.siemens.spine.db.repository.ProjectRepository;
import com.siemens.spine.db.repository.StateRepository;
import com.siemens.spine.db.repository.filter.ComponentFilter;
import com.siemens.spine.db.repository.filter.ComponentStateFilter;
import com.siemens.spine.generated.toolchain.State;
import com.siemens.spine.logic.dto.ChangeGroupDto;
import com.siemens.spine.logic.dto.ComponentConditionDTO;
import com.siemens.spine.logic.dto.ComponentStateChangedResponseDto;
import com.siemens.spine.logic.dto.SimplePairDto;
import com.siemens.spine.logic.dto.StateDto;
import com.siemens.spine.logic.dto.request.StateChangedRequestDto;
import com.siemens.spine.logic.dto.request.UndeleteComponentRequestDto;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.mapper.ChangeGroupMapper;
import com.siemens.spine.logic.mapper.StateMapper;
import com.siemens.spine.logic.service.BatchJobService;
import com.siemens.spine.logic.service.ChangeGroupService;
import com.siemens.spine.logic.service.ComponentStateService;
import com.siemens.spine.logic.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@ApplicationScoped
@Slf4j
public class ComponentStateServiceImpl implements ComponentStateService {

    private final BatchJobService batchJobService;

    private final ChangeGroupService changeGroupService;

    private final ProjectRepository projectRepository;

    private final ComponentRepository componentRepository;

    private final StateRepository stateRepository;

    private final ChangeGroupRepository changeGroupRepository;

    @Inject
    public ComponentStateServiceImpl(BatchJobService batchJobService,
                                     ChangeGroupService changeGroupService,
                                     ProjectRepository projectRepository,
                                     ComponentRepository componentRepository,
                                     StateRepository stateRepository,
                                     ChangeGroupRepository changeGroupRepository) {
        this.batchJobService = batchJobService;
        this.changeGroupService = changeGroupService;
        this.projectRepository = projectRepository;
        this.componentRepository = componentRepository;
        this.stateRepository = stateRepository;
        this.changeGroupRepository = changeGroupRepository;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public boolean setStates(String projectName, List<State> states) throws SpineException {
        log.info("Start set state of {} items", states.size());
        if (CollectionUtils.isEmpty(states) || StringUtils.isEmpty(projectName)) {
            throw new SpineException("Invalid request");
        }

        Optional<ProjectEntity> project = projectRepository.findByName(projectName);
        if (project.isEmpty() || ProjectState.CLOSED.name().equals(project.get().getMainProjectState())) {
            throw new SpineException("No project was found by name: " + projectName);
        }

        List<StateEntity> newStateEntities = new ArrayList<>();
        Multimap<Integer, Long> updateStateComponentIdsMMap = ArrayListMultimap.create();
        List<Long> componentIds = states.stream().map(State::getUniqueID).filter(StringUtils::isNotEmpty).map(s -> {
            try {
                return Long.valueOf(s);
            } catch (NumberFormatException e) {
                return null;
            }
        }).filter(Objects::nonNull).toList();
        List<ComponentEntity> components = componentRepository.findByProjectNameAndComponentIds(projectName,
                componentIds);
        Map<Long, ComponentEntity> componentEntityMap = components.stream()
                .collect(Collectors.toMap(ComponentEntity::getId, Function.identity(), (c1, c2) -> c1));
        for (State state : states) {
            Integer newState = state.getState();
            if (!validateState(newState, project.get().getMainProjectState())) {
                continue;
            }

            Long componentId;
            try {
                componentId = Long.parseLong(state.getUniqueID());
            } catch (Exception e) {
                log.error("Could not update state of component " + state.getUniqueID(), e.getMessage());
                continue;
            }
            ComponentEntity component = componentEntityMap.get(componentId);
            Integer currentState = component.getCurrentState();
            if (Objects.equals(currentState, state.getState())) {
                log.warn("Skip update component {}", componentId);
                continue;
            }
            component.setSysModDate(new Timestamp(System.currentTimeMillis()));
            updateStateComponentIdsMMap.put(state.getState(), componentId);

            ChangeGroupEntity changeGroupEntity = null;
            if (state.getChangeGroupId() != null) {
                Optional<ChangeGroupEntity> changeGroupOpt = changeGroupRepository.findById(
                        (long) state.getChangeGroupId());
                if (changeGroupOpt.isPresent()) {
                    changeGroupEntity = changeGroupOpt.get();
                }
            }

            // Create the state entity
            StateEntity newStateEntity = StateEntity.builder().component(componentEntityMap.get(componentId))
                    .state(newState).remark(state.getRemark()).reason(state.getReason())
                    .statusDate(new Timestamp(System.currentTimeMillis()))
                    .username(SecurityUtils.getUsernameFromToken()).changeGroup(changeGroupEntity).build();
            newStateEntities.add(newStateEntity);
        }

        updateCurrentStateComponent(updateStateComponentIdsMMap);

        stateRepository.saveAll(newStateEntities);
        log.info("End set state of {} items", states.size());
        return true;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public List<State> getLatestStates(ComponentConditionDTO componentConditionDTO) {
        // 1. Validate
        if (componentConditionDTO == null) {
            throw new IllegalArgumentException("Invalid input");
        }

        // 2. Get components
        ComponentFilter filter = ComponentFilter.builder().projectId(componentConditionDTO.getProjectId())
                .specificProjectName(componentConditionDTO.getSpecificProjectName())
                .componentIds(componentConditionDTO.getComponentIds())
                .groupMapping(componentConditionDTO.getGroupMapping()).build();
        List<ComponentEntity> componentEntities = componentRepository.filter(filter);

        // 3. Get states
        List<Long> componentIds = componentEntities.stream().map(ComponentEntity::getId).toList();
        List<StateEntity> stateEntities = stateRepository.findLatestComponentState(componentIds);

        return StateMapper.INSTANCE.toStates(stateEntities);
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public List<StateDto> getStates(ComponentStateFilter filter) {
        List<StateEntity> stateEntities = stateRepository.filter(filter);

        if (stateEntities == null || stateEntities.isEmpty()) {
            return Collections.emptyList();
        }

        return StateMapper.INSTANCE.toStateDtos(stateEntities);
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public ComponentStateChangedResponseDto moveForward(StateChangedRequestDto request) throws SpineException {
        // 1. Validate move forward request
        if (request == null || request.getComponentIds() == null || request.getComponentIds().isEmpty()) {
            throw new SpineException("Please select at least 1 component to move forward");
        }

        Optional<ProjectEntity> projectEntity = projectRepository.findById(request.getProjectId());
        if (projectEntity.isEmpty() || ProjectState.CLOSED.name().equals(projectEntity.get().getMainProjectState())) {
            throw new SpineException(
                    String.format("The project %d was not found or already closed", request.getProjectId()));
        }

        // split and execute multiple tasks
        List<ComponentStateChangedResponseDto> batchResponses = batchJobService.executeComponentStateChanged(
                this::doMoveForward, request, BatchJobService.DEFAULT_BATCH_SIZE);
        return buildUpdatingComponentResponse(batchResponses);
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public ComponentStateChangedResponseDto moveBackward(StateChangedRequestDto request) throws SpineException {
        // 1. Validate move backward request
        if (request == null || request.getComponentIds() == null || request.getComponentIds().isEmpty()) {
            throw new SpineException("Please select at least 1 component to move backward");
        }

        Optional<ProjectEntity> projectEntity = projectRepository.findById(request.getProjectId());
        if (projectEntity.isEmpty() || ProjectState.CLOSED.name().equals(projectEntity.get().getMainProjectState())) {
            throw new SpineException(
                    String.format("The project %d was not found or already closed", request.getProjectId()));
        }

        // Validate state of all components
        int newState = request.getNewState();
        if (ComponentState.resolve(newState) == null) {
            throw new SpineException(String.format("The component state '%d' has not been defined yet", newState));
        }

        // create new change_group or select the exist one
        ChangeGroupEntity changeGroup = selectChangeGroup(request.getChangeGroup(), request.getProjectId());
        if (changeGroup != null) {
            request.setChangeGroup(ChangeGroupMapper.INSTANCE.toChangeGroupDto(changeGroup));
        }

        List<ComponentStateChangedResponseDto> batchResponses = batchJobService.executeComponentStateChanged(
                this::doMoveBackward, request, BatchJobService.DEFAULT_BATCH_SIZE);
        ComponentStateChangedResponseDto ret = buildUpdatingComponentResponse(batchResponses);

        // Count the number of unknown components (by its id) that can not be processed
        int otherSkipped = request.getComponentIds().size() - ret.getTotalSkipped() - ret.getNewStates().size();
        ret.getSkippedReason().put(UpdatingComponentFailedReason.OTHER, otherSkipped);

        return ret;
    }

    @Override
    public List<SimplePairDto> getAllowedBackwardState(StateChangedRequestDto request) throws SpineException {
        // Validate move backward request
        if (request == null || request.getComponentIds() == null || request.getComponentIds().isEmpty()) {
            throw new SpineException("Please select at least 1 component to move backward");
        }

        Optional<ProjectEntity> projectEntity = projectRepository.findById(request.getProjectId());
        if (projectEntity.isEmpty() || ProjectState.CLOSED.name().equals(projectEntity.get().getMainProjectState())) {
            throw new SpineException(
                    String.format("The project %d was not found or already closed", request.getProjectId()));
        }

        // find the components
        List<List<ComponentEntity>> batchResponses = batchJobService.executeJobs(this::getComponentEntities,
                request.getComponentIds());
        List<ComponentEntity> entities = new ArrayList<>();
        for (List<ComponentEntity> batchRes : batchResponses) {
            entities.addAll(batchRes);
        }

        // Find the smallest state of the components
        String projectState = projectEntity.get().getMainProjectState();
        int smallestAllowedState = ProjectState.EXECUTION.name().equals(projectState) ?
                ComponentState.EXECUTION_PHASE_200_0.getV1Status() :
                ComponentState.PROPOSAL_PHASE_40_0.getV1Status();
        int highestAllowedState = ProjectState.EXECUTION.name().equals(projectState) ?
                ComponentState.EXECUTION_PHASE_650_F.getV1Status() :
                ComponentState.PROPOSAL_PHASE_40_F.getV1Status();
        for (ComponentEntity entity : entities) {
            Integer componentState = entity.getCurrentState();
            if (componentState == null || ComponentState.resolve(componentState) == null) {
                continue;
            }

            if (componentState == ComponentState.DELETED.getV1Status()) {
                highestAllowedState = ComponentState.DELETED.getV1Status();
                break;
            }

            if (componentState >= smallestAllowedState && componentState < highestAllowedState) {
                highestAllowedState = componentState;
            }
        }

        // convert the response
        List<SimplePairDto> ret = new ArrayList<>();
        for (ComponentState componentState : ComponentState.values()) {
            if (componentState.getV1Status() >= smallestAllowedState && componentState.getV1Status() < highestAllowedState) {
                ret.add(new SimplePairDto(String.valueOf(componentState.getV1Status()), componentState.getName()));
            }
        }

        return ret;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public ComponentStateChangedResponseDto markAsDeleted(List<Long> componentIds) throws SpineException {
        // split and execute multiple tasks
        List<ComponentStateChangedResponseDto> batchResponses = batchJobService.executeJobs(this::doMarkAsDeleted,
                componentIds);
        return buildUpdatingComponentResponse(batchResponses);
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public ComponentStateChangedResponseDto undelete(UndeleteComponentRequestDto request) throws SpineException {
        // Validate move backward request
        if (request == null || request.getComponentIds() == null || request.getComponentIds().isEmpty()) {
            throw new SpineException("Invalid request");
        }

        Optional<ProjectEntity> projectEntity = projectRepository.findById(request.getProjectId());
        if (projectEntity.isEmpty() || ProjectState.CLOSED.name().equals(projectEntity.get().getMainProjectState())) {
            throw new SpineException(
                    String.format("The project %d was not found or already closed", request.getProjectId()));
        }

        ComponentFilter filter = ComponentFilter.builder().projectId(request.getProjectId())
                .componentIds(request.getComponentIds()).states(List.of(ComponentState.DELETED.getV1Status())).build();
        List<ComponentEntity> foundComponents = componentRepository.filter(filter);

        if (foundComponents.isEmpty()) {
            throw new SpineException("No input components was marked as deleted. Please reload the data!");
        }

        RollbackStateType rollbackStateType = RollbackStateType.resolve(request.getTypeNum());
        if (rollbackStateType == null) {
            rollbackStateType = RollbackStateType.TYPE_0;
        }

        ComponentState newComponentState;
        if (ProjectState.EXECUTION.name().equals(projectEntity.get().getMainProjectState())) {
            newComponentState = rollbackStateType == RollbackStateType.TYPE_0 ?
                    ComponentState.EXECUTION_PHASE_200_0 :
                    ComponentState.EXECUTION_PHASE_200_1;
        } else {
            newComponentState = rollbackStateType == RollbackStateType.TYPE_0 ?
                    ComponentState.PROPOSAL_PHASE_40_0 :
                    ComponentState.PROPOSAL_PHASE_40_1;
        }

        ChangeGroupEntity changeGroup = selectChangeGroup(request.getChangeGroup(), request.getProjectId());
        List<StateEntity> newStateEntities = new ArrayList<>();
        Multimap<Integer, Long> updateStateComponentIdsMMap = ArrayListMultimap.create();
        for (ComponentEntity component : foundComponents) {
            component.setSysModDate(new Timestamp(System.currentTimeMillis()));
            // save the current status of component
            updateStateComponentIdsMMap.put(newComponentState.getV1Status(), component.getId());

            StateEntity backwardState = StateEntity.builder().state(newComponentState.getV1Status())
                    .username(SecurityUtils.getUsernameFromToken())
                    .statusDate(new Timestamp(System.currentTimeMillis())).component(component)
                    .remark(request.getDescription()).changeGroup(changeGroup).build();

            newStateEntities.add(backwardState);
        }

        updateCurrentStateComponent(updateStateComponentIdsMMap);

        // save all new states
        List<StateEntity> savedEntities = stateRepository.saveAll(newStateEntities);
        List<StateDto> stateDtos = StateMapper.INSTANCE.toStateDtos(savedEntities);
        // Count the number of unknown components (by its id) that can not be processed
        int otherSkipped = request.getComponentIds().size() - stateDtos.size();
        Map<UpdatingComponentFailedReason, Integer> skippedInfo = Map.of(UpdatingComponentFailedReason.OTHER,
                otherSkipped);

        return ComponentStateChangedResponseDto.builder().totalSkipped(otherSkipped).newStates(stateDtos)
                .skippedReason(skippedInfo).build();
    }

    private ComponentStateChangedResponseDto doMoveForward(StateChangedRequestDto request) throws SpineException {
        List<ComponentEntity> componentEntities = getComponentEntities(request.getProjectId(),
                request.getComponentIds());
        if (componentEntities.isEmpty()) {
            return null;
        }
        Optional<ProjectEntity> project = projectRepository.findById(request.getProjectId());
        if (project.isEmpty()) {
            throw new SpineException(String.format("Project %d not exists", request.getProjectId()));
        }
        String projectState = project.get().getMainProjectState();
        int maximumState = ProjectState.OFFER.name().equals(projectState) ?
                ComponentState.PROPOSAL_PHASE_40_F.getV1Status() :
                ComponentState.EXECUTION_PHASE_650_F.getV1Status();
        // Response information
        int totalSkipped = 0;
        Map<UpdatingComponentFailedReason, Integer> skippedInfo = new LinkedHashMap<>(initFailedReasonMap());

        List<StateEntity> newStateEntities = new ArrayList<>();
        Multimap<Integer, Long> updateStateComponentIdsMMap = ArrayListMultimap.create();
        for (ComponentEntity component : componentEntities) {
            // validate if it was marked as deleted
            if (component.getCurrentState() == ComponentState.DELETED.getV1Status()) {
                log.warn("Skip moving forward the component {} because it was marked as deleted", component.getId());
                skippedInfo.compute(UpdatingComponentFailedReason.MARKED_AS_DELETED,
                        (k, numberOfDeleted) -> numberOfDeleted == null ? 1 : numberOfDeleted + 1);

                totalSkipped += 1;
                continue;
            }

            if (component.getCurrentState() >= maximumState) {
                log.warn("The current state of component {} is in final state when project state is {}",
                        component.getId(), projectState);
                skippedInfo.compute(UpdatingComponentFailedReason.INVALID_STATUS,
                        (k, otherFailed) -> otherFailed == null ? 1 : otherFailed + 1);

                totalSkipped += 1;
                continue;
            }

            // validate if the next status is not determined
            ComponentState nextStatus = ComponentState.getNextState(component.getCurrentState());
            if (nextStatus == null) {
                log.warn("Could not determine the next status of the component {}. Current: {}", component.getId(),
                        component.getCurrentState());
                skippedInfo.compute(UpdatingComponentFailedReason.INVALID_STATUS,
                        (k, otherFailed) -> otherFailed == null ? 1 : otherFailed + 1);

                totalSkipped += 1;
                continue;
            }
            component.setSysModDate(new Timestamp(System.currentTimeMillis()));
            // save the current status of component
            // By the requirement, don't save the history of the component state when move forward
            updateStateComponentIdsMMap.put(nextStatus.getV1Status(), component.getId());

            StateEntity newState = StateEntity.builder().state(nextStatus.getV1Status())
                    .statusDate(new Timestamp(System.currentTimeMillis())).component(component).build();
            newStateEntities.add(newState);
        }

        updateCurrentStateComponent(updateStateComponentIdsMMap);

        // save all new states
        List<StateEntity> savedEntities = stateRepository.saveAll(newStateEntities);
        // build the response
        List<StateDto> newStates = StateMapper.INSTANCE.toStateDtos(savedEntities);

        // Count the number of unknown components (by its id) that can not be processed
        int otherSkipped = request.getComponentIds().size() - totalSkipped - newStates.size();
        totalSkipped += otherSkipped;
        skippedInfo.put(UpdatingComponentFailedReason.OTHER, otherSkipped);

        return ComponentStateChangedResponseDto.builder().totalSkipped(totalSkipped).newStates(newStates)
                .skippedReason(skippedInfo).build();
    }

    private ComponentStateChangedResponseDto doMoveBackward(StateChangedRequestDto request) {
        List<ComponentEntity> componentEntities = getComponentEntities(request.getProjectId(),
                request.getComponentIds());
        int newState = request.getNewState();
        String description = request.getDescription();

        ChangeGroupEntity changeGroup = null;
        // When change group is needed, it required to saved successfully into database first
        if (request.getChangeGroup() != null && request.getChangeGroup().getId() != null) {
            changeGroup = ChangeGroupMapper.INSTANCE.toChangeGroupEntity(request.getChangeGroup());
        }

        List<StateEntity> newStateEntities = new ArrayList<>();
        int totalSkipped = 0;
        Map<UpdatingComponentFailedReason, Integer> skippedInfo = new LinkedHashMap<>(initFailedReasonMap());
        Multimap<Integer, Long> updateStateComponentIdsMMap = ArrayListMultimap.create();
        for (ComponentEntity component : componentEntities) {
            Long componentId = component.getId();

            if (component.getCurrentState() == ComponentState.DELETED.getV1Status()) {
                log.warn("Skip moving backward the component {} because it was marked as deleted", componentId);

                skippedInfo.compute(UpdatingComponentFailedReason.MARKED_AS_DELETED,
                        (k, numberOfDeleted) -> numberOfDeleted == null ? 1 : numberOfDeleted + 1);
                totalSkipped += 1;
                continue;
            }

            if (component.getCurrentState() <= newState) {
                log.warn("Could not move backward state of component {} from {} (current) to {}",
                        componentId, component.getCurrentState(), newState);

                totalSkipped += 1;
                skippedInfo.compute(UpdatingComponentFailedReason.INVALID_STATUS,
                        (k, numberOfDeleted) -> numberOfDeleted == null ? 1 : numberOfDeleted + 1);
                continue;
            }
            component.setSysModDate(new Timestamp(System.currentTimeMillis()));
            // save the current status of component
            // By the requirement, don't save the history of the component state when move forward
            updateStateComponentIdsMMap.put(newState, componentId);

            StateEntity backwardState = StateEntity.builder().state(newState)
                    .username(SecurityUtils.getUsernameFromToken())
                    .statusDate(new Timestamp(System.currentTimeMillis())).component(component).remark(description)
                    .changeGroup(changeGroup).build();
            newStateEntities.add(backwardState);
        }

        updateCurrentStateComponent(updateStateComponentIdsMMap);

        // save all new states
        List<StateEntity> savedEntities = stateRepository.saveAll(newStateEntities);

        // build the response
        List<StateDto> newStates = StateMapper.INSTANCE.toStateDtos(savedEntities);

        return ComponentStateChangedResponseDto.builder().skippedReason(skippedInfo).totalSkipped(totalSkipped)
                .newStates(newStates).build();
    }

    private void updateCurrentStateComponent(Multimap<Integer, Long> updateStateComponentIdsMMap) {
        for (Map.Entry<Integer, Collection<Long>> updateStateComponentIds : updateStateComponentIdsMMap.asMap()
                .entrySet()) {
            componentRepository.updateStateComponentByIds(new ArrayList<>(updateStateComponentIds.getValue()),
                    updateStateComponentIds.getKey());
        }
    }

    private ComponentStateChangedResponseDto doMarkAsDeleted(List<Long> componentIds) {
        // Response information
        int totalSkipped = 0;
        Map<UpdatingComponentFailedReason, Integer> skippedInfo = new LinkedHashMap<>(initFailedReasonMap());
        List<StateEntity> newStates = new ArrayList<>();

        List<ComponentEntity> entities = getComponentEntities(componentIds);
        Multimap<Integer, Long> updateStateComponentIdsMMap = ArrayListMultimap.create();
        for (ComponentEntity entity : entities) {
            if (entity.getCurrentState() == ComponentState.DELETED.getV1Status()) {
                log.warn(String.format("Component %s has been already marked as deleted", entity.getId()));
                int numberOfDeleted = skippedInfo.get(UpdatingComponentFailedReason.MARKED_AS_DELETED);
                skippedInfo.put(UpdatingComponentFailedReason.MARKED_AS_DELETED, numberOfDeleted + 1);
                totalSkipped += 1;

                continue;
            }
            // save the current status of component
            entity.setSysModDate(new Timestamp(System.currentTimeMillis()));
            updateStateComponentIdsMMap.put(ComponentState.DELETED.getV1Status(), entity.getId());

            StateEntity deletedState = StateEntity.builder().state(ComponentState.DELETED.getV1Status())
                    .username(SecurityUtils.getUsernameFromToken())
                    .statusDate(new Timestamp(System.currentTimeMillis())).component(entity).build();
            newStates.add(deletedState);
        }

        updateCurrentStateComponent(updateStateComponentIdsMMap);

        // save all new states
        List<StateEntity> savedEntities = stateRepository.saveAll(newStates);

        List<StateDto> stateDtos = StateMapper.INSTANCE.toStateDtos(savedEntities);
        // Count the number of unknown components (by its id) that can not be processed
        int otherSkipped = componentIds.size() - totalSkipped - stateDtos.size();
        totalSkipped += otherSkipped;
        skippedInfo.put(UpdatingComponentFailedReason.OTHER, otherSkipped);

        return ComponentStateChangedResponseDto.builder().totalSkipped(totalSkipped).newStates(stateDtos)
                .skippedReason(skippedInfo).build();
    }

    private List<ComponentEntity> getComponentEntities(List<Long> componentIds) {
        return getComponentEntities(null, componentIds);
    }

    private List<ComponentEntity> getComponentEntities(Long projectId, List<Long> componentIds) {
        if (componentIds.isEmpty()) {
            return Collections.emptyList();
        }

        ComponentFilter filter = ComponentFilter.builder().projectId(projectId).componentIds(componentIds).build();
        List<ComponentEntity> entities = componentRepository.filter(filter);
        return entities == null ? Collections.emptyList() : entities;
    }

    private ChangeGroupEntity selectChangeGroup(ChangeGroupDto changeGroupDto, Long projectId) throws SpineException {
        // No change group was selected
        if (changeGroupDto == null) {
            log.warn(String.format("No change group of project %s was selected", projectId));
            return null;
        }

        // create new change group
        Long changeGroupId = changeGroupDto.getId();
        if (changeGroupId == null) {
            ChangeGroupDto newChangeGroup = changeGroupService.create(projectId, changeGroupDto);
            changeGroupId = newChangeGroup.getId();
        }

        // Find the change group by id
        Optional<ChangeGroupEntity> changeGroupEntity = changeGroupRepository.findById(changeGroupId);
        if (changeGroupEntity.isEmpty()) {
            throw new SpineException(
                    String.format("The selected change group %s is no longer available", changeGroupId));
        }

        if (!projectId.equals(changeGroupEntity.get().getProjectEntity().getProjectID())) {
            throw new SpineException(
                    String.format("The selected change group %s is not belong to project %s", changeGroupId,
                            projectId));
        }

        return changeGroupEntity.get();
    }

    private Map<UpdatingComponentFailedReason, Integer> initFailedReasonMap() {
        Map<UpdatingComponentFailedReason, Integer> ret = new LinkedHashMap<>();
        for (UpdatingComponentFailedReason reason : UpdatingComponentFailedReason.values()) {
            ret.put(reason, 0);
        }

        return ret;
    }

    private ComponentStateChangedResponseDto buildUpdatingComponentResponse(List<ComponentStateChangedResponseDto> batchResponses) {
        int totalSkipped = 0;
        Map<UpdatingComponentFailedReason, Integer> skippedInfo = new LinkedHashMap<>(initFailedReasonMap());
        List<StateDto> newStates = new ArrayList<>();

        for (ComponentStateChangedResponseDto responseDto : batchResponses) {
            if (responseDto == null) {
                continue;
            }

            totalSkipped += responseDto.getTotalSkipped();
            newStates.addAll(responseDto.getNewStates());

            Map<UpdatingComponentFailedReason, Integer> skippedInfoMap = responseDto.getSkippedReason();
            for (Map.Entry<UpdatingComponentFailedReason, Integer> entry : skippedInfoMap.entrySet()) {
                UpdatingComponentFailedReason reason = entry.getKey();
                int failedCount = skippedInfo.get(entry.getKey());
                skippedInfo.put(reason, failedCount + entry.getValue());
            }
        }

        return ComponentStateChangedResponseDto.builder().newStates(newStates).totalSkipped(totalSkipped)
                .skippedReason(skippedInfo).build();
    }

    private boolean validateState(Integer state, String projectState) {
        ComponentState minAllowedState = ComponentState.PROPOSAL_PHASE_40_0;
        ComponentState maxAllowedState = ComponentState.EXECUTION_PHASE_400_F;
        if (ProjectState.EXECUTION.name().equals(projectState)) {
            minAllowedState = ComponentState.EXECUTION_PHASE_200_0;
            maxAllowedState = ComponentState.EXECUTION_PHASE_650_F;
        }

        if (state == null || ComponentState.resolve(state) == null) {
            log.error(String.format("The new state '%d' is undefined", state));
            return false;
        }

        // component is marked as deleted
        if (state == 0) {
            return true;
        }

        if (state < minAllowedState.getV1Status() || state > maxAllowedState.getV1Status()) {
            log.error(String.format("Could not set status '%s' to the components of a '%s' project", state,
                    projectState));
            return false;
        }

        return true;
    }

}
