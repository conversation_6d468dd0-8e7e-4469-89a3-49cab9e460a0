package com.siemens.spine.logic.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 8/2/2023
 */
@Getter
@Setter
@NoArgsConstructor // for JSON Deserialize
public class ValidationErrorInfo extends BaseErrorDto {

    private List<ValidationErrorItem> errorItems;

    public ValidationErrorInfo(Long timestamp, String errorMessage, List<ValidationErrorItem> errorItems) {
        super(timestamp, errorMessage);
        this.errorItems = errorItems;
    }

    @Getter
    @Setter
    @NoArgsConstructor // for JSON Deserialize
    @AllArgsConstructor
    public static class ValidationErrorItem {

        private String fieldName;

        private String message;

    }

}
