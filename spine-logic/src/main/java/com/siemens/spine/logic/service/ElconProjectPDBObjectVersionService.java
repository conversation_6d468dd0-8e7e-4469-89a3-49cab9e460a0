package com.siemens.spine.logic.service;

import com.siemens.spine.logic.model.ElconObject;
import com.siemens.spine.logic.model.ElconProjectPDBObjectVersion;

/**
 * <AUTHOR>
 * @since 2025/04/21
 */
public interface ElconProjectPDBObjectVersionService
        extends VersionService<Long, ElconObject, ElconProjectPDBObjectVersion, ElconObject> {

    @Override
    default Class<ElconObject> getTargetEntityClass() {
        return ElconObject.class;
    }

}
