package com.siemens.spine.logic.dto.request;

import com.siemens.spine.logic.dto.ChangeGroupDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UndeleteComponentRequestDto {

    private Long projectId;

    private List<Long> componentIds;

    private int typeNum;

    private String description;

    private ChangeGroupDto changeGroup;

}
