package com.siemens.spine.logic.dto;

import com.siemens.spine.db.constant.UpdatingComponentFailedReason;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class ComponentStateChangedResponseDto {

    private int totalSkipped;

    private Map<UpdatingComponentFailedReason, Integer> skippedReason;

    private List<StateDto> newStates;

}
