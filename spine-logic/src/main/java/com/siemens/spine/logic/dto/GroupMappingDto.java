package com.siemens.spine.logic.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GroupMappingDto {

    private String drawing;

    private String deliveryBatch;

    private String buildingSection;

    private String calculationArea;

    private String eStopGroup;

    private String plcArea;

    private String sequenceGroup;

    private String lineName;

    private String screen;

    private String installationSection;

    private String projectView;

}
