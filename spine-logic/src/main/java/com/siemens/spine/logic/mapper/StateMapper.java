package com.siemens.spine.logic.mapper;

import com.siemens.spine.db.constant.ComponentState;
import com.siemens.spine.db.entity.StateEntity;
import com.siemens.spine.generated.toolchain.State;
import com.siemens.spine.logic.dto.SimplePairDto;
import com.siemens.spine.logic.dto.StateDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 26/12/2022
 */
@Mapper
public interface StateMapper {

    StateMapper INSTANCE = Mappers.getMapper(StateMapper.class);

    @Mapping(source = "state", target = "state", qualifiedByName = "convertStateFromDBToSOAP")
    @Mapping(source = "component.id", target = "uniqueID")
    @Mapping(source = "changeGroup.id", target = "changeGroupId")
    @Mapping(source = "username", target = "user")
    @Mapping(source = "changeGroup.description", target = "remark")
    @Mapping(source = "changeGroup.defaultReason", target = "reason")
    State stateEntityToState(StateEntity entity);

    @Mapping(target = "componentId", source = "component.id")
    @Mapping(target = "state", source = "state", qualifiedByName = "componentState")
    StateDto toStateDto(StateEntity stateEntity);

    /**
     * Implementation is generated automatically
     *
     * @param states
     * @return
     */
    List<StateDto> toStateDtos(List<StateEntity> states);

    /**
     * Implementation is generated automatically
     *
     * @param entities
     * @return
     */

    List<State> toStates(List<StateEntity> entities);

    @Mapping(target = "state", source = "state", qualifiedByName = "componentState")
    StateDto toStateWS(State state);

    @Named("componentState")
    default SimplePairDto map(int state) {
        ComponentState componentState = ComponentState.resolve(state);
        return componentState == null ?
                null :
                new SimplePairDto(String.valueOf(componentState.getV1Status()), componentState.getName());
    }

    @Named("convertStateFromDBToSOAP")
    default int getSOAPState(int stateFromDB) {
        ComponentState componentState = ComponentState.resolve(stateFromDB);
        if (componentState == null) {
            return ComponentState.PROPOSAL_PHASE_40_0.getV1Status();
        }

        return componentState.getV1Status();
    }

}
