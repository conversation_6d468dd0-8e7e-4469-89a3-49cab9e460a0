package com.siemens.spine.logic.mapper;

import com.siemens.spine.db.entity.ChangeGroupEntity;
import com.siemens.spine.generated.toolchain.ChangeGroup;
import com.siemens.spine.logic.dto.ChangeGroupDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ChangeGroupMapper {

    ChangeGroupMapper INSTANCE = Mappers.getMapper(ChangeGroupMapper.class);

    ChangeGroupEntity toChangeGroupEntity(ChangeGroupDto dto);

    @Mapping(target = "projectKey", source = "projectEntity.sapProjectKey")
    ChangeGroupDto toChangeGroupDto(ChangeGroupEntity stateEntity);

    List<ChangeGroupDto> toChangeGroupDtos(List<ChangeGroupEntity> entities);

    @Mapping(target = "defaultReason", source = "defaultReason")
    ChangeGroup toSoapChangeGroup(ChangeGroupDto dto);

    List<ChangeGroup> toSoapChangeGroups(List<ChangeGroupDto> dtos);

}
