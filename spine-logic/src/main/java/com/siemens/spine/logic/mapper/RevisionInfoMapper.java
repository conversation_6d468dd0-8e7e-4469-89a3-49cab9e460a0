package com.siemens.spine.logic.mapper;

import com.siemens.spine.db.entity.RevisionInfoEntity;
import com.siemens.spine.logic.model.RevisionInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValueMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2025/04/22
 */

@Mapper(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
public interface RevisionInfoMapper {

    RevisionInfoMapper INSTANCE = Mappers.getMapper(RevisionInfoMapper.class);

    @Named("toRevisionInfo")
    @Mapping(source = "id", target = "rev")
    RevisionInfo toRevisionInfo(RevisionInfoEntity entity);

}
