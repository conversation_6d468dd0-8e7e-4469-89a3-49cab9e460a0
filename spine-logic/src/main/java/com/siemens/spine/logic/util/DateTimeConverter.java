package com.siemens.spine.logic.util;

import lombok.extern.slf4j.Slf4j;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.GregorianCalendar;

/**
 * Utility class for date and time conversions between different formats
 */
@Slf4j
public class DateTimeConverter {

    private DateTimeConverter() {
        // Private constructor to prevent instantiation
    }

    /**
     * Converts a Timestamp to XMLGregorianCalendar
     *
     * @param timestamp The timestamp to convert
     * @return XMLGregorianCalendar representation of the timestamp
     * @throws DatatypeConfigurationException If there's an error creating the XMLGregorianCalendar
     */
    public static XMLGregorianCalendar toXMLGregorianCalendar(Timestamp timestamp) throws
            DatatypeConfigurationException {
        if (timestamp == null) {
            return null;
        }

        LocalDateTime localDateTime = timestamp.toLocalDateTime();

        XMLGregorianCalendar xmlGregorianCalendar = DatatypeFactory.newInstance().newXMLGregorianCalendar();
        xmlGregorianCalendar.setDay(localDateTime.getDayOfMonth());
        xmlGregorianCalendar.setMonth(localDateTime.getMonthValue());
        xmlGregorianCalendar.setYear(localDateTime.getYear());
        xmlGregorianCalendar.setHour(localDateTime.getHour());
        xmlGregorianCalendar.setMinute(localDateTime.getMinute());
        xmlGregorianCalendar.setSecond(localDateTime.getSecond());
        xmlGregorianCalendar.setFractionalSecond(
                new BigDecimal("0." + String.format("%09d", localDateTime.getNano())));
        return xmlGregorianCalendar;
    }

    /**
     * Converts a java.util.Date to XMLGregorianCalendar
     *
     * @param date The date to convert
     * @return XMLGregorianCalendar representation of the date
     */
    public static XMLGregorianCalendar toXMLGregorianCalendar(java.util.Date date) {
        if (date == null) {
            return null;
        }

        try {
            GregorianCalendar gregorianCalendar = new GregorianCalendar();
            gregorianCalendar.setTimeInMillis(date.getTime());
            return DatatypeFactory.newInstance().newXMLGregorianCalendar(gregorianCalendar);
        } catch (DatatypeConfigurationException e) {
            log.error("Could not create data type factory", e);
            return null;
        }
    }

}
