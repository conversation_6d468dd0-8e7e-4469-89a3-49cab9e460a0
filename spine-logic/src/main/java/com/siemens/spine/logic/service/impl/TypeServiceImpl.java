package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.constant.HierarchyTypeEnum;
import com.siemens.spine.db.constant.ProjectTypeClassificationEnum;
import com.siemens.spine.db.constant.TypeAggregatorEnum;
import com.siemens.spine.db.constraint.TypeAttribute;
import com.siemens.spine.db.constraint.TypeAttributeMapper;
import com.siemens.spine.db.entity.ProjectEntity;
import com.siemens.spine.db.entity.TypeEntity;
import com.siemens.spine.db.repository.ProjectRepository;
import com.siemens.spine.db.repository.TypeRepository;
import com.siemens.spine.db.repository.filter.TypeFilter;
import com.siemens.spine.generated.toolchain.Attribute;
import com.siemens.spine.generated.toolchain.Type;
import com.siemens.spine.logic.dto.TypeDto;
import com.siemens.spine.logic.dto.TypeMetaDataDto;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.mapper.TypeMapper;
import com.siemens.spine.logic.service.TypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Pham
 * @version 1.0
 * @since 26/12/2022
 */
@ApplicationScoped
@Transactional(value = Transactional.TxType.REQUIRED, rollbackOn = Exception.class)
@Slf4j
public class TypeServiceImpl implements TypeService {

    private final TypeRepository typeRepository;

    private final ProjectRepository projectRepository;

    @Inject
    public TypeServiceImpl(TypeRepository typeRepository, ProjectRepository projectRepository) {
        this.typeRepository = typeRepository;
        this.projectRepository = projectRepository;
    }

    @Override
    public Type getTypeById(String projectName, String typeId) {
        Optional<TypeEntity> entityOpt = typeRepository.findById(typeId);
        if (entityOpt.isEmpty()) {
            log.error("No type was found by id {}", typeId);
            return null;
        }

        TypeEntity typeEntity = entityOpt.get();
        boolean isGenericType = typeEntity.getProjectSpecific() == null || !typeEntity.getProjectSpecific();
        if (!isGenericType && (typeEntity.getProject() == null || !typeEntity.getProject().getProjectName()
                .equals(projectName))) {
            String projectSpecific = typeEntity.getProject() == null ? null : typeEntity.getProject().getProjectName();
            log.error("The type is specific for project {}, not allowed in project {}", projectSpecific, projectName);
            return null;
        }

        // Build the response
        Type ret = new Type();
        Field[] fields = typeEntity.getClass().getDeclaredFields();
        for (Field field : fields) {
            TypeAttributeMapper mapper = field.getDeclaredAnnotation(TypeAttributeMapper.class);
            if (mapper != null) {
                field.setAccessible(true);
                Object value = null;
                try {
                    value = field.get(typeEntity);
                } catch (IllegalAccessException e) {
                    log.error("Could not get value of field {}", field.getName());
                }

                if (value != null) {
                    TypeAttribute typeAttribute = mapper.value();

                    Attribute attribute = new Attribute();
                    attribute.setName(typeAttribute.getName());
                    attribute.setType(typeAttribute.getType());

                    if (typeAttribute == TypeAttribute.PROJECT_TYPE_CLASSIFICATION) {
                        ProjectTypeClassificationEnum projectTypeClassification = (ProjectTypeClassificationEnum) value;
                        attribute.setValue(projectTypeClassification.getValue());
                    } else {
                        attribute.setValue(String.valueOf(value));
                    }

                    ret.getAttributes().add(attribute);
                }
            }
        }

        return ret;
    }

    @Override
    public String getAllTypes(String projectName) {
        StringBuilder response = new StringBuilder();

        // generic types when project name is empty
        List<TypeEntity> genericTypes = typeRepository.findGenericType();
        for (TypeEntity type : genericTypes) {
            response.append(TypeToCsvBuilder.extractTypeCSV(type));
        }
        if (!StringUtils.isEmpty(projectName)) {
            TypeFilter filter = TypeFilter.builder().specificProjectName(projectName).outdated(false).build();

            // find types
            List<TypeEntity> typeEntities = typeRepository.filter(filter);
            for (TypeEntity typeEntity : typeEntities) {
                response.append(TypeToCsvBuilder.extractTypeCSV(typeEntity));
            }
        }

        return response.toString();
    }

    @Override
    public List<TypeDto> getAllTypeByProjectId(Long projectId) {
        if (projectId == null) {
            return TypeMapper.INSTANCE.toTypes(typeRepository.findGenericType());
        }

        TypeFilter filter = TypeFilter.builder().projectIds(List.of(projectId)).outdated(false).build();
        List<TypeEntity> projectSpecificTypes = typeRepository.filter(filter);
        List<TypeEntity> genericTypes = typeRepository.findGenericType();
        projectSpecificTypes.addAll(genericTypes);
        return TypeMapper.INSTANCE.toTypes(projectSpecificTypes);
    }

    @Override
    public TypeDto update(String typeId, TypeDto modifiedType) throws SpineException {
        Optional<TypeEntity> typeEntityOpt = typeRepository.findById(typeId);
        if (typeEntityOpt.isEmpty()) {
            throw new SpineException("No type was found by id " + typeId);
        }

        TypeEntity type = typeEntityOpt.get();

        // Set the new value of attributes for the type
        try {
            type.setMainType(modifiedType.getMainType());
            type.setSubType(modifiedType.getSubType());
            type.setSpecialType(modifiedType.getSpecialType());
            type.setTypeVersion(modifiedType.getTypeVersion());

            String typeAggregator = modifiedType.getTypeAggregator();
            if (!StringUtils.isEmpty(typeAggregator) && TypeAggregatorEnum.resolve(typeAggregator) == null) {
                throw new SpineException(String.format("The type aggregator '%s' is not valid", typeAggregator));
            }
            type.setTypeAggregator(modifiedType.getTypeAggregator());

            String projectTypeClassification = modifiedType.getProjectTypeClassification();
            if (!StringUtils.isEmpty(projectTypeClassification) && ProjectTypeClassificationEnum.resolve(
                    projectTypeClassification) == null) {
                throw new SpineException(
                        String.format("The project type classification '%s' is not valid", projectTypeClassification));
            }
            type.setProjectTypeClassification(ProjectTypeClassificationEnum.resolve(projectTypeClassification));

            type.setOriginalType(modifiedType.getOriginalType());
            type.setDescription(modifiedType.getDescription());
            type.setMechMaterialCostKpi(modifiedType.getMechMaterialCostKpi());
            type.setEngineeringMeHKpi(modifiedType.getEngineeringMeHKpi());
            type.setEngineeringHwHPki(modifiedType.getEngineeringHwHPki());
            type.setEngineeringPlcHKpi(modifiedType.getEngineeringPlcHKpi());
            type.setEngineeringItHKpi(modifiedType.getEngineeringItHKpi());
            type.setMps(modifiedType.getMps());
            type.setInstallationHKpi(modifiedType.getInstallationHKpi());
            type.setPlcCommissioningHKpi(modifiedType.getPlcCommissioningHKpi());
            type.setTypeVariant(modifiedType.getTypeVariant());
            type.setDefaultSlope(modifiedType.getDefaultSlope());
            type.setDefaultDriveCount(modifiedType.getDefaultDriveCount());
            type.setVersionNumber(modifiedType.getVersionNumber());
            type.setSupplier(modifiedType.getSupplier());
            type.setEngineeringScadaHKpi(modifiedType.getEngineeringScadaHKpi());
            type.setSapMaterialNumber(modifiedType.getSapMaterialNumber());
            type.setUrl1(modifiedType.getUrl1());
            type.setUrl2(modifiedType.getUrl2());
            type.setUrl3(modifiedType.getUrl3());
            type.setUrl4(modifiedType.getUrl4());
            type.setUrl5(modifiedType.getUrl5());
            type.setBeltMass(modifiedType.getBeltMass());
            type.setRequiredAcceleration(modifiedType.getRequiredAcceleration());
            type.setDrivePulleyDiameter(modifiedType.getDrivePulleyDiameter());
            type.setFrictionFactor(modifiedType.getFrictionFactor());
            type.setDriveShaftDiameter(modifiedType.getDriveShaftDiameter());
            type.setEfficiencyGearbox(modifiedType.getEfficiencyGearbox());
            type.setEngineeringMeOneOffCosts(modifiedType.getEngineeringMeOneOffCosts());
            type.setEngineeringHwOneOffCosts(modifiedType.getEngineeringHwOneOffCosts());
            type.setEngineeringPlcOneOffCosts(modifiedType.getEngineeringPlcOneOffCosts());
            type.setEngineeringItOneOffCosts(modifiedType.getEngineeringItOneOffCosts());
            type.setEngineeringScadaOneOffCosts(modifiedType.getEngineeringScadaOneOffCosts());
            type.setHierarchyType(modifiedType.getHierarchyType());
            type.setOutdated(modifiedType.getOutdated());

            TypeEntity updatedType = typeRepository.save(type);
            return TypeMapper.INSTANCE.toType(updatedType);
        } catch (Exception e) {
            throw new SpineException(e.getMessage());
        }
    }

    @Override
    public void deleteTypeByIds(List<String> ids) throws SpineException {
        if (ids == null || ids.isEmpty()) {
            throw new SpineException("Invalid input");
        }

        int deleted = typeRepository.deleteByIds(ids);
        if (deleted == 0) {
            throw new SpineException("Could not delete the selected type. Please reload the data then try again");
        }
    }

    @Override
    public void createTypes(String projectName, List<Type> typeAttributes) throws SpineException {
        Optional<ProjectEntity> project = projectRepository.findByName(projectName);
        if (project.isEmpty()) {
            throw new SpineException("Missing project");
        }
        List<TypeEntity> types = mappingAttribute(typeAttributes);
        for (TypeEntity type : types) {
            log.info("Saving project specific types {} to project {}", type.getTypeId(), projectName);
            validateType(type);
            type.setProject(project.get());
            type.setProjectSpecific(true);
            type.setCreationDate(new Timestamp(System.currentTimeMillis()));
            updateFromGenericType(type);
        }
        typeRepository.saveAll(types);
    }

    @Override
    public TypeMetaDataDto getTypeMetadata() {
        List<String> projectTypeClassification = Arrays.stream(ProjectTypeClassificationEnum.values())
                .map(ProjectTypeClassificationEnum::getValue).toList();

        List<String> typeAggregator = Arrays.stream(TypeAggregatorEnum.values()).map(TypeAggregatorEnum::getValue)
                .toList();

        return TypeMetaDataDto.builder().projectTypeClassification(projectTypeClassification)
                .typeAggregator(typeAggregator).build();
    }

    @Override
    public void create(TypeDto type) throws SpineException {
        if (StringUtils.isEmpty(type.getTypeId())) {
            throw new SpineException("The generic type requires id");
        }
        TypeEntity typeEntity = typeRepository.findGenericTypeById(type.getTypeId());
        if (typeEntity != null) {
            throw new SpineException("The generic type has already exists");
        }
        TypeEntity saved = new TypeEntity();
        try {
            saved.setTypeId(type.getTypeId());
            saved.setBeltMass(type.getBeltMass());
            saved.setDefaultDriveCount(type.getDefaultDriveCount());
            saved.setDefaultSlope(type.getDefaultSlope());
            saved.setDescription(type.getDescription());
            saved.setDrivePulleyDiameter(type.getDrivePulleyDiameter());
            saved.setDriveShaftDiameter(type.getDriveShaftDiameter());
            saved.setEfficiencyGearbox(type.getEfficiencyGearbox());
            saved.setEngineeringHwHPki(type.getEngineeringHwHPki());
            saved.setEngineeringHwOneOffCosts(type.getEngineeringHwOneOffCosts());
            saved.setEngineeringItHKpi(type.getEngineeringItHKpi());
            saved.setEngineeringItOneOffCosts(type.getEngineeringItOneOffCosts());
            saved.setEngineeringMeHKpi(type.getEngineeringMeHKpi());
            saved.setEngineeringMeOneOffCosts(type.getEngineeringMeOneOffCosts());
            saved.setEngineeringPlcHKpi(type.getEngineeringPlcHKpi());
            saved.setEngineeringPlcOneOffCosts(type.getEngineeringPlcOneOffCosts());
            saved.setEngineeringScadaHKpi(type.getEngineeringScadaHKpi());
            saved.setEngineeringScadaOneOffCosts(type.getEngineeringScadaOneOffCosts());
            saved.setFrictionFactor(type.getFrictionFactor());
            saved.setHierarchyType(type.getHierarchyType());
            saved.setInstallationHKpi(type.getInstallationHKpi());
            saved.setMainType(type.getMainType());
            saved.setMechMaterialCostKpi(type.getMechMaterialCostKpi());
            saved.setMps(type.getMps());
            saved.setOriginalType(type.getOriginalType());
            saved.setOutdated(type.getOutdated());
            saved.setPlcCommissioningHKpi(type.getPlcCommissioningHKpi());

            String projectTypeClassification = type.getProjectTypeClassification();
            if (!StringUtils.isEmpty(projectTypeClassification) && !ProjectTypeClassificationEnum.GENERIC.equals(
                    ProjectTypeClassificationEnum.resolve(projectTypeClassification))) {
                throw new SpineException(
                        String.format("The project type classification '%s' is not valid", projectTypeClassification));
            }
            saved.setProjectTypeClassification(ProjectTypeClassificationEnum.resolve(projectTypeClassification));

            saved.setRequiredAcceleration(type.getRequiredAcceleration());
            saved.setSapMaterialNumber(type.getSapMaterialNumber());
            saved.setSolutionId(type.getSolutionId());
            saved.setSpecialType(type.getSpecialType());
            saved.setSubType(type.getSubType());
            saved.setSupplier(type.getSupplier());

            String typeAggregator = type.getTypeAggregator();
            if (!StringUtils.isEmpty(typeAggregator) && TypeAggregatorEnum.resolve(typeAggregator) == null) {
                throw new SpineException(String.format("The type aggregator '%s' is not valid", typeAggregator));
            }
            saved.setTypeAggregator(type.getTypeAggregator());

            saved.setTypeVariant(type.getTypeVariant());
            saved.setTypeVersion(type.getTypeVersion());
            saved.setUrl1(type.getUrl1());
            saved.setUrl2(type.getUrl2());
            saved.setUrl3(type.getUrl3());
            saved.setUrl4(type.getUrl4());
            saved.setUrl5(type.getUrl5());
            saved.setVersionNumber(type.getVersionNumber());
            saved.setProjectSpecific(false);
            saved.setCreationDate(new Timestamp(new Date().getTime()));
            saved.setModificationDate(null);
            typeRepository.save(saved);
        } catch (Exception e) {
            throw new SpineException(e.getMessage());
        }
    }

    private void validateType(TypeEntity type) throws SpineException {
        if (TypeAggregatorEnum.resolve(type.getTypeAggregator()) == null) {
            throw new SpineException(
                    "SPINE 0027:{Field TypeAggregator is empty for type " + type.getTypeId() + ". Following values are permitted: 'mechanic', 'electric', 'steelwork', 'virtual'");
        }
        if (HierarchyTypeEnum.resolve(type.getHierarchyType()) == null) {
            throw new SpineException(
                    "SPINE 0052:{Wrong value of HierarchyType for type " + type.getTypeId() + ". Only " + Arrays.stream(
                                    HierarchyTypeEnum.values()).map(HierarchyTypeEnum::getValue)
                            .collect(Collectors.joining(", ")) + " are permitted.");
        }
    }

    private List<TypeEntity> mappingAttribute(List<Type> types) {
        List<TypeEntity> entities = new ArrayList<>();
        for (Type type : types) {
            Map<String, String> attributeMaps = new HashMap<>();
            for (Attribute attribute : type.getAttributes()) {
                attributeMaps.put(attribute.getName(), attribute.getValue());
            }
            TypeEntity entity = TypeMapper.INSTANCE.toType(attributeMaps);
            entities.add(entity);
        }

        return entities;
    }

    private void updateFromGenericType(TypeEntity type) throws SpineException {
        if (isUpdatedFromGeneric(type)) {
            TypeEntity genericType = typeRepository.findGenericTypeById(type.getOriginalType());
            if (genericType == null) {
                throw new SpineException("Missing type");
            }
            updateGenericType(genericType, type);
        }
    }

    private void setTypeAggregatorFromGenericForDerived(TypeEntity type, TypeEntity genericType) {
        if (ProjectTypeClassificationEnum.DERIVED.equals(type.getProjectTypeClassification())) {
            type.setTypeAggregator(genericType.getTypeAggregator());
        }
    }

    private boolean isUpdatedFromGeneric(TypeEntity type) {
        return type.getProjectTypeClassification() != null && type.getOriginalType() != null;
    }

    public void updateGenericType(TypeEntity source, TypeEntity target) {
        if (source == null) {
            return;
        }

        if (source.getMainType() != null && target.getMainType() == null) {
            target.setMainType(source.getMainType());
        }
        if (source.getSubType() != null && target.getSubType() == null) {
            target.setSubType(source.getSubType());
        }
        if (source.getSpecialType() != null && target.getSpecialType() == null) {
            target.setSpecialType(source.getSpecialType());
        }
        if (source.getTypeVersion() != null && target.getTypeVersion() == null) {
            target.setTypeVersion(source.getTypeVersion());
        }
        if (source.getDescription() != null && target.getDescription() == null) {
            target.setDescription(source.getDescription());
        }
        if (source.getMechMaterialCostKpi() != null && target.getMechMaterialCostKpi() == null) {
            target.setMechMaterialCostKpi(source.getMechMaterialCostKpi());
        }
        if (source.getEngineeringMeHKpi() != null && target.getEngineeringMeHKpi() == null) {
            target.setEngineeringMeHKpi(source.getEngineeringMeHKpi());
        }
        if (source.getEngineeringHwHPki() != null && target.getEngineeringHwHPki() == null) {
            target.setEngineeringHwHPki(source.getEngineeringHwHPki());
        }
        if (source.getEngineeringPlcHKpi() != null && target.getEngineeringPlcHKpi() == null) {
            target.setEngineeringPlcHKpi(source.getEngineeringPlcHKpi());
        }
        if (source.getEngineeringItHKpi() != null && target.getEngineeringItHKpi() == null) {
            target.setEngineeringItHKpi(source.getEngineeringItHKpi());
        }
        if (source.getUrl1() != null && target.getUrl1() == null) {
            target.setUrl1(source.getUrl1());
        }
        if (source.getUrl2() != null && target.getUrl2() == null) {
            target.setUrl2(source.getUrl2());
        }
        if (source.getUrl3() != null && target.getUrl3() == null) {
            target.setUrl3(source.getUrl3());
        }
        if (source.getUrl4() != null && target.getUrl4() == null) {
            target.setUrl4(source.getUrl4());
        }
        if (source.getUrl5() != null && target.getUrl5() == null) {
            target.setUrl5(source.getUrl5());
        }
        if (source.getTypeVariant() != null && target.getTypeVariant() == null) {
            target.setTypeVariant(source.getTypeVariant());
        }
        if (source.getDefaultSlope() != null && target.getDefaultSlope() == null) {
            target.setDefaultSlope(source.getDefaultSlope());
        }
        if (source.getDefaultDriveCount() != null && target.getDefaultDriveCount() == null) {
            target.setDefaultDriveCount(source.getDefaultDriveCount());
        }
        if (source.getVersionNumber() != null && target.getVersionNumber() == null) {
            target.setVersionNumber(source.getVersionNumber());
        }
        if (source.getSupplier() != null && target.getSupplier() == null) {
            target.setSupplier(source.getSupplier());
        }
        if (source.getEngineeringScadaHKpi() != null && target.getEngineeringScadaHKpi() == null) {
            target.setEngineeringScadaHKpi(source.getEngineeringScadaHKpi());
        }
        if (source.getSapMaterialNumber() != null && target.getSapMaterialNumber() == null) {
            target.setSapMaterialNumber(source.getSapMaterialNumber());
        }
        if (source.getMps() != null && target.getMps() == null) {
            target.setMps(source.getMps());
        }
        if (source.getInstallationHKpi() != null && target.getInstallationHKpi() == null) {
            target.setInstallationHKpi(source.getInstallationHKpi());
        }
        if (source.getPlcCommissioningHKpi() != null && target.getPlcCommissioningHKpi() == null) {
            target.setPlcCommissioningHKpi(source.getPlcCommissioningHKpi());
        }
        if (source.getBeltMass() != null && target.getBeltMass() == null) {
            target.setBeltMass(source.getBeltMass());
        }
        if (source.getRequiredAcceleration() != null && target.getRequiredAcceleration() == null) {
            target.setRequiredAcceleration(source.getRequiredAcceleration());
        }
        if (source.getDrivePulleyDiameter() != null && target.getDrivePulleyDiameter() == null) {
            target.setDrivePulleyDiameter(source.getDrivePulleyDiameter());
        }
        if (source.getFrictionFactor() != null && target.getFrictionFactor() == null) {
            target.setFrictionFactor(source.getFrictionFactor());
        }
        if (source.getDriveShaftDiameter() != null && target.getDriveShaftDiameter() == null) {
            target.setDriveShaftDiameter(source.getDriveShaftDiameter());
        }
        if (source.getEfficiencyGearbox() != null && target.getEfficiencyGearbox() == null) {
            target.setEfficiencyGearbox(source.getEfficiencyGearbox());
        }
        if (source.getTypeAggregator() != null && target.getTypeAggregator() == null) {
            target.setTypeAggregator(source.getTypeAggregator());
        }
        if (source.getOriginalType() != null && target.getOriginalType() == null) {
            target.setOriginalType(source.getOriginalType());
        }
        if (source.getProjectTypeClassification() != null && target.getProjectTypeClassification() == null) {
            target.setProjectTypeClassification(source.getProjectTypeClassification());
        }
        if (source.getEngineeringMeOneOffCosts() != null && target.getEngineeringMeOneOffCosts() == null) {
            target.setEngineeringMeOneOffCosts(source.getEngineeringMeOneOffCosts());
        }
        if (source.getEngineeringHwOneOffCosts() != null && target.getEngineeringHwOneOffCosts() == null) {
            target.setEngineeringHwOneOffCosts(source.getEngineeringHwOneOffCosts());
        }
        if (source.getEngineeringPlcOneOffCosts() != null && target.getEngineeringPlcOneOffCosts() == null) {
            target.setEngineeringPlcOneOffCosts(source.getEngineeringPlcOneOffCosts());
        }
        if (source.getEngineeringItOneOffCosts() != null && target.getEngineeringItOneOffCosts() == null) {
            target.setEngineeringItOneOffCosts(source.getEngineeringItOneOffCosts());
        }
        if (source.getEngineeringScadaOneOffCosts() != null && target.getEngineeringScadaOneOffCosts() == null) {
            target.setEngineeringScadaOneOffCosts(source.getEngineeringScadaOneOffCosts());
        }
        if (source.getHierarchyType() != null && target.getHierarchyType() == null) {
            target.setHierarchyType(source.getHierarchyType());
        }
        if (source.getOutdated() != null && target.getOutdated() == null) {
            target.setOutdated(source.getOutdated());
        }
        if (source.getSolutionId() != null && target.getSolutionId() == null) {
            target.setSolutionId(source.getSolutionId());
        }
        if (source.getProjectSpecific() != null && target.getProjectSpecific() == null) {
            target.setProjectSpecific(source.getProjectSpecific());
        }
        if (source.getModificationDate() != null && target.getModificationDate() == null) {
            target.setModificationDate(source.getModificationDate());
        }
        if (source.getCreationDate() != null && target.getCreationDate() == null) {
            target.setCreationDate(source.getCreationDate());
        }

        setTypeAggregatorFromGenericForDerived(target, source);
    }

}
