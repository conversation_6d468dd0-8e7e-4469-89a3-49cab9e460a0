package com.siemens.spine.logic.dto.request;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.Map;

@Setter
@Getter
public class DynamicQueryByNameDTO {

    @JsonAlias({ "templateName", "template_name" })
    @NotNull
    private String templateName;
    private int page = 1;
    private int size = 10;
    private Map<String, Object> parameters;

}