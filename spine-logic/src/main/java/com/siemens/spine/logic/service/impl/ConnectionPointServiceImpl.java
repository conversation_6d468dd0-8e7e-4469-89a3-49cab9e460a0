package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.ConnectionPointEntity;
import com.siemens.spine.db.entity.NeighborConnectionEntity;
import com.siemens.spine.db.repository.ComponentRepository;
import com.siemens.spine.db.repository.ConnectionPointRepository;
import com.siemens.spine.db.repository.NeighborConnectionRepository;
import com.siemens.spine.generated.toolchain.ComponentNeighborConnections;
import com.siemens.spine.generated.toolchain.NeighborConnection;
import com.siemens.spine.logic.dto.ConnectionPointDto;
import com.siemens.spine.logic.dto.NeighborConnectionDto;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.service.ConnectionPointService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@ApplicationScoped
@Slf4j
public class ConnectionPointServiceImpl implements ConnectionPointService {

    public static final String OVERWRITE_MODE = "overwrite";
    public static final String APPEND_MODE = "append";
    public static final String DELETE_MODE = "delete";

    private final ComponentRepository componentRepository;

    private final ConnectionPointRepository connectionPointRepository;

    private final NeighborConnectionRepository neighborConnectionRepository;

    @Inject
    public ConnectionPointServiceImpl(ComponentRepository componentRepository,
                                      ConnectionPointRepository connectionPointRepository,
                                      NeighborConnectionRepository neighborConnectionRepository) {
        this.componentRepository = componentRepository;
        this.connectionPointRepository = connectionPointRepository;
        this.neighborConnectionRepository = neighborConnectionRepository;
    }

    @Override
    public List<ConnectionPointDto> findComponentConnectionPoint(Long componentId) {
        List<ConnectionPointEntity> connectionPoints = connectionPointRepository.findByComponentId(componentId);
        List<NeighborConnectionEntity> neighborConnections = neighborConnectionRepository.findNeighborConnectionByComponentIds(
                List.of(componentId));
        Map<Long, NeighborConnectionEntity> neighborConnectionMaps = neighborConnections.stream()
                .collect(Collectors.toMap(nc -> nc.getOwner().getId(), Function.identity(), (nc1, nc2) -> nc1));
        List<ConnectionPointEntity> neighborConnectionPoints = connectionPointRepository.findAllByIds(
                neighborConnections.stream().map(nc -> nc.getNeighbor().getId()).toList());
        Map<Long, ConnectionPointEntity> neighborConnectionPointsMap = neighborConnectionPoints.stream()
                .collect(Collectors.toMap(ConnectionPointEntity::getId, Function.identity(), (cp1, cp2) -> cp1));
        List<ConnectionPointDto> connectionPointDtos = new ArrayList<>();
        for (ConnectionPointEntity connectionPoint : connectionPoints) {
            ConnectionPointDto connectionPointDto = new ConnectionPointDto();
            connectionPointDto.setId(connectionPoint.getId());
            connectionPointDto.setName(connectionPoint.getName());
            connectionPointDto.setType(connectionPoint.getType());
            connectionPointDto.setX(connectionPoint.getX());
            connectionPointDto.setY(connectionPoint.getY());
            connectionPointDto.setZ(connectionPoint.getZ());
            if (neighborConnectionMaps.containsKey(connectionPoint.getId())) {
                NeighborConnectionEntity neighborConnection = neighborConnectionMaps.get(connectionPoint.getId());
                NeighborConnectionDto neighborConnectionDto = new NeighborConnectionDto();
                neighborConnectionDto.setGap(neighborConnection.getGap());
                neighborConnectionDto.setAssignment(neighborConnection.getAssignment());
                neighborConnectionDto.setManual(neighborConnection.getManual());
                if (neighborConnection.getNeighbor() != null) {
                    ConnectionPointEntity neighbor = neighborConnectionPointsMap.get(neighborConnection.getNeighbor().getId());
                    neighborConnectionDto.setNeighborComponentId(neighbor.getComponent().getId());
                    neighborConnectionDto.setNeighborConnectionName(neighbor.getName());
                    neighborConnectionDto.setNeighborType(neighbor.getType());
                }
                connectionPointDto.setNeighbor(neighborConnectionDto);
            }
            connectionPointDtos.add(connectionPointDto);
        }
        return connectionPointDtos;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public void deleteComponentConnectionPoint(Long componentId) {
        Optional<ComponentEntity> componentEntity = componentRepository.findById(componentId);
        if (componentEntity.isPresent()) {
            List<ConnectionPointEntity> cpList = componentEntity.get().getConnectionPoints();
            deleteConnectionPoints(cpList);
        }
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public void deleteConnectionPoints(List<ConnectionPointEntity> connectionPoints) {
        if (connectionPoints == null || connectionPoints.isEmpty()) {
            return;
        }

        for (ConnectionPointEntity connectionPoint : connectionPoints) {
            // remove the all the neighbor connections
            neighborConnectionRepository.removeAllNeighborConnections(connectionPoint);
            connectionPointRepository.delete(connectionPoint);
        }
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public List<ComponentNeighborConnections> getNeighborConnection(String projectName, List<Long> componentIds) {
        List<ComponentEntity> components = componentRepository.findByProjectNameAndComponentIds(projectName,
                componentIds);
        if (CollectionUtils.isEmpty(components)) {
            return Collections.emptyList();
        }
        // TODO need refactor for performance
        List<ComponentNeighborConnections> listComponentNeighborConnections = new ArrayList<>();
        List<NeighborConnectionEntity> neighborConnectionEntities = neighborConnectionRepository.findNeighborConnectionByComponentIds(
                componentIds);
        List<Long> allNeighborIds = neighborConnectionEntities.stream()
                .map(nc -> nc.getNeighbor().getId())
                .toList();
        Map<Long, List<NeighborConnectionEntity>> neighborConnectionMaps = neighborConnectionEntities.stream()
                .collect(Collectors.groupingBy(nc -> nc.getOwner().getId()));
        Map<Long, ConnectionPointEntity> allNeighbors = connectionPointRepository.findAllByIds(allNeighborIds).stream()
                .collect(Collectors.toMap(ConnectionPointEntity::getId, Function.identity(), (cp1, cp2) -> cp1));
        for (ComponentEntity componentEntity : components) {
            List<NeighborConnectionEntity> neighborConnections = new ArrayList<>();
            List<ConnectionPointEntity> connectionPoints = componentEntity.getConnectionPoints();
            for (ConnectionPointEntity connectionPoint : connectionPoints) {
                if (neighborConnectionMaps.containsKey(connectionPoint.getId())) {
                    neighborConnections.addAll(neighborConnectionMaps.get(connectionPoint.getId()));
                }
            }
            Map<Long, ConnectionPointEntity> cpMap = connectionPoints.stream()
                    .collect(Collectors.toMap(ConnectionPointEntity::getId, Function.identity(), (cp1, cp2) -> cp1));
            List<NeighborConnection> neighborConnectionResponses = new ArrayList<>();
            for (NeighborConnectionEntity neighborConnectionEntity : neighborConnections) {
                NeighborConnection neighborConnection = new NeighborConnection();
                ConnectionPointEntity owner = cpMap.get(neighborConnectionEntity.getOwner().getId());
                ConnectionPointEntity neighbor = allNeighbors.get(neighborConnectionEntity.getNeighbor().getId());
                neighborConnection.setGap(neighborConnectionEntity.getGap().intValue());
                neighborConnection.setIsManual(neighborConnectionEntity.getManual());
                neighborConnection.setNeighborAssignment(neighborConnectionEntity.getAssignment());
                neighborConnection.setOwnUniqueId(owner.getComponent().getId().toString());
                neighborConnection.setOwnCP(owner.getName());
                if (neighbor != null) {
                    neighborConnection.setNeighborUniqueId(neighbor.getComponent().getId().toString());
                    neighborConnection.setNeighborCP(neighbor.getName());
                } else {
                    neighborConnection.setNeighborUniqueId(StringUtils.EMPTY);
                    neighborConnection.setNeighborCP("CP_");
                }
                neighborConnectionResponses.add(neighborConnection);
            }
            ComponentNeighborConnections componentNeighborConnections = new ComponentNeighborConnections();
            componentNeighborConnections.setUniqueId(componentEntity.getId().toString());
            componentNeighborConnections.getConnections().addAll(neighborConnectionResponses);
            listComponentNeighborConnections.add(componentNeighborConnections);
        }

        return listComponentNeighborConnections;
    }

    @Override
    public boolean setNeighborConnection(String projectName,
                                         List<ComponentNeighborConnections> componentNeighborConnections)
            throws SpineException {
        if (CollectionUtils.isEmpty(componentNeighborConnections)) {
            return true;
        }
        Map<String, List<ComponentNeighborConnections>> componentNeighborConnectionsMode = componentNeighborConnections.stream()
                .filter(componentNC -> StringUtils.isNotEmpty(componentNC.getMode()))
                .collect(Collectors.groupingBy(ComponentNeighborConnections::getMode));
        if (componentNeighborConnectionsMode.containsKey(OVERWRITE_MODE)) {
            List<NeighborConnection> neighborConnections = getNeighborConnections(componentNeighborConnectionsMode,
                    OVERWRITE_MODE);
            overwriteNeighborConnections(neighborConnections);
        } else if (componentNeighborConnectionsMode.containsKey(DELETE_MODE)) {
            List<NeighborConnection> neighborConnections = getNeighborConnections(componentNeighborConnectionsMode,
                    DELETE_MODE);
            deleteNeighborConnections(neighborConnections);
        } else {
            List<NeighborConnection> neighborConnections = getNeighborConnections(componentNeighborConnectionsMode,
                    APPEND_MODE);
            appendNeighborConnections(neighborConnections);
        }
        return true;
    }

    private List<NeighborConnection> getNeighborConnections(Map<String, List<ComponentNeighborConnections>> componentNeighborConnectionsMode,
                                                            String mode) {
        List<ComponentNeighborConnections> deletes = componentNeighborConnectionsMode.get(mode);
        List<NeighborConnection> neighborConnections = new ArrayList<>();
        for (ComponentNeighborConnections delete : deletes) {
            if (StringUtils.isEmpty(delete.getUniqueId())) {
                log.info("Component unique id is undefined");
                continue;
            }
            neighborConnections.addAll(delete.getConnections());
        }
        return neighborConnections;
    }

    private void deleteNeighborConnections(List<NeighborConnection> neighborConnections) {
        if (CollectionUtils.isEmpty(neighborConnections)) {
            return;
        }
        List<Pair<String, String>> neighbors = new ArrayList<>();
        for (NeighborConnection neighborConnection : neighborConnections) {
            neighbors.add(Pair.of(neighborConnection.getOwnUniqueId(), neighborConnection.getOwnCP()));
        }
        List<ConnectionPointEntity> ownerConnectionPoints = connectionPointRepository.findByComponentIdAndName(
                neighbors);
        List<Long> ownerConnectionPointIds = ownerConnectionPoints.stream().map(ConnectionPointEntity::getId)
                .toList();
        neighborConnectionRepository.deleteAll(ownerConnectionPointIds);
    }

    private void appendNeighborConnections(List<NeighborConnection> neighborConnections) throws SpineException {
        if (CollectionUtils.isEmpty(neighborConnections)) {
            return;
        }
        List<NeighborConnectionEntity> appendNeighbors = new ArrayList<>();
        List<Pair<String, String>> owners = new ArrayList<>();
        List<Pair<String, String>> neighbors = new ArrayList<>();
        for (NeighborConnection neighborConnection : neighborConnections) {
            owners.add(Pair.of(neighborConnection.getOwnUniqueId(), neighborConnection.getOwnCP()));
            if (StringUtils.isNotEmpty(neighborConnection.getNeighborUniqueId())) {
                neighbors.add(Pair.of(neighborConnection.getNeighborUniqueId(), neighborConnection.getNeighborCP()));
            }
        }
        List<ConnectionPointEntity> ownerCPs = connectionPointRepository.findByComponentIdAndName(owners);
        List<ConnectionPointEntity> neighborCPs = connectionPointRepository.findByComponentIdAndName(neighbors);
        Map<Pair<Long, String>, ConnectionPointEntity> ownerCPMap = ownerCPs.stream().collect(
                Collectors.toMap(ownerCP -> Pair.of(ownerCP.getComponent().getId(), ownerCP.getName()),
                        Function.identity(), (cp1, cp2) -> cp1));
        Map<Pair<Long, String>, ConnectionPointEntity> neighborCPMap = neighborCPs.stream().collect(
                Collectors.toMap(neighborCP -> Pair.of(neighborCP.getComponent().getId(), neighborCP.getName()),
                        Function.identity(), (cp1, cp2) -> cp1));
        for (NeighborConnection neighborConnection : neighborConnections) {
            String ownerUniqueId = neighborConnection.getOwnUniqueId();
            String neighborUniqueId = neighborConnection.getNeighborUniqueId();
            if (StringUtils.isEmpty(ownerUniqueId)) {
                log.warn(String.format("Owner unique id ('%s') could not be null/empty", ownerUniqueId));
                continue;
            }
            String ownerCPName = neighborConnection.getOwnCP();
            String neighborCPName = neighborConnection.getNeighborCP();
            //get connection point owner
            ConnectionPointEntity ownerCP = ownerCPMap.get(Pair.of(Long.valueOf(ownerUniqueId), ownerCPName));
            if (ownerCP == null) {
                String msg = String.format("Please creating a connection point with name %s for component %s",
                        neighborConnection.getOwnCP(), neighborConnection.getOwnUniqueId());
                throw new SpineException(msg);
            }

            //get connection point neighbor
            NeighborConnectionEntity neighborConnectionEntity = NeighborConnectionEntity.builder()
                    .gap(Double.valueOf(neighborConnection.getGap()))
                    .assignment(neighborConnection.getNeighborAssignment()).manual(neighborConnection.isIsManual())
                    .build();
            if (StringUtils.isNotEmpty(neighborUniqueId)) {
                ConnectionPointEntity neighborCP = neighborCPMap.get(
                        Pair.of(Long.valueOf(neighborUniqueId), neighborCPName));
                if (neighborCP != null) {
                    neighborConnectionEntity.setNeighbor(neighborCP);
                }
            }
            neighborConnectionEntity.setOwner(ownerCP);
            appendNeighbors.add(neighborConnectionEntity);
        }
        neighborConnectionRepository.updateAll(appendNeighbors);
    }

    private void overwriteNeighborConnections(List<NeighborConnection> neighborConnections) throws SpineException {
        deleteNeighborConnections(neighborConnections);
        appendNeighborConnections(neighborConnections);
    }

}
