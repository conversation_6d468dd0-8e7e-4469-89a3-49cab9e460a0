package com.siemens.spine.logic.security;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.siemens.spine.db.constant.RoleEnum;
import com.siemens.spine.db.entity.OperationToRoleEntity;
import com.siemens.spine.db.repository.GroupToProjectAndRoleRepository;
import com.siemens.spine.db.repository.OperationToRoleRepository;
import com.siemens.spine.logic.exception.ForbiddenException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <PERSON>am
 * @version 1.0
 * @since 8/3/2023
 */
@ApplicationScoped
@Slf4j
public class SecurityCheckImpl implements SecurityCheck {

    public static final String REALM_ADMIN_GROUP = "ADMIN";
    public static final String REALM_SPINE_ADMIN = "SPINE-admin";
    private final GroupToProjectAndRoleRepository groupToProjectAndRoleRepository;
    private final OperationToRoleRepository operationToRoleRepository;
    private final Multimap<String, String> rolesPermission = ArrayListMultimap.create();

    @Inject
    public SecurityCheckImpl(GroupToProjectAndRoleRepository groupToProjectAndRoleRepository,
                             OperationToRoleRepository operationToRoleRepository) {
        this.groupToProjectAndRoleRepository = groupToProjectAndRoleRepository;
        this.operationToRoleRepository = operationToRoleRepository;
    }

    @Override
    public void checkPermission(String subject, String permission) {
        fetchRolesAndPermissions();
        Authentication authentication = SecurityContextHolder.getAuthentication();
        if (isAdmin(authentication)) {
            return;
        }

        if (subject == null) {
            log.error("Target subject can not be null.");
            throw new ForbiddenException();
        }

        if (StringUtils.isEmpty(permission)) {
            log.error("At least one permission is required.");
            throw new ForbiddenException();
        }

        Set<String> userGroups = authentication.getGroups();
        try {
            List<String> roles = groupToProjectAndRoleRepository.findRoleByProjectAndUserGroup(subject,
                    List.copyOf(userGroups));
            boolean isAllowed = false;
            for (String role : roles) {
                if (rolesPermission.get(role).contains(permission)) {
                    isAllowed = true;
                    break;
                }
            }
            if (!isAllowed) {
                log.error("Permission denied.");
                throw new ForbiddenException();
            }
        } catch (Exception ex) {
            log.error(ex.getMessage());
            throw new ForbiddenException();
        }
    }

    @Override
    public void checkPermissions(String subject, String... permissions) {
        fetchRolesAndPermissions();
        for (String permission : permissions) {
            checkPermission(subject, permission);
        }
    }

    private boolean isAdmin(Authentication authentication) {
        if (authentication == null) {
            log.error("User was not authenticated");
            throw new ForbiddenException();
        }

        if (CollectionUtils.isEmpty(authentication.getGroups())) {
            log.error("No group was found from the authentication");
            throw new ForbiddenException();
        }

        Set<String> userGroups = authentication.getGroups();
        Set<String> roles = authentication.getRoles();
        // super admin user
        if (userGroups.contains(REALM_ADMIN_GROUP) && roles.contains(REALM_SPINE_ADMIN)) {
            return true;
        }
        // spine admin
        List<String> adminGroups = groupToProjectAndRoleRepository.findAllUserGroupsByRole(RoleEnum.ADMIN.name());
        for (String adminGroup : adminGroups) {
            if (userGroups.contains(adminGroup)) {
                return true;
            }
        }
        return false;
    }

    private void fetchRolesAndPermissions() {
        if (rolesPermission.isEmpty()) {
            List<OperationToRoleEntity> operationsToRoles = operationToRoleRepository.findAll();
            for (OperationToRoleEntity operationsToRole : operationsToRoles) {
                rolesPermission.put(operationsToRole.getRoleName(), operationsToRole.getOperation());
            }
        }
    }

}
