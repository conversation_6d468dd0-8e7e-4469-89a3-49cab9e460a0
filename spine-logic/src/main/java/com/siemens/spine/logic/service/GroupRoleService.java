package com.siemens.spine.logic.service;

import com.siemens.spine.logic.dto.GroupToProjectAndRoleDto;
import com.siemens.spine.logic.dto.request.GroupToProjectAndRoleRequestDto;
import com.siemens.spine.logic.exception.SpineException;

import java.util.List;

public interface GroupRoleService {

    List<GroupToProjectAndRoleDto> getGroupRoles(Long projectId) throws SpineException;

    GroupToProjectAndRoleDto createGroupRole(GroupToProjectAndRoleRequestDto groupToProjectAndRoleRequestDto)
            throws SpineException;

    GroupToProjectAndRoleDto updateGroupRole(Long id, GroupToProjectAndRoleRequestDto groupToProjectAndRoleRequestDto)
            throws SpineException;

    void deleteGroupRole(List<Long> ids) throws SpineException;

    List<String> getRoles();

}
