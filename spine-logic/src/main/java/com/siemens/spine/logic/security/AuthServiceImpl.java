package com.siemens.spine.logic.security;

import com.siemens.spine.logic.exception.ForbiddenException;
import com.siemens.spine.logic.exception.UnauthorizedException;
import io.helidon.common.Errors;
import io.helidon.security.jwt.Jwt;
import io.helidon.security.jwt.SignedJwt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.Set;

@ApplicationScoped
@Slf4j
public class AuthServiceImpl implements AuthService {

    public static final String DEFAULT_MESSAGE = "Access is denied due to invalid credentials";

    @Inject
    private SecurityConfig securityConfig;

    @Inject
    private KeycloakJwtAuthenticationConverter converter;

    @Override
    public void validateToken(String token) throws UnauthorizedException {
        if (!securityConfig.isEnable()) {
            return;
        }
        Jwt jwt = getJwt(token);

        String issuer = securityConfig.getIssuer();
        String audience = securityConfig.getAudience();
        Errors validationErrors = jwt.validate(issuer, audience);
        if (!validationErrors.isValid()) {
            throw new UnauthorizedException(validationErrors.toString());
        }

        // save the authentication into security holder
        Authentication authentication = converter.parseJwt(jwt);
        SecurityContextHolder.setAuthentication(authentication);
    }

    @Override
    public boolean checkRolesAllowed(String[] rolesAllowed) {
        if (!securityConfig.isEnable()) {
            return true;
        }
        if (rolesAllowed == null || rolesAllowed.length == 0) {
            return true;
        }

        Authentication authentication = SecurityContextHolder.getAuthentication();
        if (authentication == null) {
            log.error("User was not authenticated");
            throw new ForbiddenException();
        }

        if (authentication.getRoles() == null || authentication.getRoles().isEmpty()) {
            log.error("No roles was found from the authentication");
            throw new ForbiddenException();
        }

        Set<String> userRoles = authentication.getRoles();
        for (String role : rolesAllowed) {
            if (userRoles.contains(role)) {
                return true;
            }
        }

        log.error(String.format("Only user with roles '%s' is permitted", rolesAllowed));
        throw new ForbiddenException();
    }

    private Jwt getJwt(String token) throws UnauthorizedException {
        if (StringUtils.isEmpty(token)) {
            throw new UnauthorizedException("Token was not be null or empty");
        }

        SignedJwt signedJwt;
        try {
            signedJwt = SignedJwt.parseToken(token);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new UnauthorizedException();
        }

        return signedJwt.getJwt();
    }

}
