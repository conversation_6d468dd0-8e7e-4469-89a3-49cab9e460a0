package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.entity.RevisionInfoEntity;
import com.siemens.spine.logic.dto.request.GetObjectsVersionRequestDTO;
import com.siemens.spine.logic.mapper.RevisionInfoMapper;
import com.siemens.spine.logic.model.ElconObject;
import com.siemens.spine.logic.model.ElconProjectPDBObjectVersion;
import com.siemens.spine.logic.model.RevisionInfo;
import com.siemens.spine.logic.service.ElconProjectPDBObjectVersionService;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/05/27
 */

@ApplicationScoped
@Slf4j
public class ElconProjectPDBObjectVersionServiceImpl implements
        ElconProjectPDBObjectVersionService {

    @Inject
    private RevisionInfoMapper revisionInfoMapper;

    @Override
    public List<ElconObject> getObjectsInRevision(GetObjectsVersionRequestDTO requestDTO) {
        return List.of();
    }

    @Override
    public List<ElconObject> getObjectsInVersion(GetObjectsVersionRequestDTO requestDTO) {
        return List.of();
    }

    @Override
    public List<ElconProjectPDBObjectVersion> getAllVersions(Long targetId) {
        return List.of();
    }

    @Override
    public List<RevisionInfo> getAllRevisions(Long componentId) {
        return List.of();
    }

    @Override
    public void handleNewRevision(Long componentId, RevisionInfoEntity revisionInfo) {
        log.info("Handling new revision component {} with revision info {}", componentId, revisionInfo);
    }

    @Override
    public <DTO> ElconProjectPDBObjectVersion createVersion(DTO dto) {
        return null;
    }

}
