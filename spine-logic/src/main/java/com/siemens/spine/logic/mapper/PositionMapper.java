package com.siemens.spine.logic.mapper;

import com.siemens.spine.db.entity.PositionEntity;
import com.siemens.spine.generated.toolchain.Position;
import com.siemens.spine.logic.dto.PositionDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PositionMapper {

    PositionMapper INSTANCE = Mappers.getMapper(PositionMapper.class);

    PositionDto entityToDto(PositionEntity entity);

    PositionDto wsToDto(Position position);

    PositionEntity toEntity(PositionDto dto);

    List<PositionDto> toDtoList(List<PositionEntity> entities);

}
