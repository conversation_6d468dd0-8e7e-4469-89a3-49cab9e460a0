package com.siemens.spine.logic.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class DecompositionDto {

    private Long id;
    private Long componentId;
    private String changeDescription;
    private String derivedFrom;
    private String description;
    private String mpsPrefix;
    private String materialType;
    private String sapMaterialNumber;
    private Integer quantity;
    private String positionNumber;
    private String sourceInternal;
    private Boolean standardMaterial;
    private String unitInSAP;
    private String zOptions;
    private String source;

}
