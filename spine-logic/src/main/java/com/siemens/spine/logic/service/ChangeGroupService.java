package com.siemens.spine.logic.service;

import com.siemens.spine.db.repository.filter.ChangeGroupFilter;
import com.siemens.spine.db.repository.views.ComponentChangeGroupView;
import com.siemens.spine.logic.dto.ChangeGroupDto;
import com.siemens.spine.logic.exception.SpineException;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ChangeGroupService {

    /**
     * @param projectKey
     * @param request
     * @return
     * @throws SpineException
     */
    ChangeGroupDto create(String projectKey, ChangeGroupDto request) throws SpineException;

    /**
     * @param projectId
     * @param request
     * @return
     * @throws SpineException
     */
    ChangeGroupDto create(Long projectId, ChangeGroupDto request) throws SpineException;

    /**
     * Get list of change group by filter
     *
     * @param filter
     * @return
     */
    List<ChangeGroupDto> getChangeGroups(ChangeGroupFilter filter);

    List<ComponentChangeGroupView> getLatestChangeGroupByComponentIds(List<Long> ids);

}
