package com.siemens.spine.logic.mapper;

import com.siemens.spine.db.entity.DecompositionEntity;
import com.siemens.spine.generated.toolchain.Decomposition;
import com.siemens.spine.generated.toolchain.DecompositionList;
import com.siemens.spine.logic.dto.DecompositionDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 22/12/2022
 */
@Mapper
public interface DecompositionMapper {

    DecompositionMapper INSTANCE = Mappers.getMapper(DecompositionMapper.class);

    @DecompositionListMapper
    static List<DecompositionList> decompositionListMapper(
            List<DecompositionEntity> decompositions) {
        List<DecompositionList> decompositionLists = new ArrayList<>();
        for (DecompositionEntity decompositionEntity : decompositions) {
            DecompositionList decompositionList = new DecompositionList();
            Decomposition decomposition = DecompositionMapper.INSTANCE.toDecomposition(
                    decompositionEntity);
            decompositionList.getDecompositions().add(decomposition);
            decompositionList.setSource(decompositionEntity.getSource());
            // TODO set mode
            decompositionList.setMode(null);
            decompositionLists.add(decompositionList);
        }
        return decompositionLists;
    }

    @Mapping(target = "positionNumber", source = "positionNumber")
    @Mapping(target = "MPSPrefix", source = "mpsPrefix")
    Decomposition toDecomposition(DecompositionEntity decompositionEntity);

    @Mapping(target = "componentId", source = "component.id")
    @Mapping(target = "zOptions", source = "ZOptions")
    DecompositionDto toDto(DecompositionEntity entity);

    @Mapping(target = "mpsPrefix", source = "MPSPrefix", qualifiedByName = "mpsPrefix")
    @Mapping(target = "zOptions", source = "ZOptions")
    DecompositionDto wsToDto(Decomposition decomposition);

    @Mapping(target = "mpsPrefix", source = "decomposition.MPSPrefix", qualifiedByName = "mpsPrefix")
    @Mapping(target = "zOptions", source = "decomposition.ZOptions")
    DecompositionDto wsToDto(Decomposition decomposition, String source);

    List<DecompositionDto> toDtoList(List<DecompositionEntity> entity);

    @Mapping(target = "component", ignore = true)
    @Mapping(target = "zOptions", source = "ZOptions")
    DecompositionEntity toEntity(DecompositionDto dto);

    default List<DecompositionDto> wsToDtos(List<DecompositionList> decompositions) {
        List<DecompositionDto> ret = new ArrayList<>();
        for (DecompositionList decompositionList : decompositions) {
            if (decompositionList.getDecompositions() == null) {
                continue;
            }

            for (Decomposition decomposition : decompositionList.getDecompositions()) {
                DecompositionDto dto = wsToDto(decomposition);
                dto.setSource(decompositionList.getSource());

                ret.add(dto);
            }
        }

        return ret;
    }

    @Named("mpsPrefix")
    default String mpsPrefix(String mpsPrefix) {
        if (mpsPrefix.startsWith("a")) {
            return mpsPrefix.substring(1);
        } else {
            return mpsPrefix;
        }
    }

}

