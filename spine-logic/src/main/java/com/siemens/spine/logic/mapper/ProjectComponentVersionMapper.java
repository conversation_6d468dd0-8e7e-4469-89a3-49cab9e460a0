package com.siemens.spine.logic.mapper;

import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.ConnectionPointEntity;
import com.siemens.spine.db.entity.DecompositionEntity;
import com.siemens.spine.db.entity.ProjectComponentVersionEntity;
import com.siemens.spine.logic.model.Component;
import com.siemens.spine.logic.model.ConnectionPoint;
import com.siemens.spine.logic.model.Decomposition;
import com.siemens.spine.logic.model.ProjectComponentVersion;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValueMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/04/22
 */

@Mapper(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
public interface ProjectComponentVersionMapper {

    ProjectComponentVersionMapper INSTANCE = Mappers.getMapper(ProjectComponentVersionMapper.class);

    @Named("toComponentVersion")
        //    @Mapping(target = "componentRefs", ignore = true)
    ProjectComponentVersion toComponentVersion(ProjectComponentVersionEntity entity);

    @Mapping(source = "decompositions", target = "decompositions", qualifiedByName = "toDecompositions")
    @Mapping(source = "connectionPoints", target = "connectionPoints", qualifiedByName = "toConnectionPoints")
    Component toComponentVersion(ComponentEntity entity);

    @Named("toDecompositions")
    @IterableMapping(qualifiedByName = "toDecomposition")
    List<Decomposition> toDecompositions(List<DecompositionEntity> entities);

    @Named("toDecomposition")
    Decomposition toDecomposition(DecompositionEntity entity);

    @Named("toConnectionPoints")
    @IterableMapping(qualifiedByName = "toConnectionPoint")
    List<ConnectionPoint> toConnectionPoints(List<ConnectionPointEntity> entities);

    @Named("toConnectionPoint")
    ConnectionPoint toConnectionPoint(ConnectionPointEntity entity);

}
