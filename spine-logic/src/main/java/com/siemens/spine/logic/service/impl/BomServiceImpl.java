package com.siemens.spine.logic.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siemens.spine.db.constant.ComponentState;
import com.siemens.spine.db.constant.GroupTypeEnum;
import com.siemens.spine.db.constant.ModificationStateEnum;
import com.siemens.spine.db.entity.ChangeGroupEntity;
import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.ComponentStatusEntity;
import com.siemens.spine.db.entity.DecompositionEntity;
import com.siemens.spine.db.entity.GroupEntity;
import com.siemens.spine.db.entity.StateEntity;
import com.siemens.spine.db.entity.TypeEntity;
import com.siemens.spine.db.repository.ComponentRepository;
import com.siemens.spine.db.repository.StateRepository;
import com.siemens.spine.db.repository.TypeRepository;
import com.siemens.spine.db.repository.filter.ComponentStateFilter;
import com.siemens.spine.generated.toolchain.BoMComponentData;
import com.siemens.spine.generated.toolchain.BoMGetDataResult;
import com.siemens.spine.generated.toolchain.Decomposition;
import com.siemens.spine.generated.toolchain.DecompositionList;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.mapper.DecompositionMapper;
import com.siemens.spine.logic.service.BomService;
import com.siemens.spine.logic.util.ComponentUtils;
import com.siemens.spine.logic.util.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import javax.xml.datatype.XMLGregorianCalendar;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR> Pham
 * @version 1.0
 * @since 26/12/2022
 */
@ApplicationScoped
@Transactional(value = Transactional.TxType.REQUIRED, rollbackOn = Exception.class)
@Slf4j
public class BomServiceImpl implements BomService {

    private static final String CONVEYOR_PREFIX = "SP%d";

    @Inject
    private ComponentRepository componentRepository;

    @Inject
    private CounterServiceImpl counterService;

    @Inject
    private StateRepository stateRepository;

    @Inject
    private TypeRepository typeRepository;

    @Override
    public List<Long> getBomListUniqueIds(String projectName, List<String> groupDataList, boolean getFullData)
            throws SpineException {
        Set<Long> componentIds = new HashSet<>();

        ObjectMapper mapper = new ObjectMapper();
        TypeReference<HashMap<String, String>> typeRef = new TypeReference<>() {
        };
        for (String group : groupDataList) {
            HashMap<String, String> groupData;
            try {
                groupData = mapper.readValue(group, typeRef);
            } catch (JsonProcessingException e) {
                throw new SpineException("Could not parse the group info from String: " + group);
            }

            String groupName = groupData.get("Name");
            String groupTypeName = groupData.get("Type");
            try {
                GroupTypeEnum groupType = GroupTypeEnum.valueOf(groupTypeName);
                componentIds.addAll(
                        componentRepository.findIdsByProjectNameAndGroupTypeAndGroupIds(projectName, groupType,
                                List.of(groupName))
                );
            } catch (IllegalArgumentException e) {
                throw new SpineException("Unknown group type: " + groupTypeName);
            }
        }

        return new ArrayList<>(componentIds);
    }

    @Override
    public BoMGetDataResult getBomListDataByUniqueIds(String projectName, List<Long> axids) throws SpineException {
        XMLGregorianCalendar now = DateTimeUtils.toXMLGregorianCalendar(LocalDateTime.now());
        List<ComponentEntity> components = componentRepository.findByProjectNameAndComponentIds(projectName, axids);
        BoMGetDataResult result = new BoMGetDataResult();
        result.setProjectName(projectName);
        Long messageId = counterService.retrieveNextValueFromCounter(CounterServiceImpl.COUNTER_BOM);
        if (messageId != null) {
            result.setMessageID(messageId);
        }

        if (CollectionUtils.isEmpty(components)) {
            result.setProjectName(projectName);
            return result;
        }
        List<BoMComponentData> componentDataList = new ArrayList<>();
        for (ComponentEntity component : components) {
            // TODO confirm with sql
            if (excludeComponentsNotSendToSAP(component)) {
                continue;
            }
            if (CollectionUtils.isNotEmpty(component.getDecompositions())) {
                BoMComponentData boMComponentData = buildBomComponentData(component);
                if (boMComponentData != null) {
                    boMComponentData.setLastInWorkTimestamp(now);
                    componentDataList.add(boMComponentData);
                }
            }
        }
        result.getComponents().addAll(componentDataList);
        return result;
    }

    private boolean excludeComponentsNotSendToSAP(ComponentEntity component) {
        return component.getCurrentState() == ComponentState.EXECUTION_PHASE_200_0.getV1Status();
    }

    // conveyor data
    private BoMComponentData buildBomComponentData(ComponentEntity component) {
        BoMComponentData boMComponentData = new BoMComponentData();
        boMComponentData.setUniqueID(String.format(CONVEYOR_PREFIX, component.getId()));

        Map<GroupTypeEnum, String> componentGroups = ComponentUtils.getComponentGroupMapping(component);
        boMComponentData.setBuildSection(componentGroups.get(GroupTypeEnum.DeliveryBatch));

        if (StringUtils.isNotEmpty(component.getTypeId())) {
            String typeId = component.getTypeId();
            Optional<TypeEntity> optType = typeRepository.findById(typeId);
            if (optType.isPresent()) {
                TypeEntity type = optType.get();
                boMComponentData.setMPS(type.getMps());
                boMComponentData.setMainType(type.getMainType());
                boMComponentData.setSubType(type.getSubType());
                boMComponentData.setTypeID(type.getTypeId());
            }
        }
        ComponentStatusEntity status = component.getStatus();
        if (status != null) {
            boMComponentData.setSapID(status.getSapID());
            if (status.getBomState() != null) {
                boMComponentData.setStatus(ModificationStateEnum.resolve(status.getBomState()) != null ?
                        ModificationStateEnum.resolve(status.getBomState()).getValue() :
                        null);
            } else {
                boMComponentData.setStatus(ModificationStateEnum.NEW.getValue());
            }
        }
        ComponentStateFilter componentStateFilter = ComponentStateFilter.builder()
                .componentIds(List.of(component.getId()))
                .orderBy("ASC")
                .build();
        List<StateEntity> states = stateRepository.filter(componentStateFilter);

        if (CollectionUtils.isNotEmpty(states)) {
            ChangeGroupEntity previousChangeGroup = null;
            Timestamp latest = new Timestamp(Long.MIN_VALUE);
            StateEntity latestState = null;
            for (StateEntity state : states) {
                if (state.getStatusDate().getTime() >= latest.getTime()) {
                    latest = state.getStatusDate();
                    latestState = state;
                    if (state.getChangeGroup() != null) {
                        previousChangeGroup = state.getChangeGroup();
                    }
                }
            }

            if (latestState != null
                    && latestState.getState() != null
                    && ComponentState.DELETED.equals(ComponentState.resolve(latestState.getState()))) {
                boMComponentData.setStatus(ModificationStateEnum.DELETED.getValue());
            }

            if (previousChangeGroup != null) {
                boMComponentData.setReasonOfChange(
                        String.format("%s %s", previousChangeGroup.getName(), previousChangeGroup.getDescription()));
            }
            if (latestState != null) {
                if (latestState.getState() != null) {
                    boMComponentData.setSpineStatus(latestState.getState().toString());
                }
                boMComponentData.setRemark(latestState.getRemark());
            }
        }
        if (CollectionUtils.isNotEmpty(component.getDecompositions())) {
            DecompositionList decompositionList = new DecompositionList();
            for (DecompositionEntity decompositionEntity : component.getDecompositions()) {
                Decomposition decomposition = DecompositionMapper.INSTANCE.toDecomposition(decompositionEntity);
                decompositionList.getDecompositions().add(decomposition);
                decompositionList.setSource(decompositionEntity.getSource());
            }
            boMComponentData.getDecompositionLists().add(decompositionList);
        }
        Optional<GroupEntity> deliverBatchGroup = component.getGroups().stream()
                .filter(g -> GroupTypeEnum.DeliveryBatch.equals(g.getGrouptype())).findFirst();
        if (deliverBatchGroup.isPresent()) {
            GroupEntity deb = deliverBatchGroup.get();
            if (deb.getDeliverydate() != null) {
                boMComponentData.setDeliveryDate(
                        DateTimeUtils.toXMLGregorianCalendar(deb.getDeliverydate().toLocalDateTime()));
            }
        }
        boMComponentData.setLength(component.getLengthTotal());

        return boMComponentData;
    }

}
