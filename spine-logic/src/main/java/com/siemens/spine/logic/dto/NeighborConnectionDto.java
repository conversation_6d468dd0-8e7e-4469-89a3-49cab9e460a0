package com.siemens.spine.logic.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NeighborConnectionDto implements Serializable {

    private Long neighborComponentId;

    private String neighborConnectionName;

    private String neighborType;

    private String assignment;

    private Boolean manual;

    private Double gap;

}
