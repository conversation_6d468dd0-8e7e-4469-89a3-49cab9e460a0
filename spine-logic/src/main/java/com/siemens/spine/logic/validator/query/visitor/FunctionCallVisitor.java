package com.siemens.spine.logic.validator.query.visitor;

import net.sf.jsqlparser.expression.ExpressionVisitorAdapter;
import net.sf.jsqlparser.expression.Function;

import java.util.HashSet;
import java.util.Set;

public class FunctionCallVisitor extends ExpressionVisitorAdapter<Void> {

    private static final Set<String> DANGEROUS_FUNCTIONS = Set.of(
            "LOAD_FILE", "INTO_OUTFILE", "INTO_DUMPFILE",
            "SYSTEM", "CMD", "EVAL", "EXEC", "EXECUTE",
            "SP_EXECUTESQL", "XP_CMDSHELL", "SLEEP",
            "BENCHMARK", "WAITFOR", "DELAY"
    );

    private final Set<String> foundDangerousFunctions = new HashSet<>();

    @Override
    public void visit(Function function) {
        String functionName = function.getName().toUpperCase();

        if (DANGEROUS_FUNCTIONS.contains(functionName)) {
            foundDangerousFunctions.add(functionName);
        }

        // Check function parameters
        if (function.getParameters() != null && function.getParameters().getExpressions() != null) {
            function.getParameters().getExpressions().forEach(expr -> expr.accept(this));
        }
    }

    public boolean hasDangerousFunctions() {
        return !foundDangerousFunctions.isEmpty();
    }

    public Set<String> getDangerousFunctions() {
        return foundDangerousFunctions;
    }

}
