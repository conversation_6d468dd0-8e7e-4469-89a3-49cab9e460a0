package com.siemens.spine.logic.mapper;

import com.siemens.spine.db.entity.GroupToProjectAndRoleEntity;
import com.siemens.spine.logic.dto.GroupToProjectAndRoleDto;
import com.siemens.spine.logic.dto.request.GroupToProjectAndRoleRequestDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> Pham
 * @version 1.0
 * @since 10/01/2023
 */
@Mapper
public interface GroupRoleMapper {

    GroupRoleMapper INSTANCE = Mappers.getMapper(GroupRoleMapper.class);

    @Mapping(target = "projectId", source = "project.projectID")
    @Mapping(target = "projectName", source = "project.projectName")
    GroupToProjectAndRoleDto toGroupToProjectAndRole(GroupToProjectAndRoleEntity groupToProjectAndRoleEntity);

    List<GroupToProjectAndRoleDto> toGroupToProjectAndRoles(List<GroupToProjectAndRoleEntity> groupToProjectAndRoleEntities);

    GroupToProjectAndRoleEntity toEntity(GroupToProjectAndRoleRequestDto groupToProjectAndRoleRequestDto);

}
