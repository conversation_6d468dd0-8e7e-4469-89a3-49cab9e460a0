package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.constant.ComponentOperation;
import com.siemens.spine.db.constant.GroupTypeEnum;
import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.ComponentStatusEntity;
import com.siemens.spine.db.repository.ComponentRepository;
import com.siemens.spine.db.repository.ComponentStatusRepository;
import com.siemens.spine.db.repository.filter.ComponentFilter;
import com.siemens.spine.generated.toolchain.MarkAsDoneData;
import com.siemens.spine.logic.dto.ComponentStatusDto;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.mapper.ComponentStatusMapper;
import com.siemens.spine.logic.service.ComponentStatusService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@ApplicationScoped
@Slf4j
public class ComponentStatusServiceImpl implements ComponentStatusService {

    private static final int WORK_IN_PROGRESS_STATE = 20;

    private final ComponentRepository componentRepository;

    private final ComponentStatusRepository componentStatusRepository;

    @Inject
    public ComponentStatusServiceImpl(ComponentRepository componentRepository,
                                      ComponentStatusRepository componentStatusRepository) {
        this.componentRepository = componentRepository;
        this.componentStatusRepository = componentStatusRepository;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public List<ComponentStatusDto> getComponentStatuses(String projectName, List<Long> uniqueIds) {
        List<ComponentStatusEntity> statusEntities = componentStatusRepository.find(projectName, uniqueIds);
        return ComponentStatusMapper.INSTANCE.toComponentStatusDtoList(statusEntities);
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public void setSimulationStateForDrawing(String projectName, String simulationState, String drawing)
            throws SpineException {
        Integer simulationStateValue = Integer.valueOf(simulationState.trim());
        List<ComponentEntity> components = findComponentByDrawing(projectName, drawing);

        // update the information related to the simulation
        Date currentDate = new Date();
        List<Long> updateComponentIds = new ArrayList<>();
        for (ComponentEntity componentEntity : components) {
            ComponentStatusEntity componentStatus = componentEntity.getStatus();
            if (componentStatus == null) {
                // init a new entity if it was not created
                componentStatus = ComponentStatusEntity.builder()
                        .component(componentEntity)
                        .build();
                componentStatus.setSimulationState(simulationStateValue);
                componentStatus.setSimulationStateDate(currentDate);
                componentStatus.setSimulationComponentLastModDate(currentDate);
                componentStatusRepository.save(componentStatus);
            } else {
                updateComponentIds.add(componentEntity.getId());
            }
        }
        if (CollectionUtils.isNotEmpty(updateComponentIds)) {
            componentStatusRepository.updateStateByOperation(ComponentOperation.SIMULATION.name(), updateComponentIds,
                    simulationStateValue, currentDate);
        }

        log.info(String.format("Completed set simulation state to %s for drawing %s", simulationState, drawing));
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public void setCalculationStateForDrawing(String projectName, String calculationState, String drawing)
            throws SpineException {
        Integer calculationStateValue = Integer.valueOf(calculationState.trim());
        List<ComponentEntity> components = findComponentByDrawing(projectName, drawing);

        // update the information related to the simulation
        Date currentDate = new Date();
        List<Long> updateComponentIds = new ArrayList<>();
        for (ComponentEntity componentEntity : components) {
            ComponentStatusEntity componentStatus = componentEntity.getStatus();
            if (componentStatus == null) {
                // init a new entity if it was not created
                componentStatus = ComponentStatusEntity.builder()
                        .component(componentEntity)
                        .build();
                // update the information related to the simulation
                componentStatus.setCalculationState(calculationStateValue);
                componentStatus.setCalculationStateDate(currentDate);
                componentStatusRepository.save(componentStatus);
            } else {
                updateComponentIds.add(componentEntity.getId());
            }
        }
        if (CollectionUtils.isNotEmpty(updateComponentIds)) {
            componentStatusRepository.updateStateByOperation(ComponentOperation.CALCULATION.name(), updateComponentIds,
                    calculationStateValue, currentDate);
        }

        log.info(String.format("Completed set calculation state to %s for drawing %s", calculationState, drawing));
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public boolean setBOMState(String projectName, String bomState, List<Long> componentIds) throws SpineException {
        setComponentsStatusInList(projectName, ComponentOperation.BOM.name(), bomState, componentIds);
        return true;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public void setComponentsStatusInList(String projectName,
                                          String operationName,
                                          String state,
                                          List<Long> componentIds) throws SpineException {
        // validate the input
        if (StringUtils.isEmpty(projectName)) {
            throw new SpineException("Project name could not be null or empty");
        }

        ComponentOperation componentOperation;
        try {
            componentOperation = ComponentOperation.valueOf(operationName);
        } catch (Exception e) {
            throw new SpineException("Unknown operation " + operationName);
        }

        if (componentIds == null || componentIds.isEmpty()) {
            log.info("No component from the request");
            return;
        }

        Integer stateValue = Integer.parseInt(state);
        Date modifiedDate = new Date();
        List<ComponentStatusEntity> componentStatusEntities = componentStatusRepository.find(projectName, componentIds);
        Map<Long, ComponentStatusEntity> componentStatusEntityMap = componentStatusEntities.stream()
                .collect(Collectors.toMap(componentStatusEntity -> componentStatusEntity.getComponent().getId(),
                        componentStatusEntity -> componentStatusEntity,
                        (existing, replacement) -> existing));
        List<Long> updateComponentIds = new ArrayList<>();
        List<ComponentStatusEntity> addedComponentStatus = new ArrayList<>();
        for (Long componentId : componentIds) {
            if (!componentStatusEntityMap.containsKey(componentId)) {
                Optional<ComponentEntity> component = componentRepository.findById(componentId);
                if (component.isPresent()) {
                    ComponentStatusEntity componentStatus = component.get().getStatus();
                    if (componentStatus == null) {
                        componentStatus = ComponentStatusEntity.builder()
                                .component(component.get())
                                .build();
                    }
                    switch (operationName) {
                        case "BOM":
                            componentStatus.setBomState(stateValue);
                            componentStatus.setBomStateDate(modifiedDate);
                            break;
                        case "EMULATION":
                            componentStatus.setEmulationState(stateValue);
                            componentStatus.setEmulationStateDate(modifiedDate);
                            break;
                        case "IT":
                            componentStatus.setItState(stateValue);
                            componentStatus.setItStateDate(modifiedDate);
                            break;
                        case "SIMULATION":
                            componentStatus.setSimulationState(stateValue);
                            componentStatus.setSimulationStateDate(modifiedDate);
                            componentStatus.setSimulationComponentLastModDate(modifiedDate);
                            break;
                        case "CALCULATION":
                            componentStatus.setCalculationState(stateValue);
                            componentStatus.setCalculationStateDate(modifiedDate);
                            break;
                        case "OBJECTXML":
                            componentStatus.setObjectXmlExportState(stateValue);
                            componentStatus.setObjectXmlExportStateDate(modifiedDate);
                            break;
                        case "ELECTRIC":
                            componentStatus.setElectricSynchronizationState(stateValue);
                            componentStatus.setElectricSynchronizationDate(modifiedDate);
                            break;
                        default:
                            break;
                    }
                    addedComponentStatus.add(componentStatus);
                }
            } else {
                updateComponentIds.add(componentId);
            }
        }

        // update state
        componentStatusRepository.updateStateByOperation(componentOperation.name(), updateComponentIds, stateValue,
                modifiedDate);
        if (CollectionUtils.isNotEmpty(addedComponentStatus)) {
            componentStatusRepository.saveAll(addedComponentStatus);
        }
        log.info(String.format("Updated '%s state' of the %d components to %s", operationName, componentIds.size(),
                state));
    }

    @Override
    public void markAsDone(String projectName, List<MarkAsDoneData> data) {
        // validate the input
        if (data == null || data.isEmpty()) {
            log.warn("MarkAsDoneData is empty");
            return;
        }

        log.info("Start mark as done for {} items", data.size());
        Date currentDate = new Date();
        for (MarkAsDoneData markAsDone : data) {
            String componentId = markAsDone.getUniqueID();
            if (StringUtils.isEmpty(componentId)) {
                continue;
            }

            Optional<ComponentEntity> component = componentRepository.findById(Long.parseLong(componentId));
            if (component.isEmpty()) {
                log.error(String.format("No component was found by id %s", componentId));
                continue;
            }
            ComponentStatusEntity componentStatus = component.get().getStatus();
            Date date = markAsDone.getLastInWorkTimestamp() == null ?
                    currentDate : markAsDone.getLastInWorkTimestamp().toGregorianCalendar().getTime();
            if (componentStatus != null) {
                componentStatusRepository.updateMarkAsDoneBom(component.get().getId(), markAsDone.getSapID(),
                        WORK_IN_PROGRESS_STATE, date);
            }
        }
        log.info("End mark as done for {} items", data.size());
    }

    @Override
    public List<ComponentStatusDto> getChangeComponentStatuses(String projectName, List<Long> uniqueIds) {
        return ComponentStatusMapper.INSTANCE.toComponentStatusDtoList(
                componentStatusRepository.findChangeComponentStatus(uniqueIds));
    }

    private List<ComponentEntity> findComponentByDrawing(String projectName, String drawing) throws SpineException {
        // validate the input
        if (StringUtils.isEmpty(projectName)) {
            throw new SpineException("Project name could not be null or empty");
        }

        if (StringUtils.isEmpty(drawing)) {
            throw new SpineException("Drawing id could not be null or empty");
        }

        ComponentFilter filter = ComponentFilter.builder()
                .specificProjectName(projectName)
                .groupMapping(Pair.of(GroupTypeEnum.Drawing, List.of(drawing)))
                .build();
        List<ComponentEntity> components = componentRepository.filter(filter);
        log.info("Found {} components by project name '{}' and drawing '{}'", components.size(), projectName, drawing);
        return components;
    }

}