package com.siemens.spine.logic.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/05/22
 */

@Data
public class GetObjectsVersionRequestDTO {

    @NotNull
    @JsonProperty("objectId")
    private Long objectId;

    @JsonProperty("revisionId")
    private Integer revisionId;

    @JsonProperty("versionId")
    private Long versionId;

    @JsonProperty("targetIds")
    private List<Long> targetIds;

}
