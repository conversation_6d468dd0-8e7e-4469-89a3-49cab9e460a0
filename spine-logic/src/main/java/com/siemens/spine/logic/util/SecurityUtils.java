package com.siemens.spine.logic.util;

import com.siemens.spine.db.constant.Constants;
import com.siemens.spine.logic.exception.ForbiddenException;
import com.siemens.spine.logic.security.Authentication;
import com.siemens.spine.logic.security.SecurityContextHolder;

import java.util.Collections;
import java.util.Set;

public final class SecurityUtils {

    public static final String USER_GROUP_PREFIX = "ADS-KPL-SPINE";

    public static final String PAR_ADMIN_GROUP = "ADS-KPL-SPINE-ADMIN";

    private SecurityUtils() {
    }

    public static String getUsernameFromToken() {
        Authentication authentication = SecurityContextHolder.getAuthentication();
        if (authentication == null) {
            return null;
        }

        return authentication.getUsername() == null ? authentication.getEmail() : authentication.getUsername();
    }

    public static Set<String> getUserRoles() {
        Authentication authentication = SecurityContextHolder.getAuthentication();
        if (authentication == null) {
            return Collections.emptySet();
        }

        return authentication.getRoles();
    }

    public static String getRequiredOperation(String type) {
        String permission = null;
        switch (type) {
            case "BOM":
                permission = Constants.Permission.TODOLIST_SAPBOM;
                break;
            case "EMULATION":
                permission = Constants.Permission.TODOLIST_EMULATION;
                break;
            case "IT":
                permission = Constants.Permission.TODOLIST_IT;
                break;
            case "SIMULATION":
                permission = Constants.Permission.TODOLIST_SIMULATION;
                break;
            case "CALCULATION":
                permission = Constants.Permission.TODOLIST_CALCULATION;
                break;
            case "OBJECTXML":
                permission = Constants.Permission.EXPORT_OBJECTXML;
                break;
            case "ELECTRIC":
                permission = Constants.Permission.ELECTRIC_SYNCHRONIZATION;
                break;
            default:
                throw new ForbiddenException();
        }
        return permission;
    }

}
