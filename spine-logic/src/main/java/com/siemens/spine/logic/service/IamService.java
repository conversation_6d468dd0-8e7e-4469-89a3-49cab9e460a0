package com.siemens.spine.logic.service;

import com.siemens.spine.generated.toolchain.UserRole;

import java.util.List;

/**
 * This identity and access manager
 *
 * <AUTHOR>
 */
public interface IamService {

    /**
     * Get the project role of the current authenticated user
     *
     * @param projectId
     * @return
     */
    List<String> getProjectRoleOfUser(Long projectId);

    /**
     * Get the project role of the a user
     *
     * @param projectId
     * @param username
     * @return
     */
    List<String> getProjectRoleOfUser(Long projectId, String username);

    /**
     * Get the user role from access token
     *
     * @return
     */
    List<UserRole> getUserRoles();

}
