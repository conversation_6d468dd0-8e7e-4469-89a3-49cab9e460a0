package com.siemens.spine.logic.util;

import com.siemens.spine.db.constant.GroupTypeEnum;
import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.ConnectionPointEntity;
import com.siemens.spine.db.entity.GroupEntity;
import com.siemens.spine.generated.toolchain.Component;
import com.siemens.spine.generated.toolchain.Group;
import com.siemens.spine.logic.dto.ConnectionPointDto;
import com.siemens.spine.logic.dto.GroupMappingDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public final class ComponentUtils {

    public static final String CONNECTION_POINT_FORMAT = "{Name:%s,Type=%s,x=%d,y=%d,z=%d}";

    private ComponentUtils() {
    }

    /**
     * @param wsGroups
     * @return a mapping from group type to list of relevant group names
     */
    public static Map<GroupTypeEnum, List<String>> getGroupsInfo(List<Group> wsGroups) {
        if (wsGroups == null || wsGroups.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<GroupTypeEnum, List<String>> ret = new HashMap<>();

        for (Group groupItem : wsGroups) {
            Map<GroupTypeEnum, String> groupTypeMapping = getGroupInfo(groupItem);
            for (Map.Entry<GroupTypeEnum, String> entry : groupTypeMapping.entrySet()) {
                ret.computeIfAbsent(entry.getKey(), k -> new ArrayList<>());
                ret.get(entry.getKey()).add(entry.getValue());
            }
        }

        return ret;
    }

    /**
     * @param wsGroup
     * @return a mapping from group type to relevant group name
     */
    public static Map<GroupTypeEnum, String> getGroupInfo(Group wsGroup) {
        if (wsGroup == null) {
            return Collections.emptyMap();
        }

        Map<GroupTypeEnum, String> ret = new HashMap<>();
        if (!StringUtils.isEmpty(wsGroup.getDrawing()) && !StringUtils.isEmpty(wsGroup.getDrawing().trim())) {
            ret.put(GroupTypeEnum.Drawing, wsGroup.getDrawing().trim());
        }

        if (!StringUtils.isEmpty(wsGroup.getBuildSection()) && !StringUtils.isEmpty(wsGroup.getBuildSection().trim())) {
            ret.put(GroupTypeEnum.DeliveryBatch, wsGroup.getBuildSection().trim());
        }

        if (!StringUtils.isEmpty(wsGroup.getPLCArea()) && !StringUtils.isEmpty(wsGroup.getPLCArea().trim())) {
            ret.put(GroupTypeEnum.PlcArea, wsGroup.getPLCArea().trim());
        }

        if (!StringUtils.isEmpty(wsGroup.getScreen()) && !StringUtils.isEmpty(wsGroup.getScreen().trim())) {
            ret.put(GroupTypeEnum.Screen, wsGroup.getScreen().trim());
        }

        if (!StringUtils.isEmpty(wsGroup.getCalculationArea()) && !StringUtils.isEmpty(
                wsGroup.getCalculationArea().trim())) {
            ret.put(GroupTypeEnum.CalculationArea, wsGroup.getCalculationArea().trim());
        }

        if (!StringUtils.isEmpty(wsGroup.getEStopGroup()) && !StringUtils.isEmpty(wsGroup.getEStopGroup().trim())) {
            ret.put(GroupTypeEnum.EStopGroup, wsGroup.getEStopGroup().trim());
        }

        if (!StringUtils.isEmpty(wsGroup.getSequenceGroup()) && !StringUtils.isEmpty(
                wsGroup.getSequenceGroup().trim())) {
            ret.put(GroupTypeEnum.SequenceGroup, wsGroup.getSequenceGroup().trim());
        }

        if (!StringUtils.isEmpty(wsGroup.getLineName()) && !StringUtils.isEmpty(wsGroup.getLineName().trim())) {
            ret.put(GroupTypeEnum.LineName, wsGroup.getLineName().trim());
        }

        if (!StringUtils.isEmpty(wsGroup.getBuildingSection()) && !StringUtils.isEmpty(
                wsGroup.getBuildingSection().trim())) {
            ret.put(GroupTypeEnum.BuildingSection, wsGroup.getBuildingSection().trim());
        }

        if (!StringUtils.isEmpty(wsGroup.getInstallationSection()) && !StringUtils.isEmpty(
                wsGroup.getInstallationSection().trim())) {
            ret.put(GroupTypeEnum.InstallationSection, wsGroup.getInstallationSection().trim());
        }

        if (!StringUtils.isEmpty(wsGroup.getProjectView()) && !StringUtils.isEmpty(wsGroup.getProjectView().trim())) {
            ret.put(GroupTypeEnum.ProjectView, wsGroup.getProjectView().trim());
        }

        return ret;
    }

    /**
     * @param wsComponent
     * @return a mapping from group type to relevant group name
     */
    public static Map<GroupTypeEnum, String> getGroupInfo(GroupMappingDto wsComponent) {
        if (wsComponent == null) {
            return Collections.emptyMap();
        }

        Map<GroupTypeEnum, String> ret = new HashMap<>();
        if (!StringUtils.isEmpty(wsComponent.getDrawing()) && !StringUtils.isEmpty(wsComponent.getDrawing().trim())) {
            ret.put(GroupTypeEnum.Drawing, wsComponent.getDrawing().trim());
        }

        if (!StringUtils.isEmpty(wsComponent.getDeliveryBatch()) && !StringUtils.isEmpty(
                wsComponent.getDeliveryBatch().trim())) {
            ret.put(GroupTypeEnum.DeliveryBatch, wsComponent.getDeliveryBatch().trim());
        }

        if (!StringUtils.isEmpty(wsComponent.getPlcArea()) && !StringUtils.isEmpty(wsComponent.getPlcArea().trim())) {
            ret.put(GroupTypeEnum.PlcArea, wsComponent.getPlcArea().trim());
        }

        if (!StringUtils.isEmpty(wsComponent.getScreen()) && !StringUtils.isEmpty(wsComponent.getScreen().trim())) {
            ret.put(GroupTypeEnum.Screen, wsComponent.getScreen().trim());
        }

        if (!StringUtils.isEmpty(wsComponent.getCalculationArea()) && !StringUtils.isEmpty(
                wsComponent.getCalculationArea().trim())) {
            ret.put(GroupTypeEnum.CalculationArea, wsComponent.getCalculationArea().trim());
        }

        if (!StringUtils.isEmpty(wsComponent.getEStopGroup()) && !StringUtils.isEmpty(
                wsComponent.getEStopGroup().trim())) {
            ret.put(GroupTypeEnum.EStopGroup, wsComponent.getEStopGroup().trim());
        }

        if (!StringUtils.isEmpty(wsComponent.getSequenceGroup()) && !StringUtils.isEmpty(
                wsComponent.getSequenceGroup().trim())) {
            ret.put(GroupTypeEnum.SequenceGroup, wsComponent.getSequenceGroup().trim());
        }

        if (!StringUtils.isEmpty(wsComponent.getLineName()) && !StringUtils.isEmpty(wsComponent.getLineName().trim())) {
            ret.put(GroupTypeEnum.LineName, wsComponent.getLineName().trim());
        }

        if (!StringUtils.isEmpty(wsComponent.getBuildingSection()) && !StringUtils.isEmpty(
                wsComponent.getBuildingSection().trim())) {
            ret.put(GroupTypeEnum.BuildingSection, wsComponent.getBuildingSection().trim());
        }

        if (!StringUtils.isEmpty(wsComponent.getInstallationSection()) && !StringUtils.isEmpty(
                wsComponent.getInstallationSection().trim())) {
            ret.put(GroupTypeEnum.InstallationSection, wsComponent.getInstallationSection().trim());
        }

        //        if (!StringUtils.isEmpty(wsComponent.getProjectView())) {
        //            ret.put(GroupTypeEnum.ProjectView, wsComponent.getProjectView());
        //        }

        return ret;
    }

    /**
     * Get the group mapping of a component
     *
     * @param componentEntity
     * @return
     */
    public static Map<GroupTypeEnum, String> getComponentGroupMapping(ComponentEntity componentEntity) {
        if (componentEntity == null) {
            return Collections.emptyMap();
        }

        Set<GroupEntity> groupEntities = componentEntity.getGroups();
        if (groupEntities == null || groupEntities.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<GroupTypeEnum, String> ret = new HashMap<>();
        for (GroupEntity entity : groupEntities) {
            ret.put(entity.getGrouptype(), entity.getName());
        }

        return ret;
    }

    /**
     * Convert to group dto
     *
     * @param groupEntities
     * @return
     */
    public static GroupMappingDto toGroupMappingDto(Set<GroupEntity> groupEntities) {
        if (groupEntities == null || groupEntities.isEmpty()) {
            return null;
        }

        GroupMappingDto ret = new GroupMappingDto();
        for (GroupEntity group : groupEntities) {
            String groupName = group.getName();
            switch (group.getGrouptype()) {
                case BuildSection:
                case DeliveryBatch:
                    ret.setDeliveryBatch(groupName);
                    break;
                case BuildingSection:
                    ret.setBuildingSection(groupName);
                    break;
                case CalculationArea:
                    ret.setCalculationArea(groupName);
                    break;
                case Drawing:
                    ret.setDrawing(groupName);
                    break;
                case EStopGroup:
                    ret.setEStopGroup(groupName);
                    break;
                case InstallationSection:
                    ret.setInstallationSection(groupName);
                    break;
                case LineName:
                    ret.setLineName(groupName);
                    break;
                case PlcArea:
                    ret.setPlcArea(groupName);
                    break;
                case Screen:
                    ret.setScreen(groupName);
                    break;
                case SequenceGroup:
                    ret.setSequenceGroup(groupName);
                    break;
                case ProjectView:
                    ret.setProjectView(groupName);
                    break;
                default:
                    log.warn(String.format("Group type %s was not handled", group.getGrouptype()));
                    break;
            }
        }

        return ret;
    }

    public static GroupMappingDto toGroupMappingDto(Component component) {
        if (component == null) {
            return null;
        }

        return GroupMappingDto.builder()
                .buildingSection(component.getBuildingSection())
                .calculationArea(component.getCalculationArea())
                .deliveryBatch(component.getBuildSection())
                .eStopGroup(component.getEStopGroup())
                .drawing(component.getDrawing())
                .installationSection(component.getInstallationSection())
                .lineName(component.getLineName())
                .plcArea(component.getPLCArea())
                .sequenceGroup(component.getSequenceGroup())
                .screen(component.getScreen())
                .build();
    }

    public static String connectionPointsEToString(List<ConnectionPointEntity> connectionPoints) {
        if (CollectionUtils.isEmpty(connectionPoints)) {
            return StringUtils.EMPTY;
        }
        StringBuilder sb = new StringBuilder();
        connectionPoints = connectionPoints.stream()
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(ConnectionPointEntity::getName))
                .toList();
        for (ConnectionPointEntity connectionPoint : connectionPoints) {
            String cp = String.format(CONNECTION_POINT_FORMAT, connectionPoint.getName(), connectionPoint.getType(),
                    connectionPoint.getX(), connectionPoint.getY(), connectionPoint.getZ());
            sb.append(cp);
        }
        return sb.toString();
    }

    public static String connectionPointsDToString(List<ConnectionPointDto> connectionPoints) {
        if (CollectionUtils.isEmpty(connectionPoints)) {
            return StringUtils.EMPTY;
        }
        StringBuilder sb = new StringBuilder();
        connectionPoints = connectionPoints.stream().sorted(Comparator.comparing(ConnectionPointDto::getName))
                .collect(Collectors.toList());
        for (ConnectionPointDto connectionPoint : connectionPoints) {
            String cp = String.format(CONNECTION_POINT_FORMAT, connectionPoint.getName(), connectionPoint.getType(),
                    connectionPoint.getX(), connectionPoint.getY(), connectionPoint.getZ());
            sb.append(cp);
        }
        return sb.toString();
    }

}
