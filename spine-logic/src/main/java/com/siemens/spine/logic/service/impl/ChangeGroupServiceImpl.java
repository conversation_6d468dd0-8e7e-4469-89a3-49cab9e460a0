package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.entity.ChangeGroupEntity;
import com.siemens.spine.db.entity.ProjectEntity;
import com.siemens.spine.db.repository.ChangeGroupRepository;
import com.siemens.spine.db.repository.ProjectRepository;
import com.siemens.spine.db.repository.filter.ChangeGroupFilter;
import com.siemens.spine.db.repository.filter.ProjectFilter;
import com.siemens.spine.db.repository.views.ComponentChangeGroupView;
import com.siemens.spine.logic.dto.ChangeGroupDto;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.mapper.ChangeGroupMapper;
import com.siemens.spine.logic.service.ChangeGroupService;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@ApplicationScoped
@Slf4j
@Transactional(value = Transactional.TxType.REQUIRED, rollbackOn = Exception.class)
public class ChangeGroupServiceImpl implements ChangeGroupService {

    private final ChangeGroupRepository changeGroupRepository;

    private final ProjectRepository projectRepository;

    @Inject
    public ChangeGroupServiceImpl(ChangeGroupRepository changeGroupRepository, ProjectRepository projectRepository) {
        this.changeGroupRepository = changeGroupRepository;
        this.projectRepository = projectRepository;
    }

    @Override
    public ChangeGroupDto create(String projectName, ChangeGroupDto request) throws SpineException {
        if (request == null || projectName == null) {
            throw new SpineException("Project key must not be null");
        }

        if (request.getName() == null) {
            throw new SpineException("Please enter the change group name");
        }

        ProjectFilter filter = ProjectFilter.builder()
                .projectName(projectName)
                .build();
        List<ProjectEntity> projectEntities = projectRepository.find(filter);
        if (projectEntities.isEmpty()) {
            throw new SpineException(String.format("No project was found by key %s", projectName));
        }

        ChangeGroupEntity entity = ChangeGroupEntity.builder()
                .name(request.getName())
                .description(request.getDescription())
                .defaultReason(request.getDefaultReason())
                .projectEntity(projectEntities.get(0))
                .build();

        ChangeGroupEntity savedEntity = changeGroupRepository.save(entity);
        return ChangeGroupMapper.INSTANCE.toChangeGroupDto(savedEntity);
    }

    @Override
    @Transactional(value = Transactional.TxType.REQUIRES_NEW)
    public ChangeGroupDto create(Long projectId, ChangeGroupDto request) throws SpineException {
        if (request == null || projectId == null) {
            throw new SpineException("Project id must not be null");
        }

        if (request.getName() == null) {
            throw new SpineException("Please enter the change group name");
        }

        Optional<ProjectEntity> projectEntity = projectRepository.findById(projectId);
        if (projectEntity.isEmpty()) {
            throw new SpineException(String.format("No project was found by id %s", projectId));
        }

        ChangeGroupEntity entity = ChangeGroupEntity.builder()
                .name(request.getName())
                .description(request.getDescription())
                .defaultReason(request.getDefaultReason())
                .projectEntity(projectEntity.get())
                .build();

        ChangeGroupEntity savedEntity = changeGroupRepository.save(entity);
        return ChangeGroupMapper.INSTANCE.toChangeGroupDto(savedEntity);
    }

    @Override
    public List<ChangeGroupDto> getChangeGroups(ChangeGroupFilter filter) {
        List<ChangeGroupEntity> entities = changeGroupRepository.getChangeGroups(filter);

        if (entities == null || entities.isEmpty()) {
            return Collections.emptyList();
        }

        return ChangeGroupMapper.INSTANCE.toChangeGroupDtos(entities);
    }

    @Override
    public List<ComponentChangeGroupView> getLatestChangeGroupByComponentIds(List<Long> ids) {
        return changeGroupRepository.getListChangeGroupByEachComponentIds(ids);
    }

}
