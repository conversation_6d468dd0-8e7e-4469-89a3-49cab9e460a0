package com.siemens.spine.logic.service.functioninterface;

import com.siemens.spine.logic.dto.ComponentStateChangedResponseDto;
import com.siemens.spine.logic.dto.request.StateChangedRequestDto;
import com.siemens.spine.logic.exception.SpineException;

@FunctionalInterface
public interface ComponentStateChangedProcessor {

    ComponentStateChangedResponseDto process(StateChangedRequestDto arg) throws SpineException;

}
