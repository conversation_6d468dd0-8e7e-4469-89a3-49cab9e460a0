package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.constant.ComponentState;
import com.siemens.spine.db.constant.ProjectState;
import com.siemens.spine.db.constant.ProjectType;
import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.ProjectEntity;
import com.siemens.spine.db.entity.StateEntity;
import com.siemens.spine.db.repository.ComponentRepository;
import com.siemens.spine.db.repository.ProjectRepository;
import com.siemens.spine.db.repository.StateRepository;
import com.siemens.spine.db.repository.filter.ProjectFilter;
import com.siemens.spine.generated.toolchain.Project;
import com.siemens.spine.logic.dto.ProjectResponseDTO;
import com.siemens.spine.logic.dto.request.ProjectRequestDTO;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.mapper.ProjectMapper;
import com.siemens.spine.logic.service.BatchJobService;
import com.siemens.spine.logic.service.ProjectService;
import com.siemens.spine.logic.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Pham
 * @version 1.0
 * @since 21/12/2022
 */
@ApplicationScoped
@Transactional(value = Transactional.TxType.REQUIRED, rollbackOn = Exception.class)
@Slf4j
public class ProjectServiceImpl implements ProjectService {

    private final ProjectRepository projectRepository;

    private final ComponentRepository componentRepository;

    private final BatchJobService batchJobService;

    private final StateRepository stateRepository;

    private final ProjectMapper projectMapper;

    @Inject
    public ProjectServiceImpl(ProjectRepository projectRepository,
                              ComponentRepository componentRepository,
                              BatchJobService batchJobService,
                              StateRepository stateRepository, ProjectMapper projectMapper) {
        this.projectRepository = projectRepository;
        this.componentRepository = componentRepository;
        this.batchJobService = batchJobService;
        this.stateRepository = stateRepository;
        this.projectMapper = projectMapper;
    }

    private static List<String> getStates(boolean isAdmin) {
        List<String> states = new ArrayList<>();
        states.add(ProjectState.EXECUTION.name());
        states.add(ProjectState.OFFER.name());
        if (isAdmin) {
            states.add(ProjectState.CLOSED.name());
        }
        return states;
    }

    @Override
    public List<? extends Project> getAllProjects() {
        List<ProjectEntity> allProjects = getProjectsByStatusStates();
        return projectMapper.toProjects(allProjects);
    }

    @Override
    public List<ProjectResponseDTO> getAllProjectForRest() {
        List<ProjectEntity> allProjects = getProjectsByStatusStates();
        return projectMapper.toProjectsForRest(allProjects);
    }

    @Override
    public Boolean checkProjectNameExits(String projectName) {
        return projectRepository.existByName(projectName);
    }

    @Override
    public Long getProjectIdByName(String projectName) throws SpineException {
        Optional<ProjectEntity> project = projectRepository.findByName(projectName);
        if (project.isEmpty()) {
            throw new SpineException("Could not find project id");
        }
        return project.get().getProjectID();
    }

    @Override
    public ProjectResponseDTO createProject(ProjectRequestDTO projRequestDTO) throws SpineException {
        if (projRequestDTO == null) {
            throw new SpineException("Invalid input");
        }

        String projectName = projRequestDTO.getProjectName();
        String projectKey = projRequestDTO.getSapProjectKey();
        String projectType = projRequestDTO.getType();

        if (StringUtils.isEmpty(projectName) || StringUtils.isEmpty(projectKey) || StringUtils.isEmpty(projectType)) {
            throw new SpineException(String.format(
                    "The required information could not be null. Project type: '%s', Project name '%s', project key: '%s'",
                    projectType, projectName, projectKey));
        }

        try {
            ProjectState.valueOf(projRequestDTO.getMainProjectState());
        } catch (Exception e) {
            log.warn("The project state {} was invalid. Set to default state 'OFFER'",
                    projRequestDTO.getMainProjectState());
            projRequestDTO.setMainProjectState(ProjectState.OFFER.name());
        }

        // verify if project key is duplicate or not
        ProjectFilter filter = ProjectFilter.builder().projectName(projectKey.trim()).build();
        List<ProjectEntity> existingByKey = projectRepository.find(filter);
        if (existingByKey != null && !existingByKey.isEmpty()) {
            throw new SpineException(
                    String.format("Could not create new project with same project key '%s' with an existing project",
                            projectKey));
        }

        // Save the project
        ProjectEntity project = projectMapper.toProjectEntity(projRequestDTO);
        ProjectEntity savedProject = projectRepository.save(project);
        log.info("New project {} (key: {}) has been created by {}", projectName, projectKey,
                SecurityUtils.getUsernameFromToken());
        return projectMapper.toProjectResponseDTO(savedProject);
    }

    @Override
    public List<ProjectType> getListTypesProject() {
        return Arrays.asList(ProjectType.values());
    }

    @Override
    public List<ProjectState> getListStateProject() {
        return Arrays.asList(ProjectState.values());
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public ProjectResponseDTO changeProjectState(Long projectId, ProjectState newState) throws SpineException {
        Optional<ProjectEntity> optionalProject = projectRepository.findById(projectId);
        if (optionalProject.isEmpty()) {
            throw new SpineException("Could not find project id");
        }
        ProjectEntity project = optionalProject.get();
        String currentState = project.getMainProjectState();
        if (ProjectState.CLOSED.name().equals(currentState)) {
            throw new SpineException("Could not change state of a closed project");
        }
        String statusNeedToUpdate;
        if (ProjectState.OFFER.name().equals(currentState) && ProjectState.EXECUTION.equals(newState)) {
            statusNeedToUpdate = newState.name();
            //When moving a project from "Offer" to "Execution", all the component state (of the project) should be moved to 200.0
            List<ComponentEntity> componentEntities = componentRepository.findByProjectIdAndNotInExecutionPhase(
                    projectId);
            log.info("size component entities before split in batch job : {}", componentEntities.size());
            batchJobService.executeJobs(this::updateListComponentToExecution, componentEntities,
                    BatchJobService.DEFAULT_BATCH_SIZE);
        } else if (ProjectState.CLOSED.equals(newState)) {
            statusNeedToUpdate = newState.name();
        } else {
            throw new SpineException(
                    String.format("Could not change state of project from %s to %s", currentState, newState));
        }
        project.setMainProjectState(statusNeedToUpdate);
        log.info("update status: {}", statusNeedToUpdate);
        return projectMapper.toProjectResponseDTO(projectRepository.save(project));
    }

    private List<ComponentEntity> updateListComponentToExecution(List<ComponentEntity> componentEntities) {
        List<StateEntity> newStateEntities = new ArrayList<>();
        List<Long> componentIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(componentEntities)) {
            componentEntities = componentEntities.stream().map(componentEntity -> {
                componentIds.add(componentEntity.getId());
                StateEntity newState = StateEntity.builder().state(ComponentState.EXECUTION_PHASE_200_0.getV1Status())
                        .statusDate(new Timestamp(System.currentTimeMillis())).component(componentEntity).build();
                newStateEntities.add(newState);
                return componentEntity;
            }).toList();
            log.info("size sub componentEntities : {}", componentEntities.size());
            componentRepository.updateStateComponentByIds(componentIds,
                    ComponentState.EXECUTION_PHASE_200_0.getV1Status());
            stateRepository.saveAll(newStateEntities);
        }
        return componentEntities;
    }

    private List<ProjectEntity> getProjectsByStatusStates() {
        long startTime = System.currentTimeMillis();
        List<ProjectEntity> allProjects = projectRepository.findAllProjectsByStatusState(getStates(true));
        log.info("Get {} projects taken {} ms", allProjects.size(), (System.currentTimeMillis() - startTime));
        return allProjects;
    }

}
