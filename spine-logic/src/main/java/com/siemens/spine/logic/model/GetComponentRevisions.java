package com.siemens.spine.logic.model;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2025/04/22
 */

@Setter
@Getter
public class GetComponentRevisions {

    /**
     * -- GETTER --
     * Gets the value of the arg0 property.
     * <p>
     * <p>
     * -- SETTER --
     * Sets the value of the arg0 property.
     *
     * @return possible object is
     * {@link String }
     * @param value
     * allowed object is
     * {@link String }
     */
    protected String arg0;
    /**
     * -- GETTER --
     * Gets the value of the arg1 property.
     * <p>
     * <p>
     * -- SETTER --
     * Sets the value of the arg1 property.
     *
     * @return possible object is
     * {@link Long }
     * @param value
     * allowed object is
     * {@link Long }
     */
    protected Long arg1;

}
