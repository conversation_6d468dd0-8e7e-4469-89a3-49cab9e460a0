package com.siemens.spine.logic.util;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.GregorianCalendar;

/**
 * <AUTHOR> <PERSON>am
 * @version 1.0
 * @since 26/12/2022
 */
public class DateTimeUtils {

    private static final DateTimeFormatter ISO_DATE_TIME = DateTimeFormatter.ISO_DATE_TIME;

    public static XMLGregorianCalendar toXMLGregorianCalendar(LocalDateTime time) {
        GregorianCalendar gregorianCalendar = GregorianCalendar.from(
                time.atZone(ZoneId.systemDefault()));
        try {
            return DatatypeFactory.newInstance().newXMLGregorianCalendar(gregorianCalendar);
        } catch (DatatypeConfigurationException e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public static String formatDateISO(LocalDateTime dateTime) {
        return dateTime.format(DateTimeFormatter.ISO_DATE_TIME);
    }

    public static String formatDateISO(LocalDate dateTime) {
        return dateTime != null ? dateTime.format(DateTimeFormatter.ISO_DATE) : null;
    }

    public static String formatDateISO(Timestamp timestamp) {
        return timestamp != null ? timestamp.toLocalDateTime().format(DateTimeFormatter.ISO_DATE_TIME) : null;
    }

}
