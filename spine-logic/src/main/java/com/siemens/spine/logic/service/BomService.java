package com.siemens.spine.logic.service;

import com.siemens.spine.generated.toolchain.BoMGetDataResult;
import com.siemens.spine.logic.exception.SpineException;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 26/12/2022
 */
public interface BomService {

    List<Long> getBomListUniqueIds(String projectName, List<String> groupData, boolean getFullData)
            throws SpineException;

    BoMGetDataResult getBomListDataByUniqueIds(String projectName, List<Long> axids) throws SpineException;

}
