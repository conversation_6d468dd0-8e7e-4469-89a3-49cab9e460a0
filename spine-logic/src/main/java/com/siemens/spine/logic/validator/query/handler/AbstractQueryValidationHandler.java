package com.siemens.spine.logic.validator.query.handler;

import com.siemens.spine.logic.validator.query.QueryValidationContext;
import com.siemens.spine.logic.validator.query.ValidationResult;
import org.apache.commons.lang3.StringUtils;

public abstract class AbstractQueryValidationHandler implements QueryValidationHandler {

    private QueryValidationHandler nextHandler;

    @Override
    public void setNext(QueryValidationHandler handler) {
        this.nextHandler = handler;
    }

    @Override
    public ValidationResult validate(QueryValidationContext context) {
        validateContext(context);

        ValidationResult result = doValidate(context);

        if (result.valid() && nextHandler != null) {
            return nextHandler.validate(context);
        }

        return result;
    }

    protected void validateContext(QueryValidationContext context) {
        if (context == null || StringUtils.isEmpty(context.getQueryString())) {
            throw new IllegalArgumentException("Query validation context is required or query string is empty");
        }
    }

    protected abstract ValidationResult doValidate(QueryValidationContext context);

}