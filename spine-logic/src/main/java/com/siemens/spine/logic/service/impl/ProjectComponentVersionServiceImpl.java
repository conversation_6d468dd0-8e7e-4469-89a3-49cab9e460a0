package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.ComponentIdRev;
import com.siemens.spine.db.entity.ComponentReferenceSet;
import com.siemens.spine.db.entity.ProjectComponentVersionEntity;
import com.siemens.spine.db.entity.ProjectEntity;
import com.siemens.spine.db.entity.RevisionInfoEntity;
import com.siemens.spine.db.repository.ComponentRepository;
import com.siemens.spine.db.repository.ProjectComponentVersionRepository;
import com.siemens.spine.db.repository.ProjectRepository;
import com.siemens.spine.logic.dto.request.ComponentProjectRequestDTO;
import com.siemens.spine.logic.dto.request.GetObjectsVersionRequestDTO;
import com.siemens.spine.logic.mapper.ProjectComponentVersionMapper;
import com.siemens.spine.logic.mapper.RevisionInfoMapper;
import com.siemens.spine.logic.model.Component;
import com.siemens.spine.logic.model.ProjectComponentVersion;
import com.siemens.spine.logic.model.RevisionInfo;
import com.siemens.spine.logic.service.ProjectComponentVersionService;
import com.siemens.spine.logic.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/04/21
 */

@Slf4j
@ApplicationScoped
@Transactional(value = TxType.REQUIRED, rollbackOn = Exception.class)
public class ProjectComponentVersionServiceImpl implements ProjectComponentVersionService {

    private final ComponentRepository componentRepository;
    private final ProjectComponentVersionMapper projectComponentVersionMapper;
    private final ProjectComponentVersionRepository projectComponentVersionRepository;
    private final RevisionInfoMapper revisionInfoMapper;
    private final ProjectRepository projectRepository;

    @Inject
    public ProjectComponentVersionServiceImpl(ProjectRepository projectRepository,
                                              RevisionInfoMapper revisionInfoMapper,
                                              ProjectComponentVersionRepository projectComponentVersionRepository,
                                              ProjectComponentVersionMapper projectComponentVersionMapper,
                                              ComponentRepository componentRepository) {
        this.projectRepository = projectRepository;
        this.revisionInfoMapper = revisionInfoMapper;
        this.projectComponentVersionRepository = projectComponentVersionRepository;
        this.projectComponentVersionMapper = projectComponentVersionMapper;
        this.componentRepository = componentRepository;
    }

    private static Optional<Integer> findMaxRev(List<ComponentIdRev> list) {
        return list.stream()
                .map(ComponentIdRev::getRev)
                .max(Comparator.naturalOrder());
    }

    @Override
    public List<Component> getObjectsInRevision(GetObjectsVersionRequestDTO requestDTO) {
        Optional<ProjectComponentVersionEntity> componentVersionEntities = projectComponentVersionRepository.findByProjectIdAndRev(
                requestDTO.getObjectId(), requestDTO.getRevisionId());

        List<Long> targetIds = requestDTO.getTargetIds();

        if (componentVersionEntities.isEmpty()) {
            throw new IllegalArgumentException("Version ID not found");
        }

        ProjectComponentVersionEntity projectComponentVersionEntity = componentVersionEntities.get();

        if (!Objects.equals(projectComponentVersionEntity.getProjectId(), requestDTO.getObjectId())) {
            throw new IllegalArgumentException("Project ID not found");
        }

        ComponentReferenceSet componentReferenceSets = projectComponentVersionEntity.getComponentRefs();
        Map<Long, Integer> componentIdMap = componentReferenceSets.getComponentRefs();

        Map<Long, Integer> filteredMap = componentIdMap.entrySet().stream()
                .filter(entry -> targetIds.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        List<ComponentEntity> componentEntities = new ArrayList<>();

        for (Map.Entry<Long, Integer> entry : filteredMap.entrySet()) {
            Long key = entry.getKey();
            Integer value = entry.getValue();

            Optional<ComponentEntity> optionalComponent = componentRepository.getHistoryAtRevision(key,
                    value);

            if (optionalComponent.isPresent()) {
                ComponentEntity componentEntity = optionalComponent.get();
                componentEntities.add(componentEntity);
            }
        }
        return componentEntities.stream()
                .map(componentEntity -> projectComponentVersionMapper.toComponentVersion(componentEntity))
                .toList();
    }

    @Override
    public List<Component> getObjectsInVersion(GetObjectsVersionRequestDTO requestDTO) {
        Long versionId = requestDTO.getVersionId();
        List<Long> targetIds = requestDTO.getTargetIds();
        Optional<ProjectComponentVersionEntity> componentVersionEntities = projectComponentVersionRepository.findById(
                versionId);

        if (componentVersionEntities.isEmpty()) {
            throw new IllegalArgumentException("Version ID not found");
        }

        ProjectComponentVersionEntity projectComponentVersionEntity = componentVersionEntities.get();

        if (!Objects.equals(projectComponentVersionEntity.getProjectId(), requestDTO.getObjectId())) {
            throw new IllegalArgumentException("Project ID not found");
        }

        ComponentReferenceSet componentReferenceSets = projectComponentVersionEntity.getComponentRefs();
        Map<Long, Integer> componentIdMap = componentReferenceSets.getComponentRefs();

        Map<Long, Integer> filteredMap = componentIdMap.entrySet().stream()
                .filter(entry -> targetIds.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        List<ComponentEntity> componentEntities = new ArrayList<>();

        for (Map.Entry<Long, Integer> entry : filteredMap.entrySet()) {
            Long key = entry.getKey();
            Integer value = entry.getValue();

            Optional<ComponentEntity> optionalComponent = componentRepository.getHistoryAtRevision(key,
                    value);

            if (optionalComponent.isPresent()) {
                ComponentEntity componentEntity = optionalComponent.get();
                componentEntities.add(componentEntity);
            }
        }

        return componentEntities.stream()
                .map(componentEntity -> projectComponentVersionMapper.toComponentVersion(componentEntity))
                .toList();
    }

    @Override
    public List<ProjectComponentVersion> getAllVersions(Long projectId) {
        List<ProjectComponentVersionEntity> componentVersionEntities = projectComponentVersionRepository.findByProjectId(
                projectId);
        if (componentVersionEntities == null || componentVersionEntities.isEmpty()) {
            log.warn("No component versions found for project {}", projectId);
            return List.of();
        }

        return componentVersionEntities.stream()
                .map(projectComponentVersionMapper::toComponentVersion)
                .toList();
    }

    @Override
    public List<RevisionInfo> getAllRevisions(Long componentId) {
        List<RevisionInfoEntity> revisionInfoEntities = componentRepository.getAllRevisionInfo(
                componentId);
        revisionInfoEntities.sort(
                (rev1, rev2) -> Long.compare(rev2.getTimestamp(), rev1.getTimestamp()));

        return revisionInfoEntities.stream().map(revisionInfoMapper::toRevisionInfo)
                .toList();
    }

    @Override
    public void handleNewRevision(Long componentId, RevisionInfoEntity revisionInfo) {
        log.info("Handling new revision component {} with revision info {}", componentId, revisionInfo);
    }

    @Override
    public <DTO> ProjectComponentVersion createVersion(DTO rdto) {

        if (!(rdto instanceof ComponentProjectRequestDTO dto)) {
            throw new IllegalArgumentException("Request obj must be of type ComponentProjectRequestDTO");
        }

        Optional<ProjectEntity> optionalProject = projectRepository.findById(dto.getProjectId());

        if (optionalProject.isEmpty()) {
            throw new IllegalArgumentException("Project ID not found");
        }

        ProjectComponentVersionEntity projectComponentVersionEntity = new ProjectComponentVersionEntity();
        projectComponentVersionEntity.setProjectId(dto.getProjectId());
        projectComponentVersionEntity.setMessage(dto.getMessage());
        projectComponentVersionEntity.setVersionId(UUID.randomUUID().toString());
        projectComponentVersionEntity.setVersionName(dto.getVersionName());

        List<ComponentIdRev> componentIdRevs = componentRepository.findLatestComponentAuditByProjectId(
                dto.getProjectId());

        ComponentReferenceSet componentReferenceSet = convertMaxRevSorted(componentIdRevs);
        Optional<Integer> maxOpt = findMaxRev(componentIdRevs);

        if (maxOpt.isEmpty()) {
            throw new IllegalArgumentException("Max revision not found");
        }

        projectComponentVersionEntity.setComponentRefs(componentReferenceSet);
        projectComponentVersionEntity.setRev(maxOpt.get());

        Optional<ProjectComponentVersionEntity> validate = projectComponentVersionRepository.findByProjectIdAndRev(
                dto.getProjectId(), maxOpt.get());

        if (validate.isPresent()) {
            throw new IllegalArgumentException("Version ID already exists");
        }

        //Set username from token
        projectComponentVersionEntity.setCreatedBy(SecurityUtils.getUsernameFromToken());

        return projectComponentVersionMapper.toComponentVersion(
                projectComponentVersionRepository.save(projectComponentVersionEntity));
    }

    private ComponentReferenceSet convertMaxRevSorted(List<ComponentIdRev> list) {
        Map<Long, Integer> map = list.stream()
                .collect(Collectors.toMap(
                        ComponentIdRev::getId,
                        ComponentIdRev::getRev,
                        (rev1, rev2) -> rev1 > rev2 ? rev1 : rev2,
                        TreeMap::new
                ));

        ComponentReferenceSet refSet = new ComponentReferenceSet();
        refSet.setComponentRefs(map);

        return refSet;
    }

}
