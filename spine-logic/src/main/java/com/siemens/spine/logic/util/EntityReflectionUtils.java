package com.siemens.spine.logic.util;

import com.siemens.spine.db.utils.CompositeKeyUtils;
import com.siemens.spine.logic.exception.SpineException;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.EmbeddedId;
import javax.persistence.Id;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Enhanced utility service for entity reflection operations.
 * Provides methods for ID extraction, setting, and type conversion
 * commonly needed when working with JPA entities in REST operations.
 * <p>
 * Supports the new composite key format: {key1:val1}_{key2:val2}_{key3:val3}
 *
 * <AUTHOR> <PERSON>am
 * @since 1.0
 */
@Slf4j
public final class EntityReflectionUtils {

    private EntityReflectionUtils() {
    }

    /**
     * Converts a string ID to the appropriate type based on the target class.
     * Now supports composite keys in both formats:
     * - New format: {key1:val1}_{key2:val2}_{key3:val3}
     * - Legacy format: {val1}_{val2}_{val3}
     *
     * @param idStr   The string representation of the ID (single or composite)
     * @param idClass The target class - either entity class (for composite keys) or ID type class (for single keys)
     * @return The converted ID object or CompositeKeyData for composite keys
     * @throws SpineException if conversion fails
     */
    public static Object convertId(String idStr, Class<?> idClass) throws SpineException {
        Objects.requireNonNull(idStr, "ID string cannot be null");
        Objects.requireNonNull(idClass, "ID class cannot be null");

        try {
            if (CompositeKeyUtils.hasCompositeKey(idClass)) {
                log.debug("Converting composite ID string '{}' for entity class: {}", idStr, idClass.getSimpleName());

                return CompositeKeyUtils.createCompositeKeyData(idStr, idClass);
            }

            log.debug("Converting single ID '{}' to type: {}", idStr, idClass.getSimpleName());

            return getValue(idStr, idClass);
        } catch (NumberFormatException e) {
            throw new SpineException("Failed to convert ID '" + idStr + "' to appropriate type for class " +
                    idClass.getSimpleName(), e);
        }
    }

    /**
     * Extracts the ID value from an entity using reflection.
     * Enhanced to support new composite key format with explicit key-value mapping.
     *
     * @param entity The entity from which to extract the ID
     * @return The ID value (single value, formatted composite string, or CompositeKeyData)
     * @throws SpineException if reflection operations fail
     */
    public static Object extractId(Object entity) throws SpineException {
        Objects.requireNonNull(entity, "Entity cannot be null");

        Class<?> clazz = entity.getClass();

        if (CompositeKeyUtils.hasCompositeKey(clazz)) {
            return extractCompositeIdWithMapping(entity);
        }

        Field idField = findIdField(clazz);

        if (idField == null) {
            log.error("No @Id annotated field found in entity class: {}", clazz.getSimpleName());
            throw new SpineException("No @Id annotated field found in entity class: " + clazz.getSimpleName());
        }
        return getFieldValue(entity, idField);
    }

    /**
     * Extracts composite ID values from an entity and formats them with explicit key-value mapping.
     * New format: {key1:val1}_{key2:val2}_{key3:val3}
     *
     * @param entity The entity from which to extract composite ID
     * @return Formatted composite key string with explicit mapping
     * @throws SpineException if extraction fails
     */
    private static Object extractCompositeIdWithMapping(Object entity) throws SpineException {
        CompositeKeyUtils.CompositeKeyType keyType = CompositeKeyUtils.getCompositeKeyType(entity.getClass());
        Map<String, Object> keyValueMap = new HashMap<>();

        if (keyType == CompositeKeyUtils.CompositeKeyType.EMBEDDED_ID) {
            keyValueMap = extractEmbeddedIdValues(entity);
        } else {
            // For MULTIPLE_ID and ID_CLASS, extract fields directly from entity
            List<Field> idFields = CompositeKeyUtils.getIdFields(entity.getClass());
            for (Field field : idFields) {
                Object value = getFieldValue(entity, field);
                if (value == null) {
                    throw new SpineException(
                            "Composite key component '" + field.getName() + "' cannot be null in entity " +
                                    entity.getClass().getSimpleName());
                }
                keyValueMap.put(field.getName(), value);
            }
        }

        return CompositeKeyUtils.formatCompositeKeyWithMapping(keyValueMap);
    }

    /**
     * Extracts values from an @EmbeddedId entity by accessing the embedded ID object.
     */
    private static Map<String, Object> extractEmbeddedIdValues(Object entity) throws SpineException {
        Map<String, Object> keyValueMap = new HashMap<>();

        // Find the @EmbeddedId field on the entity
        Field embeddedIdField = findEmbeddedIdField(entity.getClass());
        if (embeddedIdField == null) {
            throw new SpineException("No @EmbeddedId field found in entity " + entity.getClass().getSimpleName());
        }

        try {
            embeddedIdField.setAccessible(true);
            Object embeddedIdObject = embeddedIdField.get(entity);

            if (embeddedIdObject == null) {
                throw new SpineException("@EmbeddedId object is null in entity " + entity.getClass().getSimpleName());
            }

            List<Field> idFields = CompositeKeyUtils.getIdFields(entity.getClass());
            for (Field field : idFields) {
                Object value = getFieldValue(embeddedIdObject, field);
                if (value == null) {
                    throw new SpineException(
                            "Composite key component '" + field.getName() + "' cannot be null in entity " +
                                    entity.getClass().getSimpleName());
                }
                keyValueMap.put(field.getName(), value);
            }
        } catch (Exception e) {
            throw new SpineException(
                    "Failed to extract embedded ID values from entity " + entity.getClass().getSimpleName(), e);
        }

        return keyValueMap;
    }

    /**
     * Sets the ID value on an entity using reflection.
     * Enhanced to support both single and composite keys with new format.
     * For composite keys, expects CompositeKeyData, formatted string, or explicit key-value mapping.
     *
     * @param entity The entity on which to set the ID
     * @param id     The ID value to set (single value, CompositeKeyData, or composite string)
     * @throws SpineException if reflection operations fail
     */
    public static void setIdOnEntity(Object entity, Object id) throws SpineException {
        Objects.requireNonNull(entity, "Entity cannot be null");
        Objects.requireNonNull(id, "ID cannot be null");

        Class<?> clazz = entity.getClass();

        if (CompositeKeyUtils.hasCompositeKey(clazz)) {
            setCompositeIdOnEntityWithMapping(entity, id);
            return;
        }

        Field idField = findIdField(clazz);

        if (idField == null) {
            throw new SpineException("No @Id annotated field found in entity class: " + clazz.getSimpleName());
        }

        setFieldValue(entity, idField, id);
    }

    /**
     * Sets composite ID values on an entity with support for new key-value mapping format.
     *
     * @param entity The entity on which to set the composite ID
     * @param id     The composite ID (CompositeKeyData, key-value mapping string, or legacy format)
     * @throws SpineException if setting fails
     */
    private static void setCompositeIdOnEntityWithMapping(Object entity, Object id) throws SpineException {
        CompositeKeyUtils.CompositeKeyData keyData;

        if (id instanceof CompositeKeyUtils.CompositeKeyData compositeKeyData) {
            keyData = compositeKeyData;
        } else if (id instanceof String idString) {
            keyData = CompositeKeyUtils.createCompositeKeyData(idString, entity.getClass());
        } else {
            throw new SpineException("Invalid composite key type: " + id.getClass().getSimpleName() +
                    ". Expected CompositeKeyData or String with format {key:value}_{key:value}.");
        }
        CompositeKeyUtils.CompositeKeyType keyType = keyData.getKeyType();
        if (keyType == CompositeKeyUtils.CompositeKeyType.EMBEDDED_ID) {
            setEmbeddedIdFields(entity, keyData);
        } else {
            setDirectIdFields(entity, keyData);
        }
    }

    /**
     * Sets fields directly on the entity for MULTIPLE_ID and ID_CLASS composite key types.
     */
    private static void setDirectIdFields(Object entity, CompositeKeyUtils.CompositeKeyData keyData)
            throws SpineException {
        for (CompositeKeyUtils.CompositeKeyData.KeyComponent component : keyData.getComponents()) {
            try {
                Field field = entity.getClass().getDeclaredField(component.fieldName());
                setFieldValue(entity, field, component.value());
            } catch (NoSuchFieldException e) {
                throw new SpineException("Field '" + component.fieldName() + "' not found in entity " +
                        entity.getClass().getSimpleName(), e);
            }
        }
    }

    /**
     * Sets fields on the embedded ID object for EMBEDDED_ID composite key type.
     */
    private static void setEmbeddedIdFields(Object entity, CompositeKeyUtils.CompositeKeyData keyData)
            throws SpineException {
        // Find the @EmbeddedId field on the entity
        Field embeddedIdField = findEmbeddedIdField(entity.getClass());
        if (embeddedIdField == null) {
            throw new SpineException("No @EmbeddedId field found in entity " + entity.getClass().getSimpleName());
        }

        try {
            embeddedIdField.setAccessible(true);
            Object embeddedIdObject = embeddedIdField.get(entity);

            if (embeddedIdObject == null) {
                embeddedIdObject = embeddedIdField.getType().getDeclaredConstructor().newInstance();
                embeddedIdField.set(entity, embeddedIdObject);
            }

            for (CompositeKeyUtils.CompositeKeyData.KeyComponent component : keyData.getComponents()) {
                try {
                    Field field = embeddedIdField.getType().getDeclaredField(component.fieldName());
                    setFieldValue(embeddedIdObject, field, component.value());
                } catch (NoSuchFieldException e) {
                    throw new SpineException("Field '" + component.fieldName() + "' not found in embedded ID class " +
                            embeddedIdField.getType().getSimpleName(), e);
                }
            }
        } catch (Exception e) {
            throw new SpineException("Failed to set embedded ID fields on entity " + entity.getClass().getSimpleName(),
                    e);
        }
    }

    /**
     * Finds the field annotated with @EmbeddedId in the given class.
     */
    private static Field findEmbeddedIdField(Class<?> clazz) {
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            for (Field field : currentClass.getDeclaredFields()) {
                if (field.isAnnotationPresent(EmbeddedId.class)) {
                    return field;
                }
            }
            currentClass = currentClass.getSuperclass();
        }
        return null;
    }

    /**
     * Finds the field annotated with @Id in the given class.
     * Searches through the class hierarchy if necessary.
     *
     * @param clazz The class to search
     * @return The @Id annotated field, or null if not found
     */
    private static Field findIdField(Class<?> clazz) {
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            for (Field field : currentClass.getDeclaredFields()) {
                if (field.isAnnotationPresent(Id.class)) {
                    return field;
                }
            }
            currentClass = currentClass.getSuperclass();
        }
        return null;
    }

    /**
     * Gets the value of a field from an entity using reflection.
     *
     * @param entity The entity object
     * @param field  The field to read
     * @return The field value
     * @throws SpineException if reflection operations fail
     */
    private static Object getFieldValue(Object entity, Field field) throws SpineException {
        try {
            field.setAccessible(true);
            return field.get(entity);
        } catch (IllegalAccessException e) {
            throw new SpineException("Failed to access field '" + field.getName() +
                    "' in entity " + entity.getClass().getSimpleName(), e);
        }
    }

    /**
     * Sets the value of a field on an entity using reflection.
     *
     * @param entity The entity object
     * @param field  The field to set
     * @param value  The value to set
     * @throws SpineException if reflection operations fail
     */
    private static void setFieldValue(Object entity, Field field, Object value) throws SpineException {
        try {
            field.setAccessible(true);

            // Convert value to appropriate type if necessary
            Object convertedValue = convertValueToFieldType(value, field.getType());
            field.set(entity, convertedValue);
        } catch (IllegalAccessException | IllegalArgumentException e) {
            throw new SpineException("Failed to set field '" + field.getName() +
                    "' in entity " + entity.getClass().getSimpleName(), e);
        }
    }

    /**
     * Converts a value to the target field type if necessary.
     *
     * @param value      The value to convert
     * @param targetType The target field type
     * @return The converted value
     * @throws SpineException if conversion fails
     */
    private static Object convertValueToFieldType(Object value, Class<?> targetType) {
        if (value == null) {
            return null;
        }

        // If types already match, return as-is
        if (targetType.isAssignableFrom(value.getClass())) {
            return value;
        }

        // Handle string-to-type conversions
        if (value instanceof String stringValue) {
            return getValue(stringValue, targetType);
        }
        return value;
    }

    private static Object getValue(String idStr, Class<?> idClass) {
        if (idClass == String.class) {
            return idStr;
        } else if (idClass == Long.class || idClass == long.class) {
            return Long.valueOf(idStr);
        } else if (idClass == Integer.class || idClass == int.class) {
            return Integer.valueOf(idStr);
        } else if (idClass == Double.class || idClass == double.class) {
            return Double.valueOf(idStr);
        } else if (idClass == Float.class || idClass == float.class) {
            return Float.valueOf(idStr);
        } else if (idClass == Boolean.class || idClass == boolean.class) {
            return Boolean.valueOf(idStr);
        } else if (idClass == Short.class || idClass == short.class) {
            return Short.valueOf(idStr);
        } else if (idClass == Byte.class || idClass == byte.class) {
            return Byte.valueOf(idStr);
        } else {
            log.warn("Unsupported ID type: {}. Returning as string.", idClass.getSimpleName());
            return idStr;
        }
    }

}
