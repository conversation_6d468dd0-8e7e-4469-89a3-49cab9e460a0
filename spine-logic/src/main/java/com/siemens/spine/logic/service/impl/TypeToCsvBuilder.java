package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.entity.TypeEntity;
import com.siemens.spine.logic.util.CSVUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 4/1/2023
 */
public class TypeToCsvBuilder {

    /**
     * extract type to csv builder
     *
     * @param type
     * @return
     */
    public static StringBuilder extractTypeCSV(TypeEntity type) {
        StringBuilder typeCsv = new StringBuilder();
        typeCsv.append(CSVUtils.addParam(type.getTypeId())); //0
        typeCsv.append(CSVUtils.addParam(type.getMainType())); //1
        typeCsv.append(CSVUtils.addParam(type.getSubType())); //2
        typeCsv.append(CSVUtils.addParam(type.getSpecialType())); //3
        typeCsv.append(CSVUtils.addParam(type.getTypeVersion())); //4
        typeCsv.append(CSVUtils.addParam(type.getDescription()));//5
        typeCsv.append(CSVUtils.addParam(type.getMechMaterialCostKpi()));//6
        typeCsv.append(CSVUtils.addParam(type.getEngineeringMeHKpi()));//7
        typeCsv.append(CSVUtils.addParam(type.getEngineeringHwHPki()));//8
        typeCsv.append(CSVUtils.addParam(type.getEngineeringPlcHKpi()));//9
        typeCsv.append(CSVUtils.addParam(type.getEngineeringItHKpi()));//10
        if (type.getProjectSpecific() != null) {
            typeCsv.append(CSVUtils.addParam(
                    type.getProjectSpecific() ? type.getProject().getProjectID().toString() : ""));//11
        } else {
            typeCsv.append(CSVUtils.addParam(""));//11
        }
        typeCsv.append(CSVUtils.addParam(type.getUrl1()));//12
        typeCsv.append(CSVUtils.addParam(type.getUrl2()));//13
        typeCsv.append(CSVUtils.addParam(type.getUrl3()));//14
        typeCsv.append(CSVUtils.addParam(type.getUrl4()));//15
        typeCsv.append(CSVUtils.addParam(type.getUrl5()));//16
        typeCsv.append(CSVUtils.addParam(type.getTypeVariant()));//17
        typeCsv.append(CSVUtils.addParam(type.getDefaultSlope()));//18
        typeCsv.append(CSVUtils.addParam(type.getDefaultDriveCount()));//19
        typeCsv.append(CSVUtils.addParam(type.getVersionNumber()));//20
        typeCsv.append(CSVUtils.addParam(type.getSupplier()));//21
        typeCsv.append(CSVUtils.addParam(type.getEngineeringScadaHKpi()));//22
        typeCsv.append(CSVUtils.addParam(type.getSapMaterialNumber()));//23
        typeCsv.append(CSVUtils.addParam(type.getMps()));//24
        typeCsv.append(CSVUtils.addParam(type.getInstallationHKpi()));//25
        typeCsv.append(CSVUtils.addParam(type.getPlcCommissioningHKpi()));//26
        typeCsv.append(CSVUtils.addParam(type.getBeltMass()));//27
        typeCsv.append(CSVUtils.addParam(type.getRequiredAcceleration()));//28
        typeCsv.append(CSVUtils.addParam(type.getDrivePulleyDiameter()));//29
        typeCsv.append(CSVUtils.addParam(type.getFrictionFactor()));//30
        typeCsv.append(CSVUtils.addParam(type.getDriveShaftDiameter()));//31
        typeCsv.append(CSVUtils.addParam(type.getEfficiencyGearbox()));//32
        typeCsv.append(CSVUtils.addParam(type.getTypeAggregator()));//33
        typeCsv.append(CSVUtils.addParam(type.getOriginalType()));//34
        typeCsv.append(CSVUtils.addParam(
                type.getProjectTypeClassification() == null ? "" : type.getProjectTypeClassification().fullName));//35
        typeCsv.append(CSVUtils.addParam(type.getHierarchyType()));//36
        typeCsv.append(CSVUtils.addParam(type.getOutdated() ? "1" : "0"));//37

        typeCsv.append(CSVUtils.NEW_LINE);
        return typeCsv;
    }

}
