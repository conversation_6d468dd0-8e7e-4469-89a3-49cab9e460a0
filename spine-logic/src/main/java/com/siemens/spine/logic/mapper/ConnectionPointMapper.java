package com.siemens.spine.logic.mapper;

import com.siemens.spine.db.entity.ConnectionPointEntity;
import com.siemens.spine.generated.toolchain.ConnectionPoint;
import com.siemens.spine.logic.dto.ConnectionPointDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = { NeighborConnectionMapper.class })
public interface ConnectionPointMapper {

    ConnectionPointMapper INSTANCE = Mappers.getMapper(ConnectionPointMapper.class);

    @Mapping(target = "component", ignore = true)
    ConnectionPointEntity toEntity(ConnectionPointDto dto);

    List<ConnectionPointDto> wsToDtoList(List<ConnectionPoint> entities);

}
