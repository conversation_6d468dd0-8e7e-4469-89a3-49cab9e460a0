package com.siemens.spine.logic.validator.query;

import com.siemens.spine.logic.validator.query.handler.QueryValidationHandler;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.inject.Named;

/**
 * Factory for creating query validation chains
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17/06/2025
 */
@ApplicationScoped
public class QueryValidatorFactory {

    private final QueryValidationHandler queryTypeValidationHandler;

    private final QueryValidationHandler sqlInjectionValidationHandler;

    private final QueryValidationHandler sqlSyntaxValidationHandler;

    @Inject
    public QueryValidatorFactory(@Named("queryTypeValidationHandler") QueryValidationHandler queryTypeValidationHandler,
                                 @Named("sqlInjectionValidationHandler") QueryValidationHandler sqlInjectionValidationHandler,
                                 @Named("sqlSyntaxValidationHandler") QueryValidationHandler sqlSyntaxValidationHandler) {
        this.sqlInjectionValidationHandler = sqlInjectionValidationHandler;
        this.sqlSyntaxValidationHandler = sqlSyntaxValidationHandler;
        this.queryTypeValidationHandler = queryTypeValidationHandler;
        createValidationChain();
    }

    /**
     * Creates a validation chain for query validation
     * <p>
     * Start from syntax validation, then query type, then injection
     *
     * @return The first handler in the chain
     */
    public QueryValidationHandler createValidationChain() {
        sqlSyntaxValidationHandler.setNext(queryTypeValidationHandler);
        queryTypeValidationHandler.setNext(sqlInjectionValidationHandler);
        return sqlSyntaxValidationHandler;
    }

}
