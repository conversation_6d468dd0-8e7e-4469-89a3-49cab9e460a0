package com.siemens.spine.logic.service;

import com.siemens.spine.db.constant.GroupTypeEnum;
import com.siemens.spine.db.entity.GroupEntity;
import com.siemens.spine.generated.toolchain.Group;
import com.siemens.spine.generated.toolchain.GroupItemData;
import com.siemens.spine.logic.dto.GroupDto;
import com.siemens.spine.logic.dto.request.GroupRequestDto;
import com.siemens.spine.logic.exception.SpineException;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 26/12/2022
 */
public interface GroupService {

    List<GroupItemData> getGroupItems(String projectName, Group group, GroupItemData groupItemData);

    List<GroupEntity> findByProjectNameAndGroup(String projectName, Group group) throws SpineException;

    List<GroupDto> findByProjectId(Long projectId);

    void deleteListGroup(List<Long> groupIds) throws SpineException;

    List<GroupDto> updateListGroup(Long projectId, GroupRequestDto groupRequestDto) throws SpineException;

    List<GroupDto> findComponentGroup(Long componentId);

    boolean addComponentToGroups(Long projectId, Map<GroupTypeEnum, String> groupInfo, Long componentId);

    boolean removeComponentFromGroups(Map<GroupTypeEnum, String> groupInfo, Long componentId);

    boolean deleteGroup(String projectName, GroupItemData group, boolean force);

    boolean renameGroup(String projectName, GroupItemData group, String newName);

}
