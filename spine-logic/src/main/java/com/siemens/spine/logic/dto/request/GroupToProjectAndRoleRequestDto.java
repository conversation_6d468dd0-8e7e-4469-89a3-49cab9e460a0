package com.siemens.spine.logic.dto.request;

import com.siemens.spine.db.constant.RoleEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GroupToProjectAndRoleRequestDto {

    private String userGroup;

    private Long projectId;

    @Enumerated(EnumType.STRING)
    private RoleEnum roleName;

}
