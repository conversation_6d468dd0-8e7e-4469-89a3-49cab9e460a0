package com.siemens.spine.logic.service;

import com.siemens.spine.db.constant.ProjectState;
import com.siemens.spine.db.constant.ProjectType;
import com.siemens.spine.generated.toolchain.Project;
import com.siemens.spine.logic.dto.ProjectResponseDTO;
import com.siemens.spine.logic.dto.request.ProjectRequestDTO;
import com.siemens.spine.logic.exception.SpineException;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 21/12/2022
 */
public interface ProjectService {

    List<? extends Project> getAllProjects();

    List<ProjectResponseDTO> getAllProjectForRest();

    Boolean checkProjectNameExits(String projectName);

    Long getProjectIdByName(String projectName) throws SpineException;

    ProjectResponseDTO createProject(ProjectRequestDTO projectRequestDTO) throws SpineException;

    List<ProjectType> getListTypesProject();

    List<ProjectState> getListStateProject();

    ProjectResponseDTO changeProjectState(Long projectId, ProjectState newState) throws SpineException;

}
