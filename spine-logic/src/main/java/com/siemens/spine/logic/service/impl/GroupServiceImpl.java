package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.constant.GroupTypeEnum;
import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.GroupEntity;
import com.siemens.spine.db.entity.ProjectEntity;
import com.siemens.spine.db.repository.ComponentRepository;
import com.siemens.spine.db.repository.GroupRepository;
import com.siemens.spine.db.repository.ProjectRepository;
import com.siemens.spine.generated.toolchain.Group;
import com.siemens.spine.generated.toolchain.GroupItemData;
import com.siemens.spine.generated.toolchain.SubgroupItemData;
import com.siemens.spine.logic.dto.GroupDto;
import com.siemens.spine.logic.dto.request.GroupRequestDto;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.mapper.GroupMapper;
import com.siemens.spine.logic.service.ComponentStateService;
import com.siemens.spine.logic.service.GroupService;
import com.siemens.spine.logic.util.ComponentUtils;
import com.siemens.spine.logic.util.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> Pham
 * @version 1.0
 * @since 26/12/2022
 */
@ApplicationScoped
@Transactional(value = Transactional.TxType.REQUIRED, rollbackOn = Exception.class)
@Slf4j
public class GroupServiceImpl implements GroupService {

    @Inject
    private ProjectRepository projectRepository;

    @Inject
    private ComponentRepository componentRepository;

    @Inject
    private GroupRepository groupRepository;

    @Inject
    private ComponentStateService componentStateService;

    @Override
    public List<GroupItemData> getGroupItems(String projectName, Group group, GroupItemData groupItemData) {
        List<GroupEntity> groups = findByProjectNameAndGroup(projectName, group);

        List<GroupItemData> groupItems = new ArrayList<>();
        for (GroupEntity groupEntity : groups) {
            GroupItemData groupItem = new GroupItemData();
            groupItem.setGroupType(groupEntity.getGrouptype().name());
            groupItem.setGroupName(groupEntity.getName());
            groupItem.setComment(groupEntity.getComment());
            if (groupEntity.getDeliverydate() != null) {
                groupItem.setDeliveryDate(
                        DateTimeUtils.toXMLGregorianCalendar(groupEntity.getDeliverydate().toLocalDateTime()));
            }
            if (GroupTypeEnum.ProjectView.equals(groupEntity.getGrouptype())) {
                List<GroupEntity> subGroups = groupRepository.findSubGroupsByProjectIdAndGroupNameAndGroupType(
                        groupEntity.getProjectId(), groupEntity.getName(),
                        GroupTypeEnum.resolve(groupEntity.getSubtype()));
                for (GroupEntity subGroupEntity : subGroups) {
                    SubgroupItemData subGroup = new SubgroupItemData();
                    subGroup.setGroupComponentID(subGroupEntity.getId().toString());
                    subGroup.setGroupName(subGroupEntity.getName());
                    subGroup.setGroupType(subGroupEntity.getGrouptype().name());
                    subGroup.setComment(subGroupEntity.getComment());
                    if (subGroupEntity.getDeliverydate() != null) {
                        subGroup.setDeliveryDate(
                                DateTimeUtils.toXMLGregorianCalendar(
                                        subGroupEntity.getDeliverydate().toLocalDateTime()));
                    }
                    groupItem.getSubgroups().add(subGroup);
                }
            }
            groupItem.setGroupComponentID(groupEntity.getId().toString());
            groupItems.add(groupItem);
        }

        return groupItems;
    }

    @Override
    public List<GroupEntity> findByProjectNameAndGroup(String projectName, Group group) {
        List<GroupEntity> groups = new ArrayList<>();
        Map<GroupTypeEnum, String> groupMapping = ComponentUtils.getGroupInfo(group);
        for (Map.Entry<GroupTypeEnum, String> entry : groupMapping.entrySet()) {
            GroupTypeEnum type = entry.getKey();
            String value = entry.getValue();
            log.debug(String.format("Finding group with type '%s' and name '%s'", type, value));

            if (("*").equals(value)) {
                groups.addAll(groupRepository.findGroupsByProjectNameAndGroupNameAndGroupType(projectName,
                        null, type));
            } else {
                groups.addAll(groupRepository.findGroupsByProjectNameAndGroupNameAndGroupType(projectName,
                        List.of(value), type));
            }
        }

        return groups;
    }

    @Override
    public List<GroupDto> findByProjectId(Long projectId) {
        List<GroupEntity> groups = groupRepository.findGroupsByProjectId(projectId);
        return GroupMapper.INSTANCE.toGroups(groups);
    }

    @Override
    public void deleteListGroup(List<Long> groupIds) throws SpineException {
        List<GroupEntity> groupsNotEmpty = groupRepository.findGroupsNotEmpty(groupIds);
        if (CollectionUtils.isEmpty(groupsNotEmpty)) {
            int deleted = groupRepository.deleteAll(groupIds);
            log.info("Delete {} groups", deleted);
        } else {
            log.info("Group not empty to delete");
            throw new SpineException("Group is not empty");
        }
    }

    @Override
    public List<GroupDto> updateListGroup(Long projectId, GroupRequestDto groupRequestDto) throws SpineException {

        if (groupRequestDto == null || projectId == null) {
            throw new SpineException("Body must not be null");
        }

        if (groupRequestDto.getDeliverydate() == null) {
            throw new SpineException("Please enter the delivery date");
        }

        if (CollectionUtils.isEmpty(groupRequestDto.getIds())) {
            throw new SpineException("List groupId must not be empty");
        }

        List<GroupEntity> groups = groupRepository.findGroupsByProjectIdAndGroupIdIn(projectId,
                groupRequestDto.getIds());
        List<GroupEntity> groupNeedToUpdate = groups.stream().map(groupEntity -> {
            groupEntity.setComment(groupRequestDto.getComment());
            if (groupRequestDto.getDeliverydate() != null) {
                groupEntity.setDeliverydate(groupRequestDto.getDeliverydate());
            }
            return groupEntity;
        }).toList();
        return GroupMapper.INSTANCE.toGroups(groupRepository.saveAll(groupNeedToUpdate));
    }

    @Override
    public List<GroupDto> findComponentGroup(Long componentId) {
        if (componentId == null) {
            return Collections.emptyList();
        }

        List<GroupEntity> componentGroups = groupRepository.findComponentGroup(componentId);
        return GroupMapper.INSTANCE.toGroups(componentGroups);
    }

    @Override
    public boolean addComponentToGroups(Long projectId, Map<GroupTypeEnum, String> groupInfo, Long componentId) {
        Optional<ProjectEntity> projectOpt = projectRepository.findById(projectId);
        if (projectOpt.isEmpty()) {
            log.error("No project was found by id: " + projectId);
            return false;
        }

        Optional<ComponentEntity> componentEntity = componentRepository.findById(componentId);
        if (componentEntity.isEmpty()) {
            log.error("No component was found by id: " + componentId);
            return false;
        }

        ProjectEntity projectEntity = projectOpt.get();
        for (Map.Entry<GroupTypeEnum, String> entry : groupInfo.entrySet()) {
            GroupTypeEnum type = entry.getKey();
            String groupName = entry.getValue();
            if (type == null || StringUtils.isEmpty(groupName)) {
                continue;
            }

            GroupEntity groupEntity;
            List<GroupEntity> groups = groupRepository.findGroupsByProjectNameAndGroupNameAndGroupType(
                    projectEntity.getProjectName(), List.of(groupName), type);
            if (groups.isEmpty()) {
                // do create new group
                GroupEntity newGroup = GroupEntity.builder()
                        .name(groupName)
                        .grouptype(type)
                        .projectId(projectEntity.getProjectID())
                        .deliverydate(new Timestamp(System.currentTimeMillis()))
                        .build();
                groupEntity = groupRepository.save(newGroup);
                log.info(String.format("Created new group '%s' with type '%s' in project %s", groupName, type,
                        projectEntity.getProjectName()));
            } else {
                groupEntity = groups.get(0);
            }

            if (groupEntity.getComponents() == null) {
                groupEntity.setComponents(new HashSet<>());
            }

            groupEntity.getComponents().add(componentEntity.get());
            groupRepository.save(groupEntity);
            log.info(String.format("Adding component %d to group '%s - %s' completed", componentId, type, groupName));
        }

        return true;
    }

    @Override
    public boolean removeComponentFromGroups(Map<GroupTypeEnum, String> groupInfo, Long componentId) {
        Optional<ComponentEntity> componentOpt = componentRepository.findById(componentId);
        if (componentOpt.isEmpty()) {
            log.error("No component was found by id: " + componentId);
            return false;
        }

        ComponentEntity componentEntity = componentOpt.get();
        List<GroupEntity> componentGroups = groupRepository.findComponentGroup(componentId);
        for (GroupEntity group : componentGroups) {
            String groupName = group.getName();
            GroupTypeEnum type = group.getGrouptype();

            if (groupInfo.get(type) != null && groupInfo.get(type).equals(groupName)) {
                group.getComponents().remove(componentEntity);
                groupRepository.save(group);

                log.info(String.format("Removed component '%s' from group '%s - %s'", componentId, type, groupName));
            }
        }

        return true;
    }

    @Override
    public boolean deleteGroup(String projectName, GroupItemData group, boolean force) {
        GroupTypeEnum groupType = GroupTypeEnum.resolve(group.getGroupType());
        if (groupType == null) {
            return false;
        }
        List<GroupEntity> groups = groupRepository.findGroupsByProjectNameAndGroupNameAndGroupType(projectName,
                List.of(group.getGroupName()), groupType);
        if (CollectionUtils.isEmpty(groups)) {
            return false;
        }
        List<Long> componentIds = componentRepository.findIdsByProjectNameAndGroupTypeAndGroupIds(projectName,
                groupType, List.of(group.getGroupName()));
        if (force && CollectionUtils.isNotEmpty(componentIds)) {
            try {
                log.info("Set state");
                componentStateService.markAsDeleted(componentIds);
            } catch (SpineException e) {
                log.error(e.getMessage());
                return false;
            }
        } else {
            return false;
        }
        for (GroupEntity groupEntity : groups) {
            groupRepository.delete(groupEntity);
        }
        return true;
    }

    @Override
    public boolean renameGroup(String projectName, GroupItemData group, String newName) {
        GroupTypeEnum groupType = GroupTypeEnum.resolve(group.getGroupType());
        if (groupType == null) {
            return false;
        }
        List<GroupEntity> groups = groupRepository.findGroupsByProjectNameAndGroupNameAndGroupType(projectName,
                List.of(group.getGroupName()), groupType);
        if (CollectionUtils.isEmpty(groups)) {
            return false;
        }
        for (GroupEntity groupEntity : groups) {
            for (ComponentEntity component : groupEntity.getComponents()) {
                groupEntity.removeComponent(component);
            }
            groupEntity.setName(newName);
        }
        return true;
    }

}
