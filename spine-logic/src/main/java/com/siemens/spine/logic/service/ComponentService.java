package com.siemens.spine.logic.service;

import com.siemens.spine.generated.toolchain.ComponentSpecificationMember;
import com.siemens.spine.generated.toolchain.DecompositionAttributes;
import com.siemens.spine.generated.toolchain.DecompositionList;
import com.siemens.spine.generated.toolchain.Group;
import com.siemens.spine.logic.dto.ComponentConditionDTO;
import com.siemens.spine.logic.dto.ComponentDTO;
import com.siemens.spine.logic.dto.ConnectionPointDto;
import com.siemens.spine.logic.dto.DecompositionDto;
import com.siemens.spine.logic.exception.SpineException;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 */
public interface ComponentService {

    /**
     * create new component
     *
     * @return
     */
    List<Long> importComponents(String projectName, List<ComponentDTO> request) throws SpineException;

    /**
     * Get component by unique id
     *
     * @param projectName
     * @param componentIds
     * @return
     */
    List<String> getCSVComponentsByUniqueIds(String projectName, List<Long> componentIds);

    List<ComponentDTO> getComponentsByCriteria(ComponentConditionDTO condition);

    List<String> getComponentsBySpecification(String projectName,
                                              List<ComponentSpecificationMember> specificationMembers,
                                              int start,
                                              int end) throws SpineException;

    List<DecompositionDto> getUnfinishedMaterial(Long projectId) throws SpineException;

    List<DecompositionList> getDecompositions(Long componentId);

    List<DecompositionAttributes> getDecompositionAttributes(List<String> sapNumbers);

    void createDecompositionAttributes(List<DecompositionAttributes> attributes);

    List<ConnectionPointDto> getConnectionPoints(Long componentId);

    List<Long> getUniqueIdsByProjectAndGroup(String projectName, Group axgroup) throws SpineException;

    List<Long> getUniqueIdsFromList(String projectName, String listTypes, List<Group> groups, boolean getFullData);

    void delete(List<Long> componentIds) throws SpineException;

    List<Long> getUniqueIdsByProject(String projectName);

    List<ComponentDTO> getComponentsByProjectId(Long projectId);

    ComponentDTO getComponentByIdAtRevision(Long projectId, Long componentId, Integer revision);

}
