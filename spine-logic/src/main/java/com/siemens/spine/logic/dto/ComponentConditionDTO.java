package com.siemens.spine.logic.dto;

import com.siemens.spine.db.constant.GroupTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 22/12/2022
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ComponentConditionDTO {

    private Long projectId;

    private String specificProjectName;

    private List<Long> componentIds;

    private String status;

    private LocalDateTime from;

    private LocalDateTime to;

    private String searchForDateType;

    // pair between group type and list of group names
    private Pair<GroupTypeEnum, List<String>> groupMapping;

    private String reference;

    private String sapMaterialNumber;

    private String mpsPrefix;

    private String derivedFrom;

    private Boolean driveRequired;

    private Boolean driveAttached;

}
