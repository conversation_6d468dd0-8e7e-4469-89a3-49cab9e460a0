package com.siemens.spine.logic.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 22/12/2022
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class ProjectResponseDTO {

    private Double averageObjectLengthForBelt;
    private Double averageObjectLengthForOOGTray;
    private Double averageObjectLengthForTray;
    private Double averageWeightBag;
    private Double averageWeightOversizeBag;
    private Double bagWeight;
    private String beltDriveVendor;
    private String brakeRelatedZOptionsDol;
    private String brakeRelatedZOptionsVfd;
    private Double brakeResistorCycleTime;
    private String brakingResistor;
    private String client;
    private String customerName;
    private String fieldbus;
    private Double gearboxServiceFactor;
    private String generalZOptions;
    private String increasedVfdSize;
    private String mainProjectState;
    private Double maxInputRpmGearbox;
    private Double maxRelativeHumidity;
    private Double minGapTimeForBelt;
    private Double minGapTimeForTray;
    private String motorEfficiencyClass;
    private Double motorServiceFactor;
    private Double motorStarterDriveServiceFactor;
    private String mountingType;
    private Double operatingTemperatureHigh;
    private Double operatingTemperatureLow;
    private Double oversizeBagWeight;
    private Double oversizeTrayWeight;
    private String packageType;
    private Long projectID;
    private String projectLeader;
    private Double relativeDutyFactor;
    private String repairSwitch;
    private String sapProjectKey;
    private String siteName;
    private Double supplyFrequency;
    private Double supplyVoltage;
    private String trayDriveVendor;
    private Double trayStackSize;
    private Double trayWeight;
    private String type;
    private Double vfdAccelerationTorque;
    private Double vfdDriveServiceFactor;
    private Double vfdOutputVoltage;
    private String vfdType;
    private Double weightOfBelt;
    private Boolean withSimulation;
    private String projectName;

}
