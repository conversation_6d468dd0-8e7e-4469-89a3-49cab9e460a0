package com.siemens.spine.logic.service;

import com.siemens.spine.generated.toolchain.MarkAsDoneData;
import com.siemens.spine.logic.dto.ComponentStatusDto;
import com.siemens.spine.logic.exception.SpineException;

import java.util.List;

public interface ComponentStatusService {

    /**
     * Find the component status by project name and list of component id
     *
     * @param projectName
     * @param uniqueIds
     * @return
     */
    List<ComponentStatusDto> getComponentStatuses(String projectName, List<Long> uniqueIds);

    /**
     * @param projectName
     * @param simulationState
     * @param drawing
     */
    void setSimulationStateForDrawing(String projectName, String simulationState, String drawing) throws SpineException;

    /**
     * @param projectName
     * @param calculationState
     * @param drawing
     */
    void setCalculationStateForDrawing(String projectName, String calculationState, String drawing)
            throws SpineException;

    /**
     * @param projectName
     * @param bomState
     */
    boolean setBOMState(String projectName, String bomState, List<Long> componentIds) throws SpineException;

    /**
     * @param project
     * @param operationName
     * @param state
     * @param componentUniqueIds
     * @return
     * @throws SpineException
     */
    void setComponentsStatusInList(String project, String operationName, String state, List<Long> componentUniqueIds)
            throws SpineException;

    /**
     * @param projectName
     * @param data
     */
    void markAsDone(String projectName, List<MarkAsDoneData> data) throws SpineException;

    /**
     * Find the component status by project name and list of component id
     *
     * @param projectName
     * @param uniqueIds
     * @return
     */
    List<ComponentStatusDto> getChangeComponentStatuses(String projectName, List<Long> uniqueIds);

}
