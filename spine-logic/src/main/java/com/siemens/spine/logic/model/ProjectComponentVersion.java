package com.siemens.spine.logic.model;

import com.siemens.spine.db.entity.ComponentReferenceSet;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
public class ProjectComponentVersion extends BaseVersion {

    private Long id;

    private Long projectId;

    private Integer rev;

    private Timestamp sysCreateDate;

    private Timestamp sysModDate;

    private String createdBy;

    private String message;

    private String versionName;

    private ComponentReferenceSet componentRefs;

    private boolean isCurrent = false;

}
