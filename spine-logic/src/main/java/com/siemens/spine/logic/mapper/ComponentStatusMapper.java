package com.siemens.spine.logic.mapper;

import com.siemens.spine.db.entity.ComponentStatusEntity;
import com.siemens.spine.generated.toolchain.ListStatus;
import com.siemens.spine.logic.dto.ComponentStatusDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
public interface ComponentStatusMapper {

    ComponentStatusMapper INSTANCE = Mappers.getMapper(ComponentStatusMapper.class);

    @Mapping(source = "component.id", target = "uniqueID")
    ComponentStatusDto toComponentStatusDto(ComponentStatusEntity status);

    List<ComponentStatusDto> toComponentStatusDtoList(List<ComponentStatusEntity> statuses);

    ListStatus toStatusWs(ComponentStatusDto dto);

    List<ListStatus> toStatusWsList(List<ComponentStatusDto> statuses);

}
