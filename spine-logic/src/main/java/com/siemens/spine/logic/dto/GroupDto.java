package com.siemens.spine.logic.dto;

import com.siemens.spine.db.constant.GroupTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GroupDto {

    private Long id;

    private String name;

    private GroupTypeEnum grouptype;

    private Timestamp deliverydate;

    private String comment;

    private String subtype;

}
