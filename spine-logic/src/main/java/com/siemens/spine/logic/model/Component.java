package com.siemens.spine.logic.model;

import lombok.Data;

import javax.xml.datatype.XMLGregorianCalendar;
import java.util.List;

@Data
public class Component {

    protected Long id;
    protected String versionRelease;
    protected String akz;
    protected Double acceleration;
    protected Integer amountBufferElec;
    protected Integer amountBufferMech;
    protected String angleCorr;
    protected String autoCadLayer;
    protected String breakType;
    protected Integer bufferSize;
    protected String buildSection;
    protected String buildingSection;
    protected String calculationArea;
    protected String colorGroup;
    protected String comment;
    protected List<ConnectionPoint> connectionPoints;
    protected String controlNr;
    protected XMLGregorianCalendar creationDate;
    protected Integer curveAngle;
    protected String curveDirection;
    protected Integer curveRadius;
    protected String customerAKZ;
    protected List<Decomposition> decompositions;
    protected String designPlant;
    protected String drawing;
    protected String drawingRevision;
    protected String drawingVersion;
    protected Double drivePulleyDiameter;
    protected Double driveShaftDiameter;
    protected String driveSide;
    protected String drivePosition;
    protected String driveStation;
    protected String eStopGroup;
    protected Integer engineeringHours;
    protected Integer height;
    protected Integer installationHours;
    protected String installationSection;
    protected XMLGregorianCalendar lastModificationDate;
    protected Integer lengthTotal;
    protected String levelEnd;
    protected String levelStart;
    protected String lineName;
    protected Double load;
    protected String motorController;
    protected String motorDirection;
    protected String motorPosition;
    protected String orderPlant;
    protected String outfit3D;
    protected String plcArea;
    protected String parcelTypeID;
    protected String parent;
    protected String plantDomain;
    protected String posNo;
    protected Position position;
    protected String reference;
    protected Integer rotation;
    protected Double rotation3D;
    protected String screen;
    protected Double section1Angle;
    protected Double section1Length;
    protected Double section2Angle;
    protected Double section2Length;
    protected Double section3Angle;
    protected Double section3Length;
    protected Double section4Angle;
    protected Double section4Length;
    protected String sequenceGroup;
    protected String slaveDrive;
    protected Double slope;
    protected String speed;
    protected Integer startStopCycles;
    protected State state;
    protected Boolean storageConveyor;
    protected String supplier;
    protected Integer throughput;
    protected String typeID;
    protected String uniqueID;
    protected Boolean unitReversible;
    protected String usage;
    protected boolean useConnectionPoints;
    protected boolean useDecompositions;
    protected boolean useState;
    protected String user1;
    protected String user2;
    protected String user3;
    protected String user4;
    protected String user5;
    protected String vaultInstance;
    protected Integer version;
    protected String virtual;
    protected Integer width;

}
