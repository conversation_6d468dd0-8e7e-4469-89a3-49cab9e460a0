package com.siemens.spine.logic.dto;

import com.siemens.spine.db.constant.RoleEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GroupToProjectAndRoleDto {

    private Long id;

    private String userGroup;

    private String projectName;

    private Long projectId;

    @Enumerated(EnumType.STRING)
    private RoleEnum roleName;

}
