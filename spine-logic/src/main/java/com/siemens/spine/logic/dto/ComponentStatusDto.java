package com.siemens.spine.logic.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 22/12/2022
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class ComponentStatusDto {

    protected Integer emulationState;
    protected Date emulationStateDate;
    protected Integer itState;
    protected Date itStateDate;
    protected Integer simulationState;
    protected Date simulationStateDate;
    protected Integer calculationState;
    protected Date calculationStateDate;
    protected Integer bomState;
    protected Date bomStateDate;
    protected Integer rowNum;
    protected String sapID;
    protected Date simulationComponentLastModDate;
    protected Integer objectXmlExportState;
    protected Date objectXmlExportStateDate;
    protected Integer electricSynchronizationState;
    protected Date electricSynchronizationDate;
    private Long uniqueID;

}
