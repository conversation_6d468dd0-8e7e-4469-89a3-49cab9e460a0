package com.siemens.spine.logic.util;

import com.siemens.spine.logic.exception.SpineException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Map;

/**
 * Utility class for common validation operations
 */
public final class ValidationUtils {

    private ValidationUtils() {
        // Private constructor to prevent instantiation
    }

    /**
     * Validates that a string is not null or empty
     *
     * @param value     The string to validate
     * @param fieldName The name of the field being validated
     * @throws SpineException If the string is null or empty
     */
    public static void validateNotEmpty(String value, String fieldName) throws SpineException {
        if (StringUtils.isEmpty(value)) {
            throw new SpineException(String.format("The %s could not be null or empty", fieldName));
        }
    }

    /**
     * Validates that a collection is not null or empty
     *
     * @param collection The collection to validate
     * @param fieldName  The name of the field being validated
     * @throws SpineException If the collection is null or empty
     */
    public static void validateNotEmpty(Collection<?> collection, String fieldName) throws SpineException {
        if (CollectionUtils.isEmpty(collection)) {
            throw new SpineException(String.format("The %s could not be null or empty", fieldName));
        }
    }

    /**
     * Validates that a map is not null or empty
     *
     * @param map       The map to validate
     * @param fieldName The name of the field being validated
     * @throws SpineException If the map is null or empty
     */
    public static void validateNotEmpty(Map<?, ?> map, String fieldName) throws SpineException {
        if (map == null || map.isEmpty()) {
            throw new SpineException(String.format("The %s could not be null or empty", fieldName));
        }
    }

    /**
     * Validates that an object is not null
     *
     * @param object    The object to validate
     * @param fieldName The name of the field being validated
     * @throws SpineException If the object is null
     */
    public static void validateNotNull(Object object, String fieldName) throws SpineException {
        if (object == null) {
            throw new SpineException(String.format("The %s could not be null", fieldName));
        }
    }

    /**
     * Validates that a condition is true
     *
     * @param condition The condition to validate
     * @param message   The error message if the condition is false
     * @throws SpineException If the condition is false
     */
    public static void validateCondition(boolean condition, String message) throws SpineException {
        if (!condition) {
            throw new SpineException(message);
        }
    }

    /**
     * Validates that a value is within a range
     *
     * @param value     The value to validate
     * @param min       The minimum allowed value
     * @param max       The maximum allowed value
     * @param fieldName The name of the field being validated
     * @throws SpineException If the value is outside the range
     */
    public static void validateRange(int value, int min, int max, String fieldName) throws SpineException {
        if (value < min || value > max) {
            throw new SpineException(String.format("The %s must be between %d and %d", fieldName, min, max));
        }
    }

    /**
     * Validates that a value is greater than or equal to a minimum value
     *
     * @param value     The value to validate
     * @param min       The minimum allowed value
     * @param fieldName The name of the field being validated
     * @throws SpineException If the value is less than the minimum
     */
    public static void validateMinimum(int value, int min, String fieldName) throws SpineException {
        if (value < min) {
            throw new SpineException(String.format("The %s must be at least %d", fieldName, min));
        }
    }

    /**
     * Validates that a value is less than or equal to a maximum value
     *
     * @param value     The value to validate
     * @param max       The maximum allowed value
     * @param fieldName The name of the field being validated
     * @throws SpineException If the value is greater than the maximum
     */
    public static void validateMaximum(int value, int max, String fieldName) throws SpineException {
        if (value > max) {
            throw new SpineException(String.format("The %s must be at most %d", fieldName, max));
        }
    }

}
