package com.siemens.spine.logic.model;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlElement;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/04/23
 */

@Getter
@Setter
public class GetComponentVersionsResponse {

    @XmlElement(name = "return")
    protected List<ProjectComponentVersion> _return;

    /**
     * Gets the value of the return property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the return property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getReturn().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ProjectComponentVersion }
     */
    public List<ProjectComponentVersion> getReturn() {
        if (_return == null) {
            _return = new ArrayList<ProjectComponentVersion>();
        }
        return this._return;
    }

}
