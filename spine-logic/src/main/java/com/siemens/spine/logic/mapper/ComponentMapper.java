package com.siemens.spine.logic.mapper;

import com.siemens.spine.db.constant.ComponentState;
import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.TypeEntity;
import com.siemens.spine.db.repository.views.ComponentView;
import com.siemens.spine.generated.toolchain.Component;
import com.siemens.spine.generated.toolchain.Decomposition;
import com.siemens.spine.generated.toolchain.DecompositionList;
import com.siemens.spine.generated.toolchain.State;
import com.siemens.spine.logic.dto.ComponentDTO;
import com.siemens.spine.logic.dto.ComponentStatusDto;
import com.siemens.spine.logic.dto.DecompositionDto;
import com.siemens.spine.logic.dto.DecompositionListDto;
import com.siemens.spine.logic.dto.GroupMappingDto;
import com.siemens.spine.logic.dto.SimplePairDto;
import com.siemens.spine.logic.util.ComponentUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Pham
 * @version 1.0
 * @since 22/12/2022
 */
@Mapper(uses = { DecompositionMapper.class, PositionMapper.class,
        ComponentStatusDto.class, StateMapper.class,
        ConnectionPointMapper.class, StringToLongMapper.class,
        StringToIntegerMapper.class, StringToDoubleMapper.class })
public interface ComponentMapper {

    ComponentMapper INSTANCE = Mappers.getMapper(ComponentMapper.class);

    @Mapping(target = "id", source = "uniqueID")
    @Mapping(target = "typeId", source = "type")
    @Mapping(target = "currentState", ignore = true)
    @Mapping(target = "connectionPoints", ignore = true)
    @Mapping(target = "position", ignore = true)
    @Mapping(target = "groups", ignore = true)
    @Mapping(target = "projectId", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "states", ignore = true)
    @Mapping(target = "decompositions", ignore = true)
    ComponentEntity toEntity(ComponentDTO dto);

    @Mapping(target = "uniqueID", source = "id")
    @Mapping(target = "type", source = "typeId")
    @Mapping(target = "currentState", source = "currentState", qualifiedByName = "currentState")
    @Mapping(target = "decompositionDtos", ignore = true)
    @Mapping(target = "connectionPointDtos", source = "connectionPoints", ignore = false)
    @Mapping(target = "positionDto", ignore = true)
    @Mapping(target = "componentStatus", source = "status")
    ComponentDTO toComponentDto(ComponentEntity component);

    @Mapping(target = "akz", source = "AKZ")
    @Mapping(target = "type", source = "typeID")
    @Mapping(target = "currentState", source = "state", qualifiedByName = "wsState")
    @Mapping(target = "decompositionDtos", source = "decompositionLists",
            qualifiedByName = "decompositionListToDecompositionListDto")
    @Mapping(target = "connectionPointDtos", source = "connectionPoints")
    @Mapping(target = "positionDto", source = "position")
    @Mapping(target = "componentStatus", ignore = true)
    @Mapping(target = "groupMapping", source = "component", qualifiedByName = "componentGroupMapping")
    @Mapping(target = "sysModDate", ignore = true)
    ComponentDTO toComponentDto(Component component);

    @Mapping(target = "typeId", source = "type")
    @Mapping(target = "currentState", ignore = true)
    @Mapping(target = "connectionPoints", ignore = true)
    @Mapping(target = "position", ignore = true)
    @Mapping(target = "groups", ignore = true)
    @Mapping(target = "projectId", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "states", ignore = true)
    @Mapping(target = "decompositions", ignore = true)
    void update(ComponentDTO dto, @MappingTarget ComponentEntity entity);

    @Named("currentState")
    default SimplePairDto map(int state) {
        ComponentState componentState = ComponentState.resolve(state);
        return componentState == null ? null
                : new SimplePairDto(String.valueOf(componentState.getV1Status()),
                componentState.getName());
    }

    @Named("wsState")
    default SimplePairDto map(State state) {
        if (state == null) {
            return null;
        }

        ComponentState componentState = ComponentState.resolve(state.getState());
        return componentState == null ? null
                : new SimplePairDto(String.valueOf(componentState.getV1Status()),
                componentState.getName());
    }

    @Mapping(target = "uniqueID", source = "id")
    @Mapping(target = "type", source = "typeId")
    @Mapping(target = "currentState", source = "currentState", qualifiedByName = "currentState")
    @Mapping(target = "decompositionDtos", ignore = true)
    @Mapping(target = "connectionPointDtos", ignore = true)
    @Mapping(target = "positionDto", ignore = true)
    @Mapping(target = "groupMapping", source = "groupView")
    @Mapping(target = "componentStatus", source = "bomState", qualifiedByName = "componentStatus")
    ComponentDTO toComponentDto(ComponentView component);

    @Named("componentGroupMapping")
    default GroupMappingDto mapComponentGroup(Component component) {
        return ComponentUtils.toGroupMappingDto(component);
    }

    @Named("entityGroupMapping")
    default GroupMappingDto mapComponentGroup(ComponentEntity component) {
        return ComponentUtils.toGroupMappingDto(component.getGroups());
    }

    @Named("type")
    default String getType(TypeEntity typeEntity) {
        return typeEntity == null ? null : typeEntity.getTypeId();
    }

    @Named("componentStatus")
    default ComponentStatusDto toComponentStatus(Integer bomState) {
        return ComponentStatusDto.builder().bomState(bomState).build();
    }

    @Named("decompositionListToDecompositionListDto")
    default DecompositionListDto decompositionListToDecompositionListDto(DecompositionList decompositionList) {
        if (decompositionList == null) {
            return null;
        }

        DecompositionListDto.DecompositionListDtoBuilder decompositionListDto = DecompositionListDto.builder();

        decompositionListDto.decompositions(
                decompositionListToDecompositionDtoList(decompositionList.getDecompositions(),
                        decompositionList.getSource()));
        decompositionListDto.mode(decompositionList.getMode());
        decompositionListDto.source(decompositionList.getSource());

        return decompositionListDto.build();
    }

    default List<DecompositionDto> decompositionListToDecompositionDtoList(List<Decomposition> list, String source) {
        if (list == null) {
            return null;
        }

        List<DecompositionDto> list1 = new ArrayList<>(list.size());
        for (Decomposition decomposition : list) {
            list1.add(Mappers.getMapper(DecompositionMapper.class).wsToDto(decomposition, source));
        }

        return list1;
    }

}