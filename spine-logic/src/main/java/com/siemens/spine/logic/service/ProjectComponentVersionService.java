package com.siemens.spine.logic.service;

import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.logic.model.Component;
import com.siemens.spine.logic.model.ProjectComponentVersion;

/**
 * <AUTHOR>
 * @since 2025/04/21
 */
public interface ProjectComponentVersionService
        extends VersionService<Long, Component, ProjectComponentVersion, ComponentEntity> {

    @Override
    default Class<ComponentEntity> getTargetEntityClass() {
        return ComponentEntity.class;
    }

}
