package com.siemens.spine.logic.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 22/12/2022
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ComponentDTO {

    private Long uniqueID;

    private Integer amountBufferElec;

    private Integer amountBufferMech;

    private String breakType;

    private Integer bufferSize;

    private String comment;

    private Integer curveAngle;

    private String curveDirection;

    private Integer curveRadius;

    private String drivePosition;

    private String driveStation;

    private Integer height;

    private Integer lengthTotal;

    private Double load;
    private String motorController;
    private String motorDirection;
    private String motorPosition;
    private String parent;
    private String plantDomain;
    private String posNo;
    private String reference;
    private Integer rotation;
    private Double rotation3D;
    private String screen;
    private Double section1Angle;
    private Double section1Length;
    private Double section2Angle;
    private Double section2Length;
    private Double section3Angle;
    private Double section3Length;
    private Double section4Angle;
    private Double section4Length;
    private String slaveDrive;
    private Double slope;
    private String speed;
    private Integer startStopCycles;

    private String supplier;

    private Integer throughput;

    private String type;

    private Boolean unitReversible;

    private String usage;

    private Integer version;

    private String virtual;

    private Integer width;

    private Integer installationHours;

    private Integer engineeringHours;

    private String colorGroup;

    private String akz;

    private String customerAKZ;

    private String autoCadLayer;

    private String user1;

    private String user2;

    private String user3;

    private String user4;

    private String user5;

    private String driveSide;

    private String angleCorr;

    private String levelStart;

    private String levelEnd;

    private String controlNr;

    private String vaultInstance;

    private String drawingVersion;

    private String drawingRevision;

    private String outfit3D;

    private String parcelTypeID;

    private String orderPlant;

    private String designPlant;

    private Boolean storageConveyor;

    private Double drivePulleyDiameter;

    private Double driveShaftDiameter;

    private Double acceleration;

    private SimplePairDto currentState;

    private ComponentStatusDto componentStatus;

    private GroupMappingDto groupMapping;

    private List<ConnectionPointDto> connectionPointDtos;

    private PositionDto positionDto;

    private List<DecompositionListDto> decompositionDtos;

    private Timestamp sysModDate;

    private Timestamp sysCreateDate;

    private boolean useConnectionPoints;

    private boolean useDecompositions;

    private boolean useState;

    private StateDto state;

}
