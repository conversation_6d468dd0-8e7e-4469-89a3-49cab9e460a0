package com.siemens.spine.logic.service;

import com.siemens.spine.generated.toolchain.Type;
import com.siemens.spine.logic.dto.TypeDto;
import com.siemens.spine.logic.dto.TypeMetaDataDto;
import com.siemens.spine.logic.exception.SpineException;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 26/12/2022
 */
public interface TypeService {

    Type getTypeById(String projectName, String typeId);

    String getAllTypes(String projectName);

    List<TypeDto> getAllTypeByProjectId(Long projectId);

    TypeDto update(String typeId, TypeDto modifiedType) throws SpineException;

    void deleteTypeByIds(List<String> ids) throws SpineException;

    void createTypes(String projectName, List<Type> types) throws SpineException;

    TypeMetaDataDto getTypeMetadata();

    void create(TypeDto type) throws SpineException;

}
