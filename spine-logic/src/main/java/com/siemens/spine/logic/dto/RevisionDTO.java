package com.siemens.spine.logic.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO for entity revision.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RevisionDTO {

    private Integer revisionNumber;
    private Date revisionDate;
    private String username;
    private String revisionType;
    private Object entity;

}
