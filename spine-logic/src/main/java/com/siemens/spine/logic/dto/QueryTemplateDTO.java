package com.siemens.spine.logic.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siemens.spine.logic.anotation.Trim;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.sql.Timestamp;

@Data
public class QueryTemplateDTO {

    private Long id;

    @NotNull
    @Trim
    @JsonAlias({ "template_name", "templateName" })
    private String templateName;

    @NotNull
    @Trim
    @JsonAlias({ "sqlTemplate", "sql_template" })
    private String sqlTemplate;

    @Trim
    private String description;

    @JsonAlias({ "requiresPagination", "requires_pagination" })
    private boolean requiresPagination = false;

    @JsonIgnore
    private Timestamp sysModDate;

    @JsonIgnore
    private String createdBy;

    @JsonIgnore
    private String modifiedBy;

    @JsonIgnore
    private Timestamp sysCreateDate;

    private Long version;

}
