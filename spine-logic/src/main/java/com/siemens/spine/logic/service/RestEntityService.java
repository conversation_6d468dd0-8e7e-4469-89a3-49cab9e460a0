package com.siemens.spine.logic.service;

import com.siemens.spine.db.repository.CrudRepository;
import com.siemens.spine.db.repository.paging.Page;
import com.siemens.spine.db.repository.paging.Pageable;
import com.siemens.spine.db.utils.CompositeKeyUtils;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.util.EntityReflectionUtils;
import com.siemens.spine.logic.util.JsonSerializationService;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.Objects;
import java.util.Optional;

/**
 * Service layer for handling REST entity operations.
 * This service encapsulates the business logic for CRUD operations on entities
 * exposed through REST endpoints, providing a clean separation between the
 * resource layer and repository layer.
 *
 * <AUTHOR> <PERSON>
 * @since 1.0
 */
@ApplicationScoped
@Slf4j
public class RestEntityService {

    private final RestEndpointFactory endpointGenerator;

    private final JsonSerializationService jsonSerializationService;

    /**
     * Constructor for dependency injection.
     *
     * @param endpointGenerator        Service for managing REST endpoints
     * @param jsonSerializationService Service for JSON serialization/deserialization
     */
    @Inject
    public RestEntityService(RestEndpointFactory endpointGenerator,
                             JsonSerializationService jsonSerializationService) {
        this.endpointGenerator = Objects.requireNonNull(endpointGenerator, "EndpointGenerator cannot be null");
        this.jsonSerializationService = Objects.requireNonNull(jsonSerializationService,
                "JsonSerializationService cannot be null");
    }

    /**
     * Retrieves all entities for the specified entity path.
     *
     * @param entityPath The entity path identifier
     * @return List of all entities
     * @throws SpineException if the entity path is not found or operation fails
     */
    public Page<Object> getPaginatedEntities(String entityPath, Pageable pageable) throws SpineException {
        RestEndpointFactory.RestEndpointInfo endpoint = getEndpointInfo(entityPath);
        CrudRepository<Object, Object> repository = getRepository(endpoint);
        return repository.findAll(pageable);
    }

    /**
     * Retrieves a single entity by its ID.
     *
     * @param entityPath The entity path identifier
     * @param idStr      The string representation of the entity ID
     * @return Optional containing the entity if found
     * @throws SpineException if the entity path is not found or ID conversion fails
     */
    public Optional<Object> getEntityById(String entityPath, String idStr) throws SpineException {
        RestEndpointFactory.RestEndpointInfo endpoint = getEndpointInfo(entityPath);
        Object id = getId(idStr, endpoint);
        CrudRepository<Object, Object> repository = getRepository(endpoint);
        return repository.findById(id);
    }

    /**
     * Creates a new entity from JSON data.
     *
     * @param entityPath The entity path identifier
     * @param jsonBody   The JSON representation of the entity
     * @return The created entity with generated ID
     * @throws SpineException if creation fails or JSON is invalid
     */
    public Object createEntity(String entityPath, String jsonBody) throws SpineException {
        RestEndpointFactory.RestEndpointInfo endpoint = getEndpointInfo(entityPath);
        Object entity = jsonSerializationService.deserializeEntity(jsonBody, endpoint.entityClass());
        CrudRepository<Object, Object> repository = getRepository(endpoint);
        return repository.save(entity);
    }

    /**
     * Updates an existing entity with new data from JSON.
     *
     * @param entityPath The entity path identifier
     * @param idStr      The string representation of the entity ID
     * @param jsonBody   The JSON representation of the updated entity
     * @return The updated entity
     * @throws SpineException if update fails, entity not found, or JSON is invalid
     */
    public Object updateEntity(String entityPath, String idStr, String jsonBody) throws SpineException {
        RestEndpointFactory.RestEndpointInfo endpoint = getEndpointInfo(entityPath);
        Object id = getId(idStr, endpoint);
        CrudRepository<Object, Object> repository = getRepository(endpoint);

        if (!repository.existsById(id)) {
            throw new SpineException("Entity not found with ID: " + idStr);
        }

        Object entity = jsonSerializationService.deserializeEntity(jsonBody, endpoint.entityClass());
        EntityReflectionUtils.setIdOnEntity(entity, id);

        return repository.save(entity);
    }

    /**
     * Deletes an entity by its ID.
     *
     * @param entityPath The entity path identifier
     * @param idStr      The string representation of the entity ID
     * @throws SpineException if deletion fails or entity not found
     */
    public void deleteEntity(String entityPath, String idStr) throws SpineException {
        RestEndpointFactory.RestEndpointInfo endpoint = getEndpointInfo(entityPath);
        Object id = getId(idStr, endpoint);
        CrudRepository<Object, Object> repository = getRepository(endpoint);

        if (!repository.existsById(id)) {
            throw new SpineException("Entity not found with ID: " + idStr);
        }

        repository.deleteById(id);
    }

    /**
     * Converts an ID string to the appropriate type for entity operations.
     * Supports both single and composite keys with enterprise-level validation.
     *
     * @param idStr    The ID string from the REST request
     * @param endpoint The endpoint information containing entity and ID classes
     * @return Converted ID object suitable for repository operations
     * @throws SpineException if ID conversion or validation fails
     */
    private Object getId(String idStr, RestEndpointFactory.RestEndpointInfo endpoint) throws SpineException {
        Objects.requireNonNull(idStr, "ID string cannot be null");
        Objects.requireNonNull(endpoint, "Endpoint info cannot be null");

        // Validate ID string length and format for security
        validateIdString(idStr);

        Class<?> entityClass = endpoint.entityClass();

        // For composite keys, use entity class for conversion
        if (CompositeKeyUtils.hasCompositeKey(entityClass)) {
            log.debug("Processing composite key for entity: {}", entityClass.getSimpleName());
            return EntityReflectionUtils.convertId(idStr, entityClass);
        }

        // For single keys, use ID class
        Class<?> idClass = endpoint.idClass();
        if (idClass == null) {
            log.warn("ID class is null for entity {}, using String", entityClass.getSimpleName());
            idClass = String.class;
        }

        return EntityReflectionUtils.convertId(idStr, idClass);
    }

    /**
     * Validates ID string for security and format compliance.
     * Enterprise security measure to prevent injection attacks.
     *
     * @param idStr The ID string to validate
     * @throws SpineException if validation fails
     */
    private void validateIdString(String idStr) throws SpineException {
        if (idStr == null || idStr.trim().isEmpty()) {
            throw new SpineException("ID string cannot be null or empty");
        }

        // Security: Limit ID string length to prevent DoS attacks
        if (idStr.length() > 1000) {
            throw new SpineException("ID string exceeds maximum allowed length");
        }

        // Security: Check for potentially dangerous characters
        if (containsSqlInjectionPatterns(idStr)) {
            log.warn("Potential SQL injection attempt detected in ID string: {}", idStr);
            throw new SpineException("Invalid characters detected in ID string");
        }

        // Security: Validate composite key format if applicable
        if (idStr.contains(CompositeKeyUtils.KEY_WRAPPER_START)) {
            CompositeKeyUtils.validateCompositeKeyFormat(idStr);
        }
    }

    /**
     * Checks for potential SQL injection patterns in ID strings.
     * Enterprise security measure.
     *
     * @param idStr The ID string to check
     * @return true if suspicious patterns are found
     */
    private boolean containsSqlInjectionPatterns(String idStr) {
        String lowerCase = idStr.toLowerCase();
        String[] dangerousPatterns = {
                "select", "insert", "update", "delete", "drop", "union",
                "script", "javascript", "vbscript", "onload", "onerror",
                "--", "/*", "*/", "xp_", "sp_", "exec"
        };

        for (String pattern : dangerousPatterns) {
            if (lowerCase.contains(pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Extracts the ID from a saved entity for location header generation.
     *
     * @param entity The entity from which to extract the ID
     * @return The extracted ID
     * @throws SpineException if ID extraction fails
     */
    public Object extractEntityId(Object entity) throws SpineException {
        return EntityReflectionUtils.extractId(entity);
    }

    /**
     * Retrieves endpoint information for the given entity path.
     *
     * @param entityPath The entity path identifier
     * @return The endpoint information
     * @throws SpineException if the endpoint is not found
     */
    private RestEndpointFactory.RestEndpointInfo getEndpointInfo(String entityPath) throws SpineException {
        return endpointGenerator.getGeneratedEndpoints().stream()
                .filter(info -> info.path().equals("/" + entityPath))
                .findFirst()
                .orElseThrow(() -> new SpineException("Entity path not found: " + entityPath));
    }

    /**
     * Safely casts the repository from the endpoint info.
     *
     * @param endpoint The endpoint information
     * @return The typed repository
     */
    @SuppressWarnings("unchecked")
    private CrudRepository<Object, Object> getRepository(RestEndpointFactory.RestEndpointInfo endpoint) {
        return (CrudRepository<Object, Object>) endpoint.repository();
    }

}
