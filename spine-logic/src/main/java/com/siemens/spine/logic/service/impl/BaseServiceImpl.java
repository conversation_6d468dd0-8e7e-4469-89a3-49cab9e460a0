package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.repository.GenericJpaRepository;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.util.ValidationUtils;
import lombok.extern.slf4j.Slf4j;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

/**
 * Base service implementation with common CRUD operations
 *
 * @param <T>  The entity type
 * @param <ID> The ID type
 * @param <R>  The repository type
 */
@Slf4j
@Transactional(Transactional.TxType.REQUIRED)
public abstract class BaseServiceImpl<T, ID, R extends GenericJpaRepository<T, ID>> {

    protected final R repository;

    protected BaseServiceImpl(R repository) {
        this.repository = repository;
    }

    /**
     * Find an entity by ID
     *
     * @param id The ID of the entity to find
     * @return The entity if found
     * @throws SpineException If the entity is not found
     */
    public T findById(ID id) throws SpineException {
        ValidationUtils.validateNotNull(id, "id");

        Optional<T> entity = repository.findById(id);
        if (entity.isEmpty()) {
            throw new SpineException(String.format("Entity with ID %s not found", id));
        }

        return entity.get();
    }

    /**
     * Find an entity by ID, returning null if not found
     *
     * @param id The ID of the entity to find
     * @return The entity if found, null otherwise
     */
    public T findByIdOrNull(ID id) {
        if (id == null) {
            return null;
        }

        Optional<T> entity = repository.findById(id);
        return entity.orElse(null);
    }

    /**
     * Find all entities
     *
     * @return List of all entities
     */
    public List<T> findAll() {
        return repository.findAll();
    }

    /**
     * Find entities by IDs
     *
     * @param ids The IDs of the entities to find
     * @return List of entities with the given IDs
     */
    public List<T> findAllByIds(List<ID> ids) throws SpineException {
        ValidationUtils.validateNotEmpty(ids, "ids");
        return repository.findAllByIds(ids);
    }

    /**
     * Save an entity
     *
     * @param entity The entity to save
     * @return The saved entity
     * @throws SpineException If the entity is invalid
     */
    public T save(T entity) throws SpineException {
        ValidationUtils.validateNotNull(entity, "entity");
        return repository.save(entity);
    }

    /**
     * Save multiple entities
     *
     * @param entities The entities to save
     * @return The saved entities
     * @throws SpineException If any entity is invalid
     */
    public List<T> saveAll(List<T> entities) throws SpineException {
        ValidationUtils.validateNotEmpty(entities, "entities");
        return repository.saveAll(entities);
    }

    /**
     * Delete an entity by ID
     *
     * @param id The ID of the entity to delete
     * @throws SpineException If the entity is not found
     */
    public void deleteById(ID id) throws SpineException {
        ValidationUtils.validateNotNull(id, "id");
        repository.deleteById(id);
    }

    /**
     * Delete multiple entities by IDs
     *
     * @param ids The IDs of the entities to delete
     * @throws SpineException If any entity is not found
     */
    public void deleteAllByIds(List<ID> ids) throws SpineException {
        ValidationUtils.validateNotEmpty(ids, "ids");
        repository.deleteAll(ids);
    }

    /**
     * Check if an entity exists by ID
     *
     * @param id The ID to check
     * @return True if an entity with the given ID exists, false otherwise
     */
    public boolean existsById(ID id) {
        if (id == null) {
            return false;
        }

        return repository.existsById(id);
    }

}
