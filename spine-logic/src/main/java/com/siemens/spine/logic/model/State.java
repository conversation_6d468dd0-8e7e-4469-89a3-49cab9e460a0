package com.siemens.spine.logic.model;

import javax.xml.datatype.XMLGregorianCalendar;

public class State {

    protected String changeGroupDescription;
    protected Integer changeGroupId;
    protected Integer reason;
    protected String remark;
    protected Integer state;
    protected XMLGregorianCalendar statusDate;
    protected String uniqueID;
    protected String user;

    /**
     * Gets the value of the changeGroupDescription property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getChangeGroupDescription() {
        return changeGroupDescription;
    }

    /**
     * Sets the value of the changeGroupDescription property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setChangeGroupDescription(String value) {
        this.changeGroupDescription = value;
    }

    /**
     * Gets the value of the changeGroupId property.
     *
     * @return possible object is
     * {@link Integer }
     */
    public Integer getChangeGroupId() {
        return changeGroupId;
    }

    /**
     * Sets the value of the changeGroupId property.
     *
     * @param value allowed object is
     *              {@link Integer }
     */
    public void setChangeGroupId(Integer value) {
        this.changeGroupId = value;
    }

    /**
     * Gets the value of the reason property.
     *
     * @return possible object is
     * {@link Integer }
     */
    public Integer getReason() {
        return reason;
    }

    /**
     * Sets the value of the reason property.
     *
     * @param value allowed object is
     *              {@link Integer }
     */
    public void setReason(Integer value) {
        this.reason = value;
    }

    /**
     * Gets the value of the remark property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getRemark() {
        return remark;
    }

    /**
     * Sets the value of the remark property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setRemark(String value) {
        this.remark = value;
    }

    /**
     * Gets the value of the state property.
     *
     * @return possible object is
     * {@link Integer }
     */
    public Integer getState() {
        return state;
    }

    /**
     * Sets the value of the state property.
     *
     * @param value allowed object is
     *              {@link Integer }
     */
    public void setState(Integer value) {
        this.state = value;
    }

    /**
     * Gets the value of the statusDate property.
     *
     * @return possible object is
     * {@link XMLGregorianCalendar }
     */
    public XMLGregorianCalendar getStatusDate() {
        return statusDate;
    }

    /**
     * Sets the value of the statusDate property.
     *
     * @param value allowed object is
     *              {@link XMLGregorianCalendar }
     */
    public void setStatusDate(XMLGregorianCalendar value) {
        this.statusDate = value;
    }

    /**
     * Gets the value of the uniqueID property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getUniqueID() {
        return uniqueID;
    }

    /**
     * Sets the value of the uniqueID property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUniqueID(String value) {
        this.uniqueID = value;
    }

    /**
     * Gets the value of the user property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getUser() {
        return user;
    }

    /**
     * Sets the value of the user property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUser(String value) {
        this.user = value;
    }

}
