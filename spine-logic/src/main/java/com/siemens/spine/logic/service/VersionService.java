package com.siemens.spine.logic.service;

import com.siemens.spine.db.entity.RevisionInfoEntity;
import com.siemens.spine.logic.dto.request.GetObjectsVersionRequestDTO;
import com.siemens.spine.logic.model.RevisionInfo;

import java.util.List;

/**
 * VersionService interface for handling version control of SPINECON objects.
 * ID: Type of the target object ID
 * V: Type of the version object
 * M: Type of the target object model
 * ET: Type of the target entity
 *
 * <AUTHOR>
 * @Date 2025/04/25
 */
public interface VersionService<ID, M, V, ET> {

    /**
     * Get target object detail with ve rsion of a target object.
     *
     * @param requestDTO the ID of the target object
     * @return the latest version of the target object
     */
    List<M> getObjectsInRevision(GetObjectsVersionRequestDTO requestDTO);

    /**
     * Get target object detail with version of a target object.
     *
     * @param requestDTO the ID of the target object
     * @return the latest version of the target object
     */
    List<M> getObjectsInVersion(GetObjectsVersionRequestDTO requestDTO);

    /**
     * Get all versions of a component.
     *
     * @param targetId the ID of the target object
     * @return all versions of the component
     */
    List<V> getAllVersions(ID targetId);

    /**
     * Get all revisions of a obj.
     *
     * @param componentId the ID of the obj
     * @return all revisions of the obj
     */
    List<RevisionInfo> getAllRevisions(ID componentId);

    /**
     * Handle a new revision of target.
     *
     * @param targetId     the ID of the target object
     * @param revisionInfo the revision info entity
     */
    void handleNewRevision(ID targetId, RevisionInfoEntity revisionInfo);

    /**
     * Create a new version from an entity and revision info.
     *
     * @return The created version object
     */
    <DTO> V createVersion(DTO dto);

    /**
     * Get the target entity class.
     *
     * @return
     */
    Class<ET> getTargetEntityClass();

}
