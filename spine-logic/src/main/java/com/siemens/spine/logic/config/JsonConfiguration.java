package com.siemens.spine.logic.config;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import javax.annotation.PostConstruct;
import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.context.Dependent;
import javax.enterprise.inject.Produces;

/**
 * Configuration class for JSON processing.
 * Provides CDI beans for Jackson ObjectMapper with consistent configuration
 * across the application.
 *
 * <AUTHOR> <PERSON>am
 * @since 1.0
 */
@ApplicationScoped
public class JsonConfiguration {

    private ObjectMapper mapper;

    @PostConstruct
    public void init() {
        this.mapper = createObjectMapper();
    }

    /**
     * Produces a configured ObjectMapper bean for dependency injection.
     * The ObjectMapper is configured with consistent settings for the entire application.
     *
     * @return Configured ObjectMapper instance
     */
    @Produces
    @Dependent
    public ObjectMapper objectMapper() {
        return mapper;
    }

    private ObjectMapper createObjectMapper() {
        return new ObjectMapper()
                .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
                .setSerializationInclusion(Include.NON_NULL)
                .setSerializationInclusion(Include.NON_EMPTY)
                .registerModule(new JavaTimeModule())
                .enable(SerializationFeature.INDENT_OUTPUT)
                .disable(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES)
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }

}
