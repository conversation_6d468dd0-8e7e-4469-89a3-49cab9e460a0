package com.siemens.spine.logic.service;

import com.siemens.spine.db.repository.filter.ComponentStateFilter;
import com.siemens.spine.generated.toolchain.State;
import com.siemens.spine.logic.dto.ComponentConditionDTO;
import com.siemens.spine.logic.dto.ComponentStateChangedResponseDto;
import com.siemens.spine.logic.dto.SimplePairDto;
import com.siemens.spine.logic.dto.StateDto;
import com.siemens.spine.logic.dto.request.StateChangedRequestDto;
import com.siemens.spine.logic.dto.request.UndeleteComponentRequestDto;
import com.siemens.spine.logic.exception.SpineException;

import java.util.List;

public interface ComponentStateService {

    /**
     * Set state of component to a specific state
     *
     * @param projectName
     * @param request
     * @return
     */
    boolean setStates(String projectName, List<State> request) throws SpineException;

    /**
     * Get latest states of the components
     *
     * @param componentConditionDTO
     * @return
     * @throws SpineException
     */
    List<State> getLatestStates(ComponentConditionDTO componentConditionDTO);

    /**
     * Find all the states by the filter
     *
     * @param filter
     * @return
     */
    List<StateDto> getStates(ComponentStateFilter filter);

    /**
     * Move forward
     *
     * @param request
     * @return
     * @throws Exception
     */
    ComponentStateChangedResponseDto moveForward(StateChangedRequestDto request) throws SpineException;

    /**
     * Move backward
     *
     * @param request move backward request
     * @return
     */
    ComponentStateChangedResponseDto moveBackward(StateChangedRequestDto request) throws SpineException;

    /**
     * Find the allowed states for the specific components that are executing "move backward"
     *
     * @param request
     * @return
     * @throws SpineException
     */
    List<SimplePairDto> getAllowedBackwardState(StateChangedRequestDto request) throws SpineException;

    /**
     * Set the object status to delete
     *
     * @param componentIds
     * @return
     * @throws SpineException
     */
    ComponentStateChangedResponseDto markAsDeleted(List<Long> componentIds) throws SpineException;

    /**
     * Unlock the components
     *
     * @param request
     * @return
     * @throws SpineException
     */
    ComponentStateChangedResponseDto undelete(UndeleteComponentRequestDto request) throws SpineException;

}
