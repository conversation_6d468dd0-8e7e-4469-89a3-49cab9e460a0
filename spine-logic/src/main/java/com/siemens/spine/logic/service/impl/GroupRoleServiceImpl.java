package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.constant.RoleEnum;
import com.siemens.spine.db.entity.GroupToProjectAndRoleEntity;
import com.siemens.spine.db.entity.ProjectEntity;
import com.siemens.spine.db.repository.GroupToProjectAndRoleRepository;
import com.siemens.spine.db.repository.ProjectRepository;
import com.siemens.spine.logic.dto.GroupToProjectAndRoleDto;
import com.siemens.spine.logic.dto.request.GroupToProjectAndRoleRequestDto;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.mapper.GroupRoleMapper;
import com.siemens.spine.logic.service.GroupRoleService;
import com.siemens.spine.logic.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@ApplicationScoped
@Slf4j
public class GroupRoleServiceImpl implements GroupRoleService {

    @Inject
    private GroupToProjectAndRoleRepository groupToProjectAndRoleRepository;

    @Inject
    private ProjectRepository projectRepository;

    @Override
    public List<GroupToProjectAndRoleDto> getGroupRoles(Long projectId) throws SpineException {
        if (projectId == null) {
            throw new SpineException("Project id must not be null");
        }
        List<GroupToProjectAndRoleDto> groupRoles = new ArrayList<>();

        GroupToProjectAndRoleDto parAdmin = new GroupToProjectAndRoleDto();
        parAdmin.setProjectId(projectId);
        parAdmin.setRoleName(RoleEnum.ADMIN);
        parAdmin.setUserGroup(SecurityUtils.PAR_ADMIN_GROUP);
        groupRoles.add(parAdmin);

        groupRoles.addAll(GroupRoleMapper.INSTANCE.toGroupToProjectAndRoles(
                groupToProjectAndRoleRepository.findAllProjectsByProjectsId(projectId)));
        return groupRoles;
    }

    @Override
    public GroupToProjectAndRoleDto createGroupRole(GroupToProjectAndRoleRequestDto groupToProjectAndRoleRequestDto)
            throws SpineException {

        Optional<ProjectEntity> projectEntityOptional = projectRepository.findById(
                groupToProjectAndRoleRequestDto.getProjectId());
        if (projectEntityOptional.isEmpty()) {
            throw new SpineException("Could not find project id: " + groupToProjectAndRoleRequestDto.getProjectId());
        }
        Optional<GroupToProjectAndRoleEntity> groupToProjectAndRoleEntityOptional = groupToProjectAndRoleRepository.findByProjectIdAndUserGroup(
                groupToProjectAndRoleRequestDto.getProjectId(), groupToProjectAndRoleRequestDto.getUserGroup());
        if (groupToProjectAndRoleEntityOptional.isPresent()) {
            throw new SpineException("Already exist AD Group : " + groupToProjectAndRoleRequestDto.getUserGroup() +
                    " and Project Id: " + groupToProjectAndRoleRequestDto.getProjectId());
        }
        GroupToProjectAndRoleEntity groupToProjectAndRoleEntity = GroupRoleMapper.INSTANCE.toEntity(
                groupToProjectAndRoleRequestDto);
        if (!RoleEnum.ADMIN.name().equals(groupToProjectAndRoleEntity.getRoleName())) {
            groupToProjectAndRoleEntity.setProject(projectEntityOptional.get());
        }
        return GroupRoleMapper.INSTANCE.toGroupToProjectAndRole(
                groupToProjectAndRoleRepository.save(groupToProjectAndRoleEntity));
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public GroupToProjectAndRoleDto updateGroupRole(Long id,
                                                    GroupToProjectAndRoleRequestDto groupToProjectAndRoleRequestDto)
            throws SpineException {
        Optional<GroupToProjectAndRoleEntity> groupToProjectAndRoleEntityOptional = groupToProjectAndRoleRepository.findById(
                id);
        if (groupToProjectAndRoleEntityOptional.isEmpty()) {
            throw new SpineException("No Group to project and role was found by id " + id);
        }
        Optional<ProjectEntity> projectEntityOptional = projectRepository.findById(
                groupToProjectAndRoleRequestDto.getProjectId());
        if (projectEntityOptional.isEmpty()) {
            throw new SpineException("Could not find project id: " + groupToProjectAndRoleRequestDto.getProjectId());
        }
        Optional<GroupToProjectAndRoleEntity> groupToProjectAndRoleEntityForValidateOptional =
                groupToProjectAndRoleRepository.findByProjectIdAndUserGroup(
                        groupToProjectAndRoleRequestDto.getProjectId(), groupToProjectAndRoleRequestDto.getUserGroup());
        if (groupToProjectAndRoleEntityForValidateOptional.isPresent() && !id.equals(
                groupToProjectAndRoleEntityForValidateOptional.get().getId())) {
            throw new SpineException("Already exist AD Group : " + groupToProjectAndRoleRequestDto.getUserGroup() +
                    " and Project Id: " + groupToProjectAndRoleRequestDto.getProjectId());
        }
        GroupToProjectAndRoleEntity groupToProjectAndRoleEntity = GroupRoleMapper.INSTANCE.toEntity(
                groupToProjectAndRoleRequestDto);
        groupToProjectAndRoleEntity.setProject(projectEntityOptional.get());
        groupToProjectAndRoleEntity.setId(groupToProjectAndRoleEntityOptional.get().getId());
        return GroupRoleMapper.INSTANCE.toGroupToProjectAndRole(
                groupToProjectAndRoleRepository.save(groupToProjectAndRoleEntity));
    }

    @Override
    public void deleteGroupRole(List<Long> ids) throws SpineException {
        List<GroupToProjectAndRoleEntity> groupToProjectAndRoleEntities = groupToProjectAndRoleRepository.findAllByIds(
                ids);
        if (CollectionUtils.isEmpty(groupToProjectAndRoleEntities)) {
            throw new SpineException("No Group to project and role was found by ids " + ids);
        }
        List<Long> groupRoleIds = groupToProjectAndRoleEntities.stream().map(GroupToProjectAndRoleEntity::getId)
                .toList();
        groupToProjectAndRoleRepository.deleteAll(groupRoleIds);
    }

    @Override
    public List<String> getRoles() {
        return Arrays.stream(RoleEnum.values()).filter(r -> !RoleEnum.ADMIN.equals(r)).map(RoleEnum::name)
                .toList();
    }

}
