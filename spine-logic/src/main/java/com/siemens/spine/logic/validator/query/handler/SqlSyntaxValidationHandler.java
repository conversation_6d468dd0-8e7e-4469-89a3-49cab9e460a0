package com.siemens.spine.logic.validator.query.handler;

import com.siemens.spine.logic.validator.query.QueryValidationContext;
import com.siemens.spine.logic.validator.query.ValidationResult;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.parser.feature.Feature;
import net.sf.jsqlparser.parser.feature.FeatureConfiguration;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.util.validation.Validation;
import net.sf.jsqlparser.util.validation.ValidationError;
import net.sf.jsqlparser.util.validation.ValidationException;
import net.sf.jsqlparser.util.validation.feature.DatabaseType;
import org.apache.commons.collections4.CollectionUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Named;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@ApplicationScoped
@Named("sqlSyntaxValidationHandler")
@Slf4j
public class SqlSyntaxValidationHandler extends AbstractQueryValidationHandler {

    @Override
    protected ValidationResult doValidate(QueryValidationContext context) {

        try {
            Statement statement = CCJSqlParserUtil.parse(context.getQueryString());

            FeatureConfiguration featureConfiguration = new FeatureConfiguration();
            featureConfiguration.setValue(Feature.select, true);

            List<ValidationError> errors = Validation.validate(Collections.singletonList(DatabaseType.POSTGRESQL),
                    context.getQueryString());

            if (CollectionUtils.isNotEmpty(errors)) {
                String errorMessages = errors.stream()
                        .map(ValidationError::getErrors)
                        .flatMap(errorsSet -> errorsSet.stream().map(ValidationException::getMessage))
                        .collect(Collectors.joining("; "));
                return ValidationResult.invalidSyntax("SQL syntax errors: " + errorMessages);
            }

            context.setParsedStatement(statement);

            return ValidationResult.success();

        } catch (JSQLParserException e) {
            log.error("SQL syntax error: {}", e.getMessage());
            return ValidationResult.invalidSyntax("Invalid SQL syntax: " + e.getMessage());
        }
    }

}