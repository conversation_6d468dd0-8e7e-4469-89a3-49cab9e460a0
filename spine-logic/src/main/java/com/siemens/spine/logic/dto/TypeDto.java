package com.siemens.spine.logic.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TypeDto {

    private String typeId; // cust_reference8
    private String mainType; // CUST_REFERENCE1
    private String subType; // CUST_REFERENCE2
    private String specialType; // CUST_REFERENCE3
    private String typeVersion; // CUST_REFERENCE4
    private String description; // cep_service_options
    private Double mechMaterialCostKpi; // worth
    private Double engineeringMeHKpi; // realnumber01
    private Double engineeringHwHPki; // realnumber02
    private Double engineeringPlcHKpi; // realnumber03
    private Double engineeringItHKpi; // realnumber04
    private String projectName; // project_name
    private String projectId; // vcompId
    private String url1; // lrefe01
    private String url2; // lrefe02
    private String url3; // lrefe03
    private String url4; // lrefe04
    private String url5; // lrefe05
    private String typeVariant; // srefe01
    private String defaultSlope; // srefe02
    private Integer defaultDriveCount; // intnumber01
    private String versionNumber; // srefe03
    private String supplier; // cust_reference10
    private Double engineeringScadaHKpi; // realnumber05
    private String sapMaterialNumber; // srefe04
    private String mps; // cust_reference5
    private Double installationHKpi; // zollwert
    private Double plcCommissioningHKpi; // zollwarenwert
    private Double beltMass; // realnumber06
    private Double requiredAcceleration; // realnumber07
    private Double drivePulleyDiameter; // realnumber08
    private Double frictionFactor; // realnumber09
    private Double driveShaftDiameter; // realnumber10
    private Double efficiencyGearbox; // insurance
    private String typeAggregator; // reference3
    private String originalType; // srefe05
    private String projectTypeClassification; // MAPPED SOMEHOW! NOT STRAIGHT FROM DB - dic_free_text3
    private Double engineeringMeOneOffCosts; // declared_pos_wght
    private Double engineeringHwOneOffCosts; // declared_pos_vol
    private Double engineeringPlcOneOffCosts; // warenwert_value
    private Double engineeringItOneOffCosts; // vereinbarte_fracht
    private Integer engineeringScadaOneOffCosts; // intnumber02  - in slang, read as getDouble while in db it is int
    private String hierarchyType; // reference4
    private Boolean outdated; // checkbox06
    private Integer solutionId; // unclear if future db will be instance-per-solution
    private Boolean projectSpecific;
    private Timestamp modificationDate;
    private Timestamp creationDate;

}
