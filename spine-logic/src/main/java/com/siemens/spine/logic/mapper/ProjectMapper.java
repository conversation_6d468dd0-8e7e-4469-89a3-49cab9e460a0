package com.siemens.spine.logic.mapper;

import com.siemens.spine.db.entity.ProjectEntity;
import com.siemens.spine.generated.toolchain.Project;
import com.siemens.spine.logic.dto.ProjectResponseDTO;
import com.siemens.spine.logic.dto.request.ProjectRequestDTO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.BeforeMapping;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 15/11/2022
 */
@Mapper
public interface ProjectMapper {

    ProjectMapper INSTANCE = Mappers.getMapper(ProjectMapper.class);

    ProjectEntity toProjectEntity(ProjectRequestDTO projectRequestDTO);

    @Named("soap")
    @Mapping(target = "projectID", source = "projectName")
    Project toProject(ProjectEntity projectEntity);

    @Named("rest")
    ProjectResponseDTO toProjectResponseDTO(ProjectEntity projectEntity);

    @IterableMapping(qualifiedByName = "soap")
    List<Project> toProjects(List<ProjectEntity> projectEntities);

    @IterableMapping(qualifiedByName = "rest")
    List<ProjectResponseDTO> toProjectsForRest(List<ProjectEntity> projects);

    @BeforeMapping
    default void mappingProjectName(@MappingTarget Project target, ProjectEntity source) {
        if (StringUtils.isEmpty(source.getProjectName())) {
            target.setProjectID(String.valueOf(source.getProjectID()));
        } else {
            target.setProjectID(source.getProjectName());
        }
    }

}
