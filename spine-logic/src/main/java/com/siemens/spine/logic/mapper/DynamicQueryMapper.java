package com.siemens.spine.logic.mapper;

import com.siemens.spine.db.entity.elcon.QueryTemplateEntity;
import com.siemens.spine.logic.dto.QueryTemplateDTO;
import com.siemens.spine.logic.dto.request.CreateQueryTemplateDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Date 2025/06/09
 */

@Mapper
public interface DynamicQueryMapper {

    DynamicQueryMapper INSTANCE = Mappers.getMapper(DynamicQueryMapper.class);

    QueryTemplateDTO toQueryTemplateDTO(QueryTemplateEntity dto);

    QueryTemplateEntity toQueryTemplateEntity(CreateQueryTemplateDTO dto);

}
