package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.constant.ComponentState;
import com.siemens.spine.db.constant.DecompositionAttribute;
import com.siemens.spine.db.constant.GroupTypeEnum;
import com.siemens.spine.db.constant.ProjectState;
import com.siemens.spine.db.entity.ChangeGroupEntity;
import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.ConnectionPointEntity;
import com.siemens.spine.db.entity.DecompositionAttributesEntity;
import com.siemens.spine.db.entity.DecompositionEntity;
import com.siemens.spine.db.entity.GroupEntity;
import com.siemens.spine.db.entity.PositionEntity;
import com.siemens.spine.db.entity.ProjectEntity;
import com.siemens.spine.db.entity.StateEntity;
import com.siemens.spine.db.repository.ChangeGroupRepository;
import com.siemens.spine.db.repository.ComponentRepository;
import com.siemens.spine.db.repository.ComponentStatusRepository;
import com.siemens.spine.db.repository.DecompositionAttributesRepository;
import com.siemens.spine.db.repository.DecompositionRepository;
import com.siemens.spine.db.repository.GroupRepository;
import com.siemens.spine.db.repository.ProjectRepository;
import com.siemens.spine.db.repository.StateRepository;
import com.siemens.spine.db.repository.TypeRepository;
import com.siemens.spine.db.repository.filter.ComponentFilter;
import com.siemens.spine.db.repository.filter.ProjectFilter;
import com.siemens.spine.db.repository.views.ComponentChangeGroupView;
import com.siemens.spine.db.repository.views.ComponentView;
import com.siemens.spine.generated.toolchain.Attribute;
import com.siemens.spine.generated.toolchain.ComponentSpecificationMember;
import com.siemens.spine.generated.toolchain.DecompositionAttributes;
import com.siemens.spine.generated.toolchain.DecompositionList;
import com.siemens.spine.generated.toolchain.Group;
import com.siemens.spine.logic.dto.ComponentConditionDTO;
import com.siemens.spine.logic.dto.ComponentDTO;
import com.siemens.spine.logic.dto.ConnectionPointDto;
import com.siemens.spine.logic.dto.DecompositionDto;
import com.siemens.spine.logic.dto.DecompositionListDto;
import com.siemens.spine.logic.dto.GroupMappingDto;
import com.siemens.spine.logic.dto.PositionDto;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.mapper.ComponentMapper;
import com.siemens.spine.logic.mapper.ConnectionPointMapper;
import com.siemens.spine.logic.mapper.DecompositionMapper;
import com.siemens.spine.logic.mapper.PositionMapper;
import com.siemens.spine.logic.service.ChangeGroupService;
import com.siemens.spine.logic.service.ComponentService;
import com.siemens.spine.logic.service.ConnectionPointService;
import com.siemens.spine.logic.service.CounterService;
import com.siemens.spine.logic.util.ComponentUtils;
import com.siemens.spine.logic.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@ApplicationScoped
@Transactional(value = Transactional.TxType.REQUIRED, rollbackOn = Exception.class)
@Slf4j
public class ComponentServiceImpl implements ComponentService {

    public static final String OVERWRITE_MODE = "overwrite";
    public static final String APPEND_MODE = "append";
    public static final String DELETE_MODE = "delete";

    private final ComponentRepository componentRepository;
    private final ComponentStatusRepository componentStatusRepository;
    private final DecompositionRepository decompositionRepository;
    private final DecompositionAttributesRepository decompositionAttributesRepository;
    private final StateRepository stateRepository;
    private final ProjectRepository projectRepository;
    private final ConnectionPointService connectionPointService;
    private final TypeRepository typeRepository;
    private final ChangeGroupService changeGroupService;
    private final CounterService counterService;
    private final GroupRepository groupRepository;
    private final ChangeGroupRepository changeGroupRepository;
    private final ComponentMapper componentMapper;
    private final ConnectionPointMapper connectionPointMapper;
    private final DecompositionMapper decompositionMapper;
    private final PositionMapper positionMapper;

    @Inject
    public ComponentServiceImpl(ComponentRepository componentRepository,
                                ComponentStatusRepository componentStatusRepository,
                                DecompositionRepository decompositionRepository,
                                DecompositionAttributesRepository decompositionAttributesRepository,
                                StateRepository stateRepository,
                                ProjectRepository projectRepository,
                                ConnectionPointService connectionPointService,
                                TypeRepository typeRepository,
                                ChangeGroupService changeGroupService,
                                CounterService counterService,
                                GroupRepository groupRepository,
                                ChangeGroupRepository changeGroupRepository, ComponentMapper componentMapper,
                                ConnectionPointMapper connectionPointMapper, DecompositionMapper decompositionMapper,
                                PositionMapper positionMapper) {
        this.componentRepository = componentRepository;
        this.componentStatusRepository = componentStatusRepository;
        this.decompositionRepository = decompositionRepository;
        this.decompositionAttributesRepository = decompositionAttributesRepository;
        this.stateRepository = stateRepository;
        this.projectRepository = projectRepository;
        this.connectionPointService = connectionPointService;
        this.typeRepository = typeRepository;
        this.changeGroupService = changeGroupService;
        this.counterService = counterService;
        this.groupRepository = groupRepository;
        this.changeGroupRepository = changeGroupRepository;
        this.componentMapper = componentMapper;
        this.connectionPointMapper = connectionPointMapper;
        this.decompositionMapper = decompositionMapper;
        this.positionMapper = positionMapper;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED) // this is required to update the relation data of component
    public List<Long> importComponents(String projectName, List<ComponentDTO> request) throws SpineException {
        if (StringUtils.isEmpty(projectName) || request == null || request.isEmpty()) {
            throw new SpineException("The project or component info could not be null or empty");
        }

        // Verify the project
        Optional<ProjectEntity> project = projectRepository.findByName(projectName);
        if (project.isEmpty() || ProjectState.CLOSED.name().equals(project.get().getMainProjectState())) {
            String msg = String.format("The project %s was not found or already closed", projectName);
            throw new SpineException(msg);
        }

        // Get all allowed type
        List<String> projectTypeIds = typeRepository.findAllTypeIdsByProjectId(project.get().getProjectID());

        // Save to database
        List<Long> componentIds = new ArrayList<>();
        for (ComponentDTO dto : request) {
            // Check type id
            if (!StringUtils.isEmpty(dto.getType()) && !projectTypeIds.contains(dto.getType())) {
                throw new SpineException(
                        String.format("The type %s is invalid for project %s", dto.getType(), projectName));
            }

            Long componentId = createOrUpdateComponent(project.get(), dto);
            componentIds.add(componentId);
        }

        return componentIds;
    }

    @Override
    public List<String> getCSVComponentsByUniqueIds(String projectName, List<Long> componentIds) {
        List<ComponentEntity> components = componentRepository.findByProjectNameAndComponentIds(projectName,
                componentIds);
        StringBuilder compCsv = new StringBuilder();
        List<StateEntity> states = stateRepository.findLatestComponentState(componentIds);
        Map<Long, StateEntity> statesMap = new HashMap<>();
        for (StateEntity state : states) {
            statesMap.put(state.getComponent().getId(), state);
        }
        for (ComponentEntity componentE : components) {
            StateEntity latest = statesMap.get(componentE.getId());
            compCsv.append(ComponentToCsvBuilder.extractComponentCSV(componentE, latest));
        }

        return List.of(compCsv.toString());
    }

    @Override
    public List<ComponentDTO> getComponentsByCriteria(ComponentConditionDTO condition) {
        ComponentFilter filter = ComponentFilter.builder()
                .projectId(condition.getProjectId())
                .specificProjectName(condition.getSpecificProjectName())
                .componentIds(condition.getComponentIds())
                .groupMapping(condition.getGroupMapping())
                .build();
        List<ComponentEntity> components = componentRepository.filter(filter);

        return components.stream()
                .map(componentMapper::toComponentDto)
                .toList();
    }

    @Override
    public List<String> getComponentsBySpecification(String projectName,
                                                     List<ComponentSpecificationMember> specificationMembers,
                                                     int start,
                                                     int end) throws SpineException {

        // 2. Get component by specs
        List<Long> componentIds = getComponentsBySpecMemberAndProject(projectName, specificationMembers, start, end);
        log.info("Get {} components", componentIds.size());

        if (CollectionUtils.isNotEmpty(componentIds)) {
            // 3. Get the latest Change Group
            if (specificationMembers.size() == 1 && "_getChangeReason".equals(
                    specificationMembers.get(0).getConditionName())) {
                List<ComponentChangeGroupView> changeGroupEntityList = changeGroupService.getLatestChangeGroupByComponentIds(
                        componentIds);
                log.info("Get {} changeGroup", changeGroupEntityList);
                return changeGroupEntityList.stream().map(Object::toString).toList();
            } else {
                return componentIds.stream().map(Object::toString).toList();
            }
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public List<DecompositionDto> getUnfinishedMaterial(Long projectId) throws SpineException {
        if (projectId == null) {
            throw new SpineException("Invalid project");
        }

        List<DecompositionEntity> entities = decompositionRepository.findUnfinishedMaterials(projectId);
        return decompositionMapper.toDtoList(entities);
    }

    @Override
    public List<DecompositionList> getDecompositions(Long componentId) {
        log.info("Start find decomposition by component id");
        boolean existComponent = componentRepository.existsById(componentId);
        if (!existComponent) {
            return new ArrayList<>();
        }
        List<DecompositionEntity> decompositions = decompositionRepository.findByComponentId(
                componentId);
        log.info("End find decomposition by component id");

        return DecompositionMapper.decompositionListMapper(decompositions);
    }

    @Override
    public List<DecompositionAttributes> getDecompositionAttributes(List<String> sapNumbers) {
        List<DecompositionAttributes> ret = new ArrayList<>();
        for (String sapNumber : sapNumbers) {
            Optional<DecompositionAttributesEntity> entity = decompositionAttributesRepository.findById(sapNumber);
            if (entity.isEmpty()) {
                log.warn("No decomposition was found by sap material number: {}", sapNumber);
                continue;
            }

            DecompositionAttributesEntity decompositionAttribute = entity.get();
            DecompositionAttributes decompositionAttributes = new DecompositionAttributes();

            for (DecompositionAttribute attrType : DecompositionAttribute.values()) {
                Attribute attr = new Attribute();
                attr.setName(attrType.getAttrName());
                attr.setType(attrType.getAttrType());
                Object value = attrType.getGetter().apply(decompositionAttribute);
                attr.setValue(value == null ? null : String.valueOf(value));

                decompositionAttributes.getAttributes().add(attr);
            }

            ret.add(decompositionAttributes);
        }

        return ret;
    }

    @Override
    public void createDecompositionAttributes(List<DecompositionAttributes> decompositionAttributes) {
        if (decompositionAttributes == null || decompositionAttributes.isEmpty()) {
            return;
        }

        List<DecompositionAttributesEntity> entities = new ArrayList<>();
        for (DecompositionAttributes decompositionAttribute : decompositionAttributes) {
            List<Attribute> attributes = decompositionAttribute.getAttributes();
            if (attributes.isEmpty()) {
                continue;
            }

            DecompositionAttributesEntity newAttr = new DecompositionAttributesEntity();
            for (Attribute attr : attributes) {
                String attrName = attr.getName();
                DecompositionAttribute attributeType = DecompositionAttribute.resolve(attrName);
                if (attributeType == null) {
                    log.warn("The attribute {} was not defined", attrName);
                    continue;
                }

                String attrValue = attr.getValue();
                double value = attrValue == null ? 0.0 : Double.parseDouble(attrValue);
                switch (attributeType) {
                    case DRIVE_BREAK:
                        newAttr.setDriveBreak(attrValue);
                        break;

                    case DRIVE_COS_PHI:
                        newAttr.setDriveCosPhi(value);
                        break;

                    case DRIVE_CURRENT:
                        newAttr.setDriveCurrent(value);
                        break;

                    case DRIVE_FREQUENCY:
                        newAttr.setDriveFrequency(value);
                        break;

                    case DRIVE_POWER:
                        newAttr.setDrivePower(value);
                        break;

                    case DRIVE_PROTECTION:
                        newAttr.setDriveProtection(attrValue);
                        break;

                    case DRIVE_REVERSIBLE:
                        newAttr.setDriveReversible(attrValue);
                        break;

                    case DRIVE_STARTER:
                        newAttr.setDriveStarter(attrValue);
                        break;

                    case DRIVE_STOP_CYCLES:
                        newAttr.setStartStopCycles(value);
                        break;

                    case DRIVE_T_VOLT:
                        newAttr.setDriveTVolt(attrValue);
                        break;

                    case DRIVE_TYPE:
                        newAttr.setDriveType(attrValue);
                        break;

                    case DRIVE_VFD:
                        newAttr.setDriveVFD(attrValue);
                        break;

                    case DRIVE_VOLTAGE:
                        newAttr.setDriveVoltage(attrValue);
                        break;

                    case DRIVE_WIRING:
                        newAttr.setDriveWiring(attrValue);
                        break;

                    case SAP_MATERIAL_NUMBER:
                        newAttr.setSAPMaterialNumber(attrValue);
                        break;

                    default:
                        log.warn("Handler for {} is missing", attrName);
                }
            }

            if (newAttr.getSAPMaterialNumber() == null) {
                log.error("Skip the attribute with empty SAP Material number");
                continue;
            }

            entities.add(newAttr);
        }

        decompositionAttributesRepository.saveAll(entities);
    }

    @Override
    public List<ConnectionPointDto> getConnectionPoints(Long componentId) {
        return connectionPointService.findComponentConnectionPoint(componentId);
    }

    @Override
    public List<Long> getUniqueIdsByProjectAndGroup(String projectName, Group axgroup) throws SpineException {
        ProjectFilter projectFilter = ProjectFilter.builder()
                .projectName(projectName)
                .build();
        List<ProjectEntity> projectEntity = projectRepository.find(projectFilter);
        if (projectEntity.isEmpty()) {
            throw new SpineException("No project was found by name " + projectName);
        }

        Set<Long> ret = new HashSet<>();

        Map<GroupTypeEnum, String> groupInfo = ComponentUtils.getGroupInfo(axgroup);
        List<Long> componentGroupIds = new ArrayList<>();
        if (MapUtils.isNotEmpty(groupInfo)) {
            for (Map.Entry<GroupTypeEnum, String> entry : groupInfo.entrySet()) {
                GroupTypeEnum type = entry.getKey();
                String groupName = entry.getValue();

                if ("*".equals(groupName)) {
                    componentGroupIds = componentRepository.findIdsByProjectNameAndGroupTypeAndGroupIds(projectName,
                            type, null);
                } else {
                    componentGroupIds = componentRepository.findIdsByProjectNameAndGroupTypeAndGroupIds(projectName,
                            type, List.of(groupName));
                }

                if (componentGroupIds.isEmpty()) {
                    log.warn("No component was found by group type {} and group name {}", type, groupName);
                }
            }
        } else {
            componentGroupIds = componentRepository.findIdsByProject(projectName);
        }

        if (CollectionUtils.isNotEmpty(componentGroupIds)) {
            ret.addAll(componentGroupIds);
        }

        if (ret.isEmpty()) {
            log.error("No component was found by {}", groupInfo);
        }

        return new ArrayList<>(ret);
    }

    @Override
    public List<Long> getUniqueIdsFromList(String projectName,
                                           String listTypes,
                                           List<Group> groups,
                                           boolean getFullData) {
        if (groups == null) {
            throw new IllegalArgumentException("List of groups is required!");
        }

        List<Long> componentIds = new ArrayList<>();

        Map<GroupTypeEnum, List<String>> groupMapping = ComponentUtils.getGroupsInfo(groups);
        for (Map.Entry<GroupTypeEnum, List<String>> entry : groupMapping.entrySet()) {
            GroupTypeEnum type = entry.getKey();
            List<String> groupNames = entry.getValue();

            List<Long> componentGroupIds;
            if (groupNames.contains("*")) {
                componentGroupIds = componentRepository.findIdsByProjectNameAndGroupTypeAndGroupIds(projectName, type,
                        null);
            } else {
                componentGroupIds = componentRepository.findIdsByProjectNameAndGroupTypeAndGroupIds(projectName, type,
                        groupNames);
            }

            if (componentGroupIds.isEmpty()) {
                log.warn("No component was found by group type {} and group name {}", type, groupNames);
            } else {
                componentIds.addAll(componentGroupIds);
            }
        }

        return componentIds;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public void delete(List<Long> componentIds) throws SpineException {
        if (componentIds == null || componentIds.isEmpty()) {
            throw new SpineException("Invalid input");
        }

        List<ComponentEntity> entities = componentRepository.findAllByIds(componentIds);
        List<Long> invalidComponents = new ArrayList<>();
        for (ComponentEntity entity : entities) {
            if (entity.getCurrentState() != ComponentState.DELETED.getV1Status()) {
                invalidComponents.add(entity.getId());
            }
        }

        if (!invalidComponents.isEmpty()) {
            String msg = String.format("Please set the status of component(s) %s to deleted before eliminating",
                    invalidComponents);
            throw new SpineException(msg);
        }

        // delete related data of the components
        componentStatusRepository.deleteAll(componentIds);
        stateRepository.deleteByComponentIds(componentIds);
        decompositionRepository.deleteByComponentIds(componentIds);
        for (Long uniqueId : componentIds) {
            connectionPointService.deleteComponentConnectionPoint(uniqueId);
        }

        // delete components
        int deleted = componentRepository.deleteAll(componentIds);
        if (deleted == 0) {
            throw new SpineException("No components could be deleted. Please reload the data then try again");
        }
    }

    @Override
    public List<Long> getUniqueIdsByProject(String projectName) {
        return componentRepository.findIdsByProject(projectName);
    }

    @Override
    public List<ComponentDTO> getComponentsByProjectId(Long projectId) {
        List<ComponentView> componentViews = componentRepository.findAllByProjectId(projectId);
        return componentViews.stream().map(componentMapper::toComponentDto).toList();
    }

    @Override
    public ComponentDTO getComponentByIdAtRevision(Long projectId, Long componentId, Integer revision) {
        return componentRepository.getHistoryAtRevision(componentId, revision)
                .map(componentMapper::toComponentDto)
                .orElse(null);
    }

    private List<Long> getComponentsBySpecMemberAndProject(String projectName,
                                                           List<ComponentSpecificationMember> specificationMembers,
                                                           int start,
                                                           int end) throws SpineException {
        Map<String, List<String>> conditions = new HashMap<>();
        for (ComponentSpecificationMember specificationMember : specificationMembers) {
            conditions.put(specificationMember.getConditionName(), specificationMember.getParameters());
        }

        return componentRepository.findIdsByProjectNameAndCondition(projectName, conditions, start, end);
    }

    private Long createOrUpdateComponent(ProjectEntity projectEntity, ComponentDTO dto) throws SpineException {
        Long componentId = dto.getUniqueID();
        if (componentId == null) {
            // Attempt to generate a new component ID
            componentId = getUniqueComponentId(projectEntity);
            dto.setUniqueID(componentId);
        }

        // Update for exists component or insert as new one
        Optional<ComponentEntity> componentOpt = componentRepository.findById(componentId);
        ComponentEntity component;
        if (componentOpt.isPresent()) {
            log.info("Component {} is already exist. Updating this component...", componentId);
            component = componentOpt.get();
            if (!Objects.equals(component.getProjectId(), projectEntity.getProjectID())) {
                throw new SpineException(
                        String.format("Update invalid component to project %d", projectEntity.getProjectID()));
            }
            componentMapper.update(dto, component);
        } else {
            log.info("Creating new component {}...", componentId);
            component = componentMapper.toEntity(dto);
            component.setProjectId(projectEntity.getProjectID());
            component.setSysCreateDate(Timestamp.valueOf(LocalDateTime.now()));
        }

        // Update state
        addState(projectEntity, component, dto);

        // Update the group
        updateGroups(projectEntity, component, dto.getGroupMapping());

        // update the connection point
        if (dto.isUseConnectionPoints()) {
            addConnectionPoints(component, dto.getConnectionPointDtos());
        }

        // Set position
        addPosition(component, dto.getPositionDto());

        // Set decomposition
        if (dto.isUseDecompositions()) {
            updateComponentDecompositions(component, dto.getDecompositionDtos());
        }

        // update sys mod date
        component.setSysModDate(Timestamp.valueOf(LocalDateTime.now()));
        componentRepository.save(component);
        return component.getId();
    }

    private Long getUniqueComponentId(ProjectEntity projectEntity) throws SpineException {
        Long id = counterService.retrieveNextValueFromCounter(CounterServiceImpl.COUNTER_COMPONENT_NAME);
        if (id == null) {
            log.error("Failed to generate a new component ID for project {}", projectEntity.getProjectID());
            throw new SpineException(
                    String.format("Unable to generate ID for component in project %d", projectEntity.getProjectID()));
        }
        return id;
    }

    private void addConnectionPoints(ComponentEntity entity, List<ConnectionPointDto> connectionPointDtos) {
        List<ConnectionPointEntity> currentConnectionPoints = entity.getConnectionPoints();
        if (!ComponentUtils.connectionPointsEToString(currentConnectionPoints)
                .equals(ComponentUtils.connectionPointsDToString(connectionPointDtos))) {
            log.debug("Connection_Points ARE DIFFERENT");
            entity.setSysModDate(new Timestamp(System.currentTimeMillis()));
        }
        entity.removeConnectionPoints();
        List<ConnectionPointEntity> updateConnectionPoints = connectionPointDtos.stream()
                .map(connectionPointMapper::toEntity)
                .toList();
        entity.updateConnectionPoints(updateConnectionPoints);
    }

    private void addState(ProjectEntity projectEntity, ComponentEntity entity, ComponentDTO dto) {
        ComponentState defaultState = ProjectState.EXECUTION.name().equals(projectEntity.getMainProjectState()) ?
                ComponentState.EXECUTION_PHASE_200_0 :
                ComponentState.PROPOSAL_PHASE_40_0;
        int v1State = defaultState.getV1Status();
        Integer newState = (dto.getState() == null || dto.getState().getState() == null) ?
                null :
                Integer.parseInt(dto.getState().getState().getValue());
        StateEntity newStateEntity = null;
        if (newState != null && ComponentState.resolve(newState) != null) {
            v1State = newState;
        }
        if (dto.isUseState() && dto.getState() != null && (dto.getState().getState() != null || dto.getState()
                .getReason() != null) && !Objects.equals(entity.getCurrentState(), newState)) {
            String remark = dto.getState().getRemark();
            newStateEntity = StateEntity.builder()
                    .state(v1State)
                    .remark(remark)
                    .reason(dto.getState().getReason())
                    .username(SecurityUtils.getUsernameFromToken())
                    .statusDate(dto.getState().getStatusDate() == null ?
                            new Timestamp(System.currentTimeMillis()) :
                            dto.getState().getStatusDate())
                    .component(entity)
                    .build();
            if (StringUtils.isNotEmpty(remark)) {
                Optional<ChangeGroupEntity> changeGroupEntity = changeGroupRepository.getChangeGroupByProjectIdAndName(
                        projectEntity.getProjectID(), remark);
                changeGroupEntity.ifPresent(newStateEntity::setChangeGroup);
            }
        } else if (dto.getUniqueID() == null) {
            newStateEntity = StateEntity.builder()
                    .state(v1State)
                    .username(SecurityUtils.getUsernameFromToken())
                    .statusDate(new Timestamp(System.currentTimeMillis()))
                    .component(entity)
                    .build();
        }
        if (newStateEntity != null) {
            entity.getStates().add(newStateEntity);
            entity.setCurrentState(v1State);
            log.info("Set the state of component {} to {}", entity.getId(), v1State);
        }
    }

    private void updateGroups(ProjectEntity projectEntity,
                              ComponentEntity componentEntity,
                              GroupMappingDto groupMappingDto) {
        if (groupMappingDto == null) {
            return;
        }
        Set<GroupEntity> currentComponentGroups = componentEntity.getGroups();
        Map<GroupTypeEnum, Map<String, GroupEntity>> currentComponentGroupsMap = currentComponentGroups.stream()
                .collect(Collectors.groupingBy(GroupEntity::getGrouptype,
                        Collectors.toMap(GroupEntity::getName, Function.identity(), (entity1, entity2) -> entity2,
                                HashMap::new)));
        Map<GroupTypeEnum, String> updateGroupMapping = ComponentUtils.getGroupInfo(groupMappingDto);
        if (updateGroupMapping.isEmpty()) {
            return;
        }
        List<GroupEntity> existsGroups = groupRepository.findAllGroupsByTypeAndNameAndProjectId(updateGroupMapping,
                projectEntity.getProjectID());
        Map<GroupTypeEnum, Map<String, GroupEntity>> existsGroupsMap = existsGroups.stream()
                .collect(Collectors.groupingBy(GroupEntity::getGrouptype,
                        Collectors.toMap(GroupEntity::getName, Function.identity(), (entity1, entity2) -> entity2,
                                HashMap::new)));

        for (GroupTypeEnum groupType : GroupTypeEnum.values()) {
            // remove group in component
            if (!updateGroupMapping.containsKey(groupType) && MapUtils.isNotEmpty(
                    currentComponentGroupsMap.get(groupType))) {
                for (GroupEntity group : currentComponentGroupsMap.get(groupType).values()) {
                    componentEntity.removeGroup(group);
                }
            }
            if (updateGroupMapping.containsKey(groupType) && (MapUtils.isEmpty(
                    currentComponentGroupsMap.get(groupType)) || !currentComponentGroupsMap.get(groupType)
                    .containsKey(updateGroupMapping.get(groupType)))) {
                GroupEntity group;
                if (existsGroupsMap.containsKey(groupType) && existsGroupsMap.get(groupType)
                        .containsKey(updateGroupMapping.get(groupType))) {
                    group = existsGroupsMap.get(groupType).get(updateGroupMapping.get(groupType));
                } else {
                    group = new GroupEntity();
                    group.setProjectId(projectEntity.getProjectID());
                    group.setGrouptype(groupType);
                    group.setName(updateGroupMapping.get(groupType));
                }
                componentEntity.addGroup(group);
                // remove component from other groups
                if (MapUtils.isNotEmpty(currentComponentGroupsMap.get(groupType))) {
                    for (GroupEntity other : currentComponentGroupsMap.get(groupType).values()) {
                        componentEntity.removeGroup(other);
                    }
                }
            }
        }
    }

    private void addPosition(ComponentEntity entity, PositionDto positionDto) {
        if (positionDto == null) {
            return;
        }
        PositionEntity positionEntity = entity.getPosition();
        if (positionEntity != null) {
            entity.getPosition().setX(positionDto.getX());
            entity.getPosition().setY(positionDto.getY());
            entity.getPosition().setZ(positionDto.getZ());
        } else {
            positionEntity = positionMapper.toEntity(positionDto);
            entity.setPosition(positionEntity);
        }
        positionEntity.setComponent(entity);

        log.info("Updated position of component {} to (x, y, z): ({}, {}, {})", entity.getId(), positionEntity.getX(),
                positionEntity.getY(), positionEntity.getZ());
    }

    private void updateComponentDecompositions(ComponentEntity component,
                                               List<DecompositionListDto> decompositionListDtos) {
        if (decompositionListDtos == null || decompositionListDtos.isEmpty() || StringUtils.isEmpty(
                decompositionListDtos.get(0).getSource())) {
            return;
        }
        for (DecompositionListDto decompositionListDto : decompositionListDtos) {
            List<DecompositionDto> decompositionDtos = decompositionListDto.getDecompositions();
            String mode = decompositionListDto.getMode();
            String source = decompositionListDto.getSource();
            switch (mode) {
                case OVERWRITE_MODE:
                    component.removeDecompositionsBySource(source);
                    component.updateDecompositions(decompositionDtos.stream()
                            .map(decompositionMapper::toEntity)
                            .toList());
                    break;
                case APPEND_MODE:
                    component.updateDecompositions(decompositionDtos.stream()
                            .map(decompositionMapper::toEntity)
                            .toList());
                    break;
                case DELETE_MODE:
                    component.removeDecompositionsBySource(source);
                    break;
                default:
                    log.warn("Unknown mode update decompositions");
            }
        }
    }

}
