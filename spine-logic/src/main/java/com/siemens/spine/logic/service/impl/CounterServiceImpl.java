package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.repository.CounterRepository;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.service.CounterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 6/1/2023
 */
@ApplicationScoped
@Slf4j
public class CounterServiceImpl implements CounterService {

    public static final String COUNTER_COMPONENT_NAME = "componentUniqueIdCounter";
    public static final String COUNTER_BOM = "BoMCounter";
    private static final int DEFAULT_QUANTITY = 1;
    private final CounterRepository counterRepository;

    @Inject
    public CounterServiceImpl(CounterRepository counterRepository) {
        this.counterRepository = counterRepository;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public List<Long> retrieveNextValuesFromCounter(Integer amount, String counterName) throws SpineException {
        if (StringUtils.isEmpty(counterName)) {
            throw new SpineException("Counter name could not be null or empty");
        }

        if (amount == null || amount <= 0) {
            log.warn("The amount of value is invalid. Generate 1 value by default");
            amount = DEFAULT_QUANTITY;
        }
        List<Long> ids = counterRepository.getNextValuesCounter(counterName, amount);
        if (CollectionUtils.isEmpty(ids)) {
            throw new SpineException("Can not create next value of counter " + counterName);
        }
        return ids;
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public Long retrieveNextValueFromCounter(String counterName) throws SpineException {
        List<Long> nextValues = retrieveNextValuesFromCounter(1, counterName);
        if (CollectionUtils.isEmpty(nextValues)) {
            throw new SpineException("Can not create next value of counter " + counterName);
        }
        return nextValues.get(0);
    }

}
