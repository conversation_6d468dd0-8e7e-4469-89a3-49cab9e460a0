package com.siemens.spine.logic.service;

import com.siemens.spine.logic.dto.ComponentStateChangedResponseDto;
import com.siemens.spine.logic.dto.request.StateChangedRequestDto;
import com.siemens.spine.logic.exception.SpineException;
import com.siemens.spine.logic.service.functioninterface.ComponentStateChangedProcessor;

import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
public interface BatchJobService {

    int DEFAULT_BATCH_SIZE = 1000;

    /**
     * Split the large data (input) to multiple tasks and then execute in parallel. It will wait until all tasks are completed
     *
     * @param function The execution of the input
     * @param input    The large data need to be split to execute
     * @param <T>      Type of input item
     * @param <R>      Type of response
     * @return The response collection of all tasks
     * @throws SpineException
     */
    <T, R> List<R> executeJobs(Function<List<T>, R> function, List<T> input) throws SpineException;

    /**
     * Split the large data (input) to multiple tasks and then execute in parallel. It will wait until all tasks are completed
     *
     * @param function  The execution of the input
     * @param input     The large data need to be split to execute
     * @param batchSize Maximum number of the items for each subset of the input
     * @param <T>       Type of input item
     * @param <R>       Type of response
     * @return The response collection of all tasks
     * @throws SpineException
     */
    <T, R> List<R> executeJobs(Function<List<T>, R> function, List<T> input, int batchSize) throws SpineException;

    /**
     * Split the large data (input) to multiple tasks and then execute in parallel. It will wait until all tasks are completed
     *
     * @param function         The execution of the input
     * @param input            The large data need to be split to execute
     * @param batchSize        Maximum number of the items for each subset of the input
     * @param <T>              Type of input item
     * @param <R>              Type of response
     * @param exceptionHandler Handle the exception. If null, exception will be not handled
     * @return The response collection of all tasks
     * @throws SpineException
     */
    <T, R> List<R> executeJobs(Function<List<T>, R> function,
                               List<T> input,
                               int batchSize,
                               BatchJobExceptionHandler exceptionHandler) throws SpineException;

    /**
     * Split and execute update the status of a large components
     *
     * @param processor
     * @param input
     * @return list of the response of all sub-jobs
     * @throws SpineException
     */
    List<ComponentStateChangedResponseDto> executeComponentStateChanged(ComponentStateChangedProcessor processor,
                                                                        StateChangedRequestDto input,
                                                                        int batchSize) throws SpineException;

    /**
     * Handle the exception happened in a job
     */
    interface BatchJobExceptionHandler {

        void handle(Exception e);

    }

}
