package com.siemens.spine.logic.validator.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.sf.jsqlparser.statement.Statement;

import java.util.Map;
import java.util.Set;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryValidationContext {

    private String queryString;

    private QueryType queryType;

    private Map<String, Object> parameters;

    private Set<String> allowedTables;

    private Statement parsedStatement;

}