package com.siemens.spine.logic.validator;

import com.siemens.spine.logic.service.ProjectService;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2/17/2023
 */

@ApplicationScoped
public class ProjectValidator {

    @Inject
    private ProjectService projectService;

    private final DateTimeFormatter format = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");

    public boolean validate(String projectName) {
        checkProjectNotNull(projectName);
        checkExist(projectName);
        return true;
    }

    public boolean checkProjectNotNull(String projectName) {
        if (projectName == null) {
            throw new IllegalArgumentException(
                    "[SPINE 0001:{Project name is required " + LocalDateTime.now().format(format) + "}]");
        }
        return true;

    }

    public boolean checkExist(String projectName) {
        boolean isExits = projectService.checkProjectNameExits(projectName);
        if (!isExits) {
            throw new IllegalArgumentException(
                    "[SPINE 0002:{Project with name:" + projectName + " does not exist " + LocalDateTime.now()
                            .format(format));
        }
        return true;
    }

}
