package com.siemens.spine.logic.service;

import com.siemens.spine.db.entity.ConnectionPointEntity;
import com.siemens.spine.generated.toolchain.ComponentNeighborConnections;
import com.siemens.spine.logic.dto.ConnectionPointDto;
import com.siemens.spine.logic.exception.SpineException;

import java.util.List;

public interface ConnectionPointService {

    List<ConnectionPointDto> findComponentConnectionPoint(Long componentId);

    void deleteComponentConnectionPoint(Long componentId);

    void deleteConnectionPoints(List<ConnectionPointEntity> connectionPoints);

    List<ComponentNeighborConnections> getNeighborConnection(String projectName, List<Long> componentIds)
            throws SpineException;

    boolean setNeighborConnection(String projectName, List<ComponentNeighborConnections> componentNeighborConnections)
            throws SpineException;

}
