package com.siemens.spine.logic.model;

import com.siemens.spine.db.constant.GroupTypeEnum;
import lombok.Data;

import java.sql.Timestamp;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/04/28
 */
@Data
public class Group {

    private Long id;

    private String name;

    private GroupTypeEnum grouptype;

    private Timestamp deliverydate;

    private String comment;

    private String subtype;

    private Set<Component> components = new HashSet<>();

    private Long projectId;

}
