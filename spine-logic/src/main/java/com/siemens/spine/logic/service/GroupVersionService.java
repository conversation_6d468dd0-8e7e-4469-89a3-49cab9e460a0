package com.siemens.spine.logic.service;

import com.siemens.spine.db.entity.GroupEntity;
import com.siemens.spine.db.entity.RevisionInfoEntity;
import com.siemens.spine.logic.model.BaseVersion;
import com.siemens.spine.logic.model.Group;

/**
 * <AUTHOR>
 * @since 2025/04/21
 */
public interface GroupVersionService extends VersionService<Long, Group, BaseVersion, GroupEntity> {

    /**
     * Freeze a component version when it reaches state "200.1 – Layout freeze". Commits a special
     * version for SPINE-ELCON synchronization.
     *
     * @param componentId  ID of the component
     * @param revisionInfo User performing the freeze action
     * @throws IllegalArgumentException if component not found
     * @throws IllegalStateException    if component is not in state 200.1
     */
    void handleNewRevision(Long componentId, RevisionInfoEntity revisionInfo);

    @Override
    default Class<GroupEntity> getTargetEntityClass() {
        return GroupEntity.class;
    }

}
