package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.entity.GroupToProjectAndRoleEntity;
import com.siemens.spine.db.repository.GroupToProjectAndRoleRepository;
import com.siemens.spine.generated.toolchain.UserRole;
import com.siemens.spine.logic.security.SecurityContextHolder;
import com.siemens.spine.logic.service.IamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApplicationScoped
@Slf4j
public class IamServiceImpl implements IamService {

    private final GroupToProjectAndRoleRepository roleRepository;

    @Inject
    public IamServiceImpl(GroupToProjectAndRoleRepository roleRepository) {
        this.roleRepository = roleRepository;
    }

    @Override
    public List<String> getProjectRoleOfUser(Long projectId) {
        return roleRepository.findRoleByProjectIdAndUserGroup(projectId,
                List.copyOf(SecurityContextHolder.getAuthentication().getGroups()));
    }

    @Override
    public List<String> getProjectRoleOfUser(Long projectId, String username) {
        if (StringUtils.isEmpty(username)) {
            log.error("User could not be null/empty");
            return Collections.emptyList();
        }

        if (projectId == null) {
            log.error("Project id could not be null/empty");
            return Collections.emptyList();
        }

        List<String> ret = new ArrayList<>();
        List<GroupToProjectAndRoleEntity> userProjectRole = roleRepository.findProjectRoleOfUser(projectId, username);
        for (GroupToProjectAndRoleEntity role : userProjectRole) {
            if (role.getRoleName() != null) {
                ret.add(role.getRoleName());
            }
        }

        log.debug("Role of user {} in project {}: {}", username, projectId, ret);
        return ret;
    }

    @Override
    public List<UserRole> getUserRoles() {
        // Fixed role is Admin later will get role from token and  security context
        //        Set<String> roles = SecurityUtils.getUserRoles();
        List<String> roles = List.of("ADMIN");

        List<UserRole> userRoles = roles.stream().map(role -> {
            UserRole userRole = new UserRole();
            userRole.setRole(role);
            return userRole;
        }).toList();

        return userRoles;
    }

}
