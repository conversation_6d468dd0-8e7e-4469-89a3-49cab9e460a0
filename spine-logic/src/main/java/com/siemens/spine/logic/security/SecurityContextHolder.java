package com.siemens.spine.logic.security;

public final class SecurityContextHolder {

    private static final ThreadLocal<Authentication> holder = new ThreadLocal<>();

    /**
     * Retrieve the authentication information
     *
     * @return
     */
    public static Authentication getAuthentication() {
        return holder.get();
    }

    /**
     * Associates a new Authentication with the current thread of execution.
     *
     * @return
     */
    public static void setAuthentication(Authentication authentication) {
        if (authentication == null) {
            throw new IllegalArgumentException("Authentication could not be null");
        }

        holder.set(authentication);
    }

    public static void clearAuthentication() {
        holder.remove();
    }

}
