package com.siemens.spine.logic.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StateDto {

    private Long id;

    private SimplePairDto state;

    private Integer reason;

    private String username;

    private String remark;

    private Timestamp statusDate;

    private Long componentId;

    private ChangeGroupDto changeGroup;

}
