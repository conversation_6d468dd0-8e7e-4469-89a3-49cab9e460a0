package com.siemens.spine.logic.mapper;

import com.siemens.spine.db.constant.ProjectTypeClassificationEnum;
import com.siemens.spine.db.entity.TypeEntity;
import com.siemens.spine.logic.dto.TypeDto;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Pham
 * @version 1.0
 * @since 10/01/2023
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TypeMapper {

    TypeMapper INSTANCE = Mappers.getMapper(TypeMapper.class);

    @Mapping(target = "projectId", source = "project.projectID")
    @Mapping(target = "projectName", source = "project.projectName")
    @Mapping(target = "projectTypeClassification", qualifiedByName = "getProjectTypeClassification")
    TypeDto toType(TypeEntity type);

    List<TypeDto> toTypes(List<TypeEntity> typeEntities);

    @Mapping(target = "mainType", source = "Main_Type")
    @Mapping(target = "subType", source = "Sub_Type")
    @Mapping(target = "specialType", source = "Special_Type")
    @Mapping(target = "typeVersion", source = "Type_Version")
    @Mapping(target = "description", source = "Description")
    @Mapping(target = "mechMaterialCostKpi", source = "Mech_Material_Cost_KPI", qualifiedByName = "stringToDouble")
    @Mapping(target = "engineeringMeHKpi", source = "Engineering_ME_H_KPI", qualifiedByName = "stringToDouble")
    @Mapping(target = "engineeringHwHPki", source = "Engineering_HW_H_KPI", qualifiedByName = "stringToDouble")
    @Mapping(target = "engineeringPlcHKpi", source = "Engineering_PLC_H_KPI", qualifiedByName = "stringToDouble")
    @Mapping(target = "engineeringItHKpi", source = "Engineering_IT_H_KPI", qualifiedByName = "stringToDouble")
    @Mapping(target = "typeId", source = "Type_ID")
    @Mapping(target = "mps", source = "MPS")
    @Mapping(target = "installationHKpi", source = "Installation_H_KPI", qualifiedByName = "stringToDouble")
    @Mapping(target = "plcCommissioningHKpi", source = "PLC_Commissioning_H_KPI", qualifiedByName = "stringToDouble")
    @Mapping(target = "engineeringScadaHKpi", source = "Engineering_SCADA_H_KPI", qualifiedByName = "stringToDouble")
    @Mapping(target = "supplier", source = "Supplier")
    @Mapping(target = "url1", source = "URL1")
    @Mapping(target = "url2", source = "URL2")
    @Mapping(target = "url3", source = "URL3")
    @Mapping(target = "url4", source = "URL4")
    @Mapping(target = "url5", source = "URL5")
    @Mapping(target = "defaultDriveCount", source = "Default_Drive_Count", qualifiedByName = "stringToInteger")
    @Mapping(target = "typeVariant", source = "Type_Variant")
    @Mapping(target = "defaultSlope", source = "Default_Slope")
    @Mapping(target = "versionNumber", source = "Version_Number")
    @Mapping(target = "sapMaterialNumber", source = "SAP_Material_Number")
    @Mapping(target = "beltMass", source = "BeltMass", qualifiedByName = "stringToDouble")
    @Mapping(target = "requiredAcceleration", source = "RequiredAcceleration", qualifiedByName = "stringToDouble")
    @Mapping(target = "drivePulleyDiameter", source = "DrivePulleyDiameter", qualifiedByName = "stringToDouble")
    @Mapping(target = "frictionFactor", source = "FrictionFactor", qualifiedByName = "stringToDouble")
    @Mapping(target = "driveShaftDiameter", source = "DriveShaftDiameter", qualifiedByName = "stringToDouble")
    @Mapping(target = "efficiencyGearbox", source = "EfficiencyGearbox", qualifiedByName = "stringToDouble")
    @Mapping(target = "originalType", source = "OriginalType")
    @Mapping(target = "projectTypeClassification", source = "ProjectTypeClassification",
            qualifiedByName = "stringToProjectTypeClassification")
    @Mapping(target = "typeAggregator", source = "TypeAggregator")
    @Mapping(target = "engineeringMeOneOffCosts", source = "Engineering_ME_OneOffCosts",
            qualifiedByName = "stringToDouble")
    @Mapping(target = "engineeringHwOneOffCosts", source = "Engineering_HW_OneOffCosts",
            qualifiedByName = "stringToDouble")
    @Mapping(target = "engineeringPlcOneOffCosts", source = "Engineering_PLC_OneOffCosts",
            qualifiedByName = "stringToDouble")
    @Mapping(target = "engineeringItOneOffCosts", source = "Engineering_IT_OneOffCosts",
            qualifiedByName = "stringToDouble")
    @Mapping(target = "engineeringScadaOneOffCosts", source = "Engineering_SCADA_OneOffCosts",
            qualifiedByName = "stringToInteger")
    @Mapping(target = "projectSpecific", source = "ProjectSpecific")
    @Mapping(target = "hierarchyType", source = "HierarchyType")
    @Mapping(target = "outdated", source = "Outdated", defaultValue = "false")
    @Mapping(target = "project", ignore = true)
    @Mapping(target = "modificationDate", qualifiedByName = "getCurrentDateTime")
    @Mapping(target = "creationDate", qualifiedByName = "getCurrentDateTime")
    @Mapping(target = "solutionId", source = "solutionId", qualifiedByName = "stringToInteger")
    TypeEntity toType(Map<String, String> attributes);

    @Named("getCurrentDateTime")
    default Timestamp getCurrentDateTime(String modificationDate) {
        if (StringUtils.isEmpty(modificationDate)) {
            return Timestamp.valueOf(LocalDateTime.now());
        } else {
            return Timestamp.valueOf(LocalDateTime.parse(modificationDate));
        }
    }

    @Named("getProjectTypeClassification")
    default String getProjectTypeClassification(ProjectTypeClassificationEnum projectTypeClassificationEnum) {
        return projectTypeClassificationEnum == null ? null : projectTypeClassificationEnum.getValue();
    }

    @Named("stringToProjectTypeClassification")
    default ProjectTypeClassificationEnum stringToProjectTypeClassification(String value) {
        return ProjectTypeClassificationEnum.resolve(value);
    }

    @Named("stringToDouble")
    default Double stringToDouble(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        } else {
            return Double.parseDouble(value);
        }
    }

    @Named("stringToInteger")
    default Integer stringToInteger(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        } else {
            return Integer.parseInt(value);
        }
    }

}