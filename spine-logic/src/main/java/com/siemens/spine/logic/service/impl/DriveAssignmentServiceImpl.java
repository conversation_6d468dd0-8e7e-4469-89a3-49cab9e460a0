package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.entity.ComponentEntity;
import com.siemens.spine.db.entity.DriveAssignmentDataEntity;
import com.siemens.spine.db.repository.ComponentRepository;
import com.siemens.spine.db.repository.DriveAssignmentRepository;
import com.siemens.spine.generated.toolchain.ComponentDriveAssignmentData;
import com.siemens.spine.generated.toolchain.DriveAssignmentData;
import com.siemens.spine.logic.service.DriveAssignmentService;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@ApplicationScoped
public class DriveAssignmentServiceImpl implements DriveAssignmentService {

    private final DriveAssignmentRepository driveAssignmentRepository;

    private final ComponentRepository componentRepository;

    @Inject
    public DriveAssignmentServiceImpl(DriveAssignmentRepository driveAssignmentRepository,
                                      ComponentRepository componentRepository) {
        this.driveAssignmentRepository = driveAssignmentRepository;
        this.componentRepository = componentRepository;
    }

    @Override
    public List<ComponentDriveAssignmentData> getDriveAssignment(List<Long> componentIds, String projectName) {
        class LocalComponentDriveAssignmentData extends ComponentDriveAssignmentData {

            void setDataDriveAssignment(List<DriveAssignmentData> driveAssignmentData) {
                super.driveAssignmentData = driveAssignmentData;
            }

        }
        List<DriveAssignmentDataEntity> byComponentId = driveAssignmentRepository.findByComponentId(componentIds);
        return byComponentId.stream()
                .collect(Collectors.groupingBy(e -> e.getComponent().getId(), Collectors.mapping(entity -> {
                    DriveAssignmentData driveAssignmentData = new DriveAssignmentData();
                    driveAssignmentData.setDriveNumber(entity.getDriverNumber());
                    driveAssignmentData.setDriveAssignmentData(entity.getData());
                    return driveAssignmentData;
                }, Collectors.toList())))
                .entrySet().stream().map(entry -> {
                    LocalComponentDriveAssignmentData driveAssignmentData = new LocalComponentDriveAssignmentData();
                    driveAssignmentData.setDataDriveAssignment(entry.getValue());
                    driveAssignmentData.setUniqueId(String.valueOf(entry.getKey()));
                    return (ComponentDriveAssignmentData) driveAssignmentData;
                }).toList();

    }

    @Override
    @Transactional(Transactional.TxType.REQUIRED)
    public boolean setDriveAssignment(List<ComponentDriveAssignmentData> componentDriveAssignmentDataList) {
        for (ComponentDriveAssignmentData componentDriveAssignmentData : componentDriveAssignmentDataList) {
            Long componentId = Long.valueOf(componentDriveAssignmentData.getUniqueId());
            Optional<ComponentEntity> componentEntity = componentRepository.findById(componentId);
            if (componentEntity.isEmpty()) {
                return false;
            }
            ComponentEntity component = componentEntity.get();
            for (DriveAssignmentData driveAssignmentDatum : componentDriveAssignmentData.getDriveAssignmentData()) {
                DriveAssignmentDataEntity driveAssignmentData = driveAssignmentRepository.findByComponentIdAndDriverNumber(
                        componentId, driveAssignmentDatum.getDriveNumber());
                if (driveAssignmentData != null) {
                    driveAssignmentData.setData(driveAssignmentDatum.getDriveAssignmentData());
                } else {
                    driveAssignmentData = new DriveAssignmentDataEntity();
                    driveAssignmentData.setDriverNumber(driveAssignmentDatum.getDriveNumber());
                    driveAssignmentData.setComponent(component);
                    driveAssignmentData.setData(driveAssignmentDatum.getDriveAssignmentData());
                }
                component.addDriveAssignmentData(driveAssignmentData);
            }
        }
        return true;
    }

}
