package com.siemens.spine.logic.service.impl;

import com.siemens.spine.db.entity.GroupEntity;
import com.siemens.spine.db.entity.RevisionInfoEntity;
import com.siemens.spine.db.repository.GroupRepository;
import com.siemens.spine.logic.dto.request.GetObjectsVersionRequestDTO;
import com.siemens.spine.logic.mapper.GroupVersionMapper;
import com.siemens.spine.logic.mapper.RevisionInfoMapper;
import com.siemens.spine.logic.model.BaseVersion;
import com.siemens.spine.logic.model.Group;
import com.siemens.spine.logic.model.RevisionInfo;
import com.siemens.spine.logic.service.GroupVersionService;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/04/28
 */

@Slf4j
@ApplicationScoped
@Transactional(value = TxType.REQUIRED, rollbackOn = Exception.class)
public class GroupVersionServiceImpl implements GroupVersionService {

    @Inject
    private GroupRepository groupRepository;
    @Inject
    private GroupVersionMapper groupVersionMapper;
    @Inject
    private RevisionInfoMapper revisionInfoMapper;

    @Override
    public void handleNewRevision(Long id, RevisionInfoEntity revisionInfo) {
        log.info("Handling new revision group {} with revision info {}", id, revisionInfo);
    }

    @Override
    public Class<GroupEntity> getTargetEntityClass() {
        return GroupVersionService.super.getTargetEntityClass();
    }

    @Override
    public List<Group> getObjectsInRevision(GetObjectsVersionRequestDTO requestDTO) {
        throw new UnsupportedOperationException("Not impl");
    }

    @Override
    public List<Group> getObjectsInVersion(GetObjectsVersionRequestDTO requestDTO) {
        throw new UnsupportedOperationException("Not impl");
    }

    @Override
    public List<BaseVersion> getAllVersions(Long targetId) {
        throw new UnsupportedOperationException("Not impl");
    }

    @Override
    public List<RevisionInfo> getAllRevisions(Long componentId) {
        List<RevisionInfoEntity> revisionInfoEntities = groupRepository.getAllRevisionInfo(
                componentId);
        revisionInfoEntities.sort(
                (rev1, rev2) -> Long.compare(rev2.getTimestamp(), rev1.getTimestamp()));

        return revisionInfoEntities.stream().map(revisionInfoMapper::toRevisionInfo)
                .collect(Collectors.toList());
    }

    @Override
    public <DTO> BaseVersion createVersion(DTO dto) {
        return null;
    }

}
