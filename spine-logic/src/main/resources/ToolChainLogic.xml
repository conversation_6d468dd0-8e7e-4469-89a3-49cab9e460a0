<?xml version='1.0' encoding='UTF-8'?><wsdl:definitions xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://toolchain.siemens.pl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:ns1="http://schemas.xmlsoap.org/soap/http" name="ToolChainLogic" targetNamespace="http://toolchain.siemens.pl">
  <wsdl:types>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://toolchain.siemens.pl" targetNamespace="http://toolchain.siemens.pl" version="1.0">

  <xs:element name="SetComponentsStatusInList" type="tns:SetComponentsStatusInList"/>

  <xs:element name="SetComponentsStatusInListResponse" type="tns:SetComponentsStatusInListResponse"/>

  <xs:element name="calculateDrive" type="tns:calculateDrive"/>

  <xs:element name="calculateDriveResponse" type="tns:calculateDriveResponse"/>

  <xs:element name="createChangeGroup" type="tns:createChangeGroup"/>

  <xs:element name="createChangeGroupResponse" type="tns:createChangeGroupResponse"/>

  <xs:element name="createComponents" type="tns:createComponents"/>

  <xs:element name="createComponentsResponse" type="tns:createComponentsResponse"/>

  <xs:element name="createDecompositionAttributes" type="tns:createDecompositionAttributes"/>

  <xs:element name="createDecompositionAttributesResponse" type="tns:createDecompositionAttributesResponse"/>

  <xs:element name="createTypes" type="tns:createTypes"/>

  <xs:element name="createTypesResponse" type="tns:createTypesResponse"/>

  <xs:element name="deleteComponents" type="tns:deleteComponents"/>

  <xs:element name="deleteComponentsResponse" type="tns:deleteComponentsResponse"/>

  <xs:element name="deleteGroup" type="tns:deleteGroup"/>

  <xs:element name="deleteGroupResponse" type="tns:deleteGroupResponse"/>

  <xs:element name="generateComponentUniqueIds" type="tns:generateComponentUniqueIds"/>

  <xs:element name="generateComponentUniqueIdsResponse" type="tns:generateComponentUniqueIdsResponse"/>

  <xs:element name="getAllChangeGroupsInProject" type="tns:getAllChangeGroupsInProject"/>

  <xs:element name="getAllChangeGroupsInProjectResponse" type="tns:getAllChangeGroupsInProjectResponse"/>

  <xs:element name="getAllProjects" type="tns:getAllProjects"/>

  <xs:element name="getAllProjectsResponse" type="tns:getAllProjectsResponse"/>

  <xs:element name="getAllStates" type="tns:getAllStates"/>

  <xs:element name="getAllStatesResponse" type="tns:getAllStatesResponse"/>

  <xs:element name="getAllTypes" type="tns:getAllTypes"/>

  <xs:element name="getAllTypesResponse" type="tns:getAllTypesResponse"/>

  <xs:element name="getBoMListData" type="tns:getBoMListData"/>

  <xs:element name="getBoMListDataResponse" type="tns:getBoMListDataResponse"/>

  <xs:element name="getBomListDataByUniqueIds" type="tns:getBomListDataByUniqueIds"/>

  <xs:element name="getBomListDataByUniqueIdsResponse" type="tns:getBomListDataByUniqueIdsResponse"/>

  <xs:element name="getBomListUniqueIds" type="tns:getBomListUniqueIds"/>

  <xs:element name="getBomListUniqueIdsResponse" type="tns:getBomListUniqueIdsResponse"/>

  <xs:element name="getChangeListStates" type="tns:getChangeListStates"/>

  <xs:element name="getChangeListStatesResponse" type="tns:getChangeListStatesResponse"/>

  <xs:element name="getComponent" type="tns:getComponent"/>

  <xs:element name="getComponentLastModDate" type="tns:getComponentLastModDate"/>

  <xs:element name="getComponentLastModDateResponse" type="tns:getComponentLastModDateResponse"/>

  <xs:element name="getComponentResponse" type="tns:getComponentResponse"/>

  <xs:element name="getComponents" type="tns:getComponents"/>

  <xs:element name="getComponentsBySpecification" type="tns:getComponentsBySpecification"/>

  <xs:element name="getComponentsBySpecificationResponse" type="tns:getComponentsBySpecificationResponse"/>

  <xs:element name="getComponentsByUniqueIds" type="tns:getComponentsByUniqueIds"/>

  <xs:element name="getComponentsByUniqueIdsResponse" type="tns:getComponentsByUniqueIdsResponse"/>

  <xs:element name="getComponentsResponse" type="tns:getComponentsResponse"/>

  <xs:element name="getComponentsStateHistory" type="tns:getComponentsStateHistory"/>

  <xs:element name="getComponentsStateHistoryResponse" type="tns:getComponentsStateHistoryResponse"/>

  <xs:element name="getComponentsUniqueIds" type="tns:getComponentsUniqueIds"/>

  <xs:element name="getComponentsUniqueIdsResponse" type="tns:getComponentsUniqueIdsResponse"/>

  <xs:element name="getConnectionPoints" type="tns:getConnectionPoints"/>

  <xs:element name="getConnectionPointsResponse" type="tns:getConnectionPointsResponse"/>

  <xs:element name="getDecompositionAttributes" type="tns:getDecompositionAttributes"/>

  <xs:element name="getDecompositionAttributesResponse" type="tns:getDecompositionAttributesResponse"/>

  <xs:element name="getDriveAssignmentData" type="tns:getDriveAssignmentData"/>

  <xs:element name="getDriveAssignmentDataResponse" type="tns:getDriveAssignmentDataResponse"/>

  <xs:element name="getGroupItems" type="tns:getGroupItems"/>

  <xs:element name="getGroupItemsResponse" type="tns:getGroupItemsResponse"/>

  <xs:element name="getNeighborConnections" type="tns:getNeighborConnections"/>

  <xs:element name="getNeighborConnectionsResponse" type="tns:getNeighborConnectionsResponse"/>

  <xs:element name="getPosition" type="tns:getPosition"/>

  <xs:element name="getPositionResponse" type="tns:getPositionResponse"/>

  <xs:element name="getRoleOperations" type="tns:getRoleOperations"/>

  <xs:element name="getRoleOperationsResponse" type="tns:getRoleOperationsResponse"/>

  <xs:element name="getState" type="tns:getState"/>

  <xs:element name="getStateResponse" type="tns:getStateResponse"/>

  <xs:element name="getStates" type="tns:getStates"/>

  <xs:element name="getStatesResponse" type="tns:getStatesResponse"/>

  <xs:element name="getType" type="tns:getType"/>

  <xs:element name="getTypeResponse" type="tns:getTypeResponse"/>

  <xs:element name="getUniqueIdsFromList" type="tns:getUniqueIdsFromList"/>

  <xs:element name="getUniqueIdsFromListResponse" type="tns:getUniqueIdsFromListResponse"/>

  <xs:element name="getUserRoles" type="tns:getUserRoles"/>

  <xs:element name="getUserRolesResponse" type="tns:getUserRolesResponse"/>

  <xs:element name="getValuesFromNamedCounter" type="tns:getValuesFromNamedCounter"/>

  <xs:element name="getValuesFromNamedCounterResponse" type="tns:getValuesFromNamedCounterResponse"/>

  <xs:element name="getVersion" type="tns:getVersion"/>

  <xs:element name="getVersionHistory" type="tns:getVersionHistory"/>

  <xs:element name="getVersionHistoryResponse" type="tns:getVersionHistoryResponse"/>

  <xs:element name="getVersionResponse" type="tns:getVersionResponse"/>

  <xs:element name="isOperationPermitted" type="tns:isOperationPermitted"/>

  <xs:element name="isOperationPermittedResponse" type="tns:isOperationPermittedResponse"/>

  <xs:element name="markAsDone" type="tns:markAsDone"/>

  <xs:element name="markAsDoneResponse" type="tns:markAsDoneResponse"/>

  <xs:element name="renameGroup" type="tns:renameGroup"/>

  <xs:element name="renameGroupResponse" type="tns:renameGroupResponse"/>

  <xs:element name="setBOMState" type="tns:setBOMState"/>

  <xs:element name="setBOMStateResponse" type="tns:setBOMStateResponse"/>

  <xs:element name="setCalculationState" type="tns:setCalculationState"/>

  <xs:element name="setCalculationStateForDrawing" type="tns:setCalculationStateForDrawing"/>

  <xs:element name="setCalculationStateForDrawingResponse" type="tns:setCalculationStateForDrawingResponse"/>

  <xs:element name="setCalculationStateResponse" type="tns:setCalculationStateResponse"/>

  <xs:element name="setConnectionPoints" type="tns:setConnectionPoints"/>

  <xs:element name="setConnectionPointsResponse" type="tns:setConnectionPointsResponse"/>

  <xs:element name="setDriveAssignmentData" type="tns:setDriveAssignmentData"/>

  <xs:element name="setDriveAssignmentDataResponse" type="tns:setDriveAssignmentDataResponse"/>

  <xs:element name="setEmulationState" type="tns:setEmulationState"/>

  <xs:element name="setEmulationStateResponse" type="tns:setEmulationStateResponse"/>

  <xs:element name="setITState" type="tns:setITState"/>

  <xs:element name="setITStateResponse" type="tns:setITStateResponse"/>

  <xs:element name="setNeighborConnections" type="tns:setNeighborConnections"/>

  <xs:element name="setNeighborConnectionsResponse" type="tns:setNeighborConnectionsResponse"/>

  <xs:element name="setPosition" type="tns:setPosition"/>

  <xs:element name="setPositionResponse" type="tns:setPositionResponse"/>

  <xs:element name="setProject" type="tns:setProject"/>

  <xs:element name="setProjectResponse" type="tns:setProjectResponse"/>

  <xs:element name="setSimulationState" type="tns:setSimulationState"/>

  <xs:element name="setSimulationStateForDrawing" type="tns:setSimulationStateForDrawing"/>

  <xs:element name="setSimulationStateForDrawingResponse" type="tns:setSimulationStateForDrawingResponse"/>

  <xs:element name="setSimulationStateResponse" type="tns:setSimulationStateResponse"/>

  <xs:element name="setState" type="tns:setState"/>

  <xs:element name="setStateResponse" type="tns:setStateResponse"/>

  <xs:element name="setStates" type="tns:setStates"/>

  <xs:element name="setStatesResponse" type="tns:setStatesResponse"/>

  <xs:complexType name="getNeighborConnections">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" nillable="true" type="xs:string"/>
      <xs:element minOccurs="0" name="arg3" type="tns:componentNeighborConnections"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="componentNeighborConnections">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="connections" nillable="true" type="tns:neighborConnection"/>
      <xs:element minOccurs="0" name="mode" type="xs:string"/>
      <xs:element minOccurs="0" name="uniqueId" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="neighborConnection">
    <xs:sequence>
      <xs:element minOccurs="0" name="gap" type="xs:int"/>
      <xs:element minOccurs="0" name="isManual" type="xs:boolean"/>
      <xs:element minOccurs="0" name="neighborAssignment" type="xs:string"/>
      <xs:element minOccurs="0" name="neighborCP" type="xs:string"/>
      <xs:element minOccurs="0" name="neighborUniqueId" type="xs:string"/>
      <xs:element minOccurs="0" name="ownCP" type="xs:string"/>
      <xs:element minOccurs="0" name="ownUniqueId" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getNeighborConnectionsResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="tns:componentNeighborConnections"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getType">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="tns:type"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="type">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="attributes" nillable="true" type="tns:attribute"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="attribute">
    <xs:sequence>
      <xs:element minOccurs="0" name="name" type="xs:string"/>
      <xs:element minOccurs="0" name="type" type="xs:string"/>
      <xs:element minOccurs="0" name="value" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getTypeResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="return" type="tns:type"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setSimulationState">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg3" nillable="true" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setSimulationStateResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getAllTypes">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="tns:type"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getAllTypesResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="return" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="createChangeGroup">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="tns:changeGroup"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="changeGroup">
    <xs:sequence>
      <xs:element minOccurs="0" name="defaultReason" type="xs:int"/>
      <xs:element minOccurs="0" name="description" type="xs:string"/>
      <xs:element minOccurs="0" name="id" type="xs:int"/>
      <xs:element minOccurs="0" name="name" type="xs:string"/>
      <xs:element minOccurs="0" name="projectKey" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="createChangeGroupResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="return" type="xs:int"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getComponent">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="tns:component"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="component">
    <xs:sequence>
      <xs:element minOccurs="0" name="AKZ" type="xs:string"/>
      <xs:element minOccurs="0" name="acceleration" type="xs:double"/>
      <xs:element minOccurs="0" name="amount_Buffer_Elec" type="xs:int"/>
      <xs:element minOccurs="0" name="amount_Buffer_Mech" type="xs:int"/>
      <xs:element minOccurs="0" name="angleCorr" type="xs:string"/>
      <xs:element minOccurs="0" name="autoCadLayer" type="xs:string"/>
      <xs:element minOccurs="0" name="break_Type" type="xs:string"/>
      <xs:element minOccurs="0" name="buffer_Size" type="xs:int"/>
      <xs:element minOccurs="0" name="build_Section" type="xs:string"/>
      <xs:element minOccurs="0" name="building_Section" type="xs:string"/>
      <xs:element minOccurs="0" name="calculation_Area" type="xs:string"/>
      <xs:element minOccurs="0" name="colorGroup" type="xs:string"/>
      <xs:element minOccurs="0" name="comment" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="connectionPoints" nillable="true" type="tns:connectionPoint"/>
      <xs:element minOccurs="0" name="controlNr" type="xs:string"/>
      <xs:element minOccurs="0" name="creation_Date" type="xs:dateTime"/>
      <xs:element minOccurs="0" name="curve_Angle" type="xs:int"/>
      <xs:element minOccurs="0" name="curve_Direction" type="xs:string"/>
      <xs:element minOccurs="0" name="curve_Radius" type="xs:int"/>
      <xs:element minOccurs="0" name="customerAKZ" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="decompositionLists" nillable="true" type="tns:decompositionList"/>
      <xs:element minOccurs="0" name="designPlant" type="xs:string"/>
      <xs:element minOccurs="0" name="drawing" type="xs:string"/>
      <xs:element minOccurs="0" name="drawingRevision" type="xs:string"/>
      <xs:element minOccurs="0" name="drawingVersion" type="xs:string"/>
      <xs:element minOccurs="0" name="drivePulleyDiameter" type="xs:double"/>
      <xs:element minOccurs="0" name="driveShaftDiameter" type="xs:double"/>
      <xs:element minOccurs="0" name="driveSide" type="xs:string"/>
      <xs:element minOccurs="0" name="drive_Position" type="xs:string"/>
      <xs:element minOccurs="0" name="drive_Station" type="xs:string"/>
      <xs:element minOccurs="0" name="EStop_Group" type="xs:string"/>
      <xs:element minOccurs="0" name="engineeringHours" type="xs:int"/>
      <xs:element minOccurs="0" name="height" type="xs:int"/>
      <xs:element minOccurs="0" name="installationHours" type="xs:int"/>
      <xs:element minOccurs="0" name="installation_Section" type="xs:string"/>
      <xs:element minOccurs="0" name="last_Modification_Date" type="xs:dateTime"/>
      <xs:element minOccurs="0" name="length_Total" type="xs:int"/>
      <xs:element minOccurs="0" name="levelEnd" type="xs:string"/>
      <xs:element minOccurs="0" name="levelStart" type="xs:string"/>
      <xs:element minOccurs="0" name="line_Name" type="xs:string"/>
      <xs:element minOccurs="0" name="load" type="xs:double"/>
      <xs:element minOccurs="0" name="motorController" type="xs:string"/>
      <xs:element minOccurs="0" name="motor_Direction" type="xs:string"/>
      <xs:element minOccurs="0" name="motor_Position" type="xs:string"/>
      <xs:element minOccurs="0" name="orderPlant" type="xs:string"/>
      <xs:element minOccurs="0" name="outfit3D" type="xs:string"/>
      <xs:element minOccurs="0" name="PLC_Area" type="xs:string"/>
      <xs:element minOccurs="0" name="parcelTypeID" type="xs:string"/>
      <xs:element minOccurs="0" name="parent" type="xs:string"/>
      <xs:element minOccurs="0" name="plant_Domain" type="xs:string"/>
      <xs:element minOccurs="0" name="pos_No" type="xs:string"/>
      <xs:element minOccurs="0" name="position" type="tns:position"/>
      <xs:element minOccurs="0" name="reference" type="xs:string"/>
      <xs:element minOccurs="0" name="rotation" type="xs:int"/>
      <xs:element minOccurs="0" name="rotation_3D" type="xs:double"/>
      <xs:element minOccurs="0" name="screen" type="xs:string"/>
      <xs:element minOccurs="0" name="section1_Angle" type="xs:double"/>
      <xs:element minOccurs="0" name="section1_Length" type="xs:double"/>
      <xs:element minOccurs="0" name="section2_Angle" type="xs:double"/>
      <xs:element minOccurs="0" name="section2_Length" type="xs:double"/>
      <xs:element minOccurs="0" name="section3_Angle" type="xs:double"/>
      <xs:element minOccurs="0" name="section3_Length" type="xs:double"/>
      <xs:element minOccurs="0" name="section4_Angle" type="xs:double"/>
      <xs:element minOccurs="0" name="section4_Length" type="xs:double"/>
      <xs:element minOccurs="0" name="sequence_Group" type="xs:string"/>
      <xs:element minOccurs="0" name="slave_Drive" type="xs:string"/>
      <xs:element minOccurs="0" name="slope" type="xs:double"/>
      <xs:element minOccurs="0" name="speed" type="xs:string"/>
      <xs:element minOccurs="0" name="start_Stop_Cycles" type="xs:int"/>
      <xs:element minOccurs="0" name="state" type="tns:state"/>
      <xs:element minOccurs="0" name="storageConveyor" type="xs:boolean"/>
      <xs:element minOccurs="0" name="supplier" type="xs:string"/>
      <xs:element minOccurs="0" name="throughput" type="xs:int"/>
      <xs:element minOccurs="0" name="type_ID" type="xs:string"/>
      <xs:element minOccurs="0" name="unique_ID" type="xs:string"/>
      <xs:element minOccurs="0" name="unit_Reversible" type="xs:boolean"/>
      <xs:element minOccurs="0" name="usage" type="xs:string"/>
      <xs:element name="useConnectionPoints" type="xs:boolean"/>
      <xs:element name="useDecompositions" type="xs:boolean"/>
      <xs:element name="useState" type="xs:boolean"/>
      <xs:element minOccurs="0" name="user1" type="xs:string"/>
      <xs:element minOccurs="0" name="user2" type="xs:string"/>
      <xs:element minOccurs="0" name="user3" type="xs:string"/>
      <xs:element minOccurs="0" name="user4" type="xs:string"/>
      <xs:element minOccurs="0" name="user5" type="xs:string"/>
      <xs:element minOccurs="0" name="vaultInstance" type="xs:string"/>
      <xs:element minOccurs="0" name="version" type="xs:int"/>
      <xs:element minOccurs="0" name="virtual" type="xs:string"/>
      <xs:element minOccurs="0" name="width" type="xs:int"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="connectionPoint">
    <xs:sequence>
      <xs:element minOccurs="0" name="name" type="xs:string"/>
      <xs:element minOccurs="0" name="type" type="xs:string"/>
      <xs:element minOccurs="0" name="x" type="xs:int"/>
      <xs:element minOccurs="0" name="y" type="xs:int"/>
      <xs:element minOccurs="0" name="z" type="xs:int"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="decompositionList">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="decompositions" nillable="true" type="tns:decomposition"/>
      <xs:element minOccurs="0" name="mode" type="xs:string"/>
      <xs:element minOccurs="0" name="source" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="decomposition">
    <xs:sequence>
      <xs:element minOccurs="0" name="changeDescription" type="xs:string"/>
      <xs:element minOccurs="0" name="derivedFrom" type="xs:string"/>
      <xs:element minOccurs="0" name="description" type="xs:string"/>
      <xs:element minOccurs="0" name="MPSPrefix" type="xs:string"/>
      <xs:element minOccurs="0" name="materialType" type="xs:string"/>
      <xs:element minOccurs="0" name="positionNumber" type="xs:string"/>
      <xs:element minOccurs="0" name="quantity" type="xs:int"/>
      <xs:element minOccurs="0" name="sapMaterialNumber" type="xs:string"/>
      <xs:element minOccurs="0" name="sourceInternal" type="xs:string"/>
      <xs:element minOccurs="0" name="standardMaterial" type="xs:boolean"/>
      <xs:element minOccurs="0" name="unitInSAP" type="xs:string"/>
      <xs:element minOccurs="0" name="ZOptions" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="position">
    <xs:sequence>
      <xs:element minOccurs="0" name="x" type="xs:int"/>
      <xs:element minOccurs="0" name="y" type="xs:int"/>
      <xs:element minOccurs="0" name="z" type="xs:int"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="state">
    <xs:sequence>
      <xs:element minOccurs="0" name="changeGroupDescription" type="xs:string"/>
      <xs:element minOccurs="0" name="changeGroupId" type="xs:int"/>
      <xs:element minOccurs="0" name="reason" type="xs:int"/>
      <xs:element minOccurs="0" name="remark" type="xs:string"/>
      <xs:element minOccurs="0" name="state" type="xs:int"/>
      <xs:element minOccurs="0" name="statusDate" type="xs:dateTime"/>
      <xs:element minOccurs="0" name="unique_ID" type="xs:string"/>
      <xs:element minOccurs="0" name="user" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getComponentResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="return" type="tns:component"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getComponentsByUniqueIds">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" type="xs:long"/>
      <xs:element minOccurs="0" name="arg3" type="tns:component"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getComponentsByUniqueIdsResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="createTypes">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" nillable="true" type="tns:type"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="createTypesResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getBoMListData">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" nillable="true" type="xs:string"/>
      <xs:element name="arg3" type="xs:boolean"/>
      <xs:element minOccurs="0" name="arg4" type="tns:boMGetDataResult"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="boMGetDataResult">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="components" nillable="true" type="tns:boMComponentData"/>
      <xs:element minOccurs="0" name="messageID" type="xs:long"/>
      <xs:element minOccurs="0" name="projectName" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="boMComponentData">
    <xs:sequence>
      <xs:element minOccurs="0" name="build_Section" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="decompositionLists" nillable="true" type="tns:decompositionList"/>
      <xs:element minOccurs="0" name="delivery_Date" type="xs:dateTime"/>
      <xs:element minOccurs="0" name="last_In_Work_Timestamp" type="xs:dateTime"/>
      <xs:element minOccurs="0" name="length" type="xs:int"/>
      <xs:element minOccurs="0" name="MPS" type="xs:string"/>
      <xs:element minOccurs="0" name="main_Type" type="xs:string"/>
      <xs:element minOccurs="0" name="reason_Of_Change" type="xs:string"/>
      <xs:element minOccurs="0" name="remark" type="xs:string"/>
      <xs:element minOccurs="0" name="sap_ID" type="xs:string"/>
      <xs:element minOccurs="0" name="spine_Status" type="xs:string"/>
      <xs:element minOccurs="0" name="status" type="xs:string"/>
      <xs:element minOccurs="0" name="sub_Type" type="xs:string"/>
      <xs:element minOccurs="0" name="type_ID" type="xs:string"/>
      <xs:element minOccurs="0" name="unique_ID" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getBoMListDataResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="return" type="tns:boMGetDataResult"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getAllProjects">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="tns:project"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="project">
    <xs:sequence>
      <xs:element minOccurs="0" name="averageObjectLengthForBelt" type="xs:double"/>
      <xs:element minOccurs="0" name="averageObjectLengthForOOGTray" type="xs:double"/>
      <xs:element minOccurs="0" name="averageObjectLengthForTray" type="xs:double"/>
      <xs:element minOccurs="0" name="averageWeightBag" type="xs:double"/>
      <xs:element minOccurs="0" name="averageWeightOversizeBag" type="xs:double"/>
      <xs:element minOccurs="0" name="bagWeight" type="xs:double"/>
      <xs:element minOccurs="0" name="beltDriveVendor" type="xs:string"/>
      <xs:element minOccurs="0" name="brakeRelatedZOptionsDol" type="xs:string"/>
      <xs:element minOccurs="0" name="brakeRelatedZOptionsVfd" type="xs:string"/>
      <xs:element minOccurs="0" name="brakeResistorCycleTime" type="xs:double"/>
      <xs:element minOccurs="0" name="brakingResistor" type="xs:string"/>
      <xs:element minOccurs="0" name="client" type="xs:string"/>
      <xs:element minOccurs="0" name="customerName" type="xs:string"/>
      <xs:element minOccurs="0" name="fieldbus" type="xs:string"/>
      <xs:element minOccurs="0" name="gearboxServiceFactor" type="xs:double"/>
      <xs:element minOccurs="0" name="generalZOptions" type="xs:string"/>
      <xs:element minOccurs="0" name="increasedVfdSize" type="xs:string"/>
      <xs:element minOccurs="0" name="main_Project_State" type="xs:string"/>
      <xs:element minOccurs="0" name="maxInputRpmGearbox" type="xs:double"/>
      <xs:element minOccurs="0" name="maxRelativeHumidity" type="xs:double"/>
      <xs:element minOccurs="0" name="minGapTimeForBelt" type="xs:double"/>
      <xs:element minOccurs="0" name="minGapTimeForTray" type="xs:double"/>
      <xs:element minOccurs="0" name="motorEfficiencyClass" type="xs:string"/>
      <xs:element minOccurs="0" name="motorServiceFactor" type="xs:double"/>
      <xs:element minOccurs="0" name="motorStarterDriveServiceFactor" type="xs:double"/>
      <xs:element minOccurs="0" name="mountingType" type="xs:string"/>
      <xs:element minOccurs="0" name="operatingTemperatureHigh" type="xs:double"/>
      <xs:element minOccurs="0" name="operatingTemperatureLow" type="xs:double"/>
      <xs:element minOccurs="0" name="oversizeBagWeight" type="xs:double"/>
      <xs:element minOccurs="0" name="oversizeTrayWeight" type="xs:double"/>
      <xs:element minOccurs="0" name="packageType" type="xs:string"/>
      <xs:element minOccurs="0" name="project_ID" type="xs:string"/>
      <xs:element minOccurs="0" name="project_Leader" type="xs:string"/>
      <xs:element minOccurs="0" name="relativeDutyFactor" type="xs:double"/>
      <xs:element minOccurs="0" name="repairSwitch" type="xs:string"/>
      <xs:element minOccurs="0" name="sap_Project_Key" type="xs:string"/>
      <xs:element minOccurs="0" name="siteName" type="xs:string"/>
      <xs:element minOccurs="0" name="supplyFrequency" type="xs:double"/>
      <xs:element minOccurs="0" name="supplyVoltage" type="xs:double"/>
      <xs:element minOccurs="0" name="trayDriveVendor" type="xs:string"/>
      <xs:element minOccurs="0" name="trayStackSize" type="xs:double"/>
      <xs:element minOccurs="0" name="trayWeight" type="xs:double"/>
      <xs:element minOccurs="0" name="type" type="xs:string"/>
      <xs:element minOccurs="0" name="vfdAccelerationTorque" type="xs:double"/>
      <xs:element minOccurs="0" name="vfdDriveServiceFactor" type="xs:double"/>
      <xs:element minOccurs="0" name="vfdOutputVoltage" type="xs:double"/>
      <xs:element minOccurs="0" name="vfdType" type="xs:string"/>
      <xs:element minOccurs="0" name="weight_Of_Belt" type="xs:double"/>
      <xs:element minOccurs="0" name="with_Simulation" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getAllProjectsResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="tns:project"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="markAsDone">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" nillable="true" type="tns:markAsDoneData"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="markAsDoneData">
    <xs:sequence>
      <xs:element minOccurs="0" name="last_In_Work_Timestamp" type="xs:dateTime"/>
      <xs:element minOccurs="0" name="sap_ID" type="xs:string"/>
      <xs:element minOccurs="0" name="unique_ID" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="markAsDoneResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="createComponents">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
      <xs:element minOccurs="0" name="arg3" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg4" nillable="true" type="tns:component"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="createComponentsResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getComponentsUniqueIds">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="tns:group"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="group">
    <xs:sequence>
      <xs:element minOccurs="0" name="build_Section" type="xs:string"/>
      <xs:element minOccurs="0" name="building_Section" type="xs:string"/>
      <xs:element minOccurs="0" name="calculation_Area" type="xs:string"/>
      <xs:element minOccurs="0" name="drawing" type="xs:string"/>
      <xs:element minOccurs="0" name="EStop_Group" type="xs:string"/>
      <xs:element minOccurs="0" name="installation_Section" type="xs:string"/>
      <xs:element minOccurs="0" name="line_Name" type="xs:string"/>
      <xs:element minOccurs="0" name="PLC_Area" type="xs:string"/>
      <xs:element minOccurs="0" name="project_View" type="xs:string"/>
      <xs:element minOccurs="0" name="screen" type="xs:string"/>
      <xs:element minOccurs="0" name="sequence_Group" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getComponentsUniqueIdsResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="xs:long"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getRoleOperations">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg1" nillable="true" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="tns:roleOperations"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="roleOperations">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="operations" nillable="true" type="xs:string"/>
      <xs:element minOccurs="0" name="role" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getRoleOperationsResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="tns:roleOperations"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="SetComponentsStatusInList">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
      <xs:element minOccurs="0" name="arg3" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg4" nillable="true" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="SetComponentsStatusInListResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setStates">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" nillable="true" type="tns:state"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setStatesResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getStates">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" nillable="true" type="xs:string"/>
      <xs:element minOccurs="0" name="arg3" type="tns:state"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getStatesResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="tns:state"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setEmulationState">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg3" nillable="true" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setEmulationStateResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getComponents">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="tns:group"/>
      <xs:element minOccurs="0" name="arg3" type="tns:component"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getComponentsResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="tns:component"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setProject">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="tns:project"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setProjectResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="isOperationPermitted">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="isOperationPermittedResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getDecompositionAttributes">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg1" nillable="true" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="tns:decompositionAttributes"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="decompositionAttributes">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="attributes" nillable="true" type="tns:attribute"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getDecompositionAttributesResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="tns:decompositionAttributes"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="createDecompositionAttributes">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg1" nillable="true" type="tns:decompositionAttributes"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="createDecompositionAttributesResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setCalculationState">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg3" nillable="true" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setCalculationStateResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setPosition">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
      <xs:element minOccurs="0" name="arg3" type="tns:position"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setPositionResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="deleteComponents">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" nillable="true" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="deleteComponentsResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getUserRoles">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="tns:userRole"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="userRole">
    <xs:sequence>
      <xs:element minOccurs="0" name="projectKey" type="xs:string"/>
      <xs:element minOccurs="0" name="role" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getUserRolesResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="tns:userRole"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getGroupItems">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="tns:group"/>
      <xs:element minOccurs="0" name="arg3" type="tns:groupItemData"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="groupItemData">
    <xs:sequence>
      <xs:element minOccurs="0" name="comment" type="xs:string"/>
      <xs:element minOccurs="0" name="delivery_Date" type="xs:dateTime"/>
      <xs:element minOccurs="0" name="group_Component_ID" type="xs:string"/>
      <xs:element minOccurs="0" name="group_Name" type="xs:string"/>
      <xs:element minOccurs="0" name="group_Type" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="subgroups" nillable="true" type="tns:subgroupItemData"/>
      <xs:element minOccurs="0" name="subtype" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="subgroupItemData">
    <xs:sequence>
      <xs:element minOccurs="0" name="comment" type="xs:string"/>
      <xs:element minOccurs="0" name="delivery_Date" type="xs:dateTime"/>
      <xs:element minOccurs="0" name="group_Component_ID" type="xs:string"/>
      <xs:element minOccurs="0" name="group_Name" type="xs:string"/>
      <xs:element minOccurs="0" name="group_Type" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getGroupItemsResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="tns:groupItemData"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getBomListDataByUniqueIds">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" type="xs:long"/>
      <xs:element minOccurs="0" name="arg3" type="tns:boMGetDataResult"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getBomListDataByUniqueIdsResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="return" type="tns:boMGetDataResult"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getComponentsBySpecification">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" nillable="true" type="tns:componentSpecificationMember"/>
      <xs:element name="arg3" type="xs:int"/>
      <xs:element name="arg4" type="xs:int"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="componentSpecificationMember">
    <xs:sequence>
      <xs:element minOccurs="0" name="conditionName" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="parameters" nillable="true" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getComponentsBySpecificationResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getPosition">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
      <xs:element minOccurs="0" name="arg3" type="tns:position"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getPositionResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="return" type="tns:position"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getState">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
      <xs:element minOccurs="0" name="arg3" type="tns:state"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getStateResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="return" type="tns:state"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getVersionHistory">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="tns:version"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="version">
    <xs:sequence>
      <xs:element minOccurs="0" name="info" type="xs:string"/>
      <xs:element minOccurs="0" name="number" type="xs:string"/>
      <xs:element minOccurs="0" name="validFrom" type="xs:dateTime"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getVersionHistoryResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="tns:version"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setNeighborConnections">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" nillable="true" type="tns:componentNeighborConnections"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setNeighborConnectionsResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getChangeListStates">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" nillable="true" type="xs:string"/>
      <xs:element minOccurs="0" name="arg3" type="tns:listStatus"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="listStatus">
    <xs:sequence>
      <xs:element minOccurs="0" name="bomState" type="xs:string"/>
      <xs:element minOccurs="0" name="calculationState" type="xs:string"/>
      <xs:element minOccurs="0" name="emulationState" type="xs:string"/>
      <xs:element minOccurs="0" name="itState" type="xs:string"/>
      <xs:element minOccurs="0" name="simulationState" type="xs:string"/>
      <xs:element minOccurs="0" name="uniqueID" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getChangeListStatesResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="tns:listStatus"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getComponentsStateHistory">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" type="xs:long"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getComponentsStateHistoryResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="tns:componentStateHistory"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="componentStateHistory">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="stateHistory" nillable="true" type="tns:state"/>
      <xs:element minOccurs="0" name="unique_ID" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setState">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
      <xs:element minOccurs="0" name="arg3" type="tns:state"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setStateResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setDriveAssignmentData">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" nillable="true" type="tns:componentDriveAssignmentData"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="componentDriveAssignmentData">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="driveAssignmentData" nillable="true" type="tns:driveAssignmentData"/>
      <xs:element minOccurs="0" name="uniqueId" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="driveAssignmentData">
    <xs:sequence>
      <xs:element minOccurs="0" name="driveAssignmentData" type="xs:string"/>
      <xs:element minOccurs="0" name="driveNumber" type="xs:int"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setDriveAssignmentDataResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getAllStates">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="tns:group"/>
      <xs:element minOccurs="0" name="arg3" type="tns:state"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getAllStatesResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="tns:state"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getVersion">
    <xs:sequence/>
  </xs:complexType>

  <xs:complexType name="getVersionResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="return" type="tns:version"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="calculateDrive">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="tns:component"/>
      <xs:element minOccurs="0" name="arg3" type="tns:attribute"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="calculateDriveResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="tns:attribute"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getConnectionPoints">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
      <xs:element minOccurs="0" name="arg3" type="tns:connectionPoint"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getConnectionPointsResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="tns:connectionPoint"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="renameGroup">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="tns:groupItemData"/>
      <xs:element minOccurs="0" name="arg3" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="renameGroupResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getUniqueIdsFromList">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg3" nillable="true" type="tns:group"/>
      <xs:element name="arg4" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getUniqueIdsFromListResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="xs:long"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getBomListUniqueIds">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" nillable="true" type="xs:string"/>
      <xs:element name="arg3" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getBomListUniqueIdsResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="xs:long"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getComponentLastModDate">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" type="xs:long"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getComponentLastModDateResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="xs:dateTime"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getDriveAssignmentData">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg2" nillable="true" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getDriveAssignmentDataResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="tns:componentDriveAssignmentData"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="deleteGroup">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="tns:groupItemData"/>
      <xs:element name="arg3" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="deleteGroupResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getAllChangeGroupsInProject">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="tns:changeGroup"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getAllChangeGroupsInProjectResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="tns:changeGroup"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setConnectionPoints">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg3" nillable="true" type="tns:connectionPoint"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setConnectionPointsResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setSimulationStateForDrawing">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
      <xs:element minOccurs="0" name="arg3" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setSimulationStateForDrawingResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setCalculationStateForDrawing">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
      <xs:element minOccurs="0" name="arg3" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setCalculationStateForDrawingResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="generateComponentUniqueIds">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element name="arg1" type="xs:int"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="generateComponentUniqueIdsResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="xs:long"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setITState">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg3" nillable="true" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setITStateResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setBOMState">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element minOccurs="0" name="arg1" type="xs:string"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="arg3" nillable="true" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="setBOMStateResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:boolean"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getValuesFromNamedCounter">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
      <xs:element name="arg1" type="xs:int"/>
      <xs:element minOccurs="0" name="arg2" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getValuesFromNamedCounterResponse">
    <xs:sequence>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="xs:long"/>
    </xs:sequence>
  </xs:complexType>

</xs:schema>
  </wsdl:types>
  <wsdl:message name="setProjectResponse">
    <wsdl:part element="tns:setProjectResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getType">
    <wsdl:part element="tns:getType" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getGroupItemsResponse">
    <wsdl:part element="tns:getGroupItemsResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="createChangeGroup">
    <wsdl:part element="tns:createChangeGroup" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setNeighborConnectionsResponse">
    <wsdl:part element="tns:setNeighborConnectionsResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getComponentsByUniqueIds">
    <wsdl:part element="tns:getComponentsByUniqueIds" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="createTypes">
    <wsdl:part element="tns:createTypes" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setSimulationStateResponse">
    <wsdl:part element="tns:setSimulationStateResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getTypeResponse">
    <wsdl:part element="tns:getTypeResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getBoMListData">
    <wsdl:part element="tns:getBoMListData" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getUserRolesResponse">
    <wsdl:part element="tns:getUserRolesResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getComponentsByUniqueIdsResponse">
    <wsdl:part element="tns:getComponentsByUniqueIdsResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="markAsDone">
    <wsdl:part element="tns:markAsDone" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getChangeListStatesResponse">
    <wsdl:part element="tns:getChangeListStatesResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="createComponents">
    <wsdl:part element="tns:createComponents" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="SetComponentsStatusInList">
    <wsdl:part element="tns:SetComponentsStatusInList" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setStates">
    <wsdl:part element="tns:setStates" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getStates">
    <wsdl:part element="tns:getStates" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setEmulationState">
    <wsdl:part element="tns:setEmulationState" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="deleteGroupResponse">
    <wsdl:part element="tns:deleteGroupResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getStateResponse">
    <wsdl:part element="tns:getStateResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="isOperationPermitted">
    <wsdl:part element="tns:isOperationPermitted" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setBOMStateResponse">
    <wsdl:part element="tns:setBOMStateResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getDecompositionAttributes">
    <wsdl:part element="tns:getDecompositionAttributes" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="createDecompositionAttributes">
    <wsdl:part element="tns:createDecompositionAttributes" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setCalculationState">
    <wsdl:part element="tns:setCalculationState" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getConnectionPointsResponse">
    <wsdl:part element="tns:getConnectionPointsResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getUserRoles">
    <wsdl:part element="tns:getUserRoles" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getGroupItems">
    <wsdl:part element="tns:getGroupItems" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getBomListDataByUniqueIds">
    <wsdl:part element="tns:getBomListDataByUniqueIds" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="renameGroupResponse">
    <wsdl:part element="tns:renameGroupResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="isOperationPermittedResponse">
    <wsdl:part element="tns:isOperationPermittedResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setSimulationStateForDrawingResponse">
    <wsdl:part element="tns:setSimulationStateForDrawingResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getChangeListStates">
    <wsdl:part element="tns:getChangeListStates" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getDecompositionAttributesResponse">
    <wsdl:part element="tns:getDecompositionAttributesResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setDriveAssignmentData">
    <wsdl:part element="tns:setDriveAssignmentData" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="createTypesResponse">
    <wsdl:part element="tns:createTypesResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getVersion">
    <wsdl:part element="tns:getVersion" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getComponentLastModDateResponse">
    <wsdl:part element="tns:getComponentLastModDateResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getDriveAssignmentDataResponse">
    <wsdl:part element="tns:getDriveAssignmentDataResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="createChangeGroupResponse">
    <wsdl:part element="tns:createChangeGroupResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="deleteComponentsResponse">
    <wsdl:part element="tns:deleteComponentsResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setCalculationStateForDrawingResponse">
    <wsdl:part element="tns:setCalculationStateForDrawingResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getUniqueIdsFromList">
    <wsdl:part element="tns:getUniqueIdsFromList" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="deleteGroup">
    <wsdl:part element="tns:deleteGroup" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setSimulationStateForDrawing">
    <wsdl:part element="tns:setSimulationStateForDrawing" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setConnectionPointsResponse">
    <wsdl:part element="tns:setConnectionPointsResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getNeighborConnectionsResponse">
    <wsdl:part element="tns:getNeighborConnectionsResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getComponentsStateHistoryResponse">
    <wsdl:part element="tns:getComponentsStateHistoryResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setITState">
    <wsdl:part element="tns:setITState" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getRoleOperationsResponse">
    <wsdl:part element="tns:getRoleOperationsResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setPositionResponse">
    <wsdl:part element="tns:setPositionResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getPositionResponse">
    <wsdl:part element="tns:getPositionResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getNeighborConnections">
    <wsdl:part element="tns:getNeighborConnections" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setEmulationStateResponse">
    <wsdl:part element="tns:setEmulationStateResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getUniqueIdsFromListResponse">
    <wsdl:part element="tns:getUniqueIdsFromListResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getValuesFromNamedCounterResponse">
    <wsdl:part element="tns:getValuesFromNamedCounterResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getComponentResponse">
    <wsdl:part element="tns:getComponentResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setSimulationState">
    <wsdl:part element="tns:setSimulationState" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getAllTypes">
    <wsdl:part element="tns:getAllTypes" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="createDecompositionAttributesResponse">
    <wsdl:part element="tns:createDecompositionAttributesResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getComponent">
    <wsdl:part element="tns:getComponent" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="SetComponentsStatusInListResponse">
    <wsdl:part element="tns:SetComponentsStatusInListResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getAllTypesResponse">
    <wsdl:part element="tns:getAllTypesResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="calculateDriveResponse">
    <wsdl:part element="tns:calculateDriveResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getAllProjectsResponse">
    <wsdl:part element="tns:getAllProjectsResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setStateResponse">
    <wsdl:part element="tns:setStateResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setStatesResponse">
    <wsdl:part element="tns:setStatesResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="markAsDoneResponse">
    <wsdl:part element="tns:markAsDoneResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getAllProjects">
    <wsdl:part element="tns:getAllProjects" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getVersionHistoryResponse">
    <wsdl:part element="tns:getVersionHistoryResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setITStateResponse">
    <wsdl:part element="tns:setITStateResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getComponentsUniqueIds">
    <wsdl:part element="tns:getComponentsUniqueIds" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getRoleOperations">
    <wsdl:part element="tns:getRoleOperations" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getComponentsResponse">
    <wsdl:part element="tns:getComponentsResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getComponents">
    <wsdl:part element="tns:getComponents" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setProject">
    <wsdl:part element="tns:setProject" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getComponentsBySpecificationResponse">
    <wsdl:part element="tns:getComponentsBySpecificationResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getAllStatesResponse">
    <wsdl:part element="tns:getAllStatesResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setCalculationStateResponse">
    <wsdl:part element="tns:setCalculationStateResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setPosition">
    <wsdl:part element="tns:setPosition" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="deleteComponents">
    <wsdl:part element="tns:deleteComponents" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getBomListUniqueIdsResponse">
    <wsdl:part element="tns:getBomListUniqueIdsResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getStatesResponse">
    <wsdl:part element="tns:getStatesResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getComponentsBySpecification">
    <wsdl:part element="tns:getComponentsBySpecification" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getPosition">
    <wsdl:part element="tns:getPosition" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getState">
    <wsdl:part element="tns:getState" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getVersionHistory">
    <wsdl:part element="tns:getVersionHistory" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getBoMListDataResponse">
    <wsdl:part element="tns:getBoMListDataResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setNeighborConnections">
    <wsdl:part element="tns:setNeighborConnections" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getComponentsUniqueIdsResponse">
    <wsdl:part element="tns:getComponentsUniqueIdsResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setDriveAssignmentDataResponse">
    <wsdl:part element="tns:setDriveAssignmentDataResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getComponentsStateHistory">
    <wsdl:part element="tns:getComponentsStateHistory" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setState">
    <wsdl:part element="tns:setState" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getAllStates">
    <wsdl:part element="tns:getAllStates" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="calculateDrive">
    <wsdl:part element="tns:calculateDrive" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="generateComponentUniqueIdsResponse">
    <wsdl:part element="tns:generateComponentUniqueIdsResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getConnectionPoints">
    <wsdl:part element="tns:getConnectionPoints" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="renameGroup">
    <wsdl:part element="tns:renameGroup" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getBomListDataByUniqueIdsResponse">
    <wsdl:part element="tns:getBomListDataByUniqueIdsResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getAllChangeGroupsInProjectResponse">
    <wsdl:part element="tns:getAllChangeGroupsInProjectResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getBomListUniqueIds">
    <wsdl:part element="tns:getBomListUniqueIds" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="createComponentsResponse">
    <wsdl:part element="tns:createComponentsResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getComponentLastModDate">
    <wsdl:part element="tns:getComponentLastModDate" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getDriveAssignmentData">
    <wsdl:part element="tns:getDriveAssignmentData" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getAllChangeGroupsInProject">
    <wsdl:part element="tns:getAllChangeGroupsInProject" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setConnectionPoints">
    <wsdl:part element="tns:setConnectionPoints" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setCalculationStateForDrawing">
    <wsdl:part element="tns:setCalculationStateForDrawing" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="generateComponentUniqueIds">
    <wsdl:part element="tns:generateComponentUniqueIds" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="setBOMState">
    <wsdl:part element="tns:setBOMState" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getValuesFromNamedCounter">
    <wsdl:part element="tns:getValuesFromNamedCounter" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getVersionResponse">
    <wsdl:part element="tns:getVersionResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:portType name="ToolChainLogic_Port">
    <wsdl:operation name="getNeighborConnections">
      <wsdl:input message="tns:getNeighborConnections" name="getNeighborConnections">
    </wsdl:input>
      <wsdl:output message="tns:getNeighborConnectionsResponse" name="getNeighborConnectionsResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getType">
      <wsdl:input message="tns:getType" name="getType">
    </wsdl:input>
      <wsdl:output message="tns:getTypeResponse" name="getTypeResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setSimulationState">
      <wsdl:input message="tns:setSimulationState" name="setSimulationState">
    </wsdl:input>
      <wsdl:output message="tns:setSimulationStateResponse" name="setSimulationStateResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getAllTypes">
      <wsdl:input message="tns:getAllTypes" name="getAllTypes">
    </wsdl:input>
      <wsdl:output message="tns:getAllTypesResponse" name="getAllTypesResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createChangeGroup">
      <wsdl:input message="tns:createChangeGroup" name="createChangeGroup">
    </wsdl:input>
      <wsdl:output message="tns:createChangeGroupResponse" name="createChangeGroupResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getComponent">
      <wsdl:input message="tns:getComponent" name="getComponent">
    </wsdl:input>
      <wsdl:output message="tns:getComponentResponse" name="getComponentResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getComponentsByUniqueIds">
      <wsdl:input message="tns:getComponentsByUniqueIds" name="getComponentsByUniqueIds">
    </wsdl:input>
      <wsdl:output message="tns:getComponentsByUniqueIdsResponse" name="getComponentsByUniqueIdsResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createTypes">
      <wsdl:input message="tns:createTypes" name="createTypes">
    </wsdl:input>
      <wsdl:output message="tns:createTypesResponse" name="createTypesResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getBoMListData">
      <wsdl:input message="tns:getBoMListData" name="getBoMListData">
    </wsdl:input>
      <wsdl:output message="tns:getBoMListDataResponse" name="getBoMListDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getAllProjects">
      <wsdl:input message="tns:getAllProjects" name="getAllProjects">
    </wsdl:input>
      <wsdl:output message="tns:getAllProjectsResponse" name="getAllProjectsResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="markAsDone">
      <wsdl:input message="tns:markAsDone" name="markAsDone">
    </wsdl:input>
      <wsdl:output message="tns:markAsDoneResponse" name="markAsDoneResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createComponents">
      <wsdl:input message="tns:createComponents" name="createComponents">
    </wsdl:input>
      <wsdl:output message="tns:createComponentsResponse" name="createComponentsResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getComponentsUniqueIds">
      <wsdl:input message="tns:getComponentsUniqueIds" name="getComponentsUniqueIds">
    </wsdl:input>
      <wsdl:output message="tns:getComponentsUniqueIdsResponse" name="getComponentsUniqueIdsResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRoleOperations">
      <wsdl:input message="tns:getRoleOperations" name="getRoleOperations">
    </wsdl:input>
      <wsdl:output message="tns:getRoleOperationsResponse" name="getRoleOperationsResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetComponentsStatusInList">
      <wsdl:input message="tns:SetComponentsStatusInList" name="SetComponentsStatusInList">
    </wsdl:input>
      <wsdl:output message="tns:SetComponentsStatusInListResponse" name="SetComponentsStatusInListResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setStates">
      <wsdl:input message="tns:setStates" name="setStates">
    </wsdl:input>
      <wsdl:output message="tns:setStatesResponse" name="setStatesResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getStates">
      <wsdl:input message="tns:getStates" name="getStates">
    </wsdl:input>
      <wsdl:output message="tns:getStatesResponse" name="getStatesResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setEmulationState">
      <wsdl:input message="tns:setEmulationState" name="setEmulationState">
    </wsdl:input>
      <wsdl:output message="tns:setEmulationStateResponse" name="setEmulationStateResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getComponents">
      <wsdl:input message="tns:getComponents" name="getComponents">
    </wsdl:input>
      <wsdl:output message="tns:getComponentsResponse" name="getComponentsResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setProject">
      <wsdl:input message="tns:setProject" name="setProject">
    </wsdl:input>
      <wsdl:output message="tns:setProjectResponse" name="setProjectResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="isOperationPermitted">
      <wsdl:input message="tns:isOperationPermitted" name="isOperationPermitted">
    </wsdl:input>
      <wsdl:output message="tns:isOperationPermittedResponse" name="isOperationPermittedResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getDecompositionAttributes">
      <wsdl:input message="tns:getDecompositionAttributes" name="getDecompositionAttributes">
    </wsdl:input>
      <wsdl:output message="tns:getDecompositionAttributesResponse" name="getDecompositionAttributesResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createDecompositionAttributes">
      <wsdl:input message="tns:createDecompositionAttributes" name="createDecompositionAttributes">
    </wsdl:input>
      <wsdl:output message="tns:createDecompositionAttributesResponse" name="createDecompositionAttributesResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setCalculationState">
      <wsdl:input message="tns:setCalculationState" name="setCalculationState">
    </wsdl:input>
      <wsdl:output message="tns:setCalculationStateResponse" name="setCalculationStateResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setPosition">
      <wsdl:input message="tns:setPosition" name="setPosition">
    </wsdl:input>
      <wsdl:output message="tns:setPositionResponse" name="setPositionResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="deleteComponents">
      <wsdl:input message="tns:deleteComponents" name="deleteComponents">
    </wsdl:input>
      <wsdl:output message="tns:deleteComponentsResponse" name="deleteComponentsResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getUserRoles">
      <wsdl:input message="tns:getUserRoles" name="getUserRoles">
    </wsdl:input>
      <wsdl:output message="tns:getUserRolesResponse" name="getUserRolesResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getGroupItems">
      <wsdl:input message="tns:getGroupItems" name="getGroupItems">
    </wsdl:input>
      <wsdl:output message="tns:getGroupItemsResponse" name="getGroupItemsResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getBomListDataByUniqueIds">
      <wsdl:input message="tns:getBomListDataByUniqueIds" name="getBomListDataByUniqueIds">
    </wsdl:input>
      <wsdl:output message="tns:getBomListDataByUniqueIdsResponse" name="getBomListDataByUniqueIdsResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getComponentsBySpecification">
      <wsdl:input message="tns:getComponentsBySpecification" name="getComponentsBySpecification">
    </wsdl:input>
      <wsdl:output message="tns:getComponentsBySpecificationResponse" name="getComponentsBySpecificationResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getPosition">
      <wsdl:input message="tns:getPosition" name="getPosition">
    </wsdl:input>
      <wsdl:output message="tns:getPositionResponse" name="getPositionResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getState">
      <wsdl:input message="tns:getState" name="getState">
    </wsdl:input>
      <wsdl:output message="tns:getStateResponse" name="getStateResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getVersionHistory">
      <wsdl:input message="tns:getVersionHistory" name="getVersionHistory">
    </wsdl:input>
      <wsdl:output message="tns:getVersionHistoryResponse" name="getVersionHistoryResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setNeighborConnections">
      <wsdl:input message="tns:setNeighborConnections" name="setNeighborConnections">
    </wsdl:input>
      <wsdl:output message="tns:setNeighborConnectionsResponse" name="setNeighborConnectionsResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getChangeListStates">
      <wsdl:input message="tns:getChangeListStates" name="getChangeListStates">
    </wsdl:input>
      <wsdl:output message="tns:getChangeListStatesResponse" name="getChangeListStatesResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getComponentsStateHistory">
      <wsdl:input message="tns:getComponentsStateHistory" name="getComponentsStateHistory">
    </wsdl:input>
      <wsdl:output message="tns:getComponentsStateHistoryResponse" name="getComponentsStateHistoryResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setState">
      <wsdl:input message="tns:setState" name="setState">
    </wsdl:input>
      <wsdl:output message="tns:setStateResponse" name="setStateResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setDriveAssignmentData">
      <wsdl:input message="tns:setDriveAssignmentData" name="setDriveAssignmentData">
    </wsdl:input>
      <wsdl:output message="tns:setDriveAssignmentDataResponse" name="setDriveAssignmentDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getAllStates">
      <wsdl:input message="tns:getAllStates" name="getAllStates">
    </wsdl:input>
      <wsdl:output message="tns:getAllStatesResponse" name="getAllStatesResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getVersion">
      <wsdl:input message="tns:getVersion" name="getVersion">
    </wsdl:input>
      <wsdl:output message="tns:getVersionResponse" name="getVersionResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="calculateDrive">
      <wsdl:input message="tns:calculateDrive" name="calculateDrive">
    </wsdl:input>
      <wsdl:output message="tns:calculateDriveResponse" name="calculateDriveResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getConnectionPoints">
      <wsdl:input message="tns:getConnectionPoints" name="getConnectionPoints">
    </wsdl:input>
      <wsdl:output message="tns:getConnectionPointsResponse" name="getConnectionPointsResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="renameGroup">
      <wsdl:input message="tns:renameGroup" name="renameGroup">
    </wsdl:input>
      <wsdl:output message="tns:renameGroupResponse" name="renameGroupResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getUniqueIdsFromList">
      <wsdl:input message="tns:getUniqueIdsFromList" name="getUniqueIdsFromList">
    </wsdl:input>
      <wsdl:output message="tns:getUniqueIdsFromListResponse" name="getUniqueIdsFromListResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getBomListUniqueIds">
      <wsdl:input message="tns:getBomListUniqueIds" name="getBomListUniqueIds">
    </wsdl:input>
      <wsdl:output message="tns:getBomListUniqueIdsResponse" name="getBomListUniqueIdsResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getComponentLastModDate">
      <wsdl:input message="tns:getComponentLastModDate" name="getComponentLastModDate">
    </wsdl:input>
      <wsdl:output message="tns:getComponentLastModDateResponse" name="getComponentLastModDateResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getDriveAssignmentData">
      <wsdl:input message="tns:getDriveAssignmentData" name="getDriveAssignmentData">
    </wsdl:input>
      <wsdl:output message="tns:getDriveAssignmentDataResponse" name="getDriveAssignmentDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="deleteGroup">
      <wsdl:input message="tns:deleteGroup" name="deleteGroup">
    </wsdl:input>
      <wsdl:output message="tns:deleteGroupResponse" name="deleteGroupResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getAllChangeGroupsInProject">
      <wsdl:input message="tns:getAllChangeGroupsInProject" name="getAllChangeGroupsInProject">
    </wsdl:input>
      <wsdl:output message="tns:getAllChangeGroupsInProjectResponse" name="getAllChangeGroupsInProjectResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setConnectionPoints">
      <wsdl:input message="tns:setConnectionPoints" name="setConnectionPoints">
    </wsdl:input>
      <wsdl:output message="tns:setConnectionPointsResponse" name="setConnectionPointsResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setSimulationStateForDrawing">
      <wsdl:input message="tns:setSimulationStateForDrawing" name="setSimulationStateForDrawing">
    </wsdl:input>
      <wsdl:output message="tns:setSimulationStateForDrawingResponse" name="setSimulationStateForDrawingResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setCalculationStateForDrawing">
      <wsdl:input message="tns:setCalculationStateForDrawing" name="setCalculationStateForDrawing">
    </wsdl:input>
      <wsdl:output message="tns:setCalculationStateForDrawingResponse" name="setCalculationStateForDrawingResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="generateComponentUniqueIds">
      <wsdl:input message="tns:generateComponentUniqueIds" name="generateComponentUniqueIds">
    </wsdl:input>
      <wsdl:output message="tns:generateComponentUniqueIdsResponse" name="generateComponentUniqueIdsResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setITState">
      <wsdl:input message="tns:setITState" name="setITState">
    </wsdl:input>
      <wsdl:output message="tns:setITStateResponse" name="setITStateResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setBOMState">
      <wsdl:input message="tns:setBOMState" name="setBOMState">
    </wsdl:input>
      <wsdl:output message="tns:setBOMStateResponse" name="setBOMStateResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getValuesFromNamedCounter">
      <wsdl:input message="tns:getValuesFromNamedCounter" name="getValuesFromNamedCounter">
    </wsdl:input>
      <wsdl:output message="tns:getValuesFromNamedCounterResponse" name="getValuesFromNamedCounterResponse">
    </wsdl:output>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="ToolChainLogicSoapBinding" type="tns:ToolChainLogic_Port">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="getNeighborConnections">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getNeighborConnections">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getNeighborConnectionsResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getType">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getType">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getTypeResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setSimulationState">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="setSimulationState">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="setSimulationStateResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getAllTypes">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getAllTypes">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getAllTypesResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createChangeGroup">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="createChangeGroup">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="createChangeGroupResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getComponent">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getComponent">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getComponentResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getComponentsByUniqueIds">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getComponentsByUniqueIds">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getComponentsByUniqueIdsResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createTypes">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="createTypes">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="createTypesResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getBoMListData">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getBoMListData">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getBoMListDataResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getAllProjects">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getAllProjects">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getAllProjectsResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="markAsDone">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="markAsDone">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="markAsDoneResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createComponents">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="createComponents">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="createComponentsResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getComponentsUniqueIds">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getComponentsUniqueIds">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getComponentsUniqueIdsResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRoleOperations">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getRoleOperations">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getRoleOperationsResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetComponentsStatusInList">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="SetComponentsStatusInList">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="SetComponentsStatusInListResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setStates">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="setStates">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="setStatesResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getStates">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getStates">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getStatesResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setEmulationState">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="setEmulationState">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="setEmulationStateResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getComponents">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getComponents">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getComponentsResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setProject">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="setProject">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="setProjectResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="isOperationPermitted">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="isOperationPermitted">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="isOperationPermittedResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getDecompositionAttributes">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getDecompositionAttributes">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getDecompositionAttributesResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createDecompositionAttributes">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="createDecompositionAttributes">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="createDecompositionAttributesResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setCalculationState">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="setCalculationState">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="setCalculationStateResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setPosition">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="setPosition">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="setPositionResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="deleteComponents">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="deleteComponents">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="deleteComponentsResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getUserRoles">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getUserRoles">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getUserRolesResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getGroupItems">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getGroupItems">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getGroupItemsResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getBomListDataByUniqueIds">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getBomListDataByUniqueIds">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getBomListDataByUniqueIdsResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getComponentsBySpecification">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getComponentsBySpecification">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getComponentsBySpecificationResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getPosition">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getPosition">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getPositionResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getState">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getState">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getStateResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getVersionHistory">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getVersionHistory">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getVersionHistoryResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setNeighborConnections">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="setNeighborConnections">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="setNeighborConnectionsResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getChangeListStates">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getChangeListStates">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getChangeListStatesResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getComponentsStateHistory">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getComponentsStateHistory">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getComponentsStateHistoryResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setState">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="setState">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="setStateResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setDriveAssignmentData">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="setDriveAssignmentData">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="setDriveAssignmentDataResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getAllStates">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getAllStates">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getAllStatesResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getVersion">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getVersion">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getVersionResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="calculateDrive">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="calculateDrive">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="calculateDriveResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getConnectionPoints">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getConnectionPoints">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getConnectionPointsResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="renameGroup">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="renameGroup">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="renameGroupResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getUniqueIdsFromList">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getUniqueIdsFromList">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getUniqueIdsFromListResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getBomListUniqueIds">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getBomListUniqueIds">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getBomListUniqueIdsResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getComponentLastModDate">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getComponentLastModDate">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getComponentLastModDateResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getDriveAssignmentData">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getDriveAssignmentData">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getDriveAssignmentDataResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="deleteGroup">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="deleteGroup">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="deleteGroupResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getAllChangeGroupsInProject">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getAllChangeGroupsInProject">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getAllChangeGroupsInProjectResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setConnectionPoints">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="setConnectionPoints">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="setConnectionPointsResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setSimulationStateForDrawing">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="setSimulationStateForDrawing">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="setSimulationStateForDrawingResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setCalculationStateForDrawing">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="setCalculationStateForDrawing">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="setCalculationStateForDrawingResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="generateComponentUniqueIds">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="generateComponentUniqueIds">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="generateComponentUniqueIdsResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setITState">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="setITState">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="setITStateResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="setBOMState">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="setBOMState">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="setBOMStateResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getValuesFromNamedCounter">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getValuesFromNamedCounter">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getValuesFromNamedCounterResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="ToolChainLogic">
    <wsdl:port binding="tns:ToolChainLogicSoapBinding" name="ToolChainLogic_PortPort">
      <soap:address location="http://localhost:8080/metro/soap/SoapWSService"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>