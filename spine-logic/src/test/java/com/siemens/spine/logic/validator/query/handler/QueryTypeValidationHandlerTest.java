package com.siemens.spine.logic.validator.query.handler;

import com.siemens.spine.logic.validator.query.QueryValidationContext;
import com.siemens.spine.logic.validator.query.ValidationResult;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

class QueryTypeValidationHandlerTest {

    @InjectMocks
    private QueryTypeValidationHandler querySyntaxValidationHandler;

    @Mock
    private QueryValidationContext queryValidationContext;

    private AutoCloseable mocks;

    @BeforeEach
    void setUp() {
        mocks = MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    void tearDown() throws Exception {
        if (mocks != null) {
            mocks.close();
        }
    }

    @Test
    void testNullQueryString() {
        QueryValidationContext context = Mockito.mock(QueryValidationContext.class);
        Mockito.when(context.getQueryString()).thenReturn(null);

        ValidationResult result = querySyntaxValidationHandler.doValidate(context);

        assertFalse(result.valid());
        assertEquals("Unsupported query type", result.errorMessage());
    }

    @Test
    void testEmptyQueryString() {
        QueryValidationContext context = Mockito.mock(QueryValidationContext.class);
        Mockito.when(context.getQueryString()).thenReturn("");

        ValidationResult result = querySyntaxValidationHandler.doValidate(context);

        assertFalse(result.valid());
        assertEquals("Unsupported query type", result.errorMessage());
    }

}
