package com.siemens.spine.logic.validator.query.handler;

import com.siemens.spine.logic.validator.query.QueryValidationContext;
import com.siemens.spine.logic.validator.query.ValidationResult;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

class SqlSyntaxValidationHandlerTest {

    @InjectMocks
    private SqlSyntaxValidationHandler sqlSyntaxValidationHandler;

    @Mock
    private QueryValidationContext queryValidationContext;

    private AutoCloseable mocks;

    @BeforeEach
    void setUp() {
        mocks = MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    void tearDown() throws Exception {
        if (mocks != null) {
            mocks.close();
        }
    }

    @Test
    void testValidSqlSyntax() {
        // Arrange
        String validSql = "SELECT * FROM users WHERE id = 1";
        queryValidationContext = QueryValidationContext.builder().queryString(validSql).build();

        // Act
        ValidationResult result = sqlSyntaxValidationHandler.doValidate(queryValidationContext);

        // Assert
        assertTrue(result.valid());
        assertNull(result.errorMessage());
    }

    @Test
    void testInvalidSqlSyntax() {
        // Arrange
        String invalidSql = "SELECT FROM WHERE"; // Missing table name
        queryValidationContext = QueryValidationContext.builder().queryString(invalidSql).build();

        // Act
        ValidationResult result = sqlSyntaxValidationHandler.doValidate(queryValidationContext);

        // Assert
        assertFalse(result.valid());
        assertNotNull(result.errorMessage());
        assertTrue(result.errorMessage().contains("Invalid SQL syntax"));
    }

    @Test
    void testEdgeCaseLongSqlStatement() {
        // Arrange
        String longSql = "SELECT * FROM users WHERE name = '" + "a".repeat(10000) + "'";
        queryValidationContext = QueryValidationContext.builder().queryString(longSql).build();

        // Act
        ValidationResult result = sqlSyntaxValidationHandler.doValidate(queryValidationContext);

        // Assert
        assertTrue(result.valid());
        assertNull(result.errorMessage());
    }

    @Test
    void testEdgeCaseSpecialCharacters() {
        // Arrange
        String specialCharSql = "SELECT * FROM users WHERE name = '@#$%^&*()'";
        queryValidationContext = QueryValidationContext.builder().queryString(specialCharSql).build();

        // Act
        ValidationResult result = sqlSyntaxValidationHandler.doValidate(queryValidationContext);

        // Assert
        assertTrue(result.valid());
        assertNull(result.errorMessage());
    }

    @Test
    void testExceptionHandling() {
        // Arrange
        String invalidSql = "INVALID SQL"; // Intentionally malformed SQL
        queryValidationContext = QueryValidationContext.builder().queryString(invalidSql).build();

        // Act
        ValidationResult result = sqlSyntaxValidationHandler.doValidate(queryValidationContext);

        // Assert
        assertFalse(result.valid());
        assertNotNull(result.errorMessage());
        assertTrue(result.errorMessage().contains("Invalid SQL syntax"));
    }

}
