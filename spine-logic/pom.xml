<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>spine-core</artifactId>
        <groupId>com.siemens.spine</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>spine-logic</artifactId>
    <packaging>jar</packaging>
    <properties>
        <jaxws-api.version>2.3.1</jaxws-api.version>
        <javax.jws-api.version>1.1</javax.jws-api.version>
        <jaxb-api.version>2.3.1</jaxb-api.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.siemens.spine</groupId>
            <artifactId>spine-db</artifactId>
        </dependency>


        <!-- Helidon MicroProfile Core Bundle (includes CDI) -->
        <dependency>
            <groupId>io.helidon.microprofile.bundles</groupId>
            <artifactId>helidon-microprofile-core</artifactId>
        </dependency>

        <!-- Helidon CDI Implementation -->
        <dependency>
            <groupId>io.helidon.microprofile.cdi</groupId>
            <artifactId>helidon-microprofile-cdi</artifactId>
        </dependency>

        <!-- Helidon CDI Integrations -->
        <dependency>
            <groupId>io.helidon.integrations.cdi</groupId>
            <artifactId>helidon-integrations-cdi-jta-weld</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.microprofile</groupId>
            <artifactId>helidon-microprofile-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.metro</groupId>
            <artifactId>helidon-mp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-all</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- Jakarta XML dependencies -->
        <dependency>
            <groupId>jakarta.xml.ws</groupId>
            <artifactId>jakarta.xml.ws-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.jws</groupId>
            <artifactId>jakarta.jws-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.activation</groupId>
            <artifactId>jakarta.activation-api</artifactId>
        </dependency>

        <!-- Legacy javax.* dependencies for CXF generated code compatibility -->
        <dependency>
            <groupId>javax.xml.ws</groupId>
            <artifactId>jaxws-api</artifactId>
            <version>${jaxws-api.version}</version>
        </dependency>
        <dependency>
            <groupId>javax.jws</groupId>
            <artifactId>javax.jws-api</artifactId>
            <version>${javax.jws-api.version}</version>
        </dependency>
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>${jaxb-api.version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.transaction</groupId>
            <artifactId>jakarta.transaction-api</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.github.jsqlparser/jsqlparser -->
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>5.3</version>
        </dependency>

        <!-- JUnit for unit testing -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Mockito for mocking -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- AssertJ for fluent assertions -->
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${version.plugin.compiler}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <compilerArgs>
                        <!-- This arg is necessary to retrieve the raw parameter name of a method -->
                        <arg>-parameters</arg>
                        <!-- MapStruct compiler arguments -->
                        <arg>-Amapstruct.defaultComponentModel=cdi</arg>
                        <arg>-Amapstruct.unmappedTargetPolicy=IGNORE</arg>
                    </compilerArgs>
                    <annotationProcessorPaths>
                        <!-- IMPORTANT: Lombok must come before MapStruct in the processor order -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${version.lombok}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${version.lombok-mapstruct-binding}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${version.mapstruct}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-codegen-plugin</artifactId>
                <version>${version.plugin.cxf-codegen}</version>
                <executions>
                    <execution>
                        <id>generate-toolchain-api</id>
                        <phase>generate-sources</phase>
                        <configuration>
                            <sourceRoot>${project.build.directory}/generated-sources/cxf</sourceRoot>
                            <wsdlOptions>
                                <wsdlOption>
                                    <wsdl>${basedir}/src/main/resources/ToolChainLogic.xml</wsdl>
                                    <extraargs>
                                        <extraarg>-p</extraarg>
                                        <extraarg>com.siemens.spine.generated.toolchain</extraarg>
                                        <extraarg>-wsdlLocation</extraarg>
                                        <extraarg>META-INF/wsdl/ToolChainLogic.wsdl</extraarg>
                                        <extraarg>-client</extraarg>
                                    </extraargs>
                                </wsdlOption>
                            </wsdlOptions>
                        </configuration>
                        <goals>
                            <goal>wsdl2java</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>