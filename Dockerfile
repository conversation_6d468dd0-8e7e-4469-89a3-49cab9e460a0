# Build stage
FROM node:16 AS build
ARG base_href
WORKDIR /app
COPY package.json /app/
RUN npm install
RUN npm install -g @angular/cli@14.2.13

# Configuration URL
ENV API_URL="http://192.168.68.8:8080/api/v1"
# Configuration Keycloak
ENV KEYCLOAK_URL="https://iam.local.egs-dev.site"
ENV KEYCLOAK_REALM="spine"
ENV KEYCLOAK_CLIENT_ID="spine-core"

COPY ./ /app/

RUN ng build --configuration production --aot --base-href="/spine-core-frontend/"

# Production stage
FROM nginx:alpine

RUN mkdir -p /usr/share/nginx/html/spine-ui

COPY --from=build /app/dist/spine-ui /usr/share/nginx/html/spine-ui
COPY --from=build /app/nginx.conf /etc/nginx/nginx.conf
COPY --from=build /app/default.conf /etc/nginx/conf.d/default.conf
COPY mime.types /etc/nginx/mime.types

EXPOSE 8082
CMD ["nginx", "-g", "daemon off;"]
