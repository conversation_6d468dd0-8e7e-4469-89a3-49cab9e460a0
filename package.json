{"name": "spine-ui", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production --aot --base-href=/spine-core-frontend/", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular-builders/custom-webpack": "^14.0.1", "@angular/animations": "^14.2.1", "@angular/cdk": "^14.2.2", "@angular/common": "^14.2.0", "@angular/compiler": "^14.2.0", "@angular/core": "^14.2.0", "@angular/forms": "^14.2.0", "@angular/localize": "^14.2.0", "@angular/platform-browser": "^14.2.0", "@angular/platform-browser-dynamic": "^14.2.0", "@angular/router": "^14.2.0", "@ng-bootstrap/ng-bootstrap": "^13.0.0", "@popperjs/core": "^2.10.2", "ag-grid-angular": "^28.2.1", "ag-grid-community": "^28.2.1", "bootstrap": "^5.2.1", "bootstrap-icons": "^1.9.1", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "keycloak-angular": "^12.1.0", "keycloak-js": "^19.0.3", "moment": "^2.29.4", "ngx-toastr": "^15.0.0", "rxjs": "~7.5.0", "tippy.js": "^6.3.1", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^14.2.2", "@angular/cli": "~14.2.2", "@angular/compiler-cli": "^14.2.0", "@types/exceljs": "^1.3.0", "@types/file-saver": "^2.0.5", "@types/jasmine": "~4.0.0", "@types/node": "~14.0.0", "jasmine-core": "~4.3.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~4.7.2"}}