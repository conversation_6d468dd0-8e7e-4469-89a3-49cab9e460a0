<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.helidon.applications</groupId>
        <artifactId>helidon-mp</artifactId>
        <version>2.6.11</version>
    </parent>

    <groupId>com.siemens.spine</groupId>
    <artifactId>spine-core</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>

    <modules>
        <module>spine-db</module>
        <module>spine-logic</module>
        <module>spine-soap</module>
    </modules>

    <properties>
        <!-- Application Properties -->
        <mainClass>com.siemens.spine.resource.SpineCoreApplication</mainClass>
        <java.version>17</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven.site.skip>true</maven.site.skip>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!-- Helidon -->
        <version.helidon>2.6.11</version.helidon>

        <!-- Development Tools -->
        <version.lombok>1.18.30</version.lombok>
        <version.mapstruct>1.5.5.Final</version.mapstruct>
        <version.lombok-mapstruct-binding>0.2.0</version.lombok-mapstruct-binding>

        <!-- Utility Libraries -->
        <version.commons-lang3>3.14.0</version.commons-lang3>
        <version.commons-collections4>4.4</version.commons-collections4>

        <!-- Jakarta EE / APIs -->
        <version.jakarta-servlet-api>6.0.0</version.jakarta-servlet-api>
        <version.jakarta-persistence-api>2.2.3</version.jakarta-persistence-api>
        <version.jakarta-transaction-api>1.3.3</version.jakarta-transaction-api>
        <version.metro-helidon-mp>1.0.1</version.metro-helidon-mp>
        <version.jakarta.enterprise.cdi-api.version>2.0.2</version.jakarta.enterprise.cdi-api.version>
        <version.jakarta.xml.bind-api.version>3.0.1</version.jakarta.xml.bind-api.version>
        <version.jaxb-runtime.version>3.0.2</version.jaxb-runtime.version>
        <version.jakarta.xml.ws-api>3.0.1</version.jakarta.xml.ws-api>
        <version.jakarta.jws-api>2.1.0</version.jakarta.jws-api>
        <version.jakarta.inject-api.version>2.0.1</version.jakarta.inject-api.version>
        <version.jakarta.activation-api>2.1.0</version.jakarta.activation-api>
        <version.angus-activation>2.0.0</version.angus-activation>
        <version.jaxb-core>3.0.2</version.jaxb-core>


        <!-- Database -->
        <version.postgresql>42.7.1</version.postgresql>
        <version.hibernate>5.6.15.Final</version.hibernate>
        <liquibase-core.version>4.27.0</liquibase-core.version>

        <!-- Testing -->
        <version.testcontainers>1.19.3</version.testcontainers>

        <!-- Plugin Versions -->
        <version.plugin.compiler>3.11.0</version.plugin.compiler>
        <version.plugin.dependency>3.6.1</version.plugin.dependency>
        <version.plugin.exec>3.1.0</version.plugin.exec>
        <version.plugin.failsafe>3.2.2</version.plugin.failsafe>
        <version.plugin.helidon>2.2.0</version.plugin.helidon>
        <version.plugin.jandex>1.2.3</version.plugin.jandex>
        <version.plugin.jar>3.3.0</version.plugin.jar>
        <version.plugin.jaxb>0.13.3</version.plugin.jaxb>
        <version.plugin.resources>3.3.1</version.plugin.resources>
        <version.plugin.xml>1.0.2</version.plugin.xml>
        <version.plugin.cxf-codegen>3.6.6</version.plugin.cxf-codegen>

        <junit-jupiter.version>5.9.3</junit-jupiter.version>
        <mockito-core.version>5.4.0</mockito-core.version>
        <assertj-core.version>3.24.2</assertj-core.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Development Tools -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${version.mapstruct}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${version.lombok}</version>
            </dependency>

            <!-- Helidon WS-->
            <dependency>
                <groupId>org.glassfish.metro</groupId>
                <artifactId>helidon-mp</artifactId>
                <version>${version.metro-helidon-mp}</version>
            </dependency>

            <!-- Database -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${version.postgresql}</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-core</artifactId>
                <version>${version.hibernate}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-envers</artifactId>
                <version>${version.hibernate}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.xml.ws</groupId>
                <artifactId>jakarta.xml.ws-api</artifactId>
                <version>${version.jakarta.xml.ws-api}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.jws</groupId>
                <artifactId>jakarta.jws-api</artifactId>
                <version>${version.jakarta.jws-api}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.xml.bind</groupId>
                <artifactId>jakarta.xml.bind-api</artifactId>
                <version>${version.jakarta.xml.bind-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.activation</groupId>
                <artifactId>jakarta.activation-api</artifactId>
                <version>${version.jakarta.activation-api}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.angus</groupId>
                <artifactId>angus-activation</artifactId>
                <version>${version.angus-activation}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>3.0.2</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-xjc</artifactId>
                <version>3.0.2</version>
            </dependency>

            <!-- Testing -->
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers-bom</artifactId>
                <version>${version.testcontainers}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- JUnit for unit testing -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit-jupiter.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- Mockito for mocking -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito-core.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- AssertJ for fluent assertions -->
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj-core.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.siemens.spine</groupId>
                <artifactId>spine-db</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.siemens.spine</groupId>
                <artifactId>spine-logic</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- ============================================================= -->
        <!-- Development Tools                                             -->
        <!-- ============================================================= -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <!-- ============================================================= -->
        <!-- Logging                                                       -->
        <!-- ============================================================= -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jul-to-slf4j</artifactId>
        </dependency>

        <!-- ============================================================= -->
        <!-- Utility Libraries                                             -->
        <!-- ============================================================= -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${version.commons-lang3}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>${version.commons-collections4}</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <!-- ============================================================= -->
        <!-- JSON Processing                                                -->
        <!-- ============================================================= -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.jaxrs</groupId>
            <artifactId>jackson-jaxrs-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.jaxrs</groupId>
            <artifactId>jackson-jaxrs-json-provider</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jersey.media</groupId>
            <artifactId>jersey-media-json-binding</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.eclipse</groupId>
                    <artifactId>yasson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jersey.media</groupId>
            <artifactId>jersey-media-json-jackson</artifactId>
        </dependency>

        <!-- ============================================================= -->
        <!-- Helidon Core                                                   -->
        <!-- ============================================================= -->
        <dependency>
            <groupId>io.helidon.common</groupId>
            <artifactId>helidon-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.config</groupId>
            <artifactId>helidon-config-yaml-mp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.metro</groupId>
            <artifactId>helidon-mp</artifactId>
        </dependency>

        <!-- ============================================================= -->
        <!-- Helidon MicroProfile                                           -->
        <!-- ============================================================= -->
        <dependency>
            <groupId>io.helidon.microprofile.bundles</groupId>
            <artifactId>helidon-microprofile-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.helidon.microprofile</groupId>
            <artifactId>helidon-microprofile-cors</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.media</groupId>
            <artifactId>helidon-media-jsonb</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.microprofile.scheduling</groupId>
            <artifactId>helidon-microprofile-scheduling</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.microprofile</groupId>
            <artifactId>helidon-microprofile-security</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.microprofile</groupId>
            <artifactId>helidon-microprofile-oidc</artifactId>
        </dependency>
        <dependency>
            <groupId>io.helidon.microprofile.health</groupId>
            <artifactId>helidon-microprofile-health</artifactId>
        </dependency>

        <!-- ============================================================= -->
        <!-- Helidon CDI Integrations                                       -->
        <!-- ============================================================= -->
        <dependency>
            <groupId>io.helidon.integrations.cdi</groupId>
            <artifactId>helidon-integrations-cdi-jpa</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.helidon.integrations.cdi</groupId>
            <artifactId>helidon-integrations-cdi-jta-weld</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.helidon.integrations.cdi</groupId>
            <artifactId>helidon-integrations-cdi-datasource-hikaricp</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.helidon.integrations.cdi</groupId>
            <artifactId>helidon-integrations-cdi-hibernate</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-core</artifactId>
                </exclusion>
            </exclusions>
            <scope>runtime</scope>
        </dependency>

        <!-- ============================================================= -->
        <!-- Database                                                      -->
        <!-- ============================================================= -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>org.glassfish</groupId>
            <artifactId>jakarta.el</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator-cdi</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- ============================================================= -->
        <!-- Testing                                                       -->
        <!-- ============================================================= -->
        <dependency>
            <groupId>io.helidon.microprofile.tests</groupId>
            <artifactId>helidon-microprofile-tests-junit5</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-all</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Liquibase core -->
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
            <version>${liquibase-core.version}</version>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <!-- Core Maven Plugins -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>${version.plugin.dependency}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${version.plugin.jar}</version>
                    <configuration>
                        <archive>
                            <manifest>
                                <addClasspath>true</addClasspath>
                                <classpathPrefix>libs</classpathPrefix>
                                <mainClass>${mainClass}</mainClass>
                                <useUniqueVersions>false</useUniqueVersions>
                            </manifest>
                        </archive>
                    </configuration>
                </plugin>

                <!-- Helidon Plugins -->
                <plugin>
                    <groupId>io.helidon.build-tools</groupId>
                    <artifactId>helidon-maven-plugin</artifactId>
                    <version>${version.plugin.helidon}</version>
                </plugin>

                <!-- Utility Plugins -->
                <plugin>
                    <groupId>org.jboss.jandex</groupId>
                    <artifactId>jandex-maven-plugin</artifactId>
                    <version>${version.plugin.jandex}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <version>${version.plugin.exec}</version>
                    <configuration>
                        <mainClass>${mainClass}</mainClass>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
